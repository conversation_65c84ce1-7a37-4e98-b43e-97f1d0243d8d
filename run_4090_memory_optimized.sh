#!/bin/bash
# RTX 4090内存优化启动脚本

echo "🚀 RTX 4090内存优化训练"
echo "================================"

# 设置环境变量
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128,expandable_segments:True
export TORCH_CUDNN_V8_API_ENABLED=1
export CUDA_LAUNCH_BLOCKING=0
export PYTHONWARNINGS='ignore'

# 数据路径
DATA_DIR="${1:-/media/cidi/0B33112D0B33112D/data/railway_track_dataset_6mm}"
OUTPUT_DIR="${2:-rtx4090_memory_optimized}"

echo "数据目录: $DATA_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

# 检查GPU
nvidia-smi --query-gpu=name,memory.total --format=csv,noheader

echo ""
echo "🔧 启动内存优化训练..."

# 运行训练
python scripts/ensemble_training_notebook_exact_with_fusion.py \
    --config configs/rtx4090_memory_optimized.yaml \
    --data-dir "$DATA_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --models eca_nfnet_l2 seresnet152d \
    --ensemble-iterations 1000

echo "✅ 训练完成"
