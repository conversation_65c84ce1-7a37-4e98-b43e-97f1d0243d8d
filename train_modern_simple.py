#!/usr/bin/env python3
"""
Simplified modern training script that incorporates advanced techniques
without complex dependencies.
"""

import os
import sys
import yaml
import argparse
import math
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.optim.lr_scheduler import _LRScheduler
import segmentation_models_pytorch as smp
from tqdm import tqdm
import numpy as np
import cv2


class SimpleRailwayDataset(Dataset):
    """Simple dataset class for railway track segmentation."""
    
    def __init__(self, data_dir, image_size=(544, 960)):
        self.data_dir = Path(data_dir)
        self.image_dir = self.data_dir / 'images'
        self.mask_dir = self.data_dir / 'masks'
        self.image_size = image_size
        
        # Get all image files
        self.image_files = sorted(list(self.image_dir.glob('*.jpg')) + 
                                 list(self.image_dir.glob('*.png')))
        
        print(f"Found {len(self.image_files)} images in {self.image_dir}")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # Load image
        image_path = self.image_files[idx]
        image = cv2.imread(str(image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load mask
        mask_path = self.mask_dir / image_path.name.replace('.jpg', '.png')
        if mask_path.exists():
            mask = cv2.imread(str(mask_path), cv2.IMREAD_COLOR)
            # Convert BGR to multi-label format
            multilabel_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.float32)
            multilabel_mask[:, :, 0] = (mask[:, :, 0] > 127).astype(np.float32)  # B: background
            multilabel_mask[:, :, 1] = (mask[:, :, 1] > 127).astype(np.float32)  # G: main track
            multilabel_mask[:, :, 2] = (mask[:, :, 2] > 127).astype(np.float32)  # R: fork track
        else:
            # Create empty mask if not found
            multilabel_mask = np.zeros((image.shape[0], image.shape[1], 3), dtype=np.float32)
            multilabel_mask[:, :, 0] = 1.0  # All background
        
        # Resize
        image = cv2.resize(image, self.image_size)
        multilabel_mask = cv2.resize(multilabel_mask, self.image_size)
        
        # Normalize image
        image = image.astype(np.float32) / 255.0
        image = (image - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
        
        # Convert to tensors
        image = torch.from_numpy(image.transpose(2, 0, 1)).float()
        mask = torch.from_numpy(multilabel_mask.transpose(2, 0, 1)).float()
        
        return {'image': image, 'mask': mask}


class CosineAnnealingWarmupRestarts(_LRScheduler):
    """Cosine annealing scheduler with warm restarts and linear warmup."""
    
    def __init__(self, optimizer, first_cycle_steps, max_lr=0.1, min_lr=0.001,
                 warmup_steps=0, gamma=1.0, last_epoch=-1):
        self.first_cycle_steps = first_cycle_steps
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma
        
        self.cur_cycle_steps = first_cycle_steps
        self.cycle = 0
        self.step_in_cycle = last_epoch
        
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        if self.step_in_cycle < self.warmup_steps:
            return [(self.max_lr - self.min_lr) * self.step_in_cycle / 
                    self.warmup_steps + self.min_lr for _ in self.base_lrs]
        else:
            return [self.min_lr + (self.max_lr - self.min_lr) * 
                    (1 + math.cos(math.pi * (self.step_in_cycle - self.warmup_steps) / 
                    (self.cur_cycle_steps - self.warmup_steps))) / 2
                    for _ in self.base_lrs]
    
    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
            self.step_in_cycle = self.step_in_cycle + 1
            if self.step_in_cycle >= self.cur_cycle_steps:
                self.cycle += 1
                self.step_in_cycle = self.step_in_cycle - self.cur_cycle_steps
        else:
            self.step_in_cycle = epoch
        
        self.last_epoch = math.floor(epoch)
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr


class LabelSmoothingLoss(nn.Module):
    """Label smoothing loss for multi-label segmentation."""
    
    def __init__(self, smoothing=0.1):
        super().__init__()
        self.smoothing = smoothing
        self.bce = nn.BCEWithLogitsLoss()
    
    def forward(self, pred, target):
        # Apply label smoothing
        target_smooth = target * (1 - self.smoothing) + self.smoothing / 2
        return self.bce(pred, target_smooth)


class EMA:
    """Exponential Moving Average for model parameters."""
    
    def __init__(self, model, decay=0.999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
    
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = self.decay * self.shadow[name] + \
                                   (1.0 - self.decay) * param.data
    
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.backup[name] = param.data
                param.data = self.shadow[name]
    
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                param.data = self.backup[name]
        self.backup = {}


def calculate_metrics(pred, target, threshold=0.5):
    """Calculate IoU and Dice metrics."""
    pred = (torch.sigmoid(pred) > threshold).float()
    
    # Calculate IoU
    intersection = (pred * target).sum(dim=(2, 3))
    union = (pred + target - pred * target).sum(dim=(2, 3))
    iou = (intersection + 1e-6) / (union + 1e-6)
    
    # Calculate Dice
    dice = (2.0 * intersection + 1e-6) / (pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) + 1e-6)
    
    return iou.mean(), dice.mean()


def train_epoch(model, dataloader, criterion, optimizer, device, ema=None, 
                gradient_clip=1.0, mixed_precision=True):
    """Train for one epoch with modern techniques."""
    model.train()
    total_loss = 0
    total_iou = 0
    
    # Mixed precision scaler
    scaler = torch.cuda.amp.GradScaler() if mixed_precision else None
    
    pbar = tqdm(dataloader, desc='Training')
    for batch in pbar:
        images = batch['image'].to(device)
        masks = batch['mask'].to(device)
        
        # Mixed precision training
        if mixed_precision:
            with torch.cuda.amp.autocast():
                outputs = model(images)
                loss = criterion(outputs, masks)
            
            optimizer.zero_grad()
            scaler.scale(loss).backward()
            
            # Gradient clipping
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip)
            
            scaler.step(optimizer)
            scaler.update()
        else:
            # Standard training
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip)
            optimizer.step()
        
        # Update EMA
        if ema is not None:
            ema.update()
        
        # Calculate metrics
        with torch.no_grad():
            iou, _ = calculate_metrics(outputs, masks)
        
        total_loss += loss.item()
        total_iou += iou.item()
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}', 'iou': f'{iou.item():.4f}'})
    
    return total_loss / len(dataloader), total_iou / len(dataloader)


def validate(model, dataloader, criterion, device, ema=None):
    """Validate the model."""
    model.eval()
    
    # Apply EMA if available
    if ema is not None:
        ema.apply_shadow()
    
    total_loss = 0
    total_iou = 0
    total_dice = 0
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc='Validation'):
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            iou, dice = calculate_metrics(outputs, masks)
            
            total_loss += loss.item()
            total_iou += iou.item()
            total_dice += dice.item()
    
    # Restore original weights
    if ema is not None:
        ema.restore()
    
    return (total_loss / len(dataloader), 
            total_iou / len(dataloader), 
            total_dice / len(dataloader))


def main():
    parser = argparse.ArgumentParser(description='Modern training with advanced techniques')
    parser.add_argument('--data-dir', type=str, 
                       default='/home/<USER>/data/railway_track_dataset',
                       help='Path to data directory')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of epochs')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--warmup-epochs', type=int, default=5,
                       help='Number of warmup epochs')
    parser.add_argument('--label-smoothing', type=float, default=0.1,
                       help='Label smoothing factor')
    parser.add_argument('--ema-decay', type=float, default=0.999,
                       help='EMA decay rate')
    parser.add_argument('--mixed-precision', action='store_true', default=True,
                       help='Use mixed precision training')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("Modern Training with Advanced Techniques")
    print("=" * 80)
    print(f"Configuration:")
    print(f"  Epochs: {args.epochs}")
    print(f"  Batch size: {args.batch_size}")
    print(f"  Learning rate: {args.lr}")
    print(f"  Warmup epochs: {args.warmup_epochs}")
    print(f"  Label smoothing: {args.label_smoothing}")
    print(f"  EMA decay: {args.ema_decay}")
    print(f"  Mixed precision: {args.mixed_precision}")
    print("=" * 80)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"\nUsing device: {device}")
    
    # Create datasets
    print("\nCreating datasets...")
    train_dataset = SimpleRailwayDataset(
        data_dir=os.path.join(args.data_dir, 'train'),
        image_size=(544, 960)
    )
    
    val_dataset = SimpleRailwayDataset(
        data_dir=os.path.join(args.data_dir, 'val'),
        image_size=(544, 960)
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")
    
    # Create model
    print("\nCreating model...")
    model = smp.Unet(
        encoder_name='resnet34',
        encoder_weights='imagenet',
        in_channels=3,
        classes=3,  # Multi-label: background, main_track, fork_track
    )
    model = model.to(device)
    
    # Create loss with label smoothing
    criterion = LabelSmoothingLoss(smoothing=args.label_smoothing)
    
    # Create optimizer (AdamW with decoupled weight decay)
    optimizer = torch.optim.AdamW(model.parameters(), lr=args.lr, weight_decay=0.01)
    
    # Create cosine scheduler with warmup
    steps_per_epoch = len(train_loader)
    total_steps = args.epochs * steps_per_epoch
    warmup_steps = args.warmup_epochs * steps_per_epoch
    
    scheduler = CosineAnnealingWarmupRestarts(
        optimizer,
        first_cycle_steps=total_steps,
        max_lr=args.lr,
        min_lr=1e-6,
        warmup_steps=warmup_steps
    )
    
    # Create EMA
    ema = EMA(model, decay=args.ema_decay)
    
    # Training loop
    print(f"\nStarting modern training for {args.epochs} epochs...")
    print("Features enabled:")
    print("  ✓ Cosine learning rate with warmup")
    print("  ✓ Exponential Moving Average (EMA)")
    print("  ✓ Label smoothing")
    print("  ✓ Gradient clipping")
    if args.mixed_precision:
        print("  ✓ Mixed precision training")
    print("=" * 80)
    
    best_iou = 0
    for epoch in range(1, args.epochs + 1):
        # Train
        train_loss, train_iou = train_epoch(
            model, train_loader, criterion, optimizer, device,
            ema=ema, mixed_precision=args.mixed_precision
        )
        
        # Step scheduler
        for _ in range(steps_per_epoch):
            scheduler.step()
        
        # Validate
        val_loss, val_iou, val_dice = validate(
            model, val_loader, criterion, device, ema=ema
        )
        
        # Get current learning rate
        current_lr = optimizer.param_groups[0]['lr']
        
        # Print epoch results
        print(f"\nEpoch {epoch}/{args.epochs} (LR: {current_lr:.6f})")
        print(f"Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}, Dice: {val_dice:.4f}")
        
        # Save best model
        if val_iou > best_iou:
            best_iou = val_iou
            save_path = Path('outputs/checkpoints')
            save_path.mkdir(parents=True, exist_ok=True)
            
            # Save regular model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_iou': best_iou,
            }, save_path / 'best_model_modern.pth')
            
            # Save EMA model
            ema.apply_shadow()
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'best_iou': best_iou,
            }, save_path / 'best_model_ema.pth')
            ema.restore()
            
            print(f"Saved best model with IoU: {best_iou:.4f}")
        
        print("-" * 80)
    
    print("\nModern training completed!")
    print(f"Best validation IoU: {best_iou:.4f}")
    
    # Save final models
    torch.save(model.state_dict(), 'outputs/checkpoints/final_model_modern.pth')
    ema.apply_shadow()
    torch.save(model.state_dict(), 'outputs/checkpoints/final_model_ema.pth')
    print("Saved final models (regular and EMA)")


if __name__ == '__main__':
    main()