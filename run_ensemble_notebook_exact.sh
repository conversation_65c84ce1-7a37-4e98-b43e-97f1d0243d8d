#!/bin/bash
# 集成训练运行脚本 - Notebook精确复现版

echo "=========================================="
echo "集成训练脚本 - Notebook精确复现版"
echo "=========================================="

# 设置默认参数
DATA_ROOT="${DATA_ROOT:-./}"
OUTPUT_DIR="${OUTPUT_DIR:-weights}"
BATCH_SIZE="${BATCH_SIZE:-8}"
NUM_WORKERS="${NUM_WORKERS:-2}"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --resume)
      RESUME_FLAG="--resume"
      shift
      ;;
    --data-root)
      DATA_ROOT="$2"
      shift 2
      ;;
    --output-dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --pretrained-efficientnet)
      PRETRAINED_EFFICIENTNET="--pretrained-efficientnet $2"
      shift 2
      ;;
    --pretrained-nfnet)
      PRETRAINED_NFNET="--pretrained-nfnet $2"
      shift 2
      ;;
    --pretrained-resnet)
      PRETRAINED_RESNET="--pretrained-resnet $2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# 检查数据目录
if [ ! -d "$DATA_ROOT/train/images" ] || [ ! -d "$DATA_ROOT/train/mask" ]; then
    echo "错误: 找不到训练数据目录"
    echo "期望的结构:"
    echo "  $DATA_ROOT/train/images/"
    echo "  $DATA_ROOT/train/mask/"
    exit 1
fi

# 显示配置
echo ""
echo "训练配置:"
echo "  数据根目录: $DATA_ROOT"
echo "  输出目录: $OUTPUT_DIR"
echo "  批量大小: $BATCH_SIZE"
echo "  工作线程: $NUM_WORKERS"
[ ! -z "$RESUME_FLAG" ] && echo "  恢复训练: 是"
[ ! -z "$PRETRAINED_EFFICIENTNET" ] && echo "  预训练EfficientNet: 已指定"
[ ! -z "$PRETRAINED_NFNET" ] && echo "  预训练NFNet: 已指定"
[ ! -z "$PRETRAINED_RESNET" ] && echo "  预训练ResNet: 已指定"
echo ""

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 运行训练脚本
python scripts/ensemble_training_notebook_exact.py \
    --data-root "$DATA_ROOT" \
    --output-dir "$OUTPUT_DIR" \
    --batch-size "$BATCH_SIZE" \
    --num-workers "$NUM_WORKERS" \
    $RESUME_FLAG \
    $PRETRAINED_EFFICIENTNET \
    $PRETRAINED_NFNET \
    $PRETRAINED_RESNET

# 检查训练结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "训练完成!"
    echo "模型保存在: $OUTPUT_DIR"
    echo "=========================================="
else
    echo ""
    echo "=========================================="
    echo "训练失败，请检查错误信息"
    echo "=========================================="
    exit 1
fi