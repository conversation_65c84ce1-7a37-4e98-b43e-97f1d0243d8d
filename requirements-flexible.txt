# Flexible requirements based on current railway-seg environment
# Use this for more flexible version constraints

# Core ML/DL frameworks
torch>=2.7.0
torchvision>=0.22.0
segmentation-models-pytorch>=0.5.0

# Computer Vision and Image Processing
opencv-contrib-python>=4.11.0
albumentations>=2.0.0
Pillow>=11.0.0

# Model architectures and utilities
timm>=1.0.0

# Scientific computing
numpy>=2.3.0
scipy>=1.15.0
scikit-learn>=1.7.0
pandas>=2.3.0

# Visualization
matplotlib>=3.10.0
seaborn>=0.13.0

# Utilities
tqdm>=4.67.0
pyyaml>=6.0.0

# File handling and serialization
safetensors>=0.5.0

# System monitoring
psutil>=7.0.0

# Additional ML utilities
sympy>=1.14.0
networkx>=3.5.0

# Optional dependencies for enhanced functionality
# Uncomment if needed:
# tensorboard>=2.11.0
# wandb>=0.15.0
# plotly>=5.15.0
# omegaconf>=2.3.0
# easydict>=1.13
# dill>=0.4.0
# jupyter>=1.0.0
# ipywidgets>=8.0.0
# memory-profiler>=0.61.0
# rich>=13.4.0
