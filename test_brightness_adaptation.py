#!/usr/bin/env python3
"""
测试通过调整图像亮度来改善预测效果
"""

import cv2
import numpy as np
import torch
import albumentations as A
from albumentations.pytorch import ToTensorV2
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))
from src.models.ensemble import EnsemblePredictor

def adjust_brightness(image, target_mean=50):
    """
    调整图像亮度到目标均值
    """
    # 转换为灰度计算当前亮度
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    current_mean = gray.mean()
    
    # 计算调整因子
    if current_mean > 0:
        factor = target_mean / current_mean
        # 限制调整范围，避免过度调整
        factor = np.clip(factor, 0.3, 3.0)
    else:
        factor = 1.0
    
    # 应用亮度调整
    adjusted = image * factor
    adjusted = np.clip(adjusted, 0, 255).astype(np.uint8)
    
    return adjusted, factor

def test_brightness_variations(image_path, model_predictor):
    """
    测试不同亮度下的预测效果
    """
    print(f"🔍 测试图像: {image_path}")
    
    # 读取原图
    image = cv2.imread(image_path)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 测试不同的目标亮度
    target_brightnesses = [20, 40, 60, 80, 100, 120]
    
    results = []
    
    # 预处理变换
    transforms = A.Compose([
        A.Resize(height=544, width=960),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])
    
    print(f"原始图像灰度均值: {cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY).mean():.2f}")
    print("\n测试不同亮度调整:")
    print("-" * 60)
    
    for target_brightness in target_brightnesses:
        # 调整亮度
        adjusted_image, factor = adjust_brightness(image_rgb, target_brightness)
        
        # 预处理
        transformed = transforms(image=adjusted_image)
        input_tensor = transformed['image'].unsqueeze(0).cuda()
        
        # 预测
        with torch.no_grad():
            prediction = model_predictor.predict(input_tensor)
            prediction = prediction.squeeze(0).cpu().numpy()
        
        # 统计结果
        main_track_mean = prediction[1].mean()
        main_track_max = prediction[1].max()
        fork_track_mean = prediction[2].mean()
        fork_track_max = prediction[2].max()
        
        result = {
            'target_brightness': target_brightness,
            'factor': factor,
            'actual_brightness': cv2.cvtColor(adjusted_image, cv2.COLOR_RGB2GRAY).mean(),
            'main_track_mean': main_track_mean,
            'main_track_max': main_track_max,
            'fork_track_mean': fork_track_mean,
            'fork_track_max': fork_track_max
        }
        
        results.append(result)
        
        print(f"目标亮度 {target_brightness:3d} | 调整因子 {factor:.2f} | "
              f"实际亮度 {result['actual_brightness']:6.1f} | "
              f"主轨道 均值:{main_track_mean:.4f} 最大:{main_track_max:.4f}")
    
    # 找到最佳亮度
    best_result = max(results, key=lambda x: x['main_track_mean'])
    
    print("\n" + "=" * 60)
    print("🎯 最佳调整结果:")
    print(f"   最佳目标亮度: {best_result['target_brightness']}")
    print(f"   调整因子: {best_result['factor']:.2f}")
    print(f"   主轨道检测: 均值 {best_result['main_track_mean']:.4f}, 最大 {best_result['main_track_max']:.4f}")
    
    return best_result

def load_models():
    """加载集成模型"""
    import segmentation_models_pytorch as smp
    
    weights_dir = Path('finetune')
    device = torch.device('cuda')
    
    # 模型配置
    models_config = [
        {
            'name': 'efficientnet_b4',
            'actual_name': 'efficientnetb4'
        },
        {
            'name': 'eca_nfnet_l2',
            'actual_name': 'eca_nfnet_l2'
        },
        {
            'name': 'seresnet152d',
            'actual_name': 'seresnet152d'
        }
    ]
    
    # 加载模型
    models = []
    
    for model_config in models_config:
        weights_path = weights_dir / f"{model_config['actual_name']}.pth.tar"
        model = torch.load(weights_path, map_location=device, weights_only=False)
        model.eval()
        models.append(model)
    
    # 创建均等权重
    num_classes = 3
    fusion_weights = torch.ones(num_classes, len(models)) / len(models)
    
    # 创建集成预测器
    ensemble_predictor = EnsemblePredictor(models, fusion_weights, device)
    
    return ensemble_predictor

if __name__ == "__main__":
    # 问题图像
    problem_image = "/home/<USER>/data/railway_track_dataset/test/images/20250119132744163.near.avi_frame_1291.png"
    
    if Path(problem_image).exists():
        print("="*80)
        print("🔬 亮度适应测试")
        print("="*80)
        
        # 加载模型
        predictor = load_models()
        
        # 测试不同亮度
        best_result = test_brightness_variations(problem_image, predictor)
        
        print(f"\n💡 建议:")
        print(f"   对于这类亮度正常的图像，建议调整亮度因子为 {best_result['factor']:.2f}")
        print(f"   这样可以将图像亮度从正常水平调整到模型期望的较暗水平")
        
    else:
        print(f"❌ 图像不存在: {problem_image}") 