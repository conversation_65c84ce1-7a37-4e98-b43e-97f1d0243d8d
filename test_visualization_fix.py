#!/usr/bin/env python3
"""
测试可视化修复
验证每个epoch是否从整个验证集随机抽取5张不同的图像进行可视化
"""

import subprocess
import sys
import os
import time
from pathlib import Path
import re

def test_visualization_randomness():
    """测试可视化随机性"""
    print("🧪 测试可视化随机性")
    print("=" * 80)
    
    # 设置测试参数
    data_dir = "/home/<USER>/data/railway_track_dataset"
    output_dir = "test_visualization_fix"
    
    # 检查数据目录
    if not Path(data_dir).exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 清理之前的输出
    output_path = Path(output_dir)
    if output_path.exists():
        import shutil
        shutil.rmtree(output_path)
    
    # 构建命令 - 运行2个epoch来测试可视化随机性
    cmd = [
        sys.executable,
        "scripts/ensemble_training_notebook_exact_with_fusion.py",
        "--config", "configs/railway_track_config.yaml",
        "--data-dir", data_dir,
        "--models", "eca_nfnet_l2",
        "--output-dir", output_dir,
        "--ensemble-iterations", "2",  # 运行2个epoch测试
        "--skip-ensemble"  # 跳过集成学习
    ]
    
    # 添加预训练权重（如果存在）
    if Path("eca_nfnet_l2.pth_converted.pth").exists():
        cmd.extend(["--pretrained-nfnet", "eca_nfnet_l2.pth_converted.pth"])
    
    print("🚀 运行可视化测试:")
    print(" ".join(cmd))
    print()
    
    try:
        print("开始训练测试（2个epoch）...")
        start_time = time.time()
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示输出并收集可视化相关信息
        output_lines = []
        vis_messages = []
        epoch_vis_indices = {}  # 存储每个epoch的可视化样本索引
        
        current_epoch = None
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # 检测epoch开始
                epoch_match = re.search(r'Epoch (\d+)', line)
                if epoch_match:
                    current_epoch = int(epoch_match.group(1))
                
                # 收集可视化相关信息
                if any(keyword in line for keyword in ['可视化', '本epoch将可视化样本索引', '可视化已保存']):
                    vis_messages.append(line)
                    
                    # 提取可视化样本索引
                    if '本epoch将可视化样本索引' in line and current_epoch:
                        indices_match = re.search(r'\[([\d, ]+)\]', line)
                        if indices_match:
                            indices_str = indices_match.group(1)
                            indices = [int(x.strip()) for x in indices_str.split(',')]
                            epoch_vis_indices[current_epoch] = indices
        
        # 等待进程完成
        return_code = process.poll()
        end_time = time.time()
        
        print(f"\n训练测试完成，耗时: {end_time - start_time:.1f}秒")
        print(f"返回代码: {return_code}")
        
        # 分析结果
        if return_code == 0:
            print("✅ 训练脚本运行成功！")
            
            # 分析可视化随机性
            print(f"\n📊 可视化随机性分析:")
            print("=" * 60)
            
            if len(epoch_vis_indices) >= 2:
                epoch1_indices = set(epoch_vis_indices.get(1, []))
                epoch2_indices = set(epoch_vis_indices.get(2, []))
                
                print(f"Epoch 1 可视化样本索引: {sorted(epoch1_indices)}")
                print(f"Epoch 2 可视化样本索引: {sorted(epoch2_indices)}")
                
                # 检查是否有重叠
                overlap = epoch1_indices.intersection(epoch2_indices)
                print(f"重叠的样本索引: {sorted(overlap) if overlap else '无'}")
                print(f"重叠比例: {len(overlap) / len(epoch1_indices) * 100:.1f}%")
                
                # 验证随机性
                if len(overlap) < len(epoch1_indices):
                    print("✅ 可视化样本在不同epoch间有变化")
                    randomness_ok = True
                else:
                    print("❌ 可视化样本在不同epoch间完全相同")
                    randomness_ok = False
                
                # 验证数量
                if len(epoch1_indices) == 5 and len(epoch2_indices) == 5:
                    print("✅ 每个epoch都保存了5张可视化图像")
                    count_ok = True
                else:
                    print(f"❌ 可视化数量不正确: Epoch1={len(epoch1_indices)}, Epoch2={len(epoch2_indices)}")
                    count_ok = False
                
            else:
                print("❌ 未能提取到足够的epoch可视化信息")
                randomness_ok = False
                count_ok = False
            
            return randomness_ok and count_ok
        else:
            print("❌ 训练脚本运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 运行训练脚本时出错: {e}")
        return False

def analyze_saved_visualizations():
    """分析保存的可视化文件"""
    print("\n🔍 分析保存的可视化文件")
    print("=" * 80)
    
    output_dir = Path("test_visualization_fix")
    vis_dir = output_dir / "visualizations" / "eca_nfnet_l2"
    
    if not vis_dir.exists():
        print("❌ 可视化目录不存在")
        return False
    
    try:
        # 获取所有可视化文件
        vis_files = list(vis_dir.glob("epoch_*.png"))
        print(f"📁 找到 {len(vis_files)} 个可视化文件")
        
        # 按epoch分组
        epoch_files = {}
        for file in vis_files:
            # 提取epoch和样本索引
            match = re.match(r'epoch_(\d+)_sample_(\d+)\.png', file.name)
            if match:
                epoch = int(match.group(1))
                sample_idx = int(match.group(2))
                
                if epoch not in epoch_files:
                    epoch_files[epoch] = []
                epoch_files[epoch].append(sample_idx)
        
        # 分析每个epoch的可视化
        for epoch in sorted(epoch_files.keys()):
            sample_indices = sorted(epoch_files[epoch])
            print(f"Epoch {epoch}: {len(sample_indices)} 张图像, 样本索引: {sample_indices}")
        
        # 验证随机性
        if len(epoch_files) >= 2:
            epochs = sorted(epoch_files.keys())
            epoch1_samples = set(epoch_files[epochs[0]])
            epoch2_samples = set(epoch_files[epochs[1]])
            
            overlap = epoch1_samples.intersection(epoch2_samples)
            print(f"\n📊 随机性分析:")
            print(f"Epoch {epochs[0]} vs Epoch {epochs[1]}:")
            print(f"  重叠样本: {sorted(overlap) if overlap else '无'}")
            print(f"  重叠比例: {len(overlap) / max(len(epoch1_samples), 1) * 100:.1f}%")
            
            # 验证结果
            files_ok = (len(epoch1_samples) == 5 and len(epoch2_samples) == 5 and len(overlap) < 5)
            
            if files_ok:
                print("✅ 可视化文件分析通过")
                return True
            else:
                print("❌ 可视化文件分析失败")
                return False
        else:
            print("❌ 没有足够的epoch进行比较")
            return False
            
    except Exception as e:
        print(f"❌ 分析可视化文件失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件")
    print("=" * 60)
    
    output_dir = Path("test_visualization_fix")
    if output_dir.exists():
        import shutil
        try:
            shutil.rmtree(output_dir)
            print("✅ 测试文件已清理")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")

def main():
    """主函数"""
    print("🔧 可视化随机性修复测试")
    print("=" * 80)
    
    # 1. 测试可视化随机性
    training_ok = test_visualization_randomness()
    
    # 2. 分析保存的可视化文件
    if training_ok:
        files_ok = analyze_saved_visualizations()
    else:
        files_ok = False
    
    # 3. 清理测试文件
    cleanup_test_files()
    
    print(f"\n📊 测试结果:")
    print("=" * 80)
    print(f"训练运行: {'✅' if training_ok else '❌'}")
    print(f"文件分析: {'✅' if files_ok else '❌'}")
    
    if training_ok and files_ok:
        print(f"\n🎉 可视化随机性修复成功！")
        print("✅ 验证内容:")
        print("1. 每个epoch从整个验证集随机选择5张图像")
        print("2. 不同epoch的可视化样本有变化")
        print("3. 可视化文件正确保存")
        print("4. 样本索引命名清晰")
        print("\n🚀 现在每个epoch都会显示不同的验证样本！")
    else:
        print(f"\n⚠️  测试失败，需要进一步检查")
        if not training_ok:
            print("- 训练脚本运行失败或可视化逻辑有问题")
        if not files_ok:
            print("- 可视化文件保存或随机性有问题")

if __name__ == '__main__':
    main()
