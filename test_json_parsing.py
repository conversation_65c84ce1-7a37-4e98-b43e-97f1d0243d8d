#!/usr/bin/env python3
"""
测试JSON文件解析和掩码生成
"""

import sys
import json
import numpy as np
from pathlib import Path
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser

def test_json_parsing():
    """测试JSON文件解析"""
    
    # 目标JSON文件路径
    json_path = Path("/home/<USER>/Downloads/轨道线标注导出/railway_annotation_6mm/20250119105544921.near.avi_frame_2.json")
    
    if not json_path.exists():
        print(f"错误：JSON文件不存在: {json_path}")
        return
    
    print(f"测试JSON文件: {json_path}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    try:
        # 解析JSON文件
        annotation_data = parser.parse_json_file(json_path)
        
        print("\n=== 解析结果 ===")
        print(f"文件名: {annotation_data['filename']}")
        print(f"相机类型: {annotation_data['camera_type']}")
        print(f"轨道数量: {len(annotation_data['tracks'])}")
        
        # 显示轨道信息
        for i, track in enumerate(annotation_data['tracks']):
            print(f"  轨道 {i+1}: {track['label']} - {len(track['points'])} 个点")
        
        # 测试掩码生成
        image_shape = (1080, 1920)
        print(f"\n=== 掩码生成测试 ===")
        print(f"图像尺寸: {image_shape}")
        
        # 生成多标签掩码
        multilabel_mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
        
        print(f"掩码形状: {multilabel_mask.shape}")
        print(f"掩码数据类型: {multilabel_mask.dtype}")
        
        # 统计各通道的像素数
        background_pixels = np.sum(multilabel_mask[:, :, 0] > 0)
        main_track_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_track_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        
        print(f"\n=== 像素统计 ===")
        print(f"背景像素: {background_pixels}")
        print(f"主轨道像素: {main_track_pixels}")
        print(f"分叉轨道像素: {fork_track_pixels}")
        
        # 检查是否有空标签
        if main_track_pixels == 0 and fork_track_pixels == 0:
            print("\n❌ 问题：生成了空标签（没有轨道像素）")
            
            # 进一步调试
            print("\n=== 调试信息 ===")
            print("原始JSON数据:")
            with open(json_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            print(f"labels字段数量: {len(raw_data['labels'])}")
            for i, label_data in enumerate(raw_data['labels']):
                label = label_data['label']
                points = label_data['points']
                print(f"  {i+1}. {label}: {len(points)} 个点")
                
                # 解析点数据
                parsed_points = parser._parse_points(points)
                print(f"     解析后: {len(parsed_points)} 个点")
                
                if len(parsed_points) >= 2:
                    print(f"     第一个点: {parsed_points[0]}")
                    print(f"     最后一个点: {parsed_points[-1]}")
        else:
            print("\n✅ 成功：生成了有效的掩码")
            
            # 保存测试掩码
            test_mask_path = "test_mask_20250119105544921.near.avi_frame_2.png"
            bgr_mask = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
            bgr_mask[:, :, 0] = (multilabel_mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
            bgr_mask[:, :, 1] = (multilabel_mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
            bgr_mask[:, :, 2] = (multilabel_mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
            
            cv2.imwrite(test_mask_path, bgr_mask)
            print(f"测试掩码已保存到: {test_mask_path}")
        
    except Exception as e:
        print(f"❌ 错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_json_parsing() 