#!/usr/bin/env python3
"""
集成学习测试预测示例脚本
演示如何使用集成测试预测功能
"""

import sys
import os
from pathlib import Path
import subprocess
import yaml

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_ensemble_test_prediction():
    """运行集成学习测试预测示例"""
    
    print("🚀 集成学习测试预测示例")
    print("=" * 50)
    
    # 配置参数
    config = {
        'weights_dir': 'outputs/enhanced_experiment/weights',  # 权重目录
        'config_file': 'configs/railway_track_config.yaml',    # 配置文件
        'data_dir': '/home/<USER>/data/railway_track_dataset',   # 数据目录
        'output_dir': './test_prediction_results',             # 输出目录
        'threshold': 0.5,                                      # 预测阈值
        'num_samples': 8,                                      # 可视化样本数
        'device': 'cuda'                                       # 设备
    }
    
    # 检查必要文件是否存在
    print("🔍 检查必要文件...")
    
    weights_dir = Path(config['weights_dir'])
    config_file = Path(config['config_file'])
    data_dir = Path(config['data_dir'])
    
    # 检查权重目录
    if not weights_dir.exists():
        print(f"❌ 权重目录不存在: {weights_dir}")
        print("请先训练集成模型或指定正确的权重目录")
        return False
    
    # 检查必要的权重文件
    required_weights = ['efficientnet_b4.pth', 'eca_nfnet_l2.pth', 'seresnet152d.pth']
    missing_weights = []
    
    for weight_file in required_weights:
        if not (weights_dir / weight_file).exists():
            missing_weights.append(weight_file)
    
    if missing_weights:
        print(f"❌ 缺少权重文件: {missing_weights}")
        print("请确保所有模型权重文件都存在")
        return False
    
    # 检查集成权重文件
    ensemble_weights_file = weights_dir / 'ensemble_weights.yaml'
    if not ensemble_weights_file.exists():
        print("⚠️  未找到集成权重文件，将使用等权重")
    else:
        print("✅ 找到集成权重文件")
    
    # 检查配置文件
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 检查数据目录
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        print("请指定正确的测试数据目录")
        return False
    
    # 检查测试数据
    test_images_dir = data_dir / 'test' / 'images'
    if not test_images_dir.exists() or len(list(test_images_dir.glob('*.jpg'))) == 0:
        print(f"❌ 测试图像目录为空: {test_images_dir}")
        return False
    
    test_masks_dir = data_dir / 'test' / 'masks'
    if test_masks_dir.exists():
        print("✅ 找到测试标签，将计算详细性能指标")
    else:
        print("⚠️  未找到测试标签，仅进行预测可视化")
    
    print("✅ 所有检查通过，开始测试预测...")
    
    # 构建命令
    cmd = [
        'python', str(project_root / 'scripts' / 'ensemble_test_prediction.py'),
        '--weights-dir', str(config['weights_dir']),
        '--config', str(config['config_file']),
        '--data-dir', str(config['data_dir']),
        '--output-dir', str(config['output_dir']),
        '--threshold', str(config['threshold']),
        '--num-samples', str(config['num_samples']),
        '--device', str(config['device'])
    ]
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    print()
    
    try:
        # 执行预测
        result = subprocess.run(cmd, check=True, capture_output=False)
        
        print()
        print("🎉 测试预测完成！")
        
        # 显示结果位置
        output_dir = Path(config['output_dir'])
        if output_dir.exists():
            print(f"📊 结果已保存到: {output_dir.absolute()}")
            
            # 列出生成的文件
            print("\n📁 生成的文件和目录:")
            for item in sorted(output_dir.rglob('*')):
                if item.is_file():
                    rel_path = item.relative_to(output_dir)
                    size = item.stat().st_size
                    if size > 1024 * 1024:
                        size_str = f"{size / (1024*1024):.1f}MB"
                    elif size > 1024:
                        size_str = f"{size / 1024:.1f}KB"
                    else:
                        size_str = f"{size}B"
                    print(f"  📄 {rel_path} ({size_str})")
            
            # 显示关键结果文件
            key_files = [
                'test_results.json',
                'summary_results.csv',
                'detailed_results.csv'
            ]
            
            print("\n🔍 关键结果文件:")
            for file_name in key_files:
                file_path = output_dir / file_name
                if file_path.exists():
                    print(f"  ✅ {file_name}")
                else:
                    print(f"  ❌ {file_name} (未生成)")
            
            # 显示可视化目录
            vis_dir = output_dir / 'visualizations'
            if vis_dir.exists():
                print(f"\n🎨 可视化结果目录: {vis_dir}")
                
                subdirs = [d for d in vis_dir.iterdir() if d.is_dir()]
                for subdir in sorted(subdirs):
                    file_count = len(list(subdir.glob('*.png')))
                    print(f"  📂 {subdir.name}/ ({file_count} 个PNG文件)")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试预测失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 意外错误: {e}")
        return False


def create_demo_config():
    """创建演示配置"""
    demo_config = {
        'model': {
            'classes': 3,
            'class_names': ['background', 'main_track', 'branch_track']
        },
        'data': {
            'image_size': {'height': 544, 'width': 960},
            'batch_size': {'test': 1},
            'num_workers': 2
        },
        'ensemble': {
            'models': [
                {
                    'name': 'efficientnet_b4',
                    'architecture': 'pan',
                    'encoder': 'tu-tf_efficientnet_b4_ns'
                },
                {
                    'name': 'eca_nfnet_l2', 
                    'architecture': 'pan',
                    'encoder': 'tu-eca_nfnet_l2'
                },
                {
                    'name': 'seresnet152d',
                    'architecture': 'pan', 
                    'encoder': 'tu-seresnet152d'
                }
            ]
        }
    }
    
    config_path = Path('demo_ensemble_config.yaml')
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(demo_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ 演示配置已创建: {config_path}")
    return config_path


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("-" * 30)
    
    examples = [
        {
            'title': '基础使用',
            'command': '''python scripts/ensemble_test_prediction.py \\
    --weights-dir outputs/your_experiment/weights \\
    --config configs/railway_track_config.yaml \\
    --data-dir /path/to/test_data \\
    --output-dir ./test_results'''
        },
        {
            'title': '自定义阈值',
            'command': '''python scripts/ensemble_test_prediction.py \\
    --weights-dir outputs/enhanced_experiment/weights \\
    --config configs/railway_track_config.yaml \\
    --data-dir /home/<USER>/data/railway_track_dataset \\
    --output-dir ./detailed_results \\
    --threshold 0.6 \\
    --num-samples 12'''
        },
        {
            'title': '使用CPU',
            'command': '''python scripts/ensemble_test_prediction.py \\
    --weights-dir outputs/ensemble_weights \\
    --config configs/railway_track_config.yaml \\
    --data-dir /path/to/test_data \\
    --output-dir ./cpu_results \\
    --device cpu'''
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(example['command'])


if __name__ == '__main__':
    print("🎯 集成学习测试预测示例")
    print("这个脚本演示如何使用集成学习进行测试集预测和可视化分析")
    print()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 60)
    
    # 询问是否运行示例
    response = input("是否运行演示示例？(y/n): ")
    
    if response.lower() in ['y', 'yes', '是']:
        success = run_ensemble_test_prediction()
        if success:
            print("\n🎉 演示完成！查看输出目录了解详细结果。")
        else:
            print("\n❌ 演示失败，请检查配置和数据。")
    else:
        print("\n👋 演示已取消。您可以根据上面的示例手动运行脚本。") 