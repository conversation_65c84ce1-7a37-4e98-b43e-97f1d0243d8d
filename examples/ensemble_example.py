#!/usr/bin/env python3
"""
集成学习使用示例
演示如何在Python代码中使用集成学习功能
"""

import sys
from pathlib import Path
import torch
import numpy as np
from PIL import Image
import yaml
import matplotlib.pyplot as plt

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.ensemble import EnsembleModel, EnsemblePredictor
from src.models.segmentation_model import create_model
from src.data.transforms import get_inference_transform


def load_ensemble_models(weights_dir: str, device: torch.device):
    """
    加载集成模型
    
    Args:
        weights_dir: 权重文件目录
        device: 计算设备
        
    Returns:
        ensemble_predictor: 集成预测器
    """
    weights_dir = Path(weights_dir)
    
    # 模型配置
    model_configs = [
        {
            'name': 'efficientnet_b4',
            'architecture': 'pan',
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'encoder_weights': None,  # 不加载预训练权重
        },
        {
            'name': 'eca_nfnet_l2',
            'architecture': 'pan',
            'encoder': 'tu-eca_nfnet_l2',
            'encoder_weights': None,
        },
        {
            'name': 'seresnet152d',
            'architecture': 'pan',
            'encoder': 'tu-seresnet152d',
            'encoder_weights': None,
        }
    ]
    
    # 加载模型
    models = []
    for config in model_configs:
        print(f"加载模型: {config['name']}")
        
        # 创建模型
        model = create_model(
            architecture=config['architecture'],
            backbone=config['encoder'],
            num_classes=3,
            pretrained=False
        )
        
        # 加载权重
        weights_path = weights_dir / f"{config['name']}.pth"
        if weights_path.exists():
            state_dict = torch.load(weights_path, map_location=device)
            # 处理可能的键名不匹配
            if 'model_state_dict' in state_dict:
                state_dict = state_dict['model_state_dict']
            model.load_state_dict(state_dict)
            print(f"  ✓ 权重加载成功: {weights_path}")
        else:
            print(f"  ✗ 权重文件不存在: {weights_path}")
            
        model.to(device)
        model.eval()
        models.append(model)
    
    # 加载融合权重
    fusion_weights_path = weights_dir / 'ensemble_weights.yaml'
    if fusion_weights_path.exists():
        with open(fusion_weights_path, 'r') as f:
            weights_config = yaml.safe_load(f)
        
        # 构建权重矩阵
        fusion_weights = torch.zeros(3, 3)  # 3个类别，3个模型
        for class_idx, info in weights_config.items():
            fusion_weights[int(class_idx)] = torch.tensor(info['weights'])
        
        print(f"\n融合权重:")
        for i, class_name in enumerate(['背景', '主轨道', '分叉轨道']):
            weights = fusion_weights[i].numpy()
            print(f"  {class_name}: {weights}")
    else:
        print("\n使用默认均等权重")
        fusion_weights = torch.ones(3, 3) / 3
    
    # 创建集成预测器
    ensemble_predictor = EnsemblePredictor(models, fusion_weights, device)
    
    return ensemble_predictor


def predict_single_image(image_path: str, ensemble_predictor: EnsemblePredictor):
    """
    对单张图像进行预测
    
    Args:
        image_path: 图像路径
        ensemble_predictor: 集成预测器
        
    Returns:
        prediction: 预测结果 (H, W, 3)
    """
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    original_size = image.size
    
    # 预处理
    transform = get_inference_transform(image_size=(544, 960))
    image_tensor = transform(image).unsqueeze(0)  # 添加batch维度
    
    # 预测
    with torch.no_grad():
        prediction = ensemble_predictor.predict(image_tensor)
        prediction = torch.sigmoid(prediction)  # 转换为概率
        
    # 后处理
    prediction = prediction.squeeze(0).cpu().numpy()  # (3, H, W)
    prediction = np.transpose(prediction, (1, 2, 0))  # (H, W, 3)
    
    # 调整到原始尺寸
    from PIL import Image as PILImage
    prediction_pil = PILImage.fromarray((prediction * 255).astype(np.uint8))
    prediction_pil = prediction_pil.resize(original_size, PILImage.BILINEAR)
    prediction = np.array(prediction_pil) / 255.0
    
    return prediction


def visualize_prediction(image_path: str, prediction: np.ndarray, save_path: str = None):
    """
    可视化预测结果
    
    Args:
        image_path: 原始图像路径
        prediction: 预测结果 (H, W, 3)
        save_path: 保存路径（可选）
    """
    # 加载原始图像
    image = Image.open(image_path).convert('RGB')
    image_np = np.array(image)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 原始图像
    axes[0, 0].imshow(image_np)
    axes[0, 0].set_title('原始图像')
    axes[0, 0].axis('off')
    
    # 各类别预测
    class_names = ['背景', '主轨道', '分叉轨道']
    for i in range(3):
        axes[0, i].imshow(prediction[:, :, i], cmap='hot')
        axes[0, i].set_title(f'{class_names[i]}概率图')
        axes[0, i].axis('off')
    
    # 合成掩码
    mask = np.argmax(prediction, axis=2)
    colors = np.array([[0, 0, 0], [255, 0, 0], [0, 255, 0]])
    colored_mask = colors[mask]
    
    axes[1, 0].imshow(colored_mask.astype(np.uint8))
    axes[1, 0].set_title('分割结果')
    axes[1, 0].axis('off')
    
    # 叠加显示
    overlay = image_np * 0.7 + colored_mask * 0.3
    axes[1, 1].imshow(overlay.astype(np.uint8))
    axes[1, 1].set_title('叠加显示')
    axes[1, 1].axis('off')
    
    # 隐藏最后一个子图
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
    else:
        plt.show()


def batch_predict(input_dir: str, output_dir: str, ensemble_predictor: EnsemblePredictor):
    """
    批量预测
    
    Args:
        input_dir: 输入图像目录
        output_dir: 输出目录
        ensemble_predictor: 集成预测器
    """
    input_dir = Path(input_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 支持的图像格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = [f for f in input_dir.iterdir() 
                   if f.suffix.lower() in image_extensions]
    
    print(f"\n找到 {len(image_files)} 张图像")
    
    for image_file in image_files:
        print(f"\n处理: {image_file.name}")
        
        try:
            # 预测
            prediction = predict_single_image(str(image_file), ensemble_predictor)
            
            # 保存预测掩码
            mask = np.argmax(prediction, axis=2)
            mask_path = output_dir / f"{image_file.stem}_mask.png"
            Image.fromarray((mask * 127).astype(np.uint8)).save(mask_path)
            
            # 保存可视化结果
            vis_path = output_dir / f"{image_file.stem}_visualization.png"
            visualize_prediction(str(image_file), prediction, str(vis_path))
            
            print(f"  ✓ 预测完成")
            
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")


def main():
    """主函数"""
    # 配置
    weights_dir = "checkpoints/ensemble_weights"
    test_image = "test_images/test1.jpg"
    output_dir = "outputs/ensemble_predictions"
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载集成模型
    print("\n=== 加载集成模型 ===")
    ensemble_predictor = load_ensemble_models(weights_dir, device)
    
    # 单张图像预测示例
    if Path(test_image).exists():
        print("\n=== 单张图像预测 ===")
        prediction = predict_single_image(test_image, ensemble_predictor)
        print(f"预测形状: {prediction.shape}")
        
        # 可视化
        visualize_prediction(test_image, prediction)
    
    # 批量预测示例
    test_dir = "test_images"
    if Path(test_dir).exists():
        print("\n=== 批量预测 ===")
        batch_predict(test_dir, output_dir, ensemble_predictor)
    
    # 性能评估示例
    print("\n=== 性能统计 ===")
    print("各模型推理时间:")
    
    import time
    test_input = torch.randn(1, 3, 544, 960).to(device)
    
    # 测试单个模型速度
    for i, model in enumerate(ensemble_predictor.models):
        torch.cuda.synchronize()
        start = time.time()
        
        with torch.no_grad():
            for _ in range(10):
                _ = model(test_input)
        
        torch.cuda.synchronize()
        elapsed = (time.time() - start) / 10
        print(f"  模型 {i+1}: {elapsed*1000:.2f} ms")
    
    # 测试集成预测速度
    torch.cuda.synchronize()
    start = time.time()
    
    with torch.no_grad():
        for _ in range(10):
            _ = ensemble_predictor.predict(test_input)
    
    torch.cuda.synchronize()
    elapsed = (time.time() - start) / 10
    print(f"  集成预测: {elapsed*1000:.2f} ms")


if __name__ == '__main__':
    main() 