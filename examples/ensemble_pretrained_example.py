#!/usr/bin/env python3
"""
使用预训练权重进行集成学习的示例
演示如何加载.pth.tar文件进行集成训练
"""

import subprocess
import sys
from pathlib import Path

def run_ensemble_pretrained():
    """运行预训练权重集成学习示例"""
    
    print("=== 预训练权重集成学习示例 ===\n")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 预训练权重文件检查
    pretrained_files = [
        'efficientnetb4.pth.tar',
        'eca_nfnet_l2.pth.tar', 
        'seresnet152d.pth.tar'
    ]
    
    print("检查预训练权重文件...")
    missing_files = []
    for file in pretrained_files:
        file_path = project_root / file
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"✅ {file} ({size_mb:.0f}MB)")
        else:
            missing_files.append(file)
            print(f"❌ {file} (缺失)")
    
    if missing_files:
        print(f"\n⚠️ 缺失预训练权重文件: {missing_files}")
        print("请确保这些文件在项目根目录中")
        return
    
    print("\n所有预训练权重文件已就绪！\n")
    
    # 不同运行模式的示例
    modes = {
        'ensemble_only': {
            'desc': '仅集成模式 - 直接使用预训练权重进行集成（最快）',
            'time': '约10-15分钟',
            'recommended': True
        },
        'finetune': {
            'desc': '微调模式 - 基于预训练权重进行少量epoch微调',
            'time': '约1-2小时',
            'recommended': False
        },
        'train': {
            'desc': '完整训练模式 - 基于预训练权重进行完整训练',
            'time': '约3-4小时',
            'recommended': False
        }
    }
    
    print("可用的运行模式:")
    for mode, info in modes.items():
        rec_str = " (推荐)" if info['recommended'] else ""
        print(f"  {mode}: {info['desc']}{rec_str}")
        print(f"    预计时间: {info['time']}")
    
    print("\n" + "="*60)
    print("示例命令:")
    print("="*60)
    
    # 基础配置
    config_file = "configs/railway_track_config.yaml"
    data_dir = "/path/to/your/railway/data"  # 用户需要替换
    
    # 1. 仅集成模式（推荐）
    print("\n1. 仅集成模式（推荐，最快）:")
    cmd1 = f"""python scripts/ensemble_training_pretrained.py \\
    --config {config_file} \\
    --data-dir {data_dir} \\
    --pretrained-dir . \\
    --mode ensemble_only"""
    print(cmd1)
    
    # 2. 微调模式
    print("\n2. 微调模式:")
    cmd2 = f"""python scripts/ensemble_training_pretrained.py \\
    --config {config_file} \\
    --data-dir {data_dir} \\
    --pretrained-dir . \\
    --mode finetune"""
    print(cmd2)
    
    # 3. 完整训练模式
    print("\n3. 完整训练模式:")
    cmd3 = f"""python scripts/ensemble_training_pretrained.py \\
    --config {config_file} \\
    --data-dir {data_dir} \\
    --pretrained-dir . \\
    --mode train"""
    print(cmd3)
    
    print("\n" + "="*60)
    print("参数说明:")
    print("="*60)
    print("--config: 配置文件路径")
    print("--data-dir: 数据集根目录（需要包含train和val子目录）")
    print("--pretrained-dir: 预训练权重目录（当前为项目根目录 '.'）")
    print("--mode: 运行模式 (ensemble_only/finetune/train)")
    print("--skip-training: 可选，跳过训练直接优化集成权重")
    
    print("\n" + "="*60)
    print("输出文件:")
    print("="*60)
    print("✅ 集成权重: outputs/ensemble_weights_pretrained/ensemble_weights.yaml")
    print("✅ 训练日志: outputs/ensemble_weights_pretrained/ensemble_training_*.log")
    print("✅ 模型权重: outputs/ensemble_weights_pretrained/*.pth")
    
    print("\n" + "="*60)
    print("快速开始:")
    print("="*60)
    print("1. 确保数据集目录结构正确:")
    print("   your_data_dir/")
    print("   ├── train/")
    print("   │   ├── images/")
    print("   │   └── masks/")
    print("   └── val/")
    print("       ├── images/")
    print("       └── masks/")
    print("")
    print("2. 替换命令中的数据目录路径")
    print("3. 运行推荐的仅集成模式命令")
    print("4. 查看输出的集成权重配置文件")
    
    print("\n" + "="*60)
    print("集成学习特点:")
    print("="*60)
    print("🎯 三个强大的预训练模型:")
    print("   • EfficientNet-B4 (65MB) - 高效准确")
    print("   • ECA-NFNet-L2 (209MB) - 大容量高性能") 
    print("   • SE-ResNet152d (251MB) - 深层特征提取")
    print("")
    print("🔧 智能权重优化:")
    print("   • 每个类别独立优化融合权重")
    print("   • 网格搜索找到最佳权重组合")
    print("   • 自动保存最优配置")
    print("")
    print("💡 灵活的训练模式:")
    print("   • ensemble_only: 直接使用预训练权重")
    print("   • finetune: 在预训练基础上微调")
    print("   • train: 完整训练优化")


def main():
    """主函数"""
    try:
        run_ensemble_pretrained()
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")


if __name__ == '__main__':
    main() 