# Core ML/DL frameworks (railway-seg environment)
torch==2.0.0+cu118
torchvision==0.15.0+cu118
segmentation-models-pytorch==0.3.4

# Computer Vision and Image Processing
opencv-contrib-python==********
opencv-python==*********
albumentations==1.3.1
Pillow==9.5.0

# Model architectures and utilities
timm==0.9.7
pretrainedmodels==0.7.4
efficientnet_pytorch==0.7.1

# Scientific computing
numpy==1.24.4
scipy==1.10.1
scikit-learn==1.3.2
scikit-image==0.21.0
pandas==2.0.3

# Visualization
matplotlib==3.7.5
seaborn==0.12.2

# Utilities
tqdm==4.66.6
pyyaml==6.0.2
dill==0.4.0
omegaconf==2.3.0

# Experiment tracking
tensorboard==2.13.0
wandb==0.15.12