#!/usr/bin/env python3
"""
Simple training script for railway track segmentation.
This script has minimal dependencies and focuses on getting training working.
"""

import os
import sys
import yaml
import argparse
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import segmentation_models_pytorch as smp
from tqdm import tqdm

# Import only what we need
from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.utils.metrics import calculate_iou, calculate_dice


def load_config(config_path):
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def override_config(config, key, value):
    """Override a specific config value."""
    keys = key.split('.')
    current = config
    for k in keys[:-1]:
        if k not in current:
            current[k] = {}
        current = current[k]
    
    # Try to parse value
    try:
        if '.' in value:
            current[keys[-1]] = float(value)
        else:
            current[keys[-1]] = int(value)
    except ValueError:
        if value.lower() == 'true':
            current[keys[-1]] = True
        elif value.lower() == 'false':
            current[keys[-1]] = False
        else:
            current[keys[-1]] = value
    
    return config


def train_epoch(model, dataloader, criterion, optimizer, device, epoch):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    total_iou = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    for batch_idx, batch in enumerate(pbar):
        images = batch['image'].to(device)
        masks = batch['mask'].to(device)
        
        # Forward pass
        outputs = model(images)
        loss = criterion(outputs, masks)
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Calculate metrics
        with torch.no_grad():
            pred_masks = torch.sigmoid(outputs)
            iou = calculate_iou(pred_masks, masks)
            
        total_loss += loss.item()
        total_iou += iou.item()
        
        # Update progress bar
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'iou': f'{iou.item():.4f}'
        })
    
    avg_loss = total_loss / len(dataloader)
    avg_iou = total_iou / len(dataloader)
    
    return avg_loss, avg_iou


def validate(model, dataloader, criterion, device):
    """Validate the model."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc='Validation'):
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            pred_masks = torch.sigmoid(outputs)
            iou = calculate_iou(pred_masks, masks)
            dice = calculate_dice(pred_masks, masks)
            
            total_loss += loss.item()
            total_iou += iou.item()
            total_dice += dice.item()
    
    avg_loss = total_loss / len(dataloader)
    avg_iou = total_iou / len(dataloader)
    avg_dice = total_dice / len(dataloader)
    
    return avg_loss, avg_iou, avg_dice


def main():
    parser = argparse.ArgumentParser(description='Simple training script')
    parser.add_argument('--config', type=str, 
                       default='configs/railway_track_config.yaml',
                       help='Path to config file')
    parser.add_argument('--epochs', type=int, default=10,
                       help='Number of epochs')
    parser.add_argument('--batch-size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override with command line args
    config = override_config(config, 'training.batch_size', str(args.batch_size))
    config = override_config(config, 'training.learning_rate', str(args.lr))
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create datasets
    print("\nCreating datasets...")
    train_dataset = RailwayDataset(
        data_dir=config['data']['processed_data_path'] + '/train',
        split='train',
        config=config,
        transform=None  # Simple training without augmentation for now
    )
    
    val_dataset = RailwayDataset(
        data_dir=config['data']['processed_data_path'] + '/val',
        split='val',
        config=config,
        transform=None
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size * 2,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")
    
    # Create model
    print("\nCreating model...")
    model = smp.Unet(
        encoder_name='resnet34',
        encoder_weights='imagenet',
        in_channels=3,
        classes=3,  # Multi-label: background, main_track, fork_track
    )
    model = model.to(device)
    
    # Create loss and optimizer
    criterion = MultiLabelSegmentationLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    
    # Training loop
    print(f"\nStarting training for {args.epochs} epochs...")
    print("=" * 80)
    
    best_iou = 0
    for epoch in range(1, args.epochs + 1):
        # Train
        train_loss, train_iou = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # Validate
        val_loss, val_iou, val_dice = validate(
            model, val_loader, criterion, device
        )
        
        # Print epoch results
        print(f"\nEpoch {epoch}/{args.epochs}")
        print(f"Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}, Dice: {val_dice:.4f}")
        
        # Save best model
        if val_iou > best_iou:
            best_iou = val_iou
            save_path = Path('outputs/checkpoints')
            save_path.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_iou': best_iou,
            }, save_path / 'best_model.pth')
            print(f"Saved best model with IoU: {best_iou:.4f}")
        
        print("-" * 80)
    
    print("\nTraining completed!")
    print(f"Best validation IoU: {best_iou:.4f}")


if __name__ == '__main__':
    main()