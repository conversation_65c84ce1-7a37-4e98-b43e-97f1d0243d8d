#!/usr/bin/env python3
"""
测试GPU优化是否正常工作
"""

import os
import torch

def test_environment_setup():
    """测试环境设置"""
    print("🔧 测试环境设置")
    print("=" * 50)
    
    # 设置环境变量（与训练脚本相同）
    os.environ.update({
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:128,roundup_power2_divisions:16',
        'CUDA_LAUNCH_BLOCKING': '0',
        'TORCH_CUDNN_V8_API_ENABLED': '1',
        'OMP_NUM_THREADS': '6',
        'MKL_NUM_THREADS': '6',
        'TOKENIZERS_PARALLELISM': 'false'
    })
    
    print("✅ 环境变量设置完成")
    
    # 设置CUDA优化
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    torch.backends.cudnn.allow_tf32 = True
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.set_num_threads(6)
    
    print("✅ CUDA优化设置完成")

def test_gpu_access():
    """测试GPU访问"""
    print("\n🔍 测试GPU访问")
    print("=" * 50)
    
    try:
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name()
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name}")
            print(f"✅ 内存: {gpu_memory:.1f}GB")
            
            # 测试内存分配
            test_tensor = torch.randn(1000, 1000, device='cuda')
            memory_used = torch.cuda.memory_allocated() / 1024**2
            print(f"✅ 内存分配测试成功: {memory_used:.1f}MB")
            
            del test_tensor
            torch.cuda.empty_cache()
            return True
        else:
            print("❌ CUDA不可用")
            return False
            
    except Exception as e:
        print(f"❌ GPU访问失败: {e}")
        return False

def test_data_loading_optimization():
    """测试数据加载优化"""
    print("\n📊 测试数据加载优化")
    print("=" * 50)
    
    try:
        from torch.utils.data import DataLoader, TensorDataset
        
        # 创建模拟数据集
        data = torch.randn(100, 3, 224, 224)
        labels = torch.randint(0, 3, (100, 3, 224, 224))
        dataset = TensorDataset(data, labels)
        
        # 创建优化的DataLoader
        dataloader = DataLoader(
            dataset,
            batch_size=8,
            shuffle=True,
            num_workers=6,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=3,
            drop_last=True,
            multiprocessing_context='spawn'
        )
        
        print("✅ DataLoader创建成功")
        
        # 测试数据加载
        for i, (batch_data, batch_labels) in enumerate(dataloader):
            if i >= 2:  # 只测试前2个批次
                break
            
            # 测试异步传输
            if torch.cuda.is_available():
                batch_data = batch_data.cuda(non_blocking=True)
                batch_labels = batch_labels.cuda(non_blocking=True)
                
                # 测试channels_last格式
                batch_data = batch_data.to(memory_format=torch.channels_last)
                batch_labels = batch_labels.to(memory_format=torch.channels_last)
                
                print(f"✅ 批次 {i+1}: {batch_data.shape}, 格式: {batch_data.is_contiguous(memory_format=torch.channels_last)}")
        
        print("✅ 数据加载优化测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return False

def test_training_simulation():
    """测试训练模拟"""
    print("\n🚀 测试训练模拟")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("⚠️  跳过训练测试（CUDA不可用）")
        return False
    
    try:
        # 创建简单模型
        model = torch.nn.Sequential(
            torch.nn.Conv2d(3, 16, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.Conv2d(16, 3, 3, padding=1)
        ).cuda()
        
        # 转换为channels_last格式
        model = model.to(memory_format=torch.channels_last)
        
        # 创建优化器和损失函数
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
        criterion = torch.nn.MSELoss()
        scaler = torch.cuda.amp.GradScaler()
        
        print("✅ 模型和优化器创建成功")
        
        # 模拟训练步骤
        for step in range(3):
            # 创建模拟数据
            x = torch.randn(8, 3, 224, 224, device='cuda', dtype=torch.float16)
            target = torch.randn(8, 3, 224, 224, device='cuda', dtype=torch.float16)
            
            # 转换为channels_last格式
            x = x.to(memory_format=torch.channels_last)
            target = target.to(memory_format=torch.channels_last)
            
            # 前向传播（使用混合精度）
            with torch.cuda.amp.autocast():
                output = model(x)
                loss = criterion(output, target)
            
            # 反向传播
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad(set_to_none=True)
            
            memory_used = torch.cuda.memory_allocated() / 1024**2
            print(f"✅ 步骤 {step+1}: 损失 {loss.item():.4f}, 内存 {memory_used:.1f}MB")
        
        print("✅ 训练模拟测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 训练模拟失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 GPU优化测试")
    print("=" * 80)
    
    # 1. 测试环境设置
    test_environment_setup()
    
    # 2. 测试GPU访问
    gpu_ok = test_gpu_access()
    
    # 3. 测试数据加载优化
    dataloader_ok = test_data_loading_optimization()
    
    # 4. 测试训练模拟
    training_ok = test_training_simulation()
    
    print(f"\n📊 测试结果总结:")
    print("=" * 80)
    print(f"GPU访问: {'✅' if gpu_ok else '❌'}")
    print(f"数据加载: {'✅' if dataloader_ok else '❌'}")
    print(f"训练模拟: {'✅' if training_ok else '❌'}")
    
    if gpu_ok and dataloader_ok and training_ok:
        print(f"\n🎉 所有测试通过！")
        print("GPU优化已正确配置，可以安全运行训练脚本。")
        print("\n🚀 现在可以运行:")
        print("python scripts/ensemble_training_notebook_exact_with_fusion.py \\")
        print("    --config configs/railway_track_config.yaml \\")
        print("    --data-dir /home/<USER>/data/railway_track_dataset \\")
        print("    --pretrained-nfnet eca_nfnet_l2.pth_converted.pth \\")
        print("    --pretrained-resnet seresnet152d.pth_converted.pth \\")
        print("    --models eca_nfnet_l2 seresnet152d \\")
        print("    --output-dir pretrained_ensemble_25mm")
    else:
        print(f"\n⚠️  部分测试失败，请检查配置")

if __name__ == '__main__':
    main()
