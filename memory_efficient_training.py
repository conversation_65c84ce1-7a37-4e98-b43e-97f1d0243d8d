#!/usr/bin/env python3
"""
RTX 4090内存优化训练脚本
解决ECA-NFNet-L2和SEResNet152D的OOM问题
"""

import torch
import torch.nn as nn
from torch.utils.checkpoint import checkpoint
import segmentation_models_pytorch as smp
import warnings

# 抑制警告
warnings.filterwarnings('ignore')

class MemoryEfficientModel(nn.Module):
    """内存高效的模型包装器"""
    
    def __init__(self, base_model):
        super().__init__()
        self.encoder = base_model.encoder
        self.decoder = base_model.decoder
        self.segmentation_head = base_model.segmentation_head
        
    def forward(self, x):
        # 使用梯度检查点减少内存
        features = checkpoint(self.encoder, x, use_reentrant=False)
        decoder_output = checkpoint(self.decoder, features, use_reentrant=False)
        masks = self.segmentation_head(decoder_output)
        return masks

def create_model(model_name, num_classes=3):
    """创建内存优化模型"""
    
    if model_name == 'eca_nfnet_l2':
        base_model = smp.PAN(
            encoder_name='tu-eca_nfnet_l2',
            encoder_weights='imagenet',
            classes=num_classes,
            activation=None
        )
    elif model_name == 'seresnet152d':
        base_model = smp.PAN(
            encoder_name='tu-seresnet152d',
            encoder_weights='imagenet',
            classes=num_classes,
            activation=None
        )
    else:
        raise ValueError(f"不支持的模型: {model_name}")
    
    # 包装为内存高效模型
    model = MemoryEfficientModel(base_model)
    
    # 转换为channels_last格式
    model = model.to(memory_format=torch.channels_last)
    
    return model

def setup_training_environment():
    """设置训练环境"""
    
    # CUDA优化
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    
    # 内存优化
    torch.cuda.empty_cache()
    
    # 设置环境变量
    import os
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,expandable_segments:True'
    
    print("✅ 训练环境已优化")

if __name__ == '__main__':
    setup_training_environment()
    
    # 测试模型创建
    device = torch.device('cuda')
    
    for model_name in ['eca_nfnet_l2', 'seresnet152d']:
        print(f"\n测试 {model_name}...")
        
        model = create_model(model_name)
        model = model.to(device)
        
        # 测试前向传播
        x = torch.randn(8, 3, 416, 736, device=device, dtype=torch.float16)
        x = x.to(memory_format=torch.channels_last)
        
        with torch.cuda.amp.autocast():
            output = model(x)
        
        print(f"✅ {model_name} 测试成功")
        print(f"   输入: {x.shape}")
        print(f"   输出: {output.shape}")
        print(f"   内存: {torch.cuda.memory_allocated() / 1024**3:.2f}GB")
        
        del model, x, output
        torch.cuda.empty_cache()
