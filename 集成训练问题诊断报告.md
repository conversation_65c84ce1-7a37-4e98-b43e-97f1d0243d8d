# 集成训练问题诊断报告

## 问题分析

通过对 `ensemble_training_enhanced.py` 脚本的分析，我发现了以下几个主要问题：

### 1. 数据预处理问题

#### 1.1 掩码格式不一致
- **问题**：数据集支持多种掩码格式（PNG、NPY、NPZ），但在训练时可能存在格式处理不一致的问题
- **影响**：可能导致掩码值域不正确（0-255 vs 0-1），影响损失计算
- **解决方案**：在修复版中统一处理所有格式，确保输出为 0-1 范围的 float32

#### 1.2 数据归一化
- **问题**：图像使用 ImageNet 的均值和标准差进行归一化，但这可能不适合铁路轨道数据
- **影响**：可能导致模型收敛困难
- **建议**：考虑计算数据集特定的均值和标准差

### 2. 训练设置问题

#### 2.1 学习率配置
- **问题**：ECA-NFNet 模型使用了过高的学习率，导致训练不稳定
- **原因**：ECA-NFNet 是无归一化的模型，对学习率更敏感
- **解决方案**：在修复版中为 ECA-NFNet 降低学习率（减半并设置上限）

#### 2.2 损失函数数值稳定性
- **问题**：原版中使用了 `NumericallyStableLoss` 包装器，但该类未定义
- **影响**：代码运行错误
- **解决方案**：移除未定义的包装器，使用标准的梯度裁剪来保证稳定性

#### 2.3 批处理大小
- **问题**：集成训练使用的批处理大小可能过大，导致显存不足
- **解决方案**：限制批处理大小（训练最大4，验证最大6）

### 3. 错误处理问题

#### 3.1 NaN/Inf 处理
- **问题**：虽然有 NaN 检测，但恢复机制过于复杂且可能失败
- **解决方案**：简化为跳过异常批次，继续训练

#### 3.2 空批次处理
- **问题**：如果所有批次都失败，会导致除零错误
- **解决方案**：添加批次计数检查，避免除零

### 4. 可视化问题

#### 4.1 可视化频率
- **问题**：原版只在特定 epoch（5的倍数）进行可视化
- **解决方案**：每个 epoch 都保存可视化结果到文件

#### 4.2 可视化内容
- **问题**：缺少直观的训练进展可视化
- **解决方案**：
  - 添加每个类别的独立可视化
  - 添加最佳/最差预测对比
  - 保存到独立文件夹，便于查看

## 修复版改进

### 1. 简化的训练流程
- 移除复杂的 NaN 恢复机制
- 使用简单的异常处理和跳过策略
- 清晰的日志记录

### 2. 增强的可视化功能
```python
def save_visualization(...):
    # 为每个epoch创建独立文件夹
    save_dir = visualizations_dir / model_name / f"epoch_{epoch}"
    
    # 保存每个样本的多类别可视化
    # 保存总览图（最佳/最差预测对比）
```

### 3. 改进的模型配置
- 根据模型类型自动调整学习率
- 统一的梯度裁剪策略
- 更保守的训练参数

### 4. 文件组织
```
experiments/
└── ensemble_enhanced_YYYYMMDD_HHMMSS/
    ├── checkpoints/       # 训练检查点
    ├── weights/          # 最佳模型权重
    ├── logs/            # 训练日志
    ├── tensorboard/     # TensorBoard日志
    └── visualizations/  # 可视化结果
        └── model_name/
            └── epoch_N/
                ├── epoch_sample_0.png
                ├── epoch_sample_1.png
                └── overview_epoch_N.png
```

## 使用建议

### 1. 运行修复版脚本
```bash
python scripts/ensemble_training_enhanced_fixed.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/data \
    --experiment-name my_experiment
```

### 2. 监控训练
- 查看日志文件：`experiments/my_experiment/logs/training.log`
- 查看可视化：`experiments/my_experiment/visualizations/`
- TensorBoard：`tensorboard --logdir experiments/my_experiment/tensorboard`

### 3. 调试建议
如果仍有问题：
1. 检查数据加载：确保掩码格式正确
2. 降低学习率：在配置文件中减小 learning_rate
3. 减小批处理大小：调整 batch_size
4. 检查显存：使用 `nvidia-smi` 监控 GPU 使用

## 总结

主要问题集中在：
1. **数据预处理不一致**：掩码格式和值域问题
2. **模型特定配置不当**：特别是 ECA-NFNet 的学习率
3. **错误处理过于复杂**：导致训练中断
4. **可视化不足**：难以监控训练进展

修复版通过简化流程、统一数据处理、增强可视化功能，应该能够稳定训练并提供更好的监控能力。