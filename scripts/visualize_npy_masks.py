#!/usr/bin/env python3
"""
NPY掩码可视化工具
支持美观的彩色显示，重叠区域特殊处理
"""

import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
import argparse
from typing import Union, Optional, Tuple, Dict
import colorsys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class MaskVisualizer:
    """多标签掩码可视化器"""
    
    def __init__(self):
        """初始化颜色方案"""
        # 使用更美观的颜色方案
        self.colors = {
            'background': (30, 30, 30),        # 深灰色背景
            'main_track': (255, 140, 0),       # 橙色主轨道 (活力橙)
            'fork_track': (0, 191, 255),       # 天蓝色分叉轨道 (深天蓝)
            'overlap': (255, 20, 147),         # 深粉色重叠区域 (热粉红)
            'overlap_alt': (148, 0, 211)       # 紫色备选重叠色
        }
        
        # 半透明混合颜色方案
        self.blend_colors = {
            'main_track': (255, 140, 0, 180),     # 半透明橙色
            'fork_track': (0, 191, 255, 180),     # 半透明天蓝色
            'overlap': (255, 255, 0, 255)         # 黄色重叠
        }
    
    def load_mask(self, mask_path: Union[str, Path]) -> np.ndarray:
        """加载掩码文件（支持NPY和PNG格式）"""
        mask_path = Path(mask_path)
        if not mask_path.exists():
            raise FileNotFoundError(f"掩码文件不存在: {mask_path}")
        
        if mask_path.suffix == '.npy':
            # 加载NPY格式
            mask = np.load(str(mask_path))
            # 如果是uint8格式，转换为0-1范围
            if mask.dtype == np.uint8 and mask.max() > 1:
                mask = mask.astype(np.float32) / 255.0
        elif mask_path.suffix == '.png':
            # 加载PNG格式 - RGB通道分别存储背景、主轨道、分叉轨道
            img = cv2.imread(str(mask_path), cv2.IMREAD_COLOR)
            if img is None:
                raise ValueError(f"无法读取PNG文件: {mask_path}")
            
            # 转换为多标签格式 (OpenCV使用BGR顺序)
            mask = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.float32)
            mask[:, :, 0] = (img[:, :, 0] > 127).astype(np.float32)  # B通道: 背景
            mask[:, :, 1] = (img[:, :, 1] > 127).astype(np.float32)  # G通道: 主轨道
            mask[:, :, 2] = (img[:, :, 2] > 127).astype(np.float32)  # R通道: 分叉轨道
        else:
            raise ValueError(f"不支持的文件格式: {mask_path.suffix}")
        
        if len(mask.shape) != 3 or mask.shape[2] != 3:
            raise ValueError(f"期望3通道掩码，但得到形状: {mask.shape}")
        
        return mask
    
    def load_npy_mask(self, mask_path: Union[str, Path]) -> np.ndarray:
        """兼容旧接口"""
        return self.load_mask(mask_path)
    
    def create_color_mask(self, mask: np.ndarray, style: str = 'solid') -> np.ndarray:
        """
        创建彩色可视化掩码
        
        Args:
            mask: 多标签掩码 (H, W, 3)
            style: 可视化风格 ('solid', 'blend', 'gradient')
            
        Returns:
            彩色图像 (H, W, 3) BGR格式
        """
        h, w = mask.shape[:2]
        colored = np.zeros((h, w, 3), dtype=np.uint8)
        
        # 提取各通道
        background = mask[:, :, 0] > 0.5
        main_track = mask[:, :, 1] > 0.5
        fork_track = mask[:, :, 2] > 0.5
        overlap = np.logical_and(main_track, fork_track)
        
        if style == 'solid':
            # 实心颜色风格
            colored[background] = self.colors['background']
            colored[main_track] = self.colors['main_track']
            colored[fork_track] = self.colors['fork_track']
            colored[overlap] = self.colors['overlap']
            
        elif style == 'blend':
            # 混合透明风格
            colored[background] = self.colors['background']
            
            # 主轨道
            main_only = np.logical_and(main_track, ~fork_track)
            colored[main_only] = self.colors['main_track']
            
            # 分叉轨道
            fork_only = np.logical_and(fork_track, ~main_track)
            colored[fork_only] = self.colors['fork_track']
            
            # 重叠区域使用混合色
            if np.any(overlap):
                # 计算混合色（主轨道和分叉轨道的平均）
                blend_color = (
                    (self.colors['main_track'][0] + self.colors['fork_track'][0]) // 2,
                    (self.colors['main_track'][1] + self.colors['fork_track'][1]) // 2,
                    (self.colors['main_track'][2] + self.colors['fork_track'][2]) // 2
                )
                colored[overlap] = blend_color
                
        elif style == 'gradient':
            # 渐变风格
            colored = self._create_gradient_mask(mask)
        
        return colored
    
    def _create_gradient_mask(self, mask: np.ndarray) -> np.ndarray:
        """创建渐变效果的掩码"""
        h, w = mask.shape[:2]
        colored = np.zeros((h, w, 3), dtype=np.float32)
        
        # 背景
        colored[:, :] = np.array(self.colors['background']) / 255.0
        
        # 主轨道渐变
        main_track = mask[:, :, 1]
        if np.any(main_track > 0):
            # 创建渐变效果
            gradient = self._create_radial_gradient(main_track)
            main_color = np.array(self.colors['main_track']) / 255.0
            for i in range(3):
                colored[:, :, i] = np.where(main_track > 0, 
                                          main_color[i] * gradient + colored[:, :, i] * (1 - gradient),
                                          colored[:, :, i])
        
        # 分叉轨道渐变
        fork_track = mask[:, :, 2]
        if np.any(fork_track > 0):
            gradient = self._create_radial_gradient(fork_track)
            fork_color = np.array(self.colors['fork_track']) / 255.0
            for i in range(3):
                colored[:, :, i] = np.where(fork_track > 0,
                                          fork_color[i] * gradient + colored[:, :, i] * (1 - gradient),
                                          colored[:, :, i])
        
        # 重叠区域特殊处理
        overlap = np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
        if np.any(overlap):
            overlap_color = np.array(self.colors['overlap']) / 255.0
            colored[overlap] = overlap_color
        
        return (colored * 255).astype(np.uint8)
    
    def _create_radial_gradient(self, mask_channel: np.ndarray) -> np.ndarray:
        """为掩码通道创建径向渐变"""
        # 计算距离变换
        dist_transform = cv2.distanceTransform((mask_channel > 0).astype(np.uint8), 
                                               cv2.DIST_L2, 5)
        # 归一化
        if dist_transform.max() > 0:
            gradient = dist_transform / dist_transform.max()
        else:
            gradient = dist_transform
        
        # 应用平滑
        gradient = cv2.GaussianBlur(gradient, (5, 5), 0)
        
        return gradient
    
    def add_legend(self, ax: plt.Axes):
        """添加图例"""
        legend_elements = [
            mpatches.Patch(color=np.array(self.colors['main_track'])/255, 
                          label='主轨道 (Main Track)'),
            mpatches.Patch(color=np.array(self.colors['fork_track'])/255, 
                          label='分叉轨道 (Fork Track)'),
            mpatches.Patch(color=np.array(self.colors['overlap'])/255, 
                          label='重叠区域 (Overlap)')
        ]
        ax.legend(handles=legend_elements, 
                 loc='upper right', 
                 framealpha=0.9,
                 fontsize=10)
    
    def visualize_single(self, mask_path: Union[str, Path], 
                        image_path: Optional[Union[str, Path]] = None,
                        style: str = 'solid',
                        save_path: Optional[str] = None,
                        show: bool = True) -> np.ndarray:
        """
        可视化单个掩码
        
        Args:
            mask_path: NPY掩码路径
            image_path: 可选的原始图像路径
            style: 可视化风格
            save_path: 保存路径
            show: 是否显示
            
        Returns:
            可视化结果
        """
        # 加载掩码
        mask = self.load_mask(mask_path)
        colored_mask = self.create_color_mask(mask, style)
        
        # 如果有原始图像，创建叠加视图
        if image_path and Path(image_path).exists():
            image = cv2.imread(str(image_path))
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 创建子图
            fig, axes = plt.subplots(1, 3, figsize=(18, 6))
            
            # 原始图像
            axes[0].imshow(image_rgb)
            axes[0].set_title('原始图像', fontsize=14, fontweight='bold')
            axes[0].axis('off')
            
            # 掩码
            axes[1].imshow(cv2.cvtColor(colored_mask, cv2.COLOR_BGR2RGB))
            axes[1].set_title(f'轨道掩码 ({style.title()}风格)', fontsize=14, fontweight='bold')
            axes[1].axis('off')
            self.add_legend(axes[1])
            
            # 叠加
            overlay = self.create_overlay(image, colored_mask)
            axes[2].imshow(cv2.cvtColor(overlay, cv2.COLOR_BGR2RGB))
            axes[2].set_title('叠加视图', fontsize=14, fontweight='bold')
            axes[2].axis('off')
            
        else:
            # 只显示掩码
            fig, ax = plt.subplots(1, 1, figsize=(10, 8))
            ax.imshow(cv2.cvtColor(colored_mask, cv2.COLOR_BGR2RGB))
            ax.set_title(f'轨道掩码可视化 ({style.title()}风格)', 
                        fontsize=16, fontweight='bold')
            ax.axis('off')
            self.add_legend(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"可视化已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
        
        return colored_mask
    
    def create_overlay(self, image: np.ndarray, colored_mask: np.ndarray, 
                      alpha: float = 0.4) -> np.ndarray:
        """创建图像和掩码的叠加"""
        # 创建掩码区域的二值掩码
        mask_area = np.any(colored_mask != self.colors['background'], axis=2)
        
        # 混合
        overlay = image.copy()
        overlay[mask_area] = (alpha * colored_mask[mask_area] + 
                             (1 - alpha) * image[mask_area]).astype(np.uint8)
        
        return overlay
    
    def visualize_batch(self, mask_dir: Path, num_samples: int = 6,
                       style: str = 'solid', save_dir: Optional[Path] = None):
        """批量可视化"""
        # 支持NPY和PNG格式
        mask_files = (list(mask_dir.glob('*.npy')) + list(mask_dir.glob('*.png')))[:num_samples]
        
        if not mask_files:
            print(f"未找到NPY文件在: {mask_dir}")
            return
        
        rows = (len(mask_files) + 2) // 3
        fig, axes = plt.subplots(rows, 3, figsize=(15, 5*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for idx, mask_file in enumerate(mask_files):
            row, col = idx // 3, idx % 3
            
            mask = self.load_mask(mask_file)
            colored_mask = self.create_color_mask(mask, style)
            
            ax = axes[row, col]
            ax.imshow(cv2.cvtColor(colored_mask, cv2.COLOR_BGR2RGB))
            ax.set_title(mask_file.stem, fontsize=10)
            ax.axis('off')
            
            if idx == 0:
                self.add_legend(ax)
        
        # 隐藏多余的子图
        for idx in range(len(mask_files), rows * 3):
            row, col = idx // 3, idx % 3
            axes[row, col].axis('off')
        
        plt.suptitle(f'批量掩码可视化 ({style.title()}风格)', 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_dir:
            save_path = save_dir / f'batch_visualization_{style}.png'
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"批量可视化已保存到: {save_path}")
        
        plt.show()
    
    def get_statistics(self, mask: np.ndarray) -> Dict[str, float]:
        """获取掩码统计信息"""
        total_pixels = mask.shape[0] * mask.shape[1]
        
        background = np.sum(mask[:, :, 0] > 0.5)
        main_track = np.sum(mask[:, :, 1] > 0.5)
        fork_track = np.sum(mask[:, :, 2] > 0.5)
        overlap = np.sum(np.logical_and(mask[:, :, 1] > 0.5, mask[:, :, 2] > 0.5))
        
        return {
            'total_pixels': total_pixels,
            'background': background,
            'background_ratio': background / total_pixels,
            'main_track': main_track,
            'main_track_ratio': main_track / total_pixels,
            'fork_track': fork_track,
            'fork_track_ratio': fork_track / total_pixels,
            'overlap': overlap,
            'overlap_ratio': overlap / total_pixels if main_track + fork_track > 0 else 0
        }


def main():
    parser = argparse.ArgumentParser(description='多标签掩码可视化工具（支持NPY和PNG格式）')
    parser.add_argument('mask_path', type=str, help='掩码文件路径或目录（支持NPY和PNG格式）')
    parser.add_argument('--image', type=str, help='对应的原始图像路径')
    parser.add_argument('--style', choices=['solid', 'blend', 'gradient'], 
                       default='solid', help='可视化风格')
    parser.add_argument('--save', type=str, help='保存可视化结果的路径')
    parser.add_argument('--batch', action='store_true', 
                       help='批量可视化（如果mask_path是目录）')
    parser.add_argument('--num-samples', type=int, default=6, 
                       help='批量可视化的样本数量')
    parser.add_argument('--no-show', action='store_true', 
                       help='不显示图像（仅保存）')
    parser.add_argument('--stats', action='store_true', 
                       help='显示统计信息')
    
    args = parser.parse_args()
    
    visualizer = MaskVisualizer()
    
    mask_path = Path(args.mask_path)
    
    if mask_path.is_dir() and args.batch:
        # 批量可视化
        save_dir = Path(args.save).parent if args.save else None
        visualizer.visualize_batch(mask_path, args.num_samples, 
                                  args.style, save_dir)
    else:
        # 单个可视化
        if not mask_path.exists():
            print(f"错误：文件不存在: {mask_path}")
            return
        
        colored_mask = visualizer.visualize_single(
            mask_path, 
            args.image, 
            args.style,
            args.save,
            not args.no_show
        )
        
        if args.stats:
            # 显示统计信息
            mask = visualizer.load_mask(mask_path)
            stats = visualizer.get_statistics(mask)
            
            print("\n=== 掩码统计信息 ===")
            print(f"总像素数: {stats['total_pixels']:,}")
            print(f"背景: {stats['background']:,} ({stats['background_ratio']:.1%})")
            print(f"主轨道: {stats['main_track']:,} ({stats['main_track_ratio']:.1%})")
            print(f"分叉轨道: {stats['fork_track']:,} ({stats['fork_track_ratio']:.1%})")
            print(f"重叠区域: {stats['overlap']:,} ({stats['overlap_ratio']:.1%})")


if __name__ == '__main__':
    main()