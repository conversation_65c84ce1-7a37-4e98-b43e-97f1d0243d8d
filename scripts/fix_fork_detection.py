#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复分叉轨道检测问题的脚本
"""

import yaml
import shutil
from pathlib import Path


def fix_fork_detection_config():
    """修复分叉轨道检测的配置文件"""
    print("🔧 修复分叉轨道检测配置...")
    
    config_path = "configs/railway_track_config.yaml"
    backup_path = "configs/railway_track_config.yaml.backup"
    
    # 备份原配置
    shutil.copy(config_path, backup_path)
    print(f"✅ 配置文件备份到: {backup_path}")
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 修复损失函数权重 - 增加分叉轨道权重
    print("\n🎯 修复损失函数权重...")
    config['loss']['losses'][1]['alpha'] = [0.1, 0.3, 0.6]  # 背景、主轨道、分叉轨道
    print("  - 背景权重: 0.1 → 0.1")
    print("  - 主轨道权重: 0.4 → 0.3") 
    print("  - 分叉轨道权重: 0.4 → 0.6 (提高50%)")
    
    # 调整学习率 - 降低学习率以便更好学习少数类
    print("\n📈 调整学习率...")
    original_lr = config['training']['optimizer']['lr']
    config['training']['optimizer']['lr'] = 0.0003
    print(f"  - 学习率: {original_lr} → 0.0003")
    
    # 增加训练轮数
    print("\n⏱️ 调整训练参数...")
    original_epochs = config['training']['epochs']
    config['training']['epochs'] = 60
    print(f"  - 训练轮数: {original_epochs} → 60")
    
    # 调整早停patience
    original_patience = config['training']['early_stopping_patience']
    config['training']['early_stopping_patience'] = 25
    print(f"  - 早停patience: {original_patience} → 25")
    
    # 降低推理阈值
    print("\n🎛️ 调整推理阈值...")
    original_threshold = config['inference']['threshold']
    config['inference']['threshold'] = 0.3
    print(f"  - 推理阈值: {original_threshold} → 0.3")
    
    # 保存修改后的配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
    
    print(f"\n✅ 配置修复完成，保存到: {config_path}")
    
    return config


def create_training_script():
    """创建重新训练的脚本"""
    script_content = '''#!/bin/bash
# 重新训练模型以改善分叉轨道检测

echo "🚀 开始重新训练模型..."

# 使用修复后的配置重新训练
python scripts/train.py \\
    --config configs/railway_track_config.yaml \\
    --experiment-name railway_track_fork_fixed \\
    --resume-from models/checkpoints/railway_track_config/best_model.pth

echo "✅ 训练完成！"
echo "新模型保存在: models/checkpoints/railway_track_fork_fixed/"
'''
    
    script_path = "scripts/retrain_fork_detection.sh"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 添加执行权限
    import os
    os.chmod(script_path, 0o755)
    
    print(f"✅ 重新训练脚本创建完成: {script_path}")


def update_predict_script():
    """更新预测脚本的默认阈值"""
    print("\n🔄 更新预测脚本...")
    
    # 读取预测脚本
    predict_script = "scripts/predict_inference.py"
    with open(predict_script, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新默认阈值
    content = content.replace(
        'parser.add_argument(\'--threshold\', type=float, default=0.5',
        'parser.add_argument(\'--threshold\', type=float, default=0.3'
    )
    
    # 保存修改
    with open(predict_script, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 预测脚本默认阈值更新为0.3")


def create_test_script():
    """创建测试脚本"""
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分叉轨道检测效果
"""

import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.predict_inference import RailwaySegmentationPredictor
import numpy as np

def test_fork_detection():
    """测试分叉轨道检测"""
    print("🧪 测试分叉轨道检测效果...")
    
    # 配置路径
    config_path = "configs/railway_track_config.yaml"
    checkpoint_path = "models/checkpoints/railway_track_config/best_model.pth"
    
    # 包含分叉轨道的测试图像
    test_image = "data/railway_annotation_6mm/20250118164602376.near.avi_frame_1135.png"
    
    if not Path(test_image).exists():
        print("❌ 测试图像不存在，请检查路径")
        return
    
    try:
        # 创建预测器
        predictor = RailwaySegmentationPredictor(config_path, checkpoint_path)
        
        # 使用不同阈值进行预测
        thresholds = [0.1, 0.3, 0.5, 0.7]
        
        for threshold in thresholds:
            print(f"\\n测试阈值: {threshold}")
            
            prediction_mask, prediction_probs = predictor.predict_single(test_image, threshold)
            
            # 统计预测结果
            unique_labels = np.unique(prediction_mask)
            
            for label in unique_labels:
                count = np.sum(prediction_mask == label)
                percentage = count / prediction_mask.size * 100
                
                if label == 0:
                    label_name = "背景"
                elif label == 1:
                    label_name = "主轨道"
                elif label == 2:
                    label_name = "分叉轨道"
                else:
                    label_name = f"标签{label}"
                
                print(f"  {label_name}: {count} 像素 ({percentage:.2f}%)")
            
            if 2 in unique_labels:
                print(f"  ✅ 检测到分叉轨道！")
            else:
                print(f"  ❌ 未检测到分叉轨道")
        
        print("\\n🎯 建议使用阈值0.3进行分叉轨道检测")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_fork_detection()
'''
    
    test_script = "scripts/test_fork_detection.py"
    with open(test_script, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 测试脚本创建完成: {test_script}")


def main():
    """主函数"""
    print("🔧 分叉轨道检测问题修复工具")
    print("=" * 50)
    
    # 1. 修复配置文件
    fix_fork_detection_config()
    
    # 2. 创建重新训练脚本
    create_training_script()
    
    # 3. 更新预测脚本
    update_predict_script()
    
    # 4. 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎯 修复完成！后续步骤：")
    print("1. 运行测试：python scripts/test_fork_detection.py")
    print("2. 如果效果不佳，重新训练：bash scripts/retrain_fork_detection.sh")
    print("3. 使用新配置进行预测：python scripts/predict_inference.py --input your_image.jpg")
    print("=" * 50)


if __name__ == "__main__":
    main() 