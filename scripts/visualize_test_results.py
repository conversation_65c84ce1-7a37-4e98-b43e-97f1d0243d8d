#!/usr/bin/env python3
"""
简化版测试图像可视化脚本
直接读取原始图像，避免复杂的预处理
"""

import sys
import torch
import torch.nn as nn
import numpy as np
import cv2
import yaml
import argparse
from pathlib import Path
from tqdm import tqdm
import segmentation_models_pytorch as smp

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_model(model_name: str, config: dict, device: torch.device) -> nn.Module:
    """创建指定的模型"""
    
    model_configs = {
        'efficientnet_b4': {
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'architecture': 'pan'
        },
        'eca_nfnet_l2': {
            'encoder': 'tu-eca_nfnet_l2', 
            'architecture': 'pan'
        },
        'seresnet152d': {
            'encoder': 'tu-seresnet152d',
            'architecture': 'pan'
        }
    }
    
    if model_name not in model_configs:
        raise ValueError(f"不支持的模型: {model_name}")
    
    model_config = model_configs[model_name]
    
    params = {
        'encoder_name': model_config['encoder'],
        'encoder_weights': None,
        'in_channels': 3,
        'classes': config['model']['classes'],
        'activation': None
    }
    
    if model_config['architecture'].lower() == 'pan':
        model = smp.PAN(**params)
    else:
        raise ValueError(f"不支持的架构: {model_config['architecture']}")
    
    return model.to(device)


def preprocess_image(image: np.ndarray, target_size: tuple) -> torch.Tensor:
    """预处理图像用于模型推理"""
    # 调整大小
    image = cv2.resize(image, target_size)
    
    # 转换为float32并归一化到0-1
    image = image.astype(np.float32) / 255.0
    
    # ImageNet标准化
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image = (image - mean) / std
    
    # 转换为tensor并添加批次维度
    image = np.transpose(image, (2, 0, 1))  # HWC -> CHW
    image = torch.from_numpy(image).float().unsqueeze(0)
    
    return image


def create_overlay(original_image: np.ndarray, prediction: np.ndarray, 
                  threshold: float = 0.5, alpha: float = 0.6) -> np.ndarray:
    """将预测结果叠加到原图上"""
    
    # 调整预测结果尺寸到原图大小
    height, width = original_image.shape[:2]
    prediction_resized = np.zeros((3, height, width), dtype=np.float32)
    for i in range(3):
        prediction_resized[i] = cv2.resize(prediction[i], (width, height))
    prediction = prediction_resized
    
    # 轨道掩码
    main_track_mask = (prediction[1] > threshold)
    branch_track_mask = (prediction[2] > threshold)
    
    print(f"  主轨道像素数: {np.sum(main_track_mask)}, 分叉轨道像素数: {np.sum(branch_track_mask)}")
    
    # 创建叠加图像（从原始图像开始）
    overlay = original_image.copy().astype(np.float32) / 255.0
    
    # 主轨道 - 橙色
    main_color = np.array([255, 140, 0]) / 255.0
    if np.sum(main_track_mask) > 0:
        for c in range(3):
            overlay[main_track_mask, c] = (1 - alpha) * overlay[main_track_mask, c] + alpha * main_color[c]
    
    # 分叉轨道 - 天蓝色
    branch_color = np.array([0, 191, 255]) / 255.0
    if np.sum(branch_track_mask) > 0:
        for c in range(3):
            overlay[branch_track_mask, c] = (1 - alpha) * overlay[branch_track_mask, c] + alpha * branch_color[c]
    
    # 转换回uint8
    overlay = (overlay * 255).astype(np.uint8)
    
    return overlay


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版测试图像可视化脚本')
    parser.add_argument('--model-path', type=str, required=True, help='模型权重文件路径')
    parser.add_argument('--model-name', type=str, required=True, 
                       choices=['efficientnet_b4', 'eca_nfnet_l2', 'seresnet152d'],
                       help='模型名称')
    parser.add_argument('--image-dir', type=str, required=True, help='图像目录')
    parser.add_argument('--output-dir', type=str, required=True, help='输出目录')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml', help='配置文件')
    parser.add_argument('--threshold', type=float, default=0.5, help='预测阈值')
    parser.add_argument('--max-samples', type=int, default=5, help='最大处理样本数')
    
    args = parser.parse_args()
    
    print("🎨 简化版测试图像可视化工具")
    print(f"📦 模型: {args.model_name}")
    print(f"📂 输出: {args.output_dir}")
    print("="*50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 获取目标尺寸
    target_size = (config['data']['image_size']['width'], 
                   config['data']['image_size']['height'])
    
    # 加载模型
    print(f"📦 加载模型: {args.model_name}")
    model = create_model(args.model_name, config, device)
    
    # 加载权重
    checkpoint = torch.load(args.model_path, map_location=device)
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    print(f"✅ 模型加载成功")
    
    # 创建输出目录
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取图像文件
    image_dir = Path(args.image_dir)
    if (image_dir / 'test' / 'images').exists():
        image_dir = image_dir / 'test' / 'images'
    
    image_files = list(image_dir.glob('*.png')) + list(image_dir.glob('*.jpg'))
    image_files = image_files[:args.max_samples]
    
    print(f"🎯 处理样本数: {len(image_files)}")
    
    # 处理每张图像
    for image_path in tqdm(image_files, desc="处理图像"):
        try:
            # 读取原始图像
            original_image = cv2.imread(str(image_path))
            if original_image is None:
                print(f"⚠️ 无法读取图像: {image_path}")
                continue
            
            # BGR -> RGB
            original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
            
            # 预处理图像用于模型
            processed_image = preprocess_image(original_image, target_size)
            
            # 模型预测
            with torch.no_grad():
                prediction = model(processed_image.to(device))
                prediction = torch.sigmoid(prediction)
                prediction = prediction[0].cpu().numpy()
            
            # 创建叠加图像
            overlay = create_overlay(original_image, prediction, args.threshold)
            
            # 保存结果
            save_path = output_path / f"{image_path.stem}_{args.model_name}_overlay.png"
            # RGB -> BGR for OpenCV
            overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
            cv2.imwrite(str(save_path), overlay_bgr)
            
        except Exception as e:
            print(f"⚠️ 处理 {image_path.name} 时出错: {e}")
            continue
    
    print(f"\n✅ 完成！")
    print(f"📂 结果保存在: {args.output_dir}")


if __name__ == '__main__':
    main() 