#!/usr/bin/env python3
"""
Optimize NPY mask files by:
1. Converting float32 to uint8 (4x reduction)
2. Using compressed NPZ format
3. Comparing different storage options
"""

import numpy as np
from pathlib import Path
import argparse
from tqdm import tqdm
import json


def analyze_mask_file(npy_path):
    """Analyze a single NPY mask file"""
    try:
        mask = np.load(npy_path)
    except (<PERSON><PERSON><PERSON><PERSON>rror, IOError):
        # Handle empty or corrupted files
        return {
            'shape': None,
            'dtype': None,
            'size_mb': 0,
            'unique_values': [],
            'max_value': 0,
            'min_value': 0,
            'is_binary': False,
            'non_zero_ratio': 0,
            'is_empty': True,
            'file_size_bytes': npy_path.stat().st_size
        }
    
    # Get basic info
    info = {
        'shape': mask.shape,
        'dtype': str(mask.dtype),
        'size_mb': mask.nbytes / 1024 / 1024,
        'unique_values': np.unique(mask).tolist(),
        'max_value': float(np.max(mask)),
        'min_value': float(np.min(mask)),
        'is_empty': False,
        'file_size_bytes': npy_path.stat().st_size
    }
    
    # Check if it's binary
    unique_vals = np.unique(mask)
    is_binary = len(unique_vals) <= 2 and all(v in [0, 1] for v in unique_vals)
    info['is_binary'] = is_binary
    
    # Check sparsity
    non_zero_ratio = np.count_nonzero(mask) / mask.size
    info['non_zero_ratio'] = non_zero_ratio
    
    return info


def optimize_single_mask(npy_path, output_dir, method='uint8_npz'):
    """Optimize a single mask file"""
    mask = np.load(npy_path)
    original_size = mask.nbytes
    
    output_path = output_dir / npy_path.name
    
    if method == 'uint8_npy':
        # Convert to uint8
        mask_uint8 = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
        np.save(output_path.with_suffix('.npy'), mask_uint8)
        new_size = mask_uint8.nbytes
        
    elif method == 'uint8_npz':
        # Convert to uint8 and save as compressed NPZ
        mask_uint8 = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
        np.savez_compressed(output_path.with_suffix('.npz'), mask=mask_uint8)
        new_size = output_path.with_suffix('.npz').stat().st_size
        
    elif method == 'bool_npz':
        # Convert to boolean and save as compressed NPZ
        mask_bool = mask.astype(bool)
        np.savez_compressed(output_path.with_suffix('.npz'), mask=mask_bool)
        new_size = output_path.with_suffix('.npz').stat().st_size
        
    elif method == 'sparse':
        # For multilabel masks, save each channel separately as sparse
        from scipy import sparse
        
        if len(mask.shape) == 3:
            # Save each channel as a sparse matrix
            channels = {}
            for i in range(mask.shape[2]):
                channel = mask[:, :, i]
                if np.count_nonzero(channel) > 0:
                    sparse_channel = sparse.csr_matrix(channel)
                    channels[f'channel_{i}'] = sparse_channel
            
            # Save using scipy sparse format
            output_path = output_path.with_suffix('.npz')
            sparse.save_npz(str(output_path), sparse.vstack([c.reshape(1, -1) for c in channels.values()]))
            new_size = output_path.stat().st_size
        else:
            # Single channel
            sparse_mask = sparse.csr_matrix(mask)
            sparse.save_npz(str(output_path.with_suffix('.npz')), sparse_mask)
            new_size = output_path.with_suffix('.npz').stat().st_size
    
    return original_size, new_size


def compare_storage_methods(npy_path):
    """Compare different storage methods for a mask file"""
    mask = np.load(npy_path)
    original_size = npy_path.stat().st_size
    
    results = {
        'original': {
            'size_bytes': original_size,
            'size_mb': original_size / 1024 / 1024,
            'format': 'float32_npy'
        }
    }
    
    # Test different methods
    import tempfile
    import os
    
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdir = Path(tmpdir)
        
        # Method 1: uint8 NPY
        mask_uint8 = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
        path = tmpdir / 'mask_uint8.npy'
        np.save(path, mask_uint8)
        results['uint8_npy'] = {
            'size_bytes': path.stat().st_size,
            'size_mb': path.stat().st_size / 1024 / 1024,
            'reduction': original_size / path.stat().st_size
        }
        
        # Method 2: uint8 compressed NPZ
        path = tmpdir / 'mask_uint8.npz'
        np.savez_compressed(path, mask=mask_uint8)
        results['uint8_npz'] = {
            'size_bytes': path.stat().st_size,
            'size_mb': path.stat().st_size / 1024 / 1024,
            'reduction': original_size / path.stat().st_size
        }
        
        # Method 3: boolean compressed NPZ
        mask_bool = mask.astype(bool)
        path = tmpdir / 'mask_bool.npz'
        np.savez_compressed(path, mask=mask_bool)
        results['bool_npz'] = {
            'size_bytes': path.stat().st_size,
            'size_mb': path.stat().st_size / 1024 / 1024,
            'reduction': original_size / path.stat().st_size
        }
        
        # Method 4: PNG (for reference)
        import cv2
        if len(mask.shape) == 3:
            # For multilabel, create a single channel visualization
            single_channel = np.zeros(mask.shape[:2], dtype=np.uint8)
            single_channel[mask[:, :, 1] > 0] = 85   # Main track
            single_channel[mask[:, :, 2] > 0] = 170  # Fork track
        else:
            single_channel = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
        
        path = tmpdir / 'mask.png'
        cv2.imwrite(str(path), single_channel)
        results['png'] = {
            'size_bytes': path.stat().st_size,
            'size_mb': path.stat().st_size / 1024 / 1024,
            'reduction': original_size / path.stat().st_size
        }
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Optimize NPY mask files')
    parser.add_argument('--data-dir', type=str, required=True, help='Data directory')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, don\'t convert')
    parser.add_argument('--method', type=str, default='uint8_npz', 
                       choices=['uint8_npy', 'uint8_npz', 'bool_npz', 'sparse'],
                       help='Optimization method')
    parser.add_argument('--output-dir', type=str, help='Output directory for optimized masks')
    parser.add_argument('--sample-size', type=int, default=10, help='Number of files to sample for analysis')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    
    # Find all NPY files
    npy_files = list(data_dir.rglob('masks/*.npy'))
    print(f"Found {len(npy_files)} NPY mask files")
    
    if len(npy_files) == 0:
        print("No NPY files found!")
        return
    
    # Sample files for analysis
    import random
    # Filter out empty files first
    non_empty_files = [f for f in npy_files if f.stat().st_size > 1000]  # > 1KB
    
    if len(non_empty_files) == 0:
        print("All NPY files appear to be empty!")
        return
        
    sample_files = random.sample(non_empty_files, min(args.sample_size, len(non_empty_files)))
    
    print(f"\nAnalyzing {len(sample_files)} sample files (from {len(non_empty_files)} non-empty files)...")
    print(f"Note: {len(npy_files) - len(non_empty_files)} files are empty or very small")
    
    # Analyze sample files
    total_original_size = 0
    analysis_results = []
    first_valid_file = None
    
    for npy_path in tqdm(sample_files, desc="Analyzing"):
        info = analyze_mask_file(npy_path)
        info['filename'] = npy_path.name
        analysis_results.append(info)
        if not info.get('is_empty', False):
            total_original_size += info['size_mb']
            if first_valid_file is None:
                first_valid_file = npy_path
        
        # Compare storage methods for first valid file
        if first_valid_file and len(analysis_results) == 1 and not info.get('is_empty', False):
            print(f"\nStorage comparison for {npy_path.name}:")
            comparison = compare_storage_methods(npy_path)
            for method, stats in comparison.items():
                print(f"  {method}: {stats['size_mb']:.2f} MB", end='')
                if 'reduction' in stats:
                    print(f" (reduction: {stats['reduction']:.1f}x)")
                else:
                    print()
    
    # Summary statistics
    print(f"\nAnalysis Summary:")
    print(f"Average file size: {total_original_size / len(sample_files):.2f} MB")
    print(f"Total size (sampled): {total_original_size:.2f} MB")
    print(f"Estimated total size (all files): {total_original_size / len(sample_files) * len(npy_files):.2f} MB")
    
    # Check data properties
    valid_results = [r for r in analysis_results if not r.get('is_empty', False)]
    if valid_results:
        all_binary = all(r['is_binary'] for r in valid_results)
        avg_sparsity = np.mean([r['non_zero_ratio'] for r in valid_results])
        print(f"\nData properties:")
        print(f"All masks binary: {all_binary}")
        print(f"Average non-zero ratio: {avg_sparsity:.3f}")
    else:
        print("\nNo valid mask files found for analysis!")
    
    # Save analysis results
    analysis_path = data_dir / 'mask_analysis.json'
    with open(analysis_path, 'w') as f:
        json.dump(analysis_results, f, indent=2)
    print(f"\nAnalysis results saved to: {analysis_path}")
    
    # Convert files if requested
    if not args.analyze_only and args.output_dir:
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\nConverting {len(npy_files)} files using method: {args.method}")
        
        total_original = 0
        total_new = 0
        
        for npy_path in tqdm(npy_files, desc="Converting"):
            rel_path = npy_path.relative_to(data_dir)
            output_subdir = output_dir / rel_path.parent
            output_subdir.mkdir(parents=True, exist_ok=True)
            
            orig_size, new_size = optimize_single_mask(npy_path, output_subdir, args.method)
            total_original += orig_size
            total_new += new_size
        
        print(f"\nConversion complete!")
        print(f"Original total size: {total_original / 1024 / 1024 / 1024:.2f} GB")
        print(f"New total size: {total_new / 1024 / 1024 / 1024:.2f} GB")
        print(f"Reduction: {total_original / total_new:.1f}x")
        print(f"Space saved: {(total_original - total_new) / 1024 / 1024 / 1024:.2f} GB")


if __name__ == '__main__':
    main()