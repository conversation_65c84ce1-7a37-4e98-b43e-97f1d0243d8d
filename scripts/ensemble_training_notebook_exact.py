#!/usr/bin/env python3
"""
集成训练脚本 - 与当前目录数据处理方式保持一致
使用RailwayTrackDataset和配置文件驱动的训练
"""

import os
import sys
import torch
import torch.nn as nn
import segmentation_models_pytorch as smp
from pathlib import Path
import argparse
import numpy as np
import matplotlib.pyplot as plt
import dill
import time
import random
import gc
import json
from datetime import datetime
from collections import namedtuple
from torch.utils.data import DataLoader
import yaml
from tqdm import tqdm
import io

# 显存优化配置
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

# 添加项目根目录到系统路径（与其他脚本保持一致）
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 使用与当前目录一致的导入方式
from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.data.augmentations import get_train_transform, get_val_transform
from src.utils.metrics import iou_coef
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.utils import RunningAverage
from src.train import train

# 设置matplotlib样式（与notebook保持一致）
plt.style.use('ggplot')


# 定义EpochStats（与notebook保持一致）
EpochStats = namedtuple('EpochStats', 'epoch learning_rate train_loss val_loss val_jac time')


class CustomUnpickler(torch.serialization.pickle.Unpickler):
    """自定义反序列化器，用于处理模块路径变更"""
    
    def find_class(self, module, name):
        # 处理旧版本的 PAN 模块路径映射
        if module == 'segmentation_models_pytorch.pan':
            module = 'segmentation_models_pytorch.decoders.pan'
        elif module == 'segmentation_models_pytorch.pan.model':
            module = 'segmentation_models_pytorch.decoders.pan.model'
        elif module.startswith('segmentation_models_pytorch.pan.'):
            # 替换所有以 pan. 开头的子模块
            module = module.replace('segmentation_models_pytorch.pan.', 
                                   'segmentation_models_pytorch.decoders.pan.')
        
        # 处理 timm 库的路径变更
        elif module == 'timm.models.layers.conv2d_same':
            module = 'timm.layers.conv2d_same'
        elif module == 'timm.models.layers':
            module = 'timm.layers'
        elif module.startswith('timm.models.layers.'):
            # 替换所有 timm.models.layers. 为 timm.layers.
            module = module.replace('timm.models.layers.', 'timm.layers.')
        elif module == 'timm.models.efficientnet_blocks':
            # EfficientNet blocks 在新版本中的位置
            module = 'timm.models._efficientnet_blocks'
        elif module == 'timm.models.efficientnet_builder':
            module = 'timm.models._efficientnet_builder'
        
        return super().find_class(module, name)


def load_pretrained_model(model_path, device='cuda'):
    """
    加载预训练模型，自动修复模块路径问题
    
    Args:
        model_path: 模型文件路径
        device: 设备
        
    Returns:
        加载的模型
    """
    print(f"正在加载模型: {model_path}")
    
    try:
        # 方法1: 先尝试标准加载（适用于新格式）
        model = torch.load(model_path, map_location=device, weights_only=False)
        print("✅ 标准方法加载成功")
        return model
        
    except Exception as e:
        print(f"标准方法失败: {e}")
        
        if "No module named 'segmentation_models_pytorch.pan'" in str(e):
            print("🔧 检测到模块路径问题，尝试修复...")
            
            try:
                # 方法2: 使用自定义反序列化器
                # 首先尝试 ZIP 格式（PyTorch 1.6+）
                import zipfile
                
                # 检查是否是zip格式
                if zipfile.is_zipfile(model_path):
                    print("检测到ZIP格式，使用zip方法修复...")
                    
                    # 创建临时解压环境
                    with zipfile.ZipFile(model_path, 'r') as zip_ref:
                        # 读取 data.pkl
                        with zip_ref.open('archive/data.pkl') as pkl_file:
                            buffer = io.BytesIO(pkl_file.read())
                    
                    # 使用标准torch.load，但指定pickle_module
                    class PickleModule:
                        Unpickler = CustomUnpickler
                    
                    # 关闭zip文件，重新用torch.load加载
                    model = torch.load(model_path, map_location=device, 
                                     pickle_module=PickleModule, weights_only=False)
                    print("✅ ZIP格式修复成功!")
                    
                else:
                    print("检测到Legacy Pickle格式，使用pickle方法修复...")
                    # Legacy pickle 格式
                    with open(model_path, 'rb') as f:
                        buffer = io.BytesIO(f.read())
                    
                    unpickler = CustomUnpickler(buffer)
                    
                    # 处理持久化存储
                    def persistent_load(saved_id):
                        if isinstance(saved_id, tuple):
                            # PyTorch tensor 存储格式
                            if len(saved_id) == 2:
                                return torch._utils._rebuild_tensor_v2(*saved_id)
                            else:
                                return torch.storage._load_from_bytes(*saved_id)
                        return saved_id
                    
                    unpickler.persistent_load = persistent_load
                    model = unpickler.load()
                    
                    # 移动到正确设备
                    if hasattr(model, 'to'):
                        model = model.to(device)
                    
                    print("✅ Legacy格式修复成功!")
                
                return model
                
            except Exception as e2:
                print(f"自定义方法也失败: {e2}")
                
                try:
                    # 方法3: 尝试使用 encoding='latin-1'
                    print("尝试使用latin-1编码...")
                    
                    import pickle
                    with open(model_path, 'rb') as f:
                        model = pickle.load(f, encoding='latin-1')
                    
                    if hasattr(model, 'to'):
                        model = model.to(device)
                    
                    print("✅ latin-1编码加载成功!")
                    return model
                    
                except Exception as e3:
                    print(f"所有方法都失败: {e3}")
                    raise e3
        else:
            # 其他类型的错误直接抛出
            raise e


def setup_seed(seed=0):
    """设置随机种子（与notebook保持一致）"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    random.seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def train_with_checkpoint(model, optimizer, criterion, train_loader, val_loader, 
                         epochs, lr_scheduler, weights_path, 
                         checkpoint_dir=None, resume_from=None,
                         accumulate_every_n_epochs=2, clip_gradient=True,
                         model_name='model'):
    """
    训练函数 - 基于notebook的train函数，添加checkpoint功能
    
    每个epoch保存checkpoint，支持断点续训
    """
    device = torch.device('cuda')
    model.to(device)
    scaler = torch.cuda.amp.GradScaler()
    history = []
    best_val_jac = 0.0
    start_epoch = 0
    
    # 创建checkpoint目录
    if checkpoint_dir is None:
        checkpoint_dir = Path(weights_path).parent / 'checkpoints'
    checkpoint_dir = Path(checkpoint_dir)
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建可视化目录
    vis_dir = Path(weights_path).parent / 'visualizations' / model_name
    vis_dir.mkdir(parents=True, exist_ok=True)
    
    # 恢复训练
    if resume_from and Path(resume_from).exists():
        print(f'Resuming from checkpoint: {resume_from}')
        checkpoint = torch.load(resume_from, map_location=device)
        
        # 恢复模型状态
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # 恢复学习率调度器
        if lr_scheduler is not None and 'scheduler_state_dict' in checkpoint:
            lr_scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        # 恢复训练状态
        start_epoch = checkpoint['epoch']
        best_val_jac = checkpoint.get('best_val_jac', 0.0)
        history = checkpoint.get('history', [])
        
        # 恢复随机状态
        if 'random_state' in checkpoint:
            torch.set_rng_state(checkpoint['random_state']['torch'])
            np.random.set_state(checkpoint['random_state']['numpy'])
            random.setstate(checkpoint['random_state']['python'])
            torch.cuda.set_rng_state(checkpoint['random_state']['cuda'])
        
        print(f'Resumed from epoch {start_epoch}, best val IoU: {best_val_jac:.4f}')
    
    # 训练循环（与notebook保持一致）
    for e in range(start_epoch, epochs):
        loss_avg = RunningAverage()
        val_jac_avg = RunningAverage()
        val_loss_avg = RunningAverage()
        
        torch.cuda.empty_cache()
        gc.collect()
        
        model.train()
        with tqdm(total=len(train_loader), leave=False, file=sys.stdout) as t:
            if lr_scheduler is not None:
                stats_current_lr = lr_scheduler.get_last_lr()[0]
            else:
                stats_current_lr = optimizer.param_groups[0]['lr']
            t.set_description(f'Epoch {e + 1}, LR {stats_current_lr:.6f}')
            
            for batch_n, batch_data in enumerate(train_loader):
                train_batch, labels_batch = batch_data['image'], batch_data['mask']
                train_batch, labels_batch = train_batch.to(device), labels_batch.to(device)
                
                with torch.autocast(device_type='cuda'):
                    output_batch = model(train_batch)
                    loss = criterion(output_batch, labels_batch)
                    loss_avg.update(loss.item())
                    loss /= accumulate_every_n_epochs
                
                scaler.scale(loss).backward()
                
                if (batch_n + 1) % accumulate_every_n_epochs == 0:
                    if clip_gradient:
                        torch.nn.utils.clip_grad_norm_(model.parameters(), 1)
                    scaler.step(optimizer)
                    scaler.update()
                    optimizer.zero_grad(set_to_none=True)
                
                t.set_postfix({'stats': f'train_loss: {loss_avg():.4f}'})
                t.update()
                stats_time_elapsed = t.format_interval(t.format_dict['elapsed'])
        
        if lr_scheduler is not None:
            lr_scheduler.step()
        
        # 验证（与notebook保持一致）
        model.eval()
        saved_vis = False  # 标记是否已保存可视化
        num_vis_to_save = 3  # 每个epoch保存的可视化数量
        
        with torch.no_grad():
            for batch_idx, batch_data in enumerate(val_loader):
                val_batch, val_labels_batch = batch_data['image'], batch_data['mask']
                val_batch, val_labels_batch = val_batch.to(device), val_labels_batch.to(device)
                
                val_output_batch = model(val_batch)
                val_loss = criterion(val_output_batch, val_labels_batch)
                val_loss_avg.update(val_loss.item())
                
                val_predicted = torch.nn.Sigmoid()(val_output_batch)
                val_jac_batch = iou_coef(val_labels_batch, val_predicted)
                val_jac_avg.update(val_jac_batch.item())
                
                # 保存可视化（每个epoch随机保存几张）
                if not saved_vis and batch_idx < len(val_loader) // 4:  # 只在前1/4的batch中保存
                    # 随机选择batch中的样本
                    batch_size = val_batch.shape[0]
                    vis_indices = np.random.choice(batch_size, min(num_vis_to_save, batch_size), replace=False)
                    
                    for vis_idx in vis_indices:
                        # 获取单个样本
                        image = val_batch[vis_idx].cpu()
                        true_mask = val_labels_batch[vis_idx].cpu()
                        pred_mask = val_predicted[vis_idx].cpu()
                        
                        # 反归一化图像
                        image = image * torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                        image = image + torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                        image = torch.clamp(image, 0, 1)
                        
                        # 转换为numpy数组
                        image_np = image.permute(1, 2, 0).numpy()
                        true_mask_np = true_mask.permute(1, 2, 0).numpy()
                        pred_mask_np = pred_mask.permute(1, 2, 0).numpy()
                        
                        # 创建可视化
                        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
                        
                        # 原始图像
                        axes[0].imshow(image_np)
                        axes[0].set_title('Original Image')
                        axes[0].axis('off')
                        
                        # 真实掩码
                        true_rgb = np.zeros_like(true_mask_np)
                        # 正确的通道映射：
                        # true_mask_np[:, :, 0] = 背景 (不显示)
                        # true_mask_np[:, :, 1] = 主轨道 → 红色通道
                        # true_mask_np[:, :, 2] = 分叉轨道 → 绿色通道
                        true_rgb[:, :, 0] = true_mask_np[:, :, 1]  # 主轨道 - 红色
                        true_rgb[:, :, 1] = true_mask_np[:, :, 2]  # 分叉轨道 - 绿色
                        # 背景 (true_mask_np[:, :, 0]) 保持黑色，不设置
                        axes[1].imshow(true_rgb)
                        axes[1].set_title('Ground Truth')
                        axes[1].axis('off')
                        
                        # 预测掩码
                        pred_rgb = np.zeros_like(pred_mask_np)
                        # 正确的通道映射：
                        # pred_mask_np[:, :, 0] = 背景 (不显示)
                        # pred_mask_np[:, :, 1] = 主轨道 → 红色通道
                        # pred_mask_np[:, :, 2] = 分叉轨道 → 绿色通道
                        pred_rgb[:, :, 0] = pred_mask_np[:, :, 1]  # 主轨道 - 红色
                        pred_rgb[:, :, 1] = pred_mask_np[:, :, 2]  # 分叉轨道 - 绿色
                        # 背景 (pred_mask_np[:, :, 0]) 保持黑色，不设置
                        axes[2].imshow(pred_rgb)
                        axes[2].set_title('Prediction')
                        axes[2].axis('off')
                        
                        # 叠加显示
                        overlay = image_np.copy()
                        mask_overlay = pred_rgb * 0.5
                        overlay = np.clip(overlay + mask_overlay, 0, 1)
                        axes[3].imshow(overlay)
                        axes[3].set_title('Overlay')
                        axes[3].axis('off')
                        
                        plt.suptitle(f'Epoch {e+1} - Validation Sample - IoU: {val_jac_batch.item():.4f}')
                        plt.tight_layout()
                        
                        # 保存图像
                        vis_filename = vis_dir / f'epoch_{e+1:03d}_sample_{vis_idx}.png'
                        plt.savefig(vis_filename, dpi=150, bbox_inches='tight')
                        plt.close()
                    
                    saved_vis = True
        
        stats_epoch = EpochStats(epoch=e + 1,
                               learning_rate=stats_current_lr,
                               train_loss=loss_avg(),
                               val_loss=val_loss_avg(),
                               val_jac=val_jac_avg(),
                               time=stats_time_elapsed)
        history.append(stats_epoch)
        
        print(f'Epoch {stats_epoch.epoch}. LR {stats_epoch.learning_rate:.6f}, '
              f'train_loss: {stats_epoch.train_loss:.4f}, '
              f'val_loss: {stats_epoch.val_loss:.4f}, '
              f'val_jac: {stats_epoch.val_jac:.4f}, '
              f'time: {stats_epoch.time}')
        
        # 保存最佳模型（与notebook保持一致）
        if val_jac_avg() > best_val_jac:
            torch.save(model, weights_path)
            best_val_jac = val_jac_avg()
            print(f'  -> Saved best model with val_jac: {best_val_jac:.4f}')
        
        # 每个epoch保存checkpoint（新增功能）
        checkpoint_path = checkpoint_dir / f'checkpoint_epoch_{e + 1}.pth'
        checkpoint = {
            'epoch': e + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'best_val_jac': best_val_jac,
            'history': history,
            'random_state': {
                'torch': torch.get_rng_state(),
                'numpy': np.random.get_state(),
                'python': random.getstate(),
                'cuda': torch.cuda.get_rng_state()
            }
        }
        
        if lr_scheduler is not None:
            checkpoint['scheduler_state_dict'] = lr_scheduler.state_dict()
        
        torch.save(checkpoint, checkpoint_path)
        print(f'  -> Saved checkpoint: {checkpoint_path}')
    
    return history


def main():
    """主函数 - 使用与当前目录一致的数据处理方式"""
    parser = argparse.ArgumentParser(description='集成训练脚本 - 与当前目录数据处理方式保持一致')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, 
                       help='数据集根目录')
    parser.add_argument('--output-dir', type=str, default='weights',
                      help='模型权重输出目录')
    parser.add_argument('--resume', action='store_true',
                      help='从checkpoint恢复训练')
    parser.add_argument('--pretrained-efficientnet', type=str, default=None,
                      help='预训练的EfficientNet模型路径（.pth.tar文件）')
    parser.add_argument('--pretrained-nfnet', type=str, default=None,
                      help='预训练的NFNet模型路径（.pth.tar文件）')
    parser.add_argument('--pretrained-resnet', type=str, default=None,
                      help='预训练的ResNet模型路径（.pth.tar文件）')
    parser.add_argument('--seed', type=int, default=0,
                      help='随机种子')
    
    # 新增模型选择参数
    parser.add_argument('--models', type=str, nargs='*', default=None,
                      help='指定要训练的模型列表 (可选: efficientnetb4, eca_nfnet_l2, seresnet152d)')
    parser.add_argument('--train-all', action='store_true',
                      help='训练所有可用模型（默认行为）')
    parser.add_argument('--list-models', action='store_true',
                      help='显示所有可用模型并退出')
    
    args = parser.parse_args()
    
    # 定义所有可用的模型配置
    all_models_config = {
        'efficientnetb4': {
            'name': 'efficientnetb4',
            'architecture': 'PAN',
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'encoder_weights': 'noisy-student',
            'epochs': 25,
            'pretrained_path': args.pretrained_efficientnet,
            'description': 'EfficientNet-B4 with Noisy Student weights'
        },
        'eca_nfnet_l2': {
            'name': 'eca_nfnet_l2',
            'architecture': 'PAN',
            'encoder': 'tu-eca_nfnet_l2',
            'encoder_weights': 'imagenet',
            'epochs': 25,
            'pretrained_path': args.pretrained_nfnet,
            'description': 'ECA-NFNet-L2 with ImageNet weights'
        },
        'seresnet152d': {
            'name': 'seresnet152d',
            'architecture': 'PAN',
            'encoder': 'tu-seresnet152d',
            'encoder_weights': 'imagenet',
            'epochs': 30,
            'pretrained_path': args.pretrained_resnet,
            'description': 'SE-ResNet-152D with ImageNet weights'
        }
    }
    
    # 如果用户请求列出模型，显示并退出
    if args.list_models:
        print("可用的模型列表:")
        print("-" * 60)
        for model_key, model_info in all_models_config.items():
            print(f"  {model_key:15} - {model_info['description']}")
            print(f"  {'':15}   架构: {model_info['architecture']}, 编码器: {model_info['encoder']}")
            print(f"  {'':15}   训练轮数: {model_info['epochs']}")
            print()
        print("使用示例:")
        print("  python scripts/ensemble_training_notebook_exact.py --data-dir /path/to/data --models efficientnetb4 seresnet152d")
        print("  python scripts/ensemble_training_notebook_exact.py --data-dir /path/to/data --train-all")
        return
    
    # 检查 data-dir 参数（只有在非 list-models 模式下才需要）
    if not args.data_dir:
        parser.error("--data-dir 参数是必需的（除非使用 --list-models）")
    
    # 确定要训练的模型
    if args.models is not None:
        # 用户指定了特定模型
        selected_models = []
        invalid_models = []
        
        for model_name in args.models:
            if model_name in all_models_config:
                selected_models.append(model_name)
            else:
                invalid_models.append(model_name)
        
        if invalid_models:
            print(f"错误: 无效的模型名称: {invalid_models}")
            print(f"可用模型: {list(all_models_config.keys())}")
            print("使用 --list-models 查看详细信息")
            return
        
        models_config = [all_models_config[name] for name in selected_models]
        print(f"将训练指定的 {len(models_config)} 个模型: {selected_models}")
        
    elif args.train_all:
        # 用户明确指定训练所有模型
        models_config = list(all_models_config.values())
        print(f"将训练所有 {len(models_config)} 个模型: {list(all_models_config.keys())}")
        
    else:
        # 默认行为：训练所有模型
        models_config = list(all_models_config.values())
        print(f"默认训练所有 {len(models_config)} 个模型: {list(all_models_config.keys())}")
        print("提示: 使用 --models 参数可以指定特定模型，使用 --list-models 查看可用模型")
    
    # 设置随机种子（与notebook保持一致）
    setup_seed(args.seed)
    
    # 加载配置文件（与当前目录方式一致）
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径
    config['data']['train_path'] = args.data_dir
    config['data']['val_path'] = args.data_dir
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("=" * 80)
    print("集成训练脚本 - 与当前目录数据处理方式保持一致")
    print("=" * 80)
    print(f"数据目录: {args.data_dir}")
    print(f"配置文件: {args.config}")
    print(f"设备: {device}")
    print(f"要训练的模型: {[model['name'] for model in models_config]}")
    
    # 创建数据集（使用与当前目录一致的方式）
    print("\n创建数据集...")
    
    # 获取数据变换
    train_transform = get_train_transform(config)
    val_transform = get_val_transform(config)
    
    # 创建训练和验证数据集
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val',
        config=config,
        transform=val_transform
    )
    
    # 从配置获取batch_size
    batch_size = config['data']['batch_size']['train']
    val_batch_size = config['data']['batch_size']['val']
    num_workers = config.get('project', {}).get('num_workers', 2)
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    print(f"批量大小: train={batch_size}, val={val_batch_size}")
    
    # 创建数据加载器
    loaders = {
        'train': DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True
        ),
        'val': DataLoader(
            val_dataset,
            batch_size=val_batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
    }
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 训练选定的模型
    trained_models = []
    skipped_models = []
    
    for i, model_config in enumerate(models_config, 1):
        print(f"\n{'=' * 80}")
        print(f"训练模型 {i}/{len(models_config)}: {model_config['name']}")
        print(f"描述: {model_config.get('description', 'N/A')}")
        print(f"{'=' * 80}")
        
        weights_path = output_dir / f"{model_config['name']}.pth.tar"
        history_path = output_dir / f"history_{model_config['name']}"
        checkpoint_dir = output_dir / 'checkpoints' / model_config['name']
        
        # 检查是否已有训练好的模型（与notebook保持一致）
        if weights_path.exists() and history_path.exists() and not args.resume:
            print(f'跳过训练 - 加载已有权重: {weights_path}')
            with open(history_path, 'rb') as handle:
                history = dill.load(handle)
            skipped_models.append(model_config['name'])
            continue
        
        print('开始训练...')
        
        # 创建模型（与notebook保持一致）
        if model_config['pretrained_path'] and Path(model_config['pretrained_path']).exists():
            # 加载预训练模型
            print(f'加载预训练模型: {model_config["pretrained_path"]}')
            try:
                # 首先尝试加载转换后的格式（只包含state_dict）
                checkpoint = torch.load(model_config['pretrained_path'], map_location=device)
                
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    # 这是转换后的格式
                    print("检测到转换后的模型格式，创建模型并加载权重...")
                    model = smp.PAN(
                        encoder_name=model_config['encoder'],
                        encoder_weights=None,  # 不使用预训练权重，我们会加载自己的
                        in_channels=3,
                        classes=3
                    )
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"✓ 成功从转换后的checkpoint加载模型")
                else:
                    # 尝试作为完整模型加载
                    model = load_pretrained_model(model_config['pretrained_path'], device=device)
                    
                    # 验证模型架构
                    if not hasattr(model, 'encoder'):
                        raise ValueError(f"预训练模型缺少 'encoder' 属性，可能不是有效的分割模型")
                    
                    if not hasattr(model, 'decoder'):
                        raise ValueError(f"预训练模型缺少 'decoder' 属性，可能不是有效的分割模型")
                    
                    # 验证输出类别数
                    if hasattr(model, 'segmentation_head'):
                        out_channels = model.segmentation_head[0].out_channels
                        if out_channels != 3:
                            raise ValueError(f"预训练模型输出类别数为 {out_channels}，但期望为 3")
                    
                    print(f"✓ 成功加载预训练模型，编码器: {model.encoder.name if hasattr(model.encoder, 'name') else 'unknown'}")
                
            except Exception as e:
                print(f"\n错误: 无法加载预训练模型 '{model_config['pretrained_path']}'")
                print(f"错误信息: {str(e)}")
                print("\n解决方案:")
                print("1. 如果这是旧版本的模型，请先使用convert_old_models.py转换:")
                print(f"   python scripts/convert_old_models.py {model_config['pretrained_path']} --type {model_config['name']}")
                print("2. 然后使用转换后的模型文件")
                print("3. 或者不指定 --pretrained-* 参数从头训练")
                sys.exit(1)
        else:
            # 创建新模型
            if model_config['pretrained_path']:
                # 用户指定了预训练路径但文件不存在
                print(f"\n错误: 找不到预训练模型文件: {model_config['pretrained_path']}")
                print("请检查文件路径是否正确")
                sys.exit(1)
            
            print(f"创建新模型: {model_config['name']}")
            model = smp.PAN(
                encoder_name=model_config['encoder'],
                encoder_weights=model_config['encoder_weights'],
                in_channels=3,
                classes=3
            )
            print(f"✓ 使用 {model_config['encoder_weights']} 预训练权重初始化编码器")
        
        # 创建损失函数（使用与当前目录一致的方式）
        loss_config = config.get('loss', {})
        alpha_weights = loss_config.get('alpha', [0.1, 0.3, 0.6])
        
        criterion = MultiLabelSegmentationLoss(
            jaccard_weight=loss_config.get('jaccard_weight', 0.5),
            focal_weight=loss_config.get('focal_weight', 0.5),
            alpha=alpha_weights,
            gamma=loss_config.get('gamma', 2.0)
        )
        
        # 创建优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.0005)
        
        # 创建学习率调度器
        lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=model_config['epochs'], eta_min=1e-6
        )
        
        # 确定恢复点
        resume_from = None
        if args.resume:
            latest_checkpoint = checkpoint_dir / 'latest_checkpoint.pth'
            if latest_checkpoint.exists():
                resume_from = latest_checkpoint
                print(f'将从 {resume_from} 恢复训练')
        
        # 开始训练
        history = train_with_checkpoint(
            model=model,
            optimizer=optimizer,
            criterion=criterion,
            train_loader=loaders['train'],
            val_loader=loaders['val'],
            epochs=model_config['epochs'],
            lr_scheduler=lr_scheduler,
            weights_path=weights_path,
            checkpoint_dir=checkpoint_dir,
            resume_from=resume_from,
            model_name=model_config['name']
        )
        
        # 保存训练历史（与notebook保持一致）
        with open(history_path, 'wb') as handle:
            dill.dump(history, handle)
        
        print(f'模型训练完成: {model_config["name"]}')
        print(f'权重已保存: {weights_path}')
        print(f'历史已保存: {history_path}')
        trained_models.append(model_config['name'])
        
        # 可视化训练曲线
        if history:
            # 提取数据
            epochs = [stat.epoch for stat in history]
            train_loss = [stat.train_loss for stat in history]
            val_loss = [stat.val_loss for stat in history]
            val_jac = [stat.val_jac for stat in history]
            
            x = np.arange(1, len(epochs) + 1)
            
            # 损失曲线
            plt.figure(figsize=(12, 6))
            plt.plot(x, train_loss, label='Training Set Loss')
            plt.plot(x, val_loss, label='Validation Set Loss')
            plt.title(f'Loss values during training - {model_config["name"]}')
            plt.xlabel('Epochs')
            plt.ylabel('Loss')
            plt.legend()
            plt.savefig(output_dir / f'loss_curve_{model_config["name"]}.png')
            plt.close()
            
            # IoU曲线
            plt.figure(figsize=(12, 6))
            plt.plot(x, val_jac, label='IoU')
            plt.title(f'Validation Set IoU - {model_config["name"]}')
            plt.xlabel('Epochs')
            plt.ylabel('Metric')
            plt.savefig(output_dir / f'iou_curve_{model_config["name"]}.png')
            plt.close()
    
    print("\n" + "=" * 80)
    print("训练任务完成!")
    print("=" * 80)
    
    # 显示训练结果总结
    if trained_models:
        print(f"✅ 成功训练的模型 ({len(trained_models)}个): {', '.join(trained_models)}")
    
    if skipped_models:
        print(f"⏭️  跳过的模型 ({len(skipped_models)}个): {', '.join(skipped_models)}")
    
    # 保存训练配置
    config_path = output_dir / 'training_config.json'
    with open(config_path, 'w') as f:
        json.dump({
            'data_dir': args.data_dir,
            'config_file': args.config,
            'seed': args.seed,
            'requested_models': [model['name'] for model in models_config],
            'trained_models': trained_models,
            'skipped_models': skipped_models,
            'models_config': models_config,
            'timestamp': datetime.now().isoformat()
        }, f, indent=2)
    
    print(f"\n📝 训练配置已保存至: {config_path}")


if __name__ == '__main__':
    main()