#!/usr/bin/env python
"""
铁路分割数据处理管线
完整的数据预处理流程，使用全关键点边界算法
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from tqdm import tqdm
import sys
import shutil
from datetime import datetime
import logging

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser, preprocess_json_annotations


def setup_logging(output_dir: Path):
    """设置日志记录"""
    log_file = output_dir / f'pipeline_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)


def validate_input_data(json_dir: Path, images_dir: Path = None):
    """验证输入数据"""
    logger = logging.getLogger(__name__)
    
    # 检查JSON目录
    if not json_dir.exists():
        raise FileNotFoundError(f"JSON目录不存在: {json_dir}")
    
    json_files = list(json_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"JSON目录中没有找到JSON文件: {json_dir}")
    
    logger.info(f"找到 {len(json_files)} 个JSON标注文件")
    
    # 验证JSON文件格式
    valid_files = 0
    invalid_files = []
    
    for json_file in json_files[:5]:  # 检查前5个文件
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查必要字段
            if 'info' in data and 'labels' in data:
                valid_files += 1
            else:
                invalid_files.append(json_file.name)
                
        except Exception as e:
            invalid_files.append(f"{json_file.name}: {e}")
    
    if invalid_files:
        logger.warning(f"发现 {len(invalid_files)} 个无效JSON文件: {invalid_files}")
    
    logger.info(f"JSON格式验证: {valid_files}/{min(5, len(json_files))} 有效")
    
    # 检查图像目录（如果提供）
    if images_dir:
        if not images_dir.exists():
            logger.warning(f"图像目录不存在: {images_dir}")
            return False
        
        image_files = list(images_dir.glob('*.png')) + list(images_dir.glob('*.jpg'))
        logger.info(f"找到 {len(image_files)} 个图像文件")
    
    return True


def analyze_annotation_data(json_dir: Path):
    """分析标注数据统计信息"""
    logger = logging.getLogger(__name__)
    logger.info("开始分析标注数据...")
    
    parser = RailwayAnnotationParser()
    json_files = list(json_dir.glob('*.json'))
    
    stats = {
        'total_files': len(json_files),
        'total_tracks': 0,
        'main_tracks': 0,
        'fork_tracks': 0,
        'total_main_points': 0,
        'total_fork_points': 0,
        'camera_types': {'6mm': 0, '25mm': 0},
        'files_with_main': 0,
        'files_with_fork': 0
    }
    
    for json_file in tqdm(json_files, desc="分析标注"):
        try:
            annotation_data = parser.parse_json_file(json_file)
            
            # 统计相机类型
            if annotation_data['camera_type'] == '6mm':
                stats['camera_types']['6mm'] += 1
            else:
                stats['camera_types']['25mm'] += 1
            
            # 统计轨道
            has_main = False
            has_fork = False
            
            for track in annotation_data['tracks']:
                stats['total_tracks'] += 1
                
                if track['label'] in ['Main_Left', 'Main_Right']:
                    stats['main_tracks'] += 1
                    stats['total_main_points'] += len(track['points'])
                    has_main = True
                elif track['label'] in ['Fork_Left', 'Fork_Right']:
                    stats['fork_tracks'] += 1
                    stats['total_fork_points'] += len(track['points'])
                    has_fork = True
            
            if has_main:
                stats['files_with_main'] += 1
            if has_fork:
                stats['files_with_fork'] += 1
                
        except Exception as e:
            logger.error(f"分析 {json_file.name} 时出错: {e}")
    
    # 输出统计信息
    logger.info("标注数据统计:")
    logger.info(f"  总文件数: {stats['total_files']}")
    logger.info(f"  相机类型: 6mm={stats['camera_types']['6mm']}, 25mm={stats['camera_types']['25mm']}")
    logger.info(f"  总轨道数: {stats['total_tracks']}")
    logger.info(f"  主轨道: {stats['main_tracks']} 条, {stats['total_main_points']} 个关键点")
    logger.info(f"  分叉轨道: {stats['fork_tracks']} 条, {stats['total_fork_points']} 个关键点")
    logger.info(f"  有主轨道的文件: {stats['files_with_main']}")
    logger.info(f"  有分叉轨道的文件: {stats['files_with_fork']}")
    logger.info(f"  平均每文件主轨道关键点: {stats['total_main_points']/max(stats['files_with_main'],1):.1f}")
    
    return stats


def process_masks_with_full_polygon(json_dir: Path, images_dir: Path, output_dir: Path,
                                   camera_type: str = None, max_files: int = None):
    """使用完整多边形算法处理掩码"""
    logger = logging.getLogger(__name__)
    logger.info("开始使用完整多边形算法生成掩码...")
    
    # 创建输出目录
    masks_dir = output_dir / 'masks'
    images_output_dir = output_dir / 'images'
    masks_dir.mkdir(parents=True, exist_ok=True)
    images_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 使用修正后的预处理函数
    try:
        processed_count, failed_count = preprocess_json_annotations(
            json_dir=json_dir,
            output_dir=output_dir,
            download_images=False,  # 假设图像已存在
            camera_type=camera_type,
            max_files=max_files
        )
        
        logger.info(f"掩码生成统计: 成功={processed_count}, 失败={failed_count}")
        
        # 如果图像目录不同，复制图像
        if images_dir and images_dir != images_output_dir and processed_count > 0:
            logger.info("复制图像文件...")
            
            # 获取已生成掩码对应的图像
            mask_files = list(masks_dir.glob('*.png'))
            copied_count = 0
            
            for mask_file in tqdm(mask_files, desc="复制图像"):
                try:
                    image_filename = mask_file.name
                    src_path = images_dir / image_filename
                    dst_path = images_output_dir / image_filename
                    
                    if src_path.exists() and not dst_path.exists():
                        shutil.copy2(src_path, dst_path)
                        copied_count += 1
                        
                except Exception as e:
                    if max_files and max_files <= 10:  # 仅测试模式显示错误
                        logger.error(f"复制图像 {mask_file.name} 时出错: {e}")
            
            logger.info(f"复制了 {copied_count} 个图像文件")
        
        logger.info("掩码生成完成")
        return processed_count > 0
        
    except Exception as e:
        logger.error(f"掩码处理失败: {e}")
        return False


def create_data_splits(output_dir: Path, train_ratio: float = 0.8, 
                      val_ratio: float = 0.1, test_ratio: float = 0.1):
    """创建训练、验证、测试数据集划分"""
    logger = logging.getLogger(__name__)
    logger.info("创建数据集划分...")
    
    images_dir = output_dir / 'images'
    masks_dir = output_dir / 'masks'
    
    # 获取所有图像文件
    image_files = list(images_dir.glob('*.png'))
    if not image_files:
        logger.error("没有找到图像文件")
        return False
    
    # 随机打乱
    np.random.seed(42)
    np.random.shuffle(image_files)
    
    # 计算划分
    total = len(image_files)
    train_end = int(total * train_ratio)
    val_end = train_end + int(total * val_ratio)
    
    splits = {
        'train': image_files[:train_end],
        'val': image_files[train_end:val_end],
        'test': image_files[val_end:]
    }
    
    # 创建划分目录
    for split_name, files in splits.items():
        split_images_dir = output_dir / split_name / 'images'
        split_masks_dir = output_dir / split_name / 'masks'
        split_images_dir.mkdir(parents=True, exist_ok=True)
        split_masks_dir.mkdir(parents=True, exist_ok=True)
        
        for image_file in files:
            # 复制图像
            shutil.copy2(image_file, split_images_dir / image_file.name)
            
            # 复制对应的掩码
            mask_file = masks_dir / image_file.name
            if mask_file.exists():
                shutil.copy2(mask_file, split_masks_dir / image_file.name)
        
        logger.info(f"{split_name}: {len(files)} 个文件")
    
    # 保存划分信息
    split_info = {
        'total_files': total,
        'train': len(splits['train']),
        'val': len(splits['val']),
        'test': len(splits['test']),
        'ratios': {
            'train': train_ratio,
            'val': val_ratio,
            'test': test_ratio
        }
    }
    
    with open(output_dir / 'split_info.json', 'w') as f:
        json.dump(split_info, f, indent=2)
    
    return True


def verify_processed_data(output_dir: Path):
    """验证处理后的数据"""
    logger = logging.getLogger(__name__)
    logger.info("验证处理后的数据...")
    
    # 检查目录结构
    required_dirs = ['images', 'masks', 'train', 'val', 'test']
    for dir_name in required_dirs:
        dir_path = output_dir / dir_name
        if not dir_path.exists():
            logger.error(f"缺少目录: {dir_name}")
            return False
    
    # 验证掩码
    verification_results = {}
    
    for split in ['train', 'val', 'test']:
        masks_dir = output_dir / split / 'masks'
        mask_files = list(masks_dir.glob('*.png'))
        
        if not mask_files:
            logger.warning(f"{split} 集没有掩码文件")
            continue
        
        # 检查几个掩码的质量
        valid_masks = 0
        total_main_pixels = 0
        total_fork_pixels = 0
        
        for mask_file in mask_files[:5]:  # 检查前5个
            mask = cv2.imread(str(mask_file), cv2.IMREAD_GRAYSCALE)
            if mask is not None:
                unique_values = np.unique(mask)
                
                if len(unique_values) > 1:  # 有非背景像素
                    valid_masks += 1
                    total_main_pixels += np.sum(mask == 85)
                    total_fork_pixels += np.sum(mask == 170)
        
        verification_results[split] = {
            'total_files': len(mask_files),
            'valid_masks': valid_masks,
            'main_pixels': total_main_pixels,
            'fork_pixels': total_fork_pixels
        }
        
        logger.info(f"{split} 数据验证: {valid_masks}/{min(5, len(mask_files))} 有效掩码")
    
    return verification_results


def generate_pipeline_report(output_dir: Path, stats: dict, verification_results: dict):
    """生成管线处理报告"""
    logger = logging.getLogger(__name__)
    
    report_path = output_dir / 'pipeline_report.md'
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 铁路分割数据处理管线报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 数据处理配置\n\n")
        f.write("- **算法**: 完整多边形（严格连接每个关键点）\n")
        f.write("- **标签体系**: 0=背景, 1=主轨道, 2=分叉轨道\n")
        f.write("- **轨道合并**: Main_Left + Main_Right → 主轨道(标签1)\n")
        f.write("- **边界算法**: 不使用凸包，使用所有关键点\n\n")
        
        # 如果有统计数据，显示详细信息
        if stats and 'total_files' in stats:
            f.write("## 输入数据统计\n\n")
            f.write(f"- 总文件数: {stats['total_files']}\n")
            f.write(f"- 相机类型: 6mm={stats['camera_types']['6mm']}, 25mm={stats['camera_types']['25mm']}\n")
            f.write(f"- 总轨道数: {stats['total_tracks']}\n")
            f.write(f"- 主轨道: {stats['main_tracks']} 条，{stats['total_main_points']} 个关键点\n")
            f.write(f"- 分叉轨道: {stats['fork_tracks']} 条，{stats['total_fork_points']} 个关键点\n")
            f.write(f"- 关键点利用率: 100% (所有关键点都用于边界生成)\n\n")
        else:
            f.write("## 输入数据统计\n\n")
            f.write("- 数据分析步骤已跳过\n")
            f.write("- 关键点利用率: 100% (所有关键点都用于边界生成)\n\n")
        
        # 处理结果统计
        metadata_file = output_dir / 'metadata.json'
        if metadata_file.exists():
            with open(metadata_file, 'r') as mf:
                metadata = json.load(mf)
            f.write("## 处理结果\n\n")
            f.write(f"- 成功处理: {metadata.get('processed_files', 0)} 个文件\n")
            f.write(f"- 失败文件: {metadata.get('failed_files', 0)} 个文件\n")
            f.write(f"- 成功率: {metadata.get('processed_files', 0)/max(metadata.get('processed_files', 0)+metadata.get('failed_files', 0), 1)*100:.1f}%\n\n")
        
        # 数据集划分信息
        if verification_results:
            f.write("## 数据集划分\n\n")
            for split, result in verification_results.items():
                f.write(f"### {split.upper()} 集\n")
                f.write(f"- 文件数: {result['total_files']}\n")
                f.write(f"- 有效掩码: {result['valid_masks']}\n")
                f.write(f"- 主轨道像素: {result['main_pixels']}\n")
                f.write(f"- 分叉轨道像素: {result['fork_pixels']}\n\n")
        else:
            f.write("## 数据集划分\n\n")
            f.write("- 数据集划分步骤已跳过\n\n")
        
        f.write("## 技术特性\n\n")
        f.write("✅ **完整关键点利用**: 边界严格连接每一个标注关键点\n\n")
        f.write("✅ **标签分离**: 主轨道和分叉轨道使用不同标签\n\n")
        f.write("✅ **轨道合并**: 左右轨道合并为统一区域\n\n")
        f.write("✅ **精确边界**: 不使用凸包，避免忽略中间关键点\n\n")
        
        f.write("## 输出目录结构\n\n")
        f.write("```\n")
        f.write("output_dir/\n")
        f.write("├── images/           # 原始图像\n")
        f.write("├── masks/            # 生成的掩码\n")
        f.write("├── train/            # 训练集\n")
        f.write("│   ├── images/\n")
        f.write("│   └── masks/\n")
        f.write("├── val/              # 验证集\n")
        f.write("│   ├── images/\n")
        f.write("│   └── masks/\n")
        f.write("├── test/             # 测试集\n")
        f.write("│   ├── images/\n")
        f.write("│   └── masks/\n")
        f.write("├── metadata.json     # 数据元信息\n")
        f.write("├── split_info.json   # 数据划分信息\n")
        f.write("└── pipeline_report.md # 本报告\n")
        f.write("```\n\n")
        
    logger.info(f"管线报告保存到: {report_path}")


def main():
    parser = argparse.ArgumentParser(description='铁路数据处理管线')
    parser.add_argument('--json-dir', type=str, required=True,
                       help='JSON标注目录')
    parser.add_argument('--images-dir', type=str,
                       help='图像目录')
    parser.add_argument('--output-dir', type=str, required=True,
                       help='输出目录')
    parser.add_argument('--camera-type', type=str, choices=['6mm', '25mm'],
                       help='相机类型筛选')
    parser.add_argument('--max-files', type=int,
                       help='最大处理文件数（测试用）')
    parser.add_argument('--train-ratio', type=float, default=0.8,
                       help='训练集比例')
    parser.add_argument('--val-ratio', type=float, default=0.1,
                       help='验证集比例')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='测试集比例')
    parser.add_argument('--skip-analysis', action='store_true',
                       help='跳过数据分析步骤')
    parser.add_argument('--skip-splits', action='store_true',
                       help='跳过数据集划分')
    
    args = parser.parse_args()
    
    # 设置路径
    json_dir = Path(args.json_dir)
    images_dir = Path(args.images_dir) if args.images_dir else None
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logger = setup_logging(output_dir)
    
    logger.info("🚀 开始铁路数据处理管线")
    logger.info(f"算法: 完整多边形（严格连接每个关键点）")
    logger.info(f"JSON目录: {json_dir}")
    logger.info(f"图像目录: {images_dir}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        # 步骤1: 验证输入数据
        logger.info("📋 步骤1: 验证输入数据")
        validate_input_data(json_dir, images_dir)
        
        # 步骤2: 分析标注数据
        stats = {}
        if not args.skip_analysis:
            logger.info("📊 步骤2: 分析标注数据")
            stats = analyze_annotation_data(json_dir)
        
        # 步骤3: 生成掩码
        logger.info("🎨 步骤3: 生成完整多边形掩码")
        success = process_masks_with_full_polygon(
            json_dir, images_dir, output_dir,
            args.camera_type, args.max_files
        )
        
        if not success:
            logger.error("掩码生成失败")
            return
        
        # 步骤4: 创建数据集划分
        verification_results = {}
        if not args.skip_splits:
            logger.info("📂 步骤4: 创建数据集划分")
            create_data_splits(output_dir, args.train_ratio, args.val_ratio, args.test_ratio)
            
            # 步骤5: 验证处理后的数据
            logger.info("✅ 步骤5: 验证处理后的数据")
            verification_results = verify_processed_data(output_dir)
        
        # 步骤6: 生成报告
        logger.info("📄 步骤6: 生成处理报告")
        generate_pipeline_report(output_dir, stats, verification_results)
        
        logger.info("🎉 数据处理管线完成！")
        logger.info(f"输出目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"管线执行失败: {e}")
        raise


if __name__ == '__main__':
    main() 