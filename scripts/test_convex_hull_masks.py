#!/usr/bin/env python
"""
测试凸包掩码生成脚本
验证修正后的数据预处理脚本是否正确生成凸多边形分割掩码
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def visualize_convex_hull_generation(json_file: Path, output_dir: Path):
    """可视化凸包生成过程"""
    print(f"\n=== 测试凸包掩码生成 ===")
    print(f"JSON文件: {json_file.name}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  相机类型: {annotation_data['camera_type']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 设置图像尺寸
    image_shape = (1080, 1920)
    
    # 创建可视化
    num_tracks = len(annotation_data['tracks'])
    fig, axes = plt.subplots(2, num_tracks + 1, figsize=(4 * (num_tracks + 1), 8))
    
    if num_tracks == 1:
        axes = axes.reshape(2, -1)
    
    all_points = []
    track_colors = ['red', 'blue', 'green', 'purple']
    
    for i, track in enumerate(annotation_data['tracks']):
        print(f"\n轨道 {i+1}: {track['label']}")
        print(f"  类别ID: {track['class_id']}")
        print(f"  关键点数量: {len(track['points'])}")
        
        # 转换点格式
        points = np.array([(int(x), int(y)) for x, y in track['points']], dtype=np.int32)
        all_points.extend(points)
        
        # 计算凸包
        hull = cv2.convexHull(points)
        print(f"  凸包顶点数量: {len(hull)}")
        
        # 创建空白图像用于可视化
        vis_img = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        
        # 绘制原始关键点
        for point in points:
            cv2.circle(vis_img, tuple(point), 8, (0, 255, 0), -1)  # 绿色圆点
        
        # 绘制凸包边界
        cv2.polylines(vis_img, [hull], True, (255, 255, 0), 3)  # 黄色边界
        
        # 填充凸包
        filled_img = vis_img.copy()
        cv2.fillPoly(filled_img, [hull], (255, 0, 0))  # 红色填充
        
        # 显示关键点和凸包边界
        axes[0, i].imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
        axes[0, i].set_title(f'{track["label"]}\n关键点: {len(points)}, 凸包: {len(hull)}')
        axes[0, i].axis('off')
        
        # 显示填充的凸包
        axes[1, i].imshow(cv2.cvtColor(filled_img, cv2.COLOR_BGR2RGB))
        axes[1, i].set_title(f'填充的凸包')
        axes[1, i].axis('off')
    
    # 生成完整的分割掩码
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    # 转换为单通道可视化
    merged_mask = np.argmax(mask, axis=2)
    
    # 自定义颜色映射
    colors = ['black', 'green', 'red']
    cmap = ListedColormap(colors[:len(np.unique(merged_mask))])
    
    # 显示最终掩码
    axes[0, -1].imshow(merged_mask, cmap=cmap, vmin=0, vmax=2)
    axes[0, -1].set_title('最终分割掩码\n(绿色=主轨道, 红色=分叉轨道)')
    axes[0, -1].axis('off')
    
    # 统计信息
    unique_vals, counts = np.unique(merged_mask, return_counts=True)
    stats_text = "\n".join([f"类别{val}: {count}像素" for val, count in zip(unique_vals, counts)])
    axes[1, -1].text(0.1, 0.5, stats_text, transform=axes[1, -1].transAxes, 
                     fontsize=12, verticalalignment='center')
    axes[1, -1].set_title('像素统计')
    axes[1, -1].axis('off')
    
    plt.suptitle(f'凸包掩码生成测试: {json_file.name}', fontsize=16)
    plt.tight_layout()
    
    # 保存结果
    output_path = output_dir / f'convex_hull_test_{json_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果保存到: {output_path}")
    
    return merged_mask, annotation_data


def compare_old_vs_new_method(json_file: Path, output_dir: Path):
    """对比旧方法（线条）vs 新方法（凸包）"""
    print(f"\n=== 对比线条vs凸包方法 ===")
    
    # 旧方法：模拟线条绘制
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    image_shape = (1080, 1920)
    
    # 新方法：凸包
    parser = RailwayAnnotationParser()
    annotation_data = parser.parse_json_file(json_file)
    new_mask = parser.create_segmentation_mask(annotation_data, image_shape)
    new_merged = np.argmax(new_mask, axis=2)
    
    # 模拟旧方法：线条绘制
    old_mask = np.zeros(image_shape, dtype=np.uint8)
    for label_data in data['labels']:
        if label_data['label'] in ['Main_Left', 'Main_Right']:
            points_list = label_data['points']
            points = [(int(points_list[i]), int(points_list[i+1])) 
                     for i in range(0, len(points_list), 2)]
            
            # 绘制线条（模拟旧方法）
            for i in range(len(points) - 1):
                cv2.line(old_mask, points[i], points[i+1], 1, thickness=30)
    
    # 创建对比可视化
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 旧方法（线条）
    axes[0].imshow(old_mask, cmap='gray')
    axes[0].set_title(f'旧方法（线条）\n非零像素: {np.count_nonzero(old_mask)}')
    axes[0].axis('off')
    
    # 新方法（凸包）
    colors = ['black', 'green', 'red']
    cmap = ListedColormap(colors)
    axes[1].imshow(new_merged, cmap=cmap, vmin=0, vmax=2)
    axes[1].set_title(f'新方法（凸包）\n非零像素: {np.count_nonzero(new_merged)}')
    axes[1].axis('off')
    
    # 差异
    # 将新掩码转换为二值来计算差异
    new_binary = (new_merged > 0).astype(np.uint8)
    diff = np.abs(old_mask.astype(int) - new_binary.astype(int))
    axes[2].imshow(diff, cmap='hot')
    axes[2].set_title(f'差异\n不同像素: {np.count_nonzero(diff)}')
    axes[2].axis('off')
    
    plt.suptitle('方法对比：线条绘制 vs 凸包填充', fontsize=16)
    plt.tight_layout()
    
    # 保存对比结果
    comparison_path = output_dir / f'method_comparison_{json_file.stem}.png'
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"对比结果保存到: {comparison_path}")
    
    # 输出统计信息
    print(f"\n统计对比:")
    print(f"  旧方法非零像素: {np.count_nonzero(old_mask)}")
    print(f"  新方法非零像素: {np.count_nonzero(new_merged)}")
    print(f"  面积增加: {np.count_nonzero(new_merged) - np.count_nonzero(old_mask)}")
    print(f"  面积增加率: {(np.count_nonzero(new_merged) / max(np.count_nonzero(old_mask), 1) - 1) * 100:.1f}%")


def batch_test_convex_hulls(json_dir: Path, output_dir: Path, max_files: int = 5):
    """批量测试凸包生成"""
    print(f"\n=== 批量测试凸包生成 ===")
    
    json_files = list(json_dir.glob('*.json'))[:max_files]
    parser = RailwayAnnotationParser()
    
    results = []
    
    for json_file in json_files:
        try:
            annotation_data = parser.parse_json_file(json_file)
            
            # 统计信息
            total_points = sum(len(track['points']) for track in annotation_data['tracks'])
            track_info = [f"{track['label']}({len(track['points'])}点)" 
                         for track in annotation_data['tracks']]
            
            results.append({
                'filename': json_file.name,
                'num_tracks': len(annotation_data['tracks']),
                'total_points': total_points,
                'track_info': ', '.join(track_info)
            })
            
            print(f"{json_file.name}: {len(annotation_data['tracks'])}条轨道, {total_points}个点")
            
        except Exception as e:
            print(f"处理 {json_file.name} 时出错: {e}")
    
    # 保存测试报告
    report_path = output_dir / 'convex_hull_test_report.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("凸包掩码生成测试报告\n")
        f.write("=" * 50 + "\n\n")
        
        for result in results:
            f.write(f"文件: {result['filename']}\n")
            f.write(f"  轨道数量: {result['num_tracks']}\n")
            f.write(f"  总关键点数: {result['total_points']}\n")
            f.write(f"  轨道详情: {result['track_info']}\n\n")
    
    print(f"\n测试报告保存到: {report_path}")


def main():
    parser = argparse.ArgumentParser(description='测试凸包掩码生成')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='JSON标注目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/convex_hull_test',
                       help='输出目录')
    parser.add_argument('--test-single', action='store_true',
                       help='只测试单个文件')
    parser.add_argument('--max-files', type=int, default=5,
                       help='最大测试文件数')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    json_dir = Path(args.json_dir)
    
    print("凸包掩码生成测试开始...")
    print(f"JSON目录: {json_dir}")
    print(f"输出目录: {output_dir}")
    
    json_files = list(json_dir.glob('*.json'))
    if len(json_files) == 0:
        print("错误：没有找到JSON文件")
        return
    
    if args.test_single:
        # 测试单个文件
        json_file = json_files[0]
        visualize_convex_hull_generation(json_file, output_dir)
        compare_old_vs_new_method(json_file, output_dir)
    else:
        # 批量测试
        batch_test_convex_hulls(json_dir, output_dir, args.max_files)
    
    print(f"\n测试完成！结果保存在: {output_dir}")


if __name__ == '__main__':
    main() 