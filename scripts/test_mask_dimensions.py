#!/usr/bin/env python
"""
测试掩码维度修复
验证从JSON和processed源加载的掩码维度一致性
"""

import sys
from pathlib import Path
import numpy as np
import torch

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset
from src.data.transforms import get_train_transforms


def test_mask_dimensions():
    """测试掩码维度一致性"""
    print("=== 测试掩码维度修复 ===")
    
    # 创建数据集实例
    config = {
        'image_size': {'height': 544, 'width': 960},
        'data': {'dynamic_split': True, 'split_ratios': {'train': 0.7, 'val': 0.15, 'test': 0.15}}
    }
    
    # 创建transforms - 使用正确的参数格式
    transform = get_train_transforms(config['image_size'])
    
    try:
        dataset = RailwayTrackDataset(
            data_root='data',
            split='train',
            json_dir='data/railway_annotation_6mm',
            config=config,
            transform=transform
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        if len(dataset) == 0:
            print("❌ 数据集为空，无法测试")
            return False
        
        # 测试前5个样本的掩码维度
        success_count = 0
        total_tests = min(5, len(dataset))
        
        for i in range(total_tests):
            try:
                sample = dataset[i]
                
                # 检查掩码维度
                if 'mask' in sample:
                    mask = sample['mask']
                    print(f"样本 {i}:")
                    print(f"  文件名: {sample['filename']}")
                    print(f"  掩码类型: {type(mask)}")
                    print(f"  掩码形状: {mask.shape}")
                    
                    # 验证维度
                    if isinstance(mask, torch.Tensor):
                        expected_dims = 3  # (C, H, W) 格式
                        if len(mask.shape) == expected_dims:
                            print(f"  ✅ 维度正确: {mask.shape}")
                            # 验证通道数
                            if mask.shape[0] == 3:  # 3个类别
                                print(f"  ✅ 通道数正确: {mask.shape[0]}")
                                success_count += 1
                            else:
                                print(f"  ❌ 通道数错误: 期望3，实际{mask.shape[0]}")
                        else:
                            print(f"  ❌ 维度错误: 期望3维，实际{len(mask.shape)}维")
                    else:
                        print(f"  ❌ 掩码类型错误: 期望Tensor，实际{type(mask)}")
                else:
                    print(f"样本 {i}: ❌ 没有掩码")
                    
                print()
                
            except Exception as e:
                print(f"样本 {i}: ❌ 加载失败: {e}")
                print()
        
        print(f"测试结果: {success_count}/{total_tests} 样本通过")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！掩码维度修复成功")
            return True
        else:
            print("⚠️ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return False


def test_data_loading_stability():
    """测试数据加载稳定性"""
    print("\n=== 测试数据加载稳定性 ===")
    
    config = {
        'image_size': {'height': 544, 'width': 960},
        'data': {'dynamic_split': True, 'split_ratios': {'train': 0.7, 'val': 0.15, 'test': 0.15}}
    }
    
    # 创建transforms - 使用正确的参数格式
    transform = get_train_transforms(config['image_size'])
    
    try:
        dataset = RailwayTrackDataset(
            data_root='data',
            split='train',
            json_dir='data/railway_annotation_6mm',
            config=config,
            transform=transform
        )
        
        if len(dataset) == 0:
            print("❌ 数据集为空")
            return False
        
        # 创建数据加载器
        from torch.utils.data import DataLoader
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=0)
        
        # 测试加载几个批次
        batch_count = 0
        success_count = 0
        
        for batch in dataloader:
            batch_count += 1
            
            try:
                images = batch['image']
                masks = batch['mask']
                
                print(f"批次 {batch_count}:")
                print(f"  图像形状: {images.shape}")
                print(f"  掩码形状: {masks.shape}")
                
                # 验证维度
                if len(images.shape) == 4 and len(masks.shape) == 4:
                    if images.shape[1] == 3 and masks.shape[1] == 3:  # 检查通道数
                        print(f"  ✅ 批次加载成功，维度正确")
                        success_count += 1
                    else:
                        print(f"  ❌ 通道数错误: 图像{images.shape[1]}, 掩码{masks.shape[1]}")
                else:
                    print(f"  ❌ 维度错误")
                
                if batch_count >= 3:  # 只测试前3个批次
                    break
                    
            except Exception as e:
                print(f"批次 {batch_count}: ❌ 加载失败: {e}")
                break
        
        print(f"\n批次加载结果: {success_count}/{batch_count} 成功")
        
        if success_count > 0:
            print("🎉 数据加载稳定性测试通过！")
            return True
        else:
            print("❌ 数据加载不稳定")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试掩码维度修复...")
    
    # 测试掩码维度
    test1_success = test_mask_dimensions()
    
    # 测试数据加载稳定性
    test2_success = test_data_loading_stability()
    
    print("\n" + "="*50)
    print("总测试结果:")
    print(f"掩码维度测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"数据加载测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("🎉 所有测试通过！Bug修复成功")
    else:
        print("⚠️ 仍有问题需要解决") 