#!/usr/bin/env python3
"""
Test script to verify multilabel mask loading functionality
"""

import sys
import yaml
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset


def test_mask_loading():
    """Test that the dataset correctly loads .npy mask files when use_multilabel is enabled"""
    
    # Load config
    config_path = project_root / 'configs' / 'railway_track_config.yaml'
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Create dataset instance
    dataset = RailwayTrackDataset(
        data_root=config['data']['processed_data_path'],
        split='train',
        config=config
    )
    
    # Print configuration
    use_multilabel = config['data'].get('use_multilabel', False)
    print(f"use_multilabel setting: {use_multilabel}")
    
    # Load data and check mask paths
    print(f"\nDataset contains {len(dataset.data)} samples")
    
    # Check first few samples
    for i, item in enumerate(dataset.data[:5]):
        if 'mask_path' in item:
            mask_path = item['mask_path']
            print(f"\nSample {i}:")
            print(f"  Image: {item['filename']}")
            print(f"  Mask path: {mask_path}")
            print(f"  Mask extension: {mask_path.suffix}")
            print(f"  Mask exists: {mask_path.exists()}")
            
            # Verify extension matches use_multilabel setting
            expected_ext = '.npy' if use_multilabel else '.png'
            if mask_path.suffix == expected_ext:
                print(f"  ✓ Correct extension for use_multilabel={use_multilabel}")
            else:
                print(f"  ✗ Wrong extension! Expected {expected_ext}, got {mask_path.suffix}")
    
    # Try loading a sample
    try:
        sample = dataset[0]
        print(f"\nSuccessfully loaded first sample:")
        print(f"  Image shape: {sample['image'].shape}")
        if 'mask' in sample:
            print(f"  Mask shape: {sample['mask'].shape}")
            print(f"  Mask dtype: {sample['mask'].dtype}")
    except Exception as e:
        print(f"\nError loading sample: {e}")


if __name__ == "__main__":
    test_mask_loading()