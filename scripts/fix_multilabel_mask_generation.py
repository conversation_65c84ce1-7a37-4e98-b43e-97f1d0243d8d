#!/usr/bin/env python3
"""
修复掩码生成以支持真正的多标签训练
在轨道分叉处，像素可以同时属于主轨道和分叉轨道
"""

import numpy as np
import cv2
from pathlib import Path


def create_multilabel_segmentation_mask(annotation_data, image_shape):
    """
    创建多标签分割掩码
    
    Args:
        annotation_data: 标注数据
        image_shape: 图像尺寸 (H, W)
        
    Returns:
        多标签掩码 (H, W, 3) - 每个通道是独立的二值掩码
    """
    height, width = image_shape
    
    # 创建多通道掩码，每个通道独立
    mask = np.zeros((height, width, 3), dtype=np.float32)
    
    # 通道0: 背景（将在最后设置）
    # 通道1: 主轨道
    # 通道2: 分叉轨道
    
    # 处理主轨道
    main_tracks = []
    for track_data in annotation_data.get('tracks', []):
        label = track_data.get('label', '')
        if label in ['Main_Left', 'Main_Right']:
            main_tracks.append(track_data)
    
    # 处理分叉轨道
    fork_tracks = []
    for track_data in annotation_data.get('tracks', []):
        label = track_data.get('label', '')
        if label in ['Fork_Left', 'Fork_Right']:
            fork_tracks.append(track_data)
    
    # 生成主轨道掩码
    for track in main_tracks:
        track_mask = create_track_mask(track, height, width)
        # 使用OR操作，不覆盖已有的标签
        mask[:, :, 1] = np.logical_or(mask[:, :, 1], track_mask).astype(np.float32)
    
    # 生成分叉轨道掩码
    for track in fork_tracks:
        track_mask = create_track_mask(track, height, width)
        # 使用OR操作，允许与主轨道重叠
        mask[:, :, 2] = np.logical_or(mask[:, :, 2], track_mask).astype(np.float32)
    
    # 设置背景（既不是主轨道也不是分叉轨道的区域）
    # 注意：在多标签中，背景是独立的类别
    track_area = np.logical_or(mask[:, :, 1], mask[:, :, 2])
    mask[:, :, 0] = (~track_area).astype(np.float32)
    
    return mask


def create_track_mask(track_data, height, width):
    """
    为单个轨道创建掩码
    
    Args:
        track_data: 轨道数据
        height: 图像高度
        width: 图像宽度
        
    Returns:
        二值掩码
    """
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # 获取多边形点
    polygons = track_data.get('polygons', [])
    
    for polygon in polygons:
        points = polygon.get('points', [])
        if len(points) >= 3:
            # 转换点格式
            pts = np.array([[p['x'], p['y']] for p in points], dtype=np.int32)
            # 填充多边形
            cv2.fillPoly(mask, [pts], 1)
    
    return mask


def compare_mask_generation(annotation_data, image_shape):
    """
    比较单标签和多标签掩码生成的差异
    
    Args:
        annotation_data: 标注数据
        image_shape: 图像尺寸
        
    Returns:
        比较结果
    """
    # 生成多标签掩码
    multilabel_mask = create_multilabel_segmentation_mask(annotation_data, image_shape)
    
    # 计算重叠区域
    main_track_mask = multilabel_mask[:, :, 1]
    fork_track_mask = multilabel_mask[:, :, 2]
    
    # 找出同时属于主轨道和分叉轨道的像素
    overlap_mask = np.logical_and(main_track_mask > 0, fork_track_mask > 0)
    overlap_pixels = np.sum(overlap_mask)
    
    # 统计信息
    stats = {
        'total_pixels': image_shape[0] * image_shape[1],
        'background_pixels': np.sum(multilabel_mask[:, :, 0] > 0),
        'main_track_pixels': np.sum(main_track_mask > 0),
        'fork_track_pixels': np.sum(fork_track_mask > 0),
        'overlap_pixels': overlap_pixels,
        'overlap_percentage': (overlap_pixels / (image_shape[0] * image_shape[1])) * 100
    }
    
    return multilabel_mask, stats


def visualize_multilabel_mask(mask, save_path=None):
    """
    可视化多标签掩码
    
    Args:
        mask: 多标签掩码 (H, W, 3)
        save_path: 保存路径
    """
    import matplotlib.pyplot as plt
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 显示每个通道
    channel_names = ['背景', '主轨道', '分叉轨道']
    for i in range(3):
        axes[0, i].imshow(mask[:, :, i], cmap='gray', vmin=0, vmax=1)
        axes[0, i].set_title(f'{channel_names[i]}通道')
        axes[0, i].axis('off')
    
    # 创建彩色合成图
    h, w = mask.shape[:2]
    colored = np.zeros((h, w, 3), dtype=np.uint8)
    
    # 主轨道 - 红色
    colored[mask[:, :, 1] > 0] = [255, 0, 0]
    # 分叉轨道 - 绿色（可能覆盖红色）
    colored[mask[:, :, 2] > 0] = [0, 255, 0]
    # 重叠区域 - 黄色
    overlap = np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
    colored[overlap] = [255, 255, 0]
    
    axes[1, 0].imshow(colored)
    axes[1, 0].set_title('彩色合成（黄色=重叠）')
    axes[1, 0].axis('off')
    
    # 显示重叠区域
    axes[1, 1].imshow(overlap, cmap='hot', vmin=0, vmax=1)
    axes[1, 1].set_title('重叠区域')
    axes[1, 1].axis('off')
    
    # 隐藏最后一个子图
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


def main():
    """
    演示多标签掩码生成
    """
    print("=== 多标签掩码生成演示 ===\n")
    
    # 模拟标注数据
    annotation_data = {
        'tracks': [
            {
                'label': 'Main_Left',
                'polygons': [{
                    'points': [
                        {'x': 100, 'y': 100},
                        {'x': 200, 'y': 100},
                        {'x': 200, 'y': 300},
                        {'x': 100, 'y': 300}
                    ]
                }]
            },
            {
                'label': 'Fork_Left',
                'polygons': [{
                    'points': [
                        {'x': 150, 'y': 200},
                        {'x': 250, 'y': 200},
                        {'x': 250, 'y': 400},
                        {'x': 150, 'y': 400}
                    ]
                }]
            }
        ]
    }
    
    # 生成掩码
    image_shape = (500, 500)
    mask, stats = compare_mask_generation(annotation_data, image_shape)
    
    # 打印统计信息
    print("掩码统计信息:")
    print(f"  总像素数: {stats['total_pixels']:,}")
    print(f"  背景像素: {stats['background_pixels']:,}")
    print(f"  主轨道像素: {stats['main_track_pixels']:,}")
    print(f"  分叉轨道像素: {stats['fork_track_pixels']:,}")
    print(f"  重叠像素: {stats['overlap_pixels']:,}")
    print(f"  重叠比例: {stats['overlap_percentage']:.2f}%")
    
    # 可视化
    visualize_multilabel_mask(mask)
    
    print("\n✓ 多标签掩码生成完成")
    print("在轨道分叉处，像素可以同时属于主轨道和分叉轨道")


if __name__ == '__main__':
    main()