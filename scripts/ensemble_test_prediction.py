#!/usr/bin/env python3
"""
集成学习测试集预测和可视化脚本
对测试集进行批量预测并生成全面的可视化分析
"""

import sys
import os
import torch
import torch.nn as nn
import numpy as np
import cv2
import yaml
import json
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse
from tqdm import tqdm
import segmentation_models_pytorch as smp
import albumentations as A
from albumentations.pytorch import ToTensorV2
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.calibration import calibration_curve
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.ensemble import EnsemblePredictor
from src.data.dataloader import create_dataloaders
from src.utils.metrics import iou_coef, dice_coef


class EnsembleTestPredictor:
    """集成学习测试集预测器"""
    
    def __init__(self, 
                 weights_dir: str,
                 config_path: str,
                 output_dir: str,
                 device: Optional[str] = None):
        """
        初始化测试预测器
        
        Args:
            weights_dir: 训练好的权重目录
            config_path: 配置文件路径
            output_dir: 输出目录
            device: 设备（'cuda' 或 'cpu'）
        """
        self.weights_dir = Path(weights_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"🔧 使用设备: {self.device}")
        
        # 类别配置
        self.num_classes = self.config.get('model', {}).get('classes', 3)
        self.class_names = ['背景', '主轨道', '分叉轨道']
        
        # 颜色映射
        self.colors = {
            0: [30, 30, 30],        # 背景 - 深灰色
            1: [255, 140, 0],       # 主轨道 - 橙色
            2: [0, 191, 255],       # 分叉轨道 - 天蓝色
            'overlap': [255, 20, 147]  # 重叠 - 深粉色
        }
        
        # 加载集成模型
        self.ensemble_predictor = self._load_ensemble_models()
        
        # 初始化指标存储
        self.test_results = {
            'predictions': [],
            'ground_truths': [],
            'filenames': [],
            'images': [],
            'metrics': {
                'per_sample': [],
                'per_class': {'iou': [], 'dice': [], 'accuracy': []},
                'overall': {}
            }
        }
    
    def _load_ensemble_models(self) -> EnsemblePredictor:
        """加载集成模型"""
        print("📦 加载集成模型...")
        
        # 模型配置
        model_configs = [
            {
                'name': 'efficientnet_b4',
                'architecture': 'pan',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': None,
            },
            {
                'name': 'eca_nfnet_l2',
                'architecture': 'pan',
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': None,
            },
            {
                'name': 'seresnet152d',
                'architecture': 'pan',
                'encoder': 'tu-seresnet152d',
                'encoder_weights': None,
            }
        ]
        
        # 加载模型
        models = []
        model_names = []
        
        for model_config in model_configs:
            model_name = model_config['name']
            weight_path = self.weights_dir / f"{model_name}.pth"
            
            if not weight_path.exists():
                print(f"⚠️  权重文件不存在: {weight_path}")
                continue
            
            # 创建模型
            model = self._create_single_model(model_config)
            
            # 加载权重
            try:
                checkpoint = torch.load(weight_path, map_location=self.device)
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                else:
                    model.load_state_dict(checkpoint)
                
                model.to(self.device)
                model.eval()
                models.append(model)
                model_names.append(model_name)
                print(f"✅ 加载模型: {model_name}")
                
            except Exception as e:
                print(f"❌ 加载模型 {model_name} 失败: {e}")
        
        if not models:
            raise ValueError("没有成功加载任何模型！")
        
        # 加载集成权重
        weights_path = self.weights_dir / 'ensemble_weights.yaml'
        if weights_path.exists():
            try:
                with open(weights_path, 'r', encoding='utf-8') as f:
                    weights_config = yaml.safe_load(f)
                
                # 构建权重矩阵
                fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
                
                # 处理不同格式的权重配置
                for class_idx, info in weights_config.items():
                    if isinstance(class_idx, int) and class_idx < self.num_classes:
                        if isinstance(info, dict) and 'weights' in info:
                            # 标准格式：{class_idx: {'weights': [...], 'iou': ...}}
                            weights = info['weights']
                        elif isinstance(info, (list, tuple)):
                            # 简化格式：{class_idx: [...]}
                            weights = info
                        else:
                            print(f"⚠️  类别 {class_idx} 权重格式不正确，跳过")
                            continue
                        
                        # 转换为tensor并确保长度匹配
                        if len(weights) >= len(models):
                            fusion_weights[class_idx] = torch.tensor(weights[:len(models)], dtype=torch.float32)
                        else:
                            print(f"⚠️  类别 {class_idx} 权重数量不足，使用等权重")
                
                print(f"✅ 加载集成权重: {weights_path}")
                
            except yaml.constructor.ConstructorError as e:
                print(f"⚠️  YAML文件包含numpy对象，尝试其他方法加载: {e}")
                
                # 尝试使用numpy加载
                try:
                    import numpy as np
                    # 允许加载numpy对象的unsafe方法
                    with open(weights_path, 'r', encoding='utf-8') as f:
                        weights_config = yaml.load(f, Loader=yaml.UnsafeLoader)
                    
                    fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
                    
                    for class_idx, info in weights_config.items():
                        if isinstance(class_idx, int) and class_idx < self.num_classes:
                            if isinstance(info, dict) and 'weights' in info:
                                weights = info['weights']
                            elif isinstance(info, (list, tuple, np.ndarray)):
                                weights = info
                            else:
                                continue
                            
                            # 转换numpy数组为list
                            if isinstance(weights, np.ndarray):
                                weights = weights.tolist()
                            
                            if len(weights) >= len(models):
                                fusion_weights[class_idx] = torch.tensor(weights[:len(models)], dtype=torch.float32)
                    
                    print(f"✅ 使用UnsafeLoader加载集成权重: {weights_path}")
                    
                except Exception as e2:
                    print(f"⚠️  加载集成权重失败: {e2}")
                    print("使用等权重继续")
                    fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
                    
            except Exception as e:
                print(f"⚠️  加载集成权重失败: {e}")
                print("使用等权重继续")
                fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
        else:
            # 使用等权重
            fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
            print("⚠️  未找到集成权重文件，使用等权重")
        
        # 创建集成预测器
        ensemble_predictor = EnsemblePredictor(models, fusion_weights, self.device)
        
        print(f"🎯 集成模型加载完成，包含 {len(models)} 个子模型")
        return ensemble_predictor
    
    def _create_single_model(self, config: Dict[str, Any]) -> nn.Module:
        """创建单个分割模型"""
        architecture = config.get('architecture', 'pan')
        encoder_name = config.get('encoder', 'efficientnet-b4')
        encoder_weights = config.get('encoder_weights', None)
        
        params = {
            'encoder_name': encoder_name,
            'encoder_weights': encoder_weights,
            'in_channels': 3,
            'classes': self.num_classes,
            'activation': None
        }
        
        if architecture.lower() == 'pan':
            model = smp.PAN(**params)
        elif architecture.lower() == 'unet':
            model = smp.Unet(**params)
        elif architecture.lower() == 'fpn':
            model = smp.FPN(**params)
        elif architecture.lower() == 'deeplabv3plus':
            model = smp.DeepLabV3Plus(**params)
        else:
            raise ValueError(f"不支持的架构: {architecture}")
        
        return model
    
    def predict_test_set(self, data_dir: str, threshold: float = 0.5):
        """对测试集进行预测"""
        print("🚀 开始测试集预测...")
        
        # 创建数据加载器
        test_config = self.config.copy()
        test_config['data']['data_path'] = data_dir
        test_config['data']['batch_size'] = {'test': 1}  # 单张预测便于分析
        
        dataloaders = create_dataloaders(test_config)
        test_loader = dataloaders['test']
        
        if len(test_loader) == 0:
            print("❌ 测试集为空！")
            return
        
        print(f"📊 测试集样本数: {len(test_loader)}")
        
        # 开始预测
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="预测进度")):
                try:
                    images = batch['image'].to(self.device)
                    masks = batch['mask'].to(self.device) if 'mask' in batch else None
                    filenames = batch.get('filename', [f'sample_{batch_idx}'])
                    
                    # 集成预测
                    predictions = self.ensemble_predictor.predict(images)
                    
                    # 转换为numpy
                    images_np = images.cpu().numpy()
                    predictions_np = predictions.cpu().numpy()
                    masks_np = masks.cpu().numpy() if masks is not None else None
                    
                    # 存储结果
                    for i in range(images.size(0)):
                        self.test_results['images'].append(images_np[i])
                        self.test_results['predictions'].append(predictions_np[i])
                        if masks_np is not None:
                            self.test_results['ground_truths'].append(masks_np[i])
                        self.test_results['filenames'].append(filenames[i] if isinstance(filenames, list) else filenames)
                        
                        # 计算单样本指标
                        if masks_np is not None:
                            sample_metrics = self._calculate_sample_metrics(
                                predictions_np[i], masks_np[i], threshold
                            )
                            self.test_results['metrics']['per_sample'].append(sample_metrics)
                    
                except Exception as e:
                    print(f"❌ 预测批次 {batch_idx} 时出错: {e}")
                    continue
        
        print(f"✅ 预测完成，共处理 {len(self.test_results['predictions'])} 个样本")
        
        # 计算整体指标
        if self.test_results['ground_truths']:
            self._calculate_overall_metrics(threshold)
    
    def _calculate_sample_metrics(self, prediction: np.ndarray, ground_truth: np.ndarray, 
                                threshold: float) -> Dict[str, float]:
        """计算单样本指标"""
        metrics = {}
        
        for class_idx in range(self.num_classes):
            pred_class = (prediction[class_idx] > threshold).astype(float)
            gt_class = ground_truth[class_idx]
            
            # IoU
            intersection = np.sum(pred_class * gt_class)
            union = np.sum(np.logical_or(pred_class, gt_class))
            iou = intersection / (union + 1e-8)
            
            # Dice
            dice = 2 * intersection / (np.sum(pred_class) + np.sum(gt_class) + 1e-8)
            
            # Accuracy
            accuracy = np.sum(pred_class == gt_class) / gt_class.size
            
            metrics[f'class_{class_idx}_iou'] = iou
            metrics[f'class_{class_idx}_dice'] = dice
            metrics[f'class_{class_idx}_accuracy'] = accuracy
        
        return metrics
    
    def _calculate_overall_metrics(self, threshold: float):
        """计算整体指标"""
        print("📈 计算整体指标...")
        
        all_predictions = np.array(self.test_results['predictions'])
        all_ground_truths = np.array(self.test_results['ground_truths'])
        
        # 按类别计算指标
        for class_idx in range(self.num_classes):
            pred_class = (all_predictions[:, class_idx] > threshold).astype(float)
            gt_class = all_ground_truths[:, class_idx]
            
            # 计算IoU和Dice
            ious = []
            dices = []
            accuracies = []
            
            for i in range(len(pred_class)):
                intersection = np.sum(pred_class[i] * gt_class[i])
                union = np.sum(np.logical_or(pred_class[i], gt_class[i]))
                iou = intersection / (union + 1e-8)
                
                dice = 2 * intersection / (np.sum(pred_class[i]) + np.sum(gt_class[i]) + 1e-8)
                accuracy = np.sum(pred_class[i] == gt_class[i]) / gt_class[i].size
                
                ious.append(iou)
                dices.append(dice)
                accuracies.append(accuracy)
            
            self.test_results['metrics']['per_class']['iou'].append(ious)
            self.test_results['metrics']['per_class']['dice'].append(dices)
            self.test_results['metrics']['per_class']['accuracy'].append(accuracies)
        
        # 整体平均指标
        overall_metrics = {}
        for class_idx in range(self.num_classes):
            ious = self.test_results['metrics']['per_class']['iou'][class_idx]
            dices = self.test_results['metrics']['per_class']['dice'][class_idx]
            accuracies = self.test_results['metrics']['per_class']['accuracy'][class_idx]
            
            overall_metrics[f'{self.class_names[class_idx]}_IoU'] = np.mean(ious)
            overall_metrics[f'{self.class_names[class_idx]}_Dice'] = np.mean(dices)
            overall_metrics[f'{self.class_names[class_idx]}_Accuracy'] = np.mean(accuracies)
        
        # 平均指标
        overall_metrics['Mean_IoU'] = np.mean([
            overall_metrics[f'{name}_IoU'] for name in self.class_names
        ])
        overall_metrics['Mean_Dice'] = np.mean([
            overall_metrics[f'{name}_Dice'] for name in self.class_names
        ])
        overall_metrics['Mean_Accuracy'] = np.mean([
            overall_metrics[f'{name}_Accuracy'] for name in self.class_names
        ])
        
        self.test_results['metrics']['overall'] = overall_metrics
        
        print("📊 整体指标:")
        for metric, value in overall_metrics.items():
            print(f"  {metric}: {value:.4f}")
    
    def generate_visualizations(self, threshold: float = 0.5, num_samples: int = 8):
        """生成全面的可视化结果"""
        print("🎨 生成可视化结果...")
        
        vis_dir = self.output_dir / 'visualizations'
        vis_dir.mkdir(exist_ok=True)
        
        # 1. 基础预测对比
        self._visualize_basic_predictions(vis_dir, threshold, num_samples)
        
        # 2. 性能指标分布
        if self.test_results['ground_truths']:
            self._visualize_metrics_distribution(vis_dir)
            
            # 3. 混淆矩阵
            self._visualize_confusion_matrices(vis_dir, threshold)
            
            # 4. 错误案例分析
            self._visualize_error_analysis(vis_dir, threshold)
            
            # 5. 类别性能对比
            self._visualize_class_performance(vis_dir)
        
        print(f"✅ 可视化结果已保存到: {vis_dir}")
    
    def _visualize_basic_predictions(self, vis_dir: Path, threshold: float, num_samples: int):
        """生成基础预测对比图"""
        basic_dir = vis_dir / 'basic_predictions'
        basic_dir.mkdir(exist_ok=True)
        
        num_samples = min(num_samples, len(self.test_results['predictions']))
        
        for i in range(num_samples):
            image = self.test_results['images'][i]
            prediction = self.test_results['predictions'][i]
            filename = self.test_results['filenames'][i]
            
            # 处理图像
            if image.shape[0] == 3:
                img_display = np.transpose(image, (1, 2, 0))
            else:
                img_display = image[0]
            
            # 归一化图像
            if img_display.max() > 1:
                img_display = img_display / 255.0
            img_display = np.clip(img_display, 0, 1)
            
            # 创建预测掩码可视化
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            
            # 原始图像
            axes[0, 0].imshow(img_display)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            
            # 各类别概率图
            for class_idx in range(self.num_classes):
                row = class_idx // 3
                col = (class_idx % 3) if class_idx < 3 else class_idx - 2
                if class_idx == 0:
                    row, col = 0, 1
                elif class_idx == 1:
                    row, col = 0, 2
                elif class_idx == 2:
                    row, col = 1, 0
                
                axes[row, col].imshow(prediction[class_idx], cmap='hot', vmin=0, vmax=1)
                axes[row, col].set_title(f'{self.class_names[class_idx]} (概率)')
                axes[row, col].axis('off')
            
            # 二值化结果
            pred_binary = (prediction > threshold).astype(float)
            combined_mask = np.zeros((prediction.shape[1], prediction.shape[2], 3))
            for class_idx in range(1, self.num_classes):
                color = np.array(self.colors[class_idx]) / 255.0
                mask = pred_binary[class_idx]
                for c in range(3):
                    combined_mask[:, :, c] += mask * color[c]
            
            combined_mask = np.clip(combined_mask, 0, 1)
            axes[1, 1].imshow(combined_mask)
            axes[1, 1].set_title('预测结果 (彩色)')
            axes[1, 1].axis('off')
            
            # 叠加图
            overlay = img_display * 0.6 + combined_mask * 0.4
            axes[1, 2].imshow(overlay)
            axes[1, 2].set_title('叠加图')
            axes[1, 2].axis('off')
            
            plt.suptitle(f'预测结果 - {filename}', fontsize=16)
            plt.tight_layout()
            plt.savefig(basic_dir / f'prediction_{i:03d}_{filename}.png', 
                       dpi=150, bbox_inches='tight')
            plt.close()
    
    def _visualize_metrics_distribution(self, vis_dir: Path):
        """可视化指标分布"""
        metrics_dir = vis_dir / 'metrics_distribution'
        metrics_dir.mkdir(exist_ok=True)
        
        # IoU分布
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        for class_idx in range(self.num_classes):
            ious = self.test_results['metrics']['per_class']['iou'][class_idx]
            
            axes[class_idx].hist(ious, bins=20, alpha=0.7, color=np.array(self.colors[class_idx])/255)
            axes[class_idx].set_title(f'{self.class_names[class_idx]} IoU分布')
            axes[class_idx].set_xlabel('IoU')
            axes[class_idx].set_ylabel('频次')
            axes[class_idx].axvline(np.mean(ious), color='red', linestyle='--', 
                                  label=f'平均值: {np.mean(ious):.3f}')
            axes[class_idx].legend()
        
        plt.tight_layout()
        plt.savefig(metrics_dir / 'iou_distribution.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # 箱线图对比
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        metrics_data = {
            'IoU': self.test_results['metrics']['per_class']['iou'],
            'Dice': self.test_results['metrics']['per_class']['dice'],
            'Accuracy': self.test_results['metrics']['per_class']['accuracy']
        }
        
        for i, (metric_name, metric_data) in enumerate(metrics_data.items()):
            data_for_plot = []
            labels = []
            
            for class_idx in range(self.num_classes):
                data_for_plot.append(metric_data[class_idx])
                labels.append(self.class_names[class_idx])
            
            axes[i].boxplot(data_for_plot, labels=labels)
            axes[i].set_title(f'{metric_name} 分布')
            axes[i].set_ylabel(metric_name)
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(metrics_dir / 'metrics_boxplot.png', dpi=150, bbox_inches='tight')
        plt.close()
    
    def _visualize_confusion_matrices(self, vis_dir: Path, threshold: float):
        """可视化混淆矩阵"""
        cm_dir = vis_dir / 'confusion_matrices'
        cm_dir.mkdir(exist_ok=True)
        
        all_predictions = np.array(self.test_results['predictions'])
        all_ground_truths = np.array(self.test_results['ground_truths'])
        
        # 为每个类别生成混淆矩阵
        for class_idx in range(self.num_classes):
            pred_class = (all_predictions[:, class_idx] > threshold).astype(int).flatten()
            gt_class = all_ground_truths[:, class_idx].astype(int).flatten()
            
            cm = confusion_matrix(gt_class, pred_class)
            
            plt.figure(figsize=(8, 6))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=['非轨道', '轨道'],
                       yticklabels=['非轨道', '轨道'])
            plt.title(f'{self.class_names[class_idx]} 混淆矩阵')
            plt.xlabel('预测')
            plt.ylabel('真实')
            plt.tight_layout()
            plt.savefig(cm_dir / f'confusion_matrix_{self.class_names[class_idx]}.png', 
                       dpi=150, bbox_inches='tight')
            plt.close()
    
    def _visualize_error_analysis(self, vis_dir: Path, threshold: float):
        """错误案例分析"""
        error_dir = vis_dir / 'error_analysis'
        error_dir.mkdir(exist_ok=True)
        
        # 找出最差和最佳的样本
        sample_metrics = self.test_results['metrics']['per_sample']
        
        for class_idx in range(self.num_classes):
            class_name = self.class_names[class_idx]
            ious = [m[f'class_{class_idx}_iou'] for m in sample_metrics]
            
            # 排序获取最佳和最差样本
            sorted_indices = np.argsort(ious)
            worst_indices = sorted_indices[:2]  # 最差的2个
            best_indices = sorted_indices[-2:]  # 最佳的2个
            
            # 可视化错误案例
            for idx_type, indices in [('worst', worst_indices), ('best', best_indices)]:
                for i, sample_idx in enumerate(indices):
                    self._create_error_case_visualization(
                        sample_idx, class_idx, f'{idx_type}_{i+1}', 
                        error_dir, threshold
                    )
    
    def _create_error_case_visualization(self, sample_idx: int, class_idx: int, 
                                       suffix: str, save_dir: Path, threshold: float):
        """创建错误案例可视化"""
        image = self.test_results['images'][sample_idx]
        prediction = self.test_results['predictions'][sample_idx]
        ground_truth = self.test_results['ground_truths'][sample_idx]
        filename = self.test_results['filenames'][sample_idx]
        
        # 处理图像
        if image.shape[0] == 3:
            img_display = np.transpose(image, (1, 2, 0))
        else:
            img_display = image[0]
        
        if img_display.max() > 1:
            img_display = img_display / 255.0
        img_display = np.clip(img_display, 0, 1)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 原图
        axes[0, 0].imshow(img_display)
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 真实掩码
        axes[0, 1].imshow(ground_truth[class_idx], cmap='gray', vmin=0, vmax=1)
        axes[0, 1].set_title(f'{self.class_names[class_idx]} 真实')
        axes[0, 1].axis('off')
        
        # 预测掩码
        pred_binary = (prediction[class_idx] > threshold).astype(float)
        axes[0, 2].imshow(pred_binary, cmap='gray', vmin=0, vmax=1)
        axes[0, 2].set_title(f'{self.class_names[class_idx]} 预测')
        axes[0, 2].axis('off')
        
        # 概率图
        axes[1, 0].imshow(prediction[class_idx], cmap='hot', vmin=0, vmax=1)
        axes[1, 0].set_title('预测概率')
        axes[1, 0].axis('off')
        
        # 差异图
        diff = pred_binary - ground_truth[class_idx]
        axes[1, 1].imshow(diff, cmap='RdGy', vmin=-1, vmax=1)
        axes[1, 1].set_title('差异 (红=假阳性, 灰=假阴性)')
        axes[1, 1].axis('off')
        
        # 叠加图
        overlay = img_display.copy()
        if len(overlay.shape) == 2:
            overlay = np.stack([overlay, overlay, overlay], axis=2)
        
        # 真实轨道用绿色，预测轨道用红色
        overlay[ground_truth[class_idx] > 0.5] = [0, 1, 0]  # 绿色
        overlay[pred_binary > 0.5] = [1, 0, 0]  # 红色
        overlap_mask = (ground_truth[class_idx] > 0.5) & (pred_binary > 0.5)
        overlay[overlap_mask] = [1, 1, 0]  # 黄色表示正确预测
        
        axes[1, 2].imshow(overlay)
        axes[1, 2].set_title('叠加 (绿=真实, 红=预测, 黄=正确)')
        axes[1, 2].axis('off')
        
        # 计算指标
        iou = self.test_results['metrics']['per_sample'][sample_idx][f'class_{class_idx}_iou']
        dice = self.test_results['metrics']['per_sample'][sample_idx][f'class_{class_idx}_dice']
        
        plt.suptitle(f'{self.class_names[class_idx]} {suffix} - {filename}\n'
                    f'IoU: {iou:.3f}, Dice: {dice:.3f}', fontsize=14)
        plt.tight_layout()
        plt.savefig(save_dir / f'{self.class_names[class_idx]}_{suffix}_{filename}.png', 
                   dpi=150, bbox_inches='tight')
        plt.close()
    
    def _visualize_class_performance(self, vis_dir: Path):
        """类别性能对比"""
        perf_dir = vis_dir / 'class_performance'
        perf_dir.mkdir(exist_ok=True)
        
        # 性能雷达图
        metrics = ['IoU', 'Dice', 'Accuracy']
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        for class_idx in range(self.num_classes):
            values = [
                self.test_results['metrics']['overall'][f'{self.class_names[class_idx]}_IoU'],
                self.test_results['metrics']['overall'][f'{self.class_names[class_idx]}_Dice'],
                self.test_results['metrics']['overall'][f'{self.class_names[class_idx]}_Accuracy']
            ]
            values += values[:1]  # 闭合
            
            color = np.array(self.colors[class_idx]) / 255.0
            ax.plot(angles, values, 'o-', linewidth=2, 
                   label=self.class_names[class_idx], color=color)
            ax.fill(angles, values, alpha=0.25, color=color)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('类别性能对比', size=16, y=1.1)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        plt.tight_layout()
        plt.savefig(perf_dir / 'performance_radar.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # 性能条形图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        for i, metric in enumerate(metrics):
            values = [
                self.test_results['metrics']['overall'][f'{name}_{metric}'] 
                for name in self.class_names
            ]
            colors = [np.array(self.colors[idx]) / 255.0 for idx in range(self.num_classes)]
            
            bars = axes[i].bar(self.class_names, values, color=colors, alpha=0.7)
            axes[i].set_title(f'{metric} 对比')
            axes[i].set_ylabel(metric)
            axes[i].set_ylim(0, 1)
            axes[i].grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(perf_dir / 'performance_bars.png', dpi=150, bbox_inches='tight')
        plt.close()
    
    def save_results(self, threshold: float = 0.5):
        """保存测试结果"""
        print("💾 保存测试结果...")
        
        # 保存详细结果
        results_to_save = {
            'config': self.config,
            'test_parameters': {
                'threshold': threshold,
                'device': str(self.device),
                'weights_dir': str(self.weights_dir),
                'num_samples': len(self.test_results['predictions'])
            },
            'metrics': self.test_results['metrics'],
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存JSON报告
        with open(self.output_dir / 'test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_to_save, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存CSV报告
        if self.test_results['metrics']['per_sample']:
            df = pd.DataFrame(self.test_results['metrics']['per_sample'])
            df['filename'] = self.test_results['filenames']
            df.to_csv(self.output_dir / 'detailed_results.csv', index=False)
        
        # 保存汇总报告
        if self.test_results['metrics']['overall']:
            summary = {
                '指标': list(self.test_results['metrics']['overall'].keys()),
                '数值': list(self.test_results['metrics']['overall'].values())
            }
            summary_df = pd.DataFrame(summary)
            summary_df.to_csv(self.output_dir / 'summary_results.csv', index=False)
        
        print(f"✅ 结果已保存到: {self.output_dir}")


def main():
    parser = argparse.ArgumentParser(description='集成学习测试集预测和可视化')
    
    parser.add_argument('--weights-dir', required=True, type=str,
                       help='训练好的权重目录路径')
    parser.add_argument('--config', required=True, type=str,
                       help='配置文件路径')
    parser.add_argument('--data-dir', required=True, type=str,
                       help='测试数据集目录路径')
    parser.add_argument('--output-dir', default='./test_outputs', type=str,
                       help='输出目录路径')
    parser.add_argument('--threshold', default=0.5, type=float,
                       help='预测阈值')
    parser.add_argument('--device', default=None, type=str,
                       help='计算设备 (cuda/cpu)')
    parser.add_argument('--num-samples', default=8, type=int,
                       help='可视化样本数量')
    
    args = parser.parse_args()
    
    print("🚀 启动集成学习测试集预测...")
    print(f"  权重目录: {args.weights_dir}")
    print(f"  配置文件: {args.config}")
    print(f"  数据目录: {args.data_dir}")
    print(f"  输出目录: {args.output_dir}")
    print(f"  预测阈值: {args.threshold}")
    
    # 创建预测器
    predictor = EnsembleTestPredictor(
        weights_dir=args.weights_dir,
        config_path=args.config,
        output_dir=args.output_dir,
        device=args.device
    )
    
    # 执行预测
    predictor.predict_test_set(args.data_dir, args.threshold)
    
    # 生成可视化
    predictor.generate_visualizations(args.threshold, args.num_samples)
    
    # 保存结果
    predictor.save_results(args.threshold)
    
    print("🎉 集成学习测试预测完成！")
    print(f"📊 结果保存位置: {args.output_dir}")


if __name__ == '__main__':
    main() 