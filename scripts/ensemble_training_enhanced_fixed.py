#!/usr/bin/env python3
"""
增强版集成学习训练脚本（修复版）
增加每个epoch的可视化结果保存
修复数据预处理和训练设置问题
"""

# 显存优化配置
import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 帮助调试CUDA错误

# 修复matplotlib的tkinter问题 - 设置非交互式后端
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
plt.ioff()  # 关闭交互模式

import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, TYPE_CHECKING, Tuple
import argparse
from tqdm import tqdm
import segmentation_models_pytorch as smp
import logging
import time
import json
import datetime
import psutil
import GPUtil
from collections import defaultdict
import seaborn as sns
import cv2
from PIL import Image
import gc

# TensorBoard imports (optional)
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    print("警告: TensorBoard未安装，将禁用可视化功能")

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.models.ensemble import EnsembleModel, EnsemblePredictor
from src.utils.metrics import iou_coef
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.models.segmentation_model import create_model


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 7, min_delta: float = 0.001, mode: str = 'max'):
        """
        初始化早停机制
        
        Args:
            patience: 忍耐次数
            min_delta: 最小改进阈值
            mode: 'max' 或 'min'
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        
    def __call__(self, score: float) -> bool:
        """
        检查是否需要早停
        
        Args:
            score: 当前分数
            
        Returns:
            bool: True表示需要早停
        """
        if self.best_score is None:
            self.best_score = score
            return False
        
        if self.mode == 'max':
            if score > self.best_score + self.min_delta:
                self.best_score = score
                self.counter = 0
            else:
                self.counter += 1
        else:
            if score < self.best_score - self.min_delta:
                self.best_score = score
                self.counter = 0
            else:
                self.counter += 1
        
        if self.counter >= self.patience:
            self.early_stop = True
            
        return self.early_stop


class EnhancedEnsembleTrainer:
    """增强版集成学习训练器"""
    
    def __init__(self, config: Dict[str, Any], experiment_name: Optional[str] = None):
        """
        初始化训练器
        
        Args:
            config: 配置字典
            experiment_name: 实验名称
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 实验目录设置
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.experiment_name = experiment_name or f"ensemble_enhanced_{timestamp}"
        self.experiment_dir = Path('experiments') / self.experiment_name
        
        # 创建子目录
        self.checkpoints_dir = self.experiment_dir / 'checkpoints'
        self.weights_dir = self.experiment_dir / 'weights'
        self.logs_dir = self.experiment_dir / 'logs'
        self.tensorboard_dir = self.experiment_dir / 'tensorboard'
        self.visualizations_dir = self.experiment_dir / 'visualizations'  # 新增可视化目录
        
        for dir_path in [self.checkpoints_dir, self.weights_dir, self.logs_dir, 
                         self.tensorboard_dir, self.visualizations_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 设置TensorBoard
        self.writer = None
        if TENSORBOARD_AVAILABLE:
            self.writer = SummaryWriter(self.tensorboard_dir)
            self.logger.info(f"TensorBoard日志目录: {self.tensorboard_dir}")
        
        # 模型配置
        self.models_config = config.get('ensemble', {}).get('models', [])
        if not self.models_config:
            raise ValueError("配置中未找到集成模型配置")
        
        # 训练历史
        self.training_history = defaultdict(list)
        
        # 类别名称
        self.class_names = config.get('data', {}).get('classes', ['background', 'main_track', 'fork_track'])
        
        self.logger.info(f"实验目录: {self.experiment_dir}")
        self.logger.info(f"使用设备: {self.device}")
        self.logger.info(f"模型数量: {len(self.models_config)}")
        self.logger.info(f"类别: {self.class_names}")
        
        # 保存预训练目录（如果提供）
        self.pretrained_dir = None
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.logs_dir / 'training.log'
        
        # 创建logger
        self.logger = logging.getLogger(self.experiment_name)
        self.logger.setLevel(logging.INFO)
        
        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def save_visualization(self, images: torch.Tensor, masks: torch.Tensor, 
                          predictions: torch.Tensor, model_name: str, epoch: int, 
                          prefix: str = "epoch"):
        """
        保存可视化结果到文件
        
        Args:
            images: 输入图像 (B, 3, H, W)
            masks: 真实掩码 (B, C, H, W)
            predictions: 预测结果 (B, C, H, W)
            model_name: 模型名称
            epoch: 当前epoch
            prefix: 文件前缀
        """
        # 创建模型和epoch的子目录
        save_dir = self.visualizations_dir / model_name / f"epoch_{epoch}"
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 选择要可视化的样本数量（最多8个）
        num_samples = min(images.size(0), 8)
        
        for idx in range(num_samples):
            # 反归一化图像
            img = images[idx].cpu()
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
            img = img * std + mean
            img = torch.clamp(img, 0, 1)
            
            # 获取掩码和预测
            true_mask = masks[idx].cpu()
            pred_mask = predictions[idx].cpu()
            
            # 创建包含所有类别的可视化
            num_classes = true_mask.size(0)
            fig, axes = plt.subplots(3, num_classes, figsize=(4*num_classes, 12))
            
            # 如果只有一个类别，确保axes是二维的
            if num_classes == 1:
                axes = axes.reshape(-1, 1)
            
            for class_idx in range(num_classes):
                # 原图
                axes[0, class_idx].imshow(img.permute(1, 2, 0).numpy())
                axes[0, class_idx].set_title(f'{self.class_names[class_idx]} - Original')
                axes[0, class_idx].axis('off')
                
                # 真实掩码
                axes[1, class_idx].imshow(true_mask[class_idx].numpy(), cmap='hot')
                axes[1, class_idx].set_title('Ground Truth')
                axes[1, class_idx].axis('off')
                
                # 预测掩码
                axes[2, class_idx].imshow(pred_mask[class_idx].numpy(), cmap='hot')
                iou = iou_coef(true_mask[class_idx:class_idx+1], pred_mask[class_idx:class_idx+1]).item()
                axes[2, class_idx].set_title(f'Prediction (IoU: {iou:.3f})')
                axes[2, class_idx].axis('off')
            
            plt.suptitle(f'{model_name} - Epoch {epoch} - Sample {idx}')
            plt.tight_layout()
            
            # 保存图像
            save_path = save_dir / f"{prefix}_sample_{idx}.png"
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
        
        # 创建总览图（显示每个类别的最好和最差预测）
        self.save_overview_visualization(images[:num_samples], masks[:num_samples], 
                                       predictions[:num_samples], model_name, epoch, save_dir)
    
    def save_overview_visualization(self, images: torch.Tensor, masks: torch.Tensor, 
                                  predictions: torch.Tensor, model_name: str, 
                                  epoch: int, save_dir: Path):
        """
        保存总览可视化（最好和最差的预测）
        """
        num_classes = masks.size(1)
        
        # 计算每个样本的平均IoU
        sample_ious = []
        for i in range(images.size(0)):
            ious = []
            for c in range(num_classes):
                iou = iou_coef(masks[i, c:c+1], predictions[i, c:c+1]).item()
                ious.append(iou)
            sample_ious.append(np.mean(ious))
        
        sample_ious = np.array(sample_ious)
        
        # 找出最好和最差的样本
        best_idx = np.argmax(sample_ious)
        worst_idx = np.argmin(sample_ious)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        for row, (idx, title_prefix) in enumerate([(best_idx, 'Best'), (worst_idx, 'Worst')]):
            # 反归一化图像
            img = images[idx].cpu()
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
            img = img * std + mean
            img = torch.clamp(img, 0, 1)
            
            # 原图
            axes[row, 0].imshow(img.permute(1, 2, 0).numpy())
            axes[row, 0].set_title(f'{title_prefix} - Original (Avg IoU: {sample_ious[idx]:.3f})')
            axes[row, 0].axis('off')
            
            # 创建多类别合成图
            true_composite = np.zeros((*masks[idx, 0].shape, 3))
            pred_composite = np.zeros((*predictions[idx, 0].shape, 3))
            
            # 为每个类别分配颜色
            colors = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]  # RGB for 3 classes
            
            for c in range(min(num_classes, 3)):
                true_composite[:, :, c] = masks[idx, c].numpy()
                pred_composite[:, :, c] = predictions[idx, c].numpy()
            
            # 真实掩码合成
            axes[row, 1].imshow(true_composite)
            axes[row, 1].set_title('Ground Truth Composite')
            axes[row, 1].axis('off')
            
            # 预测掩码合成
            axes[row, 2].imshow(pred_composite)
            axes[row, 2].set_title('Prediction Composite')
            axes[row, 2].axis('off')
        
        plt.suptitle(f'{model_name} - Epoch {epoch} - Best vs Worst Predictions')
        plt.tight_layout()
        
        # 保存
        save_path = save_dir / f"overview_epoch_{epoch}.png"
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def create_model(self, model_config: Dict[str, Any]) -> nn.Module:
        """创建单个模型"""
        # 优先使用与train_v2.py相同的create_model函数
        try:
            model = create_model(
                architecture=model_config['architecture'],
                backbone=model_config['encoder'],
                num_classes=self.config['data']['num_classes'],
                pretrained=True
            )
            return model.to(self.device)
        except Exception as e:
            self.logger.warning(f"使用create_model失败: {e}")
            self.logger.info("回退到原始的segmentation_models_pytorch方法")
        
        # 回退方案
        params = {
            'encoder_name': model_config['encoder'],
            'encoder_weights': model_config['encoder_weights'],
            'in_channels': 3,
            'classes': self.config['model']['classes'],
            'activation': None
        }
        
        if model_config['architecture'].lower() == 'pan':
            model = smp.PAN(**params)
        elif model_config['architecture'].lower() == 'unet':
            model = smp.Unet(**params)
        elif model_config['architecture'].lower() == 'fpn':
            model = smp.FPN(**params)
        else:
            raise ValueError(f"不支持的架构: {model_config['architecture']}")
        
        return model.to(self.device)
    
    def train_single_model(self, 
                          model: nn.Module,
                          model_config: Dict[str, Any],
                          train_loader: DataLoader,
                          val_loader: DataLoader,
                          test_loader: Optional[DataLoader] = None) -> str:
        """训练单个模型"""
        model_name = model_config['name']
        epochs = model_config['epochs']
        
        self.logger.info(f"开始训练模型: {model_name}")
        self.logger.info(f"编码器: {model_config['encoder']}")
        self.logger.info(f"训练轮数: {epochs}")
        
        # 创建损失函数
        loss_config = self.config.get('loss', {})
        criterion = MultiLabelSegmentationLoss(
            jaccard_weight=loss_config.get('jaccard_weight', 0.5),
            focal_weight=loss_config.get('focal_weight', 0.5), 
            alpha=loss_config.get('alpha', [0.1, 0.3, 0.6]),
            gamma=loss_config.get('focal_gamma', 2.0)
        )
        
        # 优化器配置
        lr = model_config.get('learning_rate', 0.0005)
        
        # 根据模型类型调整学习率
        if 'eca_nfnet' in model_name.lower():
            lr = min(lr * 0.5, 0.0002)  # 降低学习率
            self.logger.info(f"[{model_name}] 调整学习率为: {lr}")
        
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=0.0001)
        
        # 调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=epochs, eta_min=1e-6
        )
        
        # 早停机制
        early_stopping = EarlyStopping(patience=15, min_delta=0.001, mode='max')
        
        # 梯度累积设置（补偿小批次）
        accumulation_steps = 1  # 减少到1以节省内存
        
        # 训练历史
        best_iou = 0.0
        start_epoch = 0
        
        # 尝试加载检查点或预训练模型
        checkpoint_loaded = False
        
        # 1. 首先尝试加载最新的检查点（断点续训）
        latest_checkpoint = self.get_latest_checkpoint(model_name)
        if latest_checkpoint:
            try:
                self.logger.info(f"发现检查点: {latest_checkpoint}")
                checkpoint = torch.load(latest_checkpoint, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                start_epoch = checkpoint.get('epoch', 0) + 1
                best_iou = checkpoint.get('val_iou', 0.0)
                self.logger.info(f"从 epoch {start_epoch} 恢复训练，最佳 IoU: {best_iou:.4f}")
                checkpoint_loaded = True
            except Exception as e:
                self.logger.warning(f"加载检查点失败: {e}")
        
        # 2. 如果没有检查点，尝试加载预训练模型（finetune）
        if not checkpoint_loaded:
            pretrained_path = self.get_pretrained_model_path(model_name)
            if pretrained_path and pretrained_path.exists():
                try:
                    self.logger.info(f"加载预训练模型: {pretrained_path}")
                    state_dict = torch.load(pretrained_path, map_location=self.device)
                    # 如果是完整的检查点格式
                    if isinstance(state_dict, dict) and 'model_state_dict' in state_dict:
                        model.load_state_dict(state_dict['model_state_dict'])
                    else:
                        model.load_state_dict(state_dict)
                    self.logger.info("成功加载预训练模型，开始 fine-tuning")
                except Exception as e:
                    self.logger.warning(f"加载预训练模型失败: {e}")
        
        for epoch in range(start_epoch, epochs):
            epoch_start_time = time.time()
            
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_batches = 0
            
            train_pbar = tqdm(train_loader, desc=f"[{model_name}] Epoch {epoch+1}/{epochs} - 训练")
            for batch_idx, batch in enumerate(train_pbar):
                try:
                    images = batch['image'].to(self.device)
                    masks = batch['mask'].to(self.device)
                    
                    optimizer.zero_grad()
                    
                    # 使用混合精度训练减少显存使用
                    with torch.cuda.amp.autocast(enabled=False):  # 暂时禁用以提高稳定性
                        outputs = model(images)
                        loss = criterion(outputs, masks)
                    
                    # 检查NaN
                    if torch.isnan(loss) or torch.isinf(loss):
                        self.logger.warning(f"[{model_name}] 检测到NaN/Inf loss，跳过此batch")
                        continue
                    
                    # 梯度累积
                    loss = loss / accumulation_steps
                    loss.backward()
                    
                    # 每accumulation_steps个批次更新一次参数
                    if (batch_idx + 1) % accumulation_steps == 0:
                        # 梯度裁剪
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()
                        optimizer.zero_grad()
                    
                    train_loss += loss.item() * accumulation_steps
                    train_batches += 1
                    
                    train_pbar.set_postfix({
                        'loss': f'{loss.item():.4f}',
                        'avg_loss': f'{train_loss/train_batches:.4f}'
                    })
                    
                    # 更频繁地清理显存
                    if batch_idx % 5 == 0:
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                    
                except RuntimeError as e:
                    if "out of memory" in str(e):
                        self.logger.error(f"[{model_name}] CUDA OOM! 清理显存并跳过此batch")
                        # 清理显存
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        # 删除中间变量
                        if 'outputs' in locals():
                            del outputs
                        if 'loss' in locals():
                            del loss
                        if 'images' in locals():
                            del images
                        if 'masks' in locals():
                            del masks
                        torch.cuda.empty_cache()
                        gc.collect()
                        continue
                    else:
                        self.logger.error(f"[{model_name}] 训练错误: {e}")
                        continue
                except Exception as e:
                    self.logger.error(f"[{model_name}] 训练错误: {e}")
                    continue
            
            if train_batches == 0:
                self.logger.error(f"[{model_name}] Epoch {epoch}: 没有成功的训练batch")
                continue
                
            avg_train_loss = train_loss / train_batches
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_iou = 0.0
            val_batches = 0
            
            # 用于可视化的样本
            vis_images = []
            vis_masks = []
            vis_predictions = []
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f"[{model_name}] Epoch {epoch+1}/{epochs} - 验证")
                for batch_idx, batch in enumerate(val_pbar):
                    try:
                        images = batch['image'].to(self.device)
                        masks = batch['mask'].to(self.device)
                        
                        outputs = model(images)
                        loss = criterion(outputs, masks)
                        
                        if torch.isnan(loss) or torch.isinf(loss):
                            self.logger.warning(f"[{model_name}] 验证阶段检测到异常loss")
                            continue
                        
                        outputs_prob = torch.sigmoid(outputs)
                        iou = iou_coef(masks, outputs_prob)
                        
                        val_loss += loss.item()
                        val_iou += iou.item()
                        val_batches += 1
                        
                        # 收集可视化样本（前8个batch）
                        if batch_idx < 8 and len(vis_images) < 8:
                            vis_images.append(images[0:1])  # 只取第一个样本
                            vis_masks.append(masks[0:1])
                            vis_predictions.append(outputs_prob[0:1])
                        
                        val_pbar.set_postfix({
                            'loss': f'{loss.item():.4f}',
                            'iou': f'{iou.item():.4f}'
                        })
                        
                        # 更频繁地清理显存
                        if batch_idx % 5 == 0:
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                        
                    except RuntimeError as e:
                        if "out of memory" in str(e):
                            self.logger.error(f"[{model_name}] 验证阶段CUDA OOM! 清理显存并跳过")
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            gc.collect()
                            continue
                        else:
                            self.logger.warning(f"[{model_name}] 验证错误: {e}")
                            continue
                    except Exception as e:
                        self.logger.warning(f"[{model_name}] 验证错误: {e}")
                        continue
            
            if val_batches == 0:
                self.logger.error(f"[{model_name}] Epoch {epoch}: 没有成功的验证batch")
                continue
                
            avg_val_loss = val_loss / val_batches
            avg_val_iou = val_iou / val_batches
            
            # 更新学习率
            scheduler.step()
            
            # 记录日志
            self.logger.info(
                f"[{model_name}] Epoch {epoch+1}/{epochs} - "
                f"Train Loss: {avg_train_loss:.4f}, "
                f"Val Loss: {avg_val_loss:.4f}, "
                f"Val IoU: {avg_val_iou:.4f}, "
                f"LR: {optimizer.param_groups[0]['lr']:.6f}"
            )
            
            # TensorBoard记录
            if self.writer:
                self.writer.add_scalar(f'{model_name}/train_loss', avg_train_loss, epoch)
                self.writer.add_scalar(f'{model_name}/val_loss', avg_val_loss, epoch)
                self.writer.add_scalar(f'{model_name}/val_iou', avg_val_iou, epoch)
                self.writer.add_scalar(f'{model_name}/learning_rate', optimizer.param_groups[0]['lr'], epoch)
            
            # 保存可视化结果
            if vis_images:
                vis_images_tensor = torch.cat(vis_images, dim=0)
                vis_masks_tensor = torch.cat(vis_masks, dim=0)
                vis_predictions_tensor = torch.cat(vis_predictions, dim=0)
                
                self.save_visualization(
                    vis_images_tensor, vis_masks_tensor, vis_predictions_tensor,
                    model_name, epoch
                )
                
                # 清理可视化数据的内存
                del vis_images_tensor, vis_masks_tensor, vis_predictions_tensor
                del vis_images, vis_masks, vis_predictions
                torch.cuda.empty_cache()
            
            # 保存最佳模型
            if avg_val_iou > best_iou:
                best_iou = avg_val_iou
                self.save_model(model, model_name, epoch, avg_val_iou, is_best=True)
            
            # 每个epoch都保存模型
            self.save_model(model, model_name, epoch, avg_val_iou, is_best=False)
            
            # 保存测试集可视化（如果提供了测试集，每5个epoch保存一次）
            if test_loader is not None and epoch % 5 == 0:
                self.save_test_visualization(model, test_loader, model_name, epoch)
            
            # 早停检查
            if early_stopping(avg_val_iou):
                self.logger.info(f"[{model_name}] 早停触发，停止训练")
                break
            
            # 每个epoch结束后清理内存
            torch.cuda.empty_cache()
            gc.collect()
        
        # 保存最终模型
        final_path = self.weights_dir / f"{model_name}.pth"
        torch.save(model.state_dict(), final_path)
        
        return str(final_path)
    
    def save_model(self, model: nn.Module, model_name: str, epoch: int, 
                   val_iou: float, is_best: bool = False):
        """保存模型"""
        if is_best:
            path = self.weights_dir / f"{model_name}_best.pth"
        else:
            path = self.checkpoints_dir / f"{model_name}_epoch_{epoch}_iou_{val_iou:.4f}.pth"
        
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'val_iou': val_iou,
        }, path)
        
        self.logger.info(f"保存模型: {path}")
    
    def save_test_visualization(self, model: nn.Module, test_loader: DataLoader, 
                               model_name: str, epoch: int):
        """
        保存测试集可视化结果
        
        Args:
            model: 模型
            test_loader: 测试数据加载器
            model_name: 模型名称
            epoch: 当前epoch
        """
        model.eval()
        
        # 创建测试集可视化目录
        test_viz_dir = self.visualizations_dir / model_name / f"epoch_{epoch}" / "test_predictions"
        test_viz_dir.mkdir(parents=True, exist_ok=True)
        
        # 选择要可视化的样本数量
        num_samples = min(4, len(test_loader.dataset))  # 减少到4个样本以节省内存
        samples_collected = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(test_loader):
                if samples_collected >= num_samples:
                    break
                
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device) if 'mask' in batch else None
                
                # 获取预测
                outputs = model(images)
                predictions = torch.sigmoid(outputs)
                
                # 保存每个样本
                batch_size = min(num_samples - samples_collected, images.size(0))
                for i in range(batch_size):
                    # 反归一化图像
                    img = images[i].cpu()
                    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                    img = img * std + mean
                    img = torch.clamp(img, 0, 1)
                    
                    pred_mask = predictions[i].cpu()
                    
                    # 创建可视化
                    num_classes = pred_mask.size(0)
                    if masks is not None:
                        fig, axes = plt.subplots(3, num_classes, figsize=(4*num_classes, 12))
                    else:
                        fig, axes = plt.subplots(2, num_classes, figsize=(4*num_classes, 8))
                    
                    # 如果只有一个类别，确保axes是二维的
                    if num_classes == 1:
                        axes = axes.reshape(-1, 1)
                    
                    for class_idx in range(num_classes):
                        # 原图
                        axes[0, class_idx].imshow(img.permute(1, 2, 0).numpy())
                        axes[0, class_idx].set_title(f'{self.class_names[class_idx]} - Original')
                        axes[0, class_idx].axis('off')
                        
                        # 预测掩码
                        axes[1, class_idx].imshow(pred_mask[class_idx].numpy(), cmap='hot')
                        axes[1, class_idx].set_title('Prediction')
                        axes[1, class_idx].axis('off')
                        
                        # 如果有真实掩码，显示对比
                        if masks is not None:
                            true_mask = masks[i].cpu()
                            axes[2, class_idx].imshow(true_mask[class_idx].numpy(), cmap='hot')
                            iou = iou_coef(true_mask[class_idx:class_idx+1], pred_mask[class_idx:class_idx+1]).item()
                            axes[2, class_idx].set_title(f'Ground Truth (IoU: {iou:.3f})')
                            axes[2, class_idx].axis('off')
                    
                    plt.suptitle(f'{model_name} - Epoch {epoch} - Test Sample {samples_collected}')
                    plt.tight_layout()
                    
                    # 保存图像
                    save_path = test_viz_dir / f"test_sample_{samples_collected}.png"
                    plt.savefig(save_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    
                    samples_collected += 1
                
                if samples_collected >= num_samples:
                    break
        
        # 清理GPU内存
        torch.cuda.empty_cache()
        
        self.logger.info(f"保存了 {samples_collected} 个测试集可视化样本到: {test_viz_dir}")
    
    def get_latest_checkpoint(self, model_name: str) -> Optional[Path]:
        """
        获取最新的检查点文件
        
        Args:
            model_name: 模型名称
            
        Returns:
            最新检查点的路径，如果没有则返回None
        """
        checkpoint_pattern = f"{model_name}_epoch_*.pth"
        checkpoints = list(self.checkpoints_dir.glob(checkpoint_pattern))
        
        if not checkpoints:
            return None
        
        # 按修改时间排序，返回最新的
        checkpoints.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        return checkpoints[0]
    
    def get_pretrained_model_path(self, model_name: str) -> Optional[Path]:
        """
        获取预训练模型路径
        
        Args:
            model_name: 模型名称
            
        Returns:
            预训练模型路径
        """
        # 查找可能的预训练模型位置
        possible_paths = [
            Path(f"weights/{model_name}.pth.tar"),  # 原始项目格式
            Path(f"weights/{model_name}.pth"),
            Path(f"pretrained/{model_name}.pth"),
            self.weights_dir.parent.parent / f"weights/{model_name}.pth.tar",
        ]
        
        # 如果指定了预训练目录
        if hasattr(self, 'pretrained_dir') and self.pretrained_dir:
            possible_paths.insert(0, self.pretrained_dir / f"{model_name}.pth.tar")
            possible_paths.insert(1, self.pretrained_dir / f"{model_name}.pth")
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return None
    
    def train_all_models(self, train_loader: DataLoader, val_loader: DataLoader, 
                        test_loader: Optional[DataLoader] = None) -> List[str]:
        """训练所有模型"""
        weights_paths = []
        
        for model_config in self.models_config:
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"开始训练模型: {model_config['name']}")
            self.logger.info(f"{'='*50}")
            
            # 创建模型
            model = self.create_model(model_config)
            
            # 训练模型
            weights_path = self.train_single_model(model, model_config, train_loader, val_loader, test_loader)
            weights_paths.append(weights_path)
            
            # 彻底清理GPU内存
            del model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()  # 等待所有CUDA操作完成
            gc.collect()
            
            # 等待一下让系统回收内存
            time.sleep(2)
        
        return weights_paths
    
    def generate_training_report(self):
        """生成训练报告"""
        report_path = self.experiment_dir / 'training_report.json'
        
        report = {
            'experiment_name': self.experiment_name,
            'config': self.config,
            'models': self.models_config,
            'training_history': dict(self.training_history),
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=4, ensure_ascii=False)
        
        self.logger.info(f"训练报告已保存: {report_path}")
    
    def __del__(self):
        """清理资源"""
        if self.writer:
            self.writer.close()


def main():
    """主函数"""
    print("=== 增强版集成学习训练脚本（修复版） ===")
    print("✅ 修复数据预处理问题")
    print("✅ 优化训练设置")
    print("✅ 每个epoch保存可视化结果")
    print("✅ 增强的错误处理")
    print()
    
    parser = argparse.ArgumentParser(description='增强版集成学习训练脚本')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据集根目录')
    parser.add_argument('--experiment-name', type=str, default=None,
                       help='实验名称')
    parser.add_argument('--pretrained-dir', type=str, default=None,
                       help='预训练模型目录路径（用于finetune）')
    parser.add_argument('--resume', action='store_true',
                       help='从最新检查点恢复训练')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径
    config['data']['train_path'] = args.data_dir
    config['data']['val_path'] = args.data_dir
    
    # 创建训练器
    trainer = EnhancedEnsembleTrainer(config, args.experiment_name)
    
    # 设置预训练目录（如果提供）
    if args.pretrained_dir:
        trainer.pretrained_dir = Path(args.pretrained_dir)
        print(f"使用预训练模型目录: {trainer.pretrained_dir}")
    
    # 创建数据集
    print("创建数据集...")
    from src.data.augmentations import get_train_transform, get_val_transform
    
    train_transform = get_train_transform(config)
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_transform = get_val_transform(config)
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val', 
        config=config,
        transform=val_transform
    )
    
    # 使用配置文件中的批次大小
    train_batch_size = config['data']['batch_size']['train']
    val_batch_size = config['data']['batch_size']['val']
    
    print(f"使用配置文件中的batch_size: train={train_batch_size}, val={val_batch_size}")
    
    train_loader = DataLoader(
        train_dataset, batch_size=train_batch_size, shuffle=True,
        num_workers=config['project']['num_workers'], pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=val_batch_size, shuffle=False,
        num_workers=config['project']['num_workers'], pin_memory=True
    )
    
    # 创建测试集加载器（如果有测试集）
    test_loader = None
    test_path = Path(args.data_dir) / 'test'
    if test_path.exists():
        print("发现测试集，创建测试数据加载器...")
        test_dataset = RailwayDataset(
            data_root=args.data_dir,
            split='test',
            config=config,
            transform=val_transform
        )
        test_loader = DataLoader(
            test_dataset, batch_size=val_batch_size, shuffle=False,
            num_workers=config['project']['num_workers'], pin_memory=True
        )
        print(f"加载测试集: {len(test_dataset)} 个样本")
    
    # 训练模型
    print("开始训练集成模型...")
    weights_paths = trainer.train_all_models(train_loader, val_loader, test_loader)
    
    # 生成训练报告
    trainer.generate_training_report()
    
    print("\n=== 训练完成 ===")
    print(f"实验目录: {trainer.experiment_dir}")
    print(f"可视化结果: {trainer.visualizations_dir}")
    print(f"模型权重: {trainer.weights_dir}")
    print(f"TensorBoard: tensorboard --logdir {trainer.tensorboard_dir}")


if __name__ == '__main__':
    main()