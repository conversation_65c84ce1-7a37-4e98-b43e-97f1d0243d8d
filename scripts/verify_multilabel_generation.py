#!/usr/bin/env python3
"""
验证多标签掩码生成是否正确
"""

import sys
import yaml
import torch
import numpy as np
from pathlib import Path
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data import create_dataloaders


def verify_multilabel_generation():
    """验证多标签掩码生成"""
    
    # 加载配置
    with open('configs/railway_track_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    print("=== 配置检查 ===")
    print(f"use_multilabel: {config['data'].get('use_multilabel', False)}")
    print(f"loss type: {config['loss']['type']}")
    print(f"model activation: {config['model'].get('activation', 'not set')}")
    
    # 创建数据加载器
    print("\n=== 创建数据加载器 ===")
    train_loader, val_loader = create_dataloaders(config)
    
    # 统计信息
    total_batches = 0
    batches_with_main = 0
    batches_with_fork = 0
    batches_with_overlap = 0
    total_overlap_pixels = 0
    
    print("\n=== 检查前20个批次 ===")
    for i, batch in enumerate(train_loader):
        if i >= 20:  # 只检查前20个批次
            break
        
        total_batches += 1
        masks = batch['mask']
        
        # 检查每个样本
        batch_has_main = False
        batch_has_fork = False
        batch_has_overlap = False
        
        for j in range(masks.shape[0]):
            mask = masks[j]
            
            # 统计各通道像素
            bg_pixels = torch.sum(mask[0] > 0).item()
            main_pixels = torch.sum(mask[1] > 0).item()
            fork_pixels = torch.sum(mask[2] > 0).item()
            
            if main_pixels > 0:
                batch_has_main = True
            if fork_pixels > 0:
                batch_has_fork = True
            
            # 检查重叠
            overlap = torch.logical_and(mask[1] > 0, mask[2] > 0)
            overlap_pixels = torch.sum(overlap).item()
            
            if overlap_pixels > 0:
                batch_has_overlap = True
                total_overlap_pixels += overlap_pixels
                
                print(f"\n批次 {i+1}, 样本 {j+1}:")
                print(f"  文件: {batch['filename'][j]}")
                print(f"  背景: {bg_pixels} 像素")
                print(f"  主轨道: {main_pixels} 像素")
                print(f"  分叉轨道: {fork_pixels} 像素")
                print(f"  重叠: {overlap_pixels} 像素 ({overlap_pixels/(mask.shape[1]*mask.shape[2])*100:.2f}%)")
        
        if batch_has_main:
            batches_with_main += 1
        if batch_has_fork:
            batches_with_fork += 1
        if batch_has_overlap:
            batches_with_overlap += 1
    
    # 打印统计
    print(f"\n=== 统计结果 (前{total_batches}个批次) ===")
    print(f"包含主轨道的批次: {batches_with_main} ({batches_with_main/total_batches*100:.1f}%)")
    print(f"包含分叉轨道的批次: {batches_with_fork} ({batches_with_fork/total_batches*100:.1f}%)")
    print(f"包含重叠区域的批次: {batches_with_overlap} ({batches_with_overlap/total_batches*100:.1f}%)")
    print(f"总重叠像素数: {total_overlap_pixels}")
    
    if batches_with_overlap > 0:
        print("\n✅ 多标签掩码生成正常！发现重叠区域。")
    else:
        print("\n⚠️ 未发现重叠区域。可能是样本中没有分叉轨道。")
    
    # 专门查找包含分叉的样本
    print("\n=== 查找包含分叉轨道的样本 ===")
    found_fork = False
    for i, batch in enumerate(train_loader):
        if i >= 100:  # 最多检查100个批次
            break
        
        masks = batch['mask']
        for j in range(masks.shape[0]):
            mask = masks[j]
            fork_pixels = torch.sum(mask[2] > 0).item()
            
            if fork_pixels > 0:
                main_pixels = torch.sum(mask[1] > 0).item()
                overlap = torch.logical_and(mask[1] > 0, mask[2] > 0)
                overlap_pixels = torch.sum(overlap).item()
                
                print(f"\n找到分叉轨道样本:")
                print(f"  批次: {i+1}, 样本: {j+1}")
                print(f"  文件: {batch['filename'][j]}")
                print(f"  主轨道: {main_pixels} 像素")
                print(f"  分叉轨道: {fork_pixels} 像素")
                print(f"  重叠: {overlap_pixels} 像素 ({overlap_pixels/(mask.shape[1]*mask.shape[2])*100:.2f}%)")
                
                found_fork = True
                break
        
        if found_fork:
            break
    
    if not found_fork:
        print("在前100个批次中未找到分叉轨道样本")


if __name__ == '__main__':
    verify_multilabel_generation()