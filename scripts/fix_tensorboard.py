#!/usr/bin/env python3
"""
TensorBoard诊断和修复脚本
解决TensorBoard显示空白的问题
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import subprocess
import time
import argparse
from typing import List, Dict

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_tensorboard_installation():
    """检查TensorBoard安装状态"""
    print("🔍 检查TensorBoard安装状态...")
    
    try:
        from torch.utils.tensorboard import SummaryWriter
        print("✅ TensorBoard已正确安装")
        return True
    except ImportError:
        print("❌ TensorBoard未安装")
        print("请运行: pip install tensorboard")
        return False

def check_log_directories():
    """检查日志目录和文件"""
    print("\n📁 检查TensorBoard日志目录...")
    
    # 查找所有tensorboard目录
    tensorboard_dirs = []
    for root, dirs, files in os.walk('.'):
        if 'tensorboard' in dirs:
            tb_dir = Path(root) / 'tensorboard'
            tensorboard_dirs.append(tb_dir)
    
    if not tensorboard_dirs:
        print("❌ 未找到TensorBoard日志目录")
        return []
    
    print(f"✅ 找到 {len(tensorboard_dirs)} 个TensorBoard目录:")
    
    valid_dirs = []
    for tb_dir in tensorboard_dirs:
        # 检查是否有事件文件
        event_files = list(tb_dir.glob("events.out.tfevents.*"))
        file_sizes = [f.stat().st_size for f in event_files]
        total_size = sum(file_sizes)
        
        print(f"  📂 {tb_dir}")
        print(f"     文件数: {len(event_files)}")
        print(f"     总大小: {total_size} bytes")
        
        if total_size > 1000:  # 至少1KB的数据
            print(f"     ✅ 包含有效数据")
            valid_dirs.append(tb_dir)
        else:
            print(f"     ⚠️  数据很少或为空")
    
    return valid_dirs

def create_test_tensorboard_data(log_dir: Path):
    """创建测试TensorBoard数据"""
    print(f"\n🧪 创建测试数据到 {log_dir}...")
    
    try:
        from torch.utils.tensorboard import SummaryWriter
        
        log_dir.mkdir(parents=True, exist_ok=True)
        writer = SummaryWriter(str(log_dir))
        
        # 写入测试数据
        print("📊 写入测试标量数据...")
        for epoch in range(20):
            # 模拟训练指标
            train_loss = 1.0 * np.exp(-epoch * 0.1) + 0.1 + np.random.normal(0, 0.02)
            val_loss = 1.2 * np.exp(-epoch * 0.08) + 0.15 + np.random.normal(0, 0.03)
            val_iou = 0.5 + 0.4 * (1 - np.exp(-epoch * 0.15)) + np.random.normal(0, 0.01)
            learning_rate = 0.001 * (0.95 ** epoch)
            
            writer.add_scalar('Train/Loss', train_loss, epoch)
            writer.add_scalar('Validation/Loss', val_loss, epoch)
            writer.add_scalar('Validation/IoU', val_iou, epoch)
            writer.add_scalar('Training/LearningRate', learning_rate, epoch)
            
            # 模拟ECA-NFNet的特殊指标
            if epoch % 5 == 0:
                gradient_norm = np.random.uniform(0.3, 0.8)
                nan_recovery_count = np.random.poisson(0.2)
                writer.add_scalar('eca_nfnet_l2/gradient_norm', gradient_norm, epoch)
                writer.add_scalar('eca_nfnet_l2/nan_recovery_count', nan_recovery_count, epoch)
        
        # 写入测试图像
        print("🖼️  写入测试图像...")
        for i in range(5):
            # 创建模拟的分割结果
            img = torch.rand(3, 256, 256)
            mask = torch.rand(3, 256, 256)
            pred = torch.rand(3, 256, 256)
            
            combined = torch.cat([img, mask, pred], dim=2)
            writer.add_image(f'eca_nfnet_l2/predictions/class_0/sample_{i}', combined, i)
        
        # 写入测试直方图
        print("📈 写入测试直方图...")
        for epoch in range(0, 20, 5):
            weights = torch.randn(1000)
            writer.add_histogram('model/weights', weights, epoch)
        
        writer.flush()
        writer.close()
        
        print("✅ 测试数据创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def start_tensorboard_server(log_dirs: List[Path], port: int = 6006):
    """启动TensorBoard服务器"""
    print(f"\n🚀 启动TensorBoard服务器...")
    
    if not log_dirs:
        print("❌ 没有有效的日志目录")
        return False
    
    # 使用第一个有效目录，或者创建测试数据
    main_log_dir = log_dirs[0].parent if log_dirs else Path("test_tensorboard_output")
    
    print(f"📂 使用日志目录: {main_log_dir}")
    print(f"🌐 端口: {port}")
    
    try:
        # 检查端口是否被占用
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            result = s.connect_ex(('localhost', port))
            if result == 0:
                print(f"⚠️  端口 {port} 已被占用，尝试其他端口...")
                port += 1
        
        # 启动TensorBoard
        cmd = f"tensorboard --logdir {main_log_dir} --port {port} --reload_interval 1"
        print(f"💻 执行命令: {cmd}")
        
        # 后台启动
        process = subprocess.Popen(
            cmd.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(3)
        
        if process.poll() is None:
            print(f"✅ TensorBoard已启动")
            print(f"🌐 访问地址: http://localhost:{port}")
            print(f"🔄 刷新间隔: 1秒")
            print("\n📝 使用说明:")
            print("   1. 在浏览器中打开上述地址")
            print("   2. 如果看不到数据，点击左上角的刷新按钮")
            print("   3. 检查时间范围选择器")
            print("   4. 按Ctrl+C停止服务器")
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ TensorBoard启动失败")
            print(f"错误信息: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动TensorBoard失败: {e}")
        return False

def diagnose_ensemble_training():
    """诊断集成训练脚本的TensorBoard配置"""
    print("\n🔧 诊断集成训练脚本...")
    
    script_path = Path("scripts/ensemble_training_enhanced.py")
    if not script_path.exists():
        print("❌ 集成训练脚本不存在")
        return
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查TensorBoard相关代码
    checks = [
        ("SummaryWriter导入", "from torch.utils.tensorboard import SummaryWriter"),
        ("writer初始化", "SummaryWriter("),
        ("add_scalar调用", "add_scalar("),
        ("add_image调用", "add_image("),
        ("writer.flush", "flush()"),
        ("writer.close", "close()")
    ]
    
    print("检查TensorBoard代码集成:")
    for check_name, pattern in checks:
        if pattern in content:
            print(f"  ✅ {check_name}")
        else:
            print(f"  ❌ {check_name}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TensorBoard诊断和修复工具')
    parser.add_argument('--create-test', action='store_true', 
                       help='创建测试数据')
    parser.add_argument('--start-server', action='store_true',
                       help='启动TensorBoard服务器')
    parser.add_argument('--port', type=int, default=6006,
                       help='TensorBoard端口')
    parser.add_argument('--test-dir', type=str, default='test_tensorboard_output',
                       help='测试数据目录')
    
    args = parser.parse_args()
    
    print("=== TensorBoard诊断和修复工具 ===\n")
    
    # 1. 检查安装
    if not check_tensorboard_installation():
        return
    
    # 2. 检查现有日志
    valid_dirs = check_log_directories()
    
    # 3. 诊断训练脚本
    diagnose_ensemble_training()
    
    # 4. 创建测试数据（如果需要）
    if args.create_test or not valid_dirs:
        test_dir = Path(args.test_dir) / "tensorboard"
        if create_test_tensorboard_data(test_dir):
            valid_dirs.append(test_dir)
    
    # 5. 启动服务器
    if args.start_server or not valid_dirs:
        success = start_tensorboard_server(valid_dirs, args.port)
        if success:
            try:
                print("\n⏸️  按Ctrl+C停止服务器...")
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 TensorBoard服务器已停止")
    
    # 6. 提供修复建议
    print("\n🔧 修复建议:")
    print("1. 如果TensorBoard仍然空白:")
    print("   - 检查浏览器控制台是否有错误")
    print("   - 尝试清除浏览器缓存")
    print("   - 使用无痕模式打开")
    
    print("\n2. 如果集成训练没有产生数据:")
    print("   - 确保训练脚本实际运行了")
    print("   - 检查训练脚本的writer.add_scalar调用")
    print("   - 确保writer.flush()被调用")
    
    print("\n3. 重新启动集成训练:")
    print("   python scripts/ensemble_training_enhanced.py --data-dir /path/to/data")
    
    print("\n4. 使用测试数据验证TensorBoard:")
    print(f"   python {__file__} --create-test --start-server")

if __name__ == '__main__':
    main() 