#!/usr/bin/env python3
"""列出所有支持的编码器"""

import segmentation_models_pytorch as smp

print("=== Segmentation Models PyTorch 支持的编码器 ===\n")

# 获取所有支持的编码器
encoders = smp.encoders.get_encoder_names()

print(f"总共支持 {len(encoders)} 个编码器:\n")

# 按类别分组显示
categories = {
    'ResNet系列': [],
    'EfficientNet系列': [],
    'DenseNet系列': [],
    'VGG系列': [],
    'MobileNet系列': [],
    'SE系列': [],
    'RegNet系列': [],
    'ResNeSt系列': [],
    'MiT系列': [],
    '其他': []
}

for encoder in encoders:
    if 'resnet' in encoder and 'se_' not in encoder and 'resnest' not in encoder and 'resnext' not in encoder:
        categories['ResNet系列'].append(encoder)
    elif 'resnext' in encoder:
        categories['ResNet系列'].append(encoder)
    elif 'efficientnet' in encoder:
        categories['EfficientNet系列'].append(encoder)
    elif 'densenet' in encoder:
        categories['DenseNet系列'].append(encoder)
    elif 'vgg' in encoder:
        categories['VGG系列'].append(encoder)
    elif 'mobilenet' in encoder or 'mobileone' in encoder:
        categories['MobileNet系列'].append(encoder)
    elif 'se_' in encoder or 'senet' in encoder:
        categories['SE系列'].append(encoder)
    elif 'regnet' in encoder:
        categories['RegNet系列'].append(encoder)
    elif 'resnest' in encoder:
        categories['ResNeSt系列'].append(encoder)
    elif 'mit_' in encoder:
        categories['MiT系列'].append(encoder)
    else:
        categories['其他'].append(encoder)

# 打印分类结果
for category, encoders_list in categories.items():
    if encoders_list:
        print(f"\n{category} ({len(encoders_list)}个):")
        for encoder in sorted(encoders_list):
            print(f"  - {encoder}")

# 推荐的稳定组合
print("\n\n=== 推荐的集成学习编码器组合 ===")
print("\n组合1（轻量级，适合快速训练）:")
print("  - mobilenet_v2")
print("  - efficientnet-b0")
print("  - resnet34")

print("\n组合2（中等，平衡性能和速度）:")
print("  - resnet50")
print("  - efficientnet-b4")
print("  - se_resnet50")

print("\n组合3（重量级，追求最佳性能）:")
print("  - se_resnext101_32x4d")
print("  - timm-efficientnet-b7")
print("  - densenet201")