#!/usr/bin/env python3
"""
多标签训练示例
展示如何使用多标签Pipeline进行端到端训练
"""

import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def main():
    """主函数"""
    print("=== 多标签训练示例 ===\n")
    
    # 配置文件路径
    config_path = project_root / 'configs' / 'railway_track_config.yaml'
    
    # 检查配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("当前配置:")
    print(f"  - 使用多标签: {config['data'].get('use_multilabel', False)}")
    print(f"  - 损失函数: {config['loss']['type']}")
    print(f"  - 类别权重: {config['loss'].get('alpha', [0.1, 0.3, 0.6])}")
    print(f"  - 模型激活: {config['model'].get('activation', 'sigmoid')}")
    
    # 训练命令
    print("\n训练命令:")
    print("python scripts/train.py \\")
    print(f"  --config {config_path} \\")
    print("  --experiment-name multilabel_experiment \\")
    print("  --auto-resume")
    
    print("\n测试Pipeline:")
    print("python scripts/test_multilabel_pipeline.py")
    
    print("\n验证多标签掩码生成:")
    print("python scripts/test_multilabel_masks.py")
    
    print("\n集成预测（训练后）:")
    print("python scripts/ensemble_prediction.py \\")
    print("  --weights-dir path/to/weights \\")
    print("  --input path/to/test/images \\")
    print("  --output path/to/predictions \\")
    print("  --threshold 0.5")
    
    print("\n=== 关键特性 ===")
    print("1. 像素可同时属于多个类别（如轨道分叉处）")
    print("2. 使用Focal Loss处理类别不平衡")
    print("3. 每个类别有独立的权重控制")
    print("4. 支持per-class评估指标")
    print("5. 与notebook实现完全兼容")
    
    print("\n=== 注意事项 ===")
    print("1. 确保配置文件中 use_multilabel: true")
    print("2. 损失函数使用 multilabel_combined_loss")
    print("3. 模型激活函数为 sigmoid")
    print("4. 数据集会自动使用多标签掩码生成")
    
    # 可选：直接运行测试
    import subprocess
    
    response = input("\n是否运行Pipeline测试? (y/n): ")
    if response.lower() == 'y':
        subprocess.run([sys.executable, str(project_root / "scripts" / "test_multilabel_pipeline.py")])


if __name__ == '__main__':
    main()