#!/usr/bin/env python3
"""
测试多标签掩码生成
验证标签是否支持真正的多标签训练
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_mask_generation():
    """测试单标签和多标签掩码生成的差异"""
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 模拟标注数据 - 有重叠区域
    annotation_data = {
        'tracks': [
            {
                'label': 'Main_Left',
                'points': [
                    [100, 0],
                    [100, 400]
                ]
            },
            {
                'label': 'Main_Right',
                'points': [
                    [200, 0],
                    [200, 400]
                ]
            },
            {
                'label': 'Fork_Left',
                'points': [
                    [150, 200],
                    [250, 400]
                ]
            },
            {
                'label': 'Fork_Right',
                'points': [
                    [250, 200],
                    [350, 400]
                ]
            }
        ]
    }
    
    image_shape = (400, 400)
    
    # 生成单标签掩码
    single_label_mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    # 生成多标签掩码
    multi_label_mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
    
    # 分析差异
    print("=== 掩码生成分析 ===\n")
    
    # 单标签统计
    print("单标签掩码:")
    unique_values = np.unique(single_label_mask)
    print(f"  唯一值: {unique_values}")
    for val in unique_values:
        count = np.sum(single_label_mask == val)
        print(f"  类别 {val}: {count} 像素 ({count/(400*400)*100:.2f}%)")
    
    # 多标签统计
    print("\n多标签掩码:")
    print(f"  形状: {multi_label_mask.shape}")
    for i, class_name in enumerate(['背景', '主轨道', '分叉轨道']):
        count = np.sum(multi_label_mask[:, :, i] > 0)
        print(f"  {class_name}: {count} 像素 ({count/(400*400)*100:.2f}%)")
    
    # 计算重叠区域
    overlap = np.logical_and(
        multi_label_mask[:, :, 1] > 0,  # 主轨道
        multi_label_mask[:, :, 2] > 0   # 分叉轨道
    )
    overlap_pixels = np.sum(overlap)
    print(f"\n重叠区域: {overlap_pixels} 像素 ({overlap_pixels/(400*400)*100:.2f}%)")
    
    # 可视化
    visualize_comparison(single_label_mask, multi_label_mask, overlap)
    
    return overlap_pixels > 0  # 如果有重叠，说明支持多标签


def visualize_comparison(single_label, multi_label, overlap):
    """可视化单标签和多标签的差异"""
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 单标签显示
    axes[0, 0].imshow(single_label, cmap='tab20')
    axes[0, 0].set_title('单标签掩码')
    axes[0, 0].axis('off')
    
    # 单标签彩色化
    single_colored = np.zeros((single_label.shape[0], single_label.shape[1], 3), dtype=np.uint8)
    single_colored[single_label == 1] = [255, 0, 0]    # 主轨道 - 红色
    single_colored[single_label == 2] = [0, 255, 0]    # 分叉轨道 - 绿色
    axes[0, 1].imshow(single_colored)
    axes[0, 1].set_title('单标签彩色化')
    axes[0, 1].axis('off')
    
    # 空白
    axes[0, 2].axis('off')
    
    # 多标签各通道
    channel_names = ['背景', '主轨道', '分叉轨道']
    for i in range(3):
        axes[1, i].imshow(multi_label[:, :, i], cmap='gray', vmin=0, vmax=1)
        axes[1, i].set_title(f'多标签 - {channel_names[i]}')
        axes[1, i].axis('off')
    
    # 添加重叠区域可视化
    fig2, ax = plt.subplots(1, 2, figsize=(10, 5))
    
    # 多标签彩色化
    multi_colored = np.zeros((multi_label.shape[0], multi_label.shape[1], 3), dtype=np.uint8)
    multi_colored[multi_label[:, :, 1] > 0] = [255, 0, 0]    # 主轨道 - 红色
    multi_colored[multi_label[:, :, 2] > 0] = [0, 255, 0]    # 分叉轨道 - 绿色
    multi_colored[overlap] = [255, 255, 0]                    # 重叠 - 黄色
    
    ax[0].imshow(multi_colored)
    ax[0].set_title('多标签彩色化（黄色=重叠）')
    ax[0].axis('off')
    
    # 重叠区域热图
    ax[1].imshow(overlap, cmap='hot', vmin=0, vmax=1)
    ax[1].set_title('重叠区域')
    ax[1].axis('off')
    
    plt.tight_layout()
    plt.show()


def main():
    """主函数"""
    print("=== 测试多标签掩码生成 ===\n")
    
    # 测试掩码生成
    supports_multilabel = test_mask_generation()
    
    print("\n=== 结论 ===")
    if supports_multilabel:
        print("✓ 当前实现支持真正的多标签训练")
        print("  - 轨道分叉处的像素可以同时属于主轨道和分叉轨道")
        print("  - 每个类别有独立的二值掩码通道")
        print("  - 配合多标签损失函数可以进行端到端训练")
    else:
        print("✗ 当前实现不支持多标签训练")
        print("  - 需要修改掩码生成逻辑")
        print("  - 建议使用 create_multilabel_segmentation_mask 方法")
    
    print("\n配置建议:")
    print("1. 在 configs/railway_track_config.yaml 中设置 use_multilabel: true")
    print("2. 使用 multilabel_combined_loss 作为损失函数")
    print("3. 确保模型使用 sigmoid 激活函数")


if __name__ == '__main__':
    main()