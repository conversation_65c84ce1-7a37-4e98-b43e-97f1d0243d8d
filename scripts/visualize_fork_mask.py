#!/usr/bin/env python3
"""
可视化包含分叉轨道的掩码
"""

import numpy as np
import cv2
from pathlib import Path

# 加载掩码
mask_path = Path('/home/<USER>/data/railway_track_dataset/train/masks/20250119131944202.near.avi_frame_1743.npy')
mask = np.load(mask_path)

print(f"掩码形状: {mask.shape}")
print(f"背景像素: {np.sum(mask[:, :, 0] > 0)}")
print(f"主轨道像素: {np.sum(mask[:, :, 1] > 0)}")
print(f"分叉轨道像素: {np.sum(mask[:, :, 2] > 0)}")
print(f"重叠像素: {np.sum(np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0))}")

# 创建可视化
vis_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)

# 主轨道为绿色
vis_mask[:, :, 1] = (mask[:, :, 1] > 0).astype(np.uint8) * 255

# 分叉轨道为红色
vis_mask[:, :, 0] = (mask[:, :, 2] > 0).astype(np.uint8) * 255

# 重叠区域为黄色
overlap = np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
vis_mask[overlap] = [255, 255, 0]

# 保存可视化
output_path = 'fork_track_visualization.png'
cv2.imwrite(output_path, vis_mask)
print(f"\n已保存可视化到: {output_path}")

# 检查对应的图像是否存在
image_path = Path('/home/<USER>/data/railway_track_dataset/train/images/20250119131944202.near.avi_frame_1743.png')
if image_path.exists():
    # 加载原始图像
    image = cv2.imread(str(image_path))
    
    # 创建叠加显示
    overlay = image.copy()
    mask_indices = np.logical_or(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
    overlay[mask_indices] = overlay[mask_indices] * 0.5 + vis_mask[mask_indices] * 0.5
    
    # 保存叠加图像
    overlay_path = 'fork_track_overlay.png'
    cv2.imwrite(overlay_path, overlay)
    print(f"已保存叠加图像到: {overlay_path}")
else:
    print("未找到对应的原始图像")