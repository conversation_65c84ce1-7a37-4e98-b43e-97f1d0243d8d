#!/usr/bin/env python
"""
修复掩码生成脚本
重新从JSON标注生成正确的掩码文件
现在使用完整多边形算法，严格连接每个关键点形成边界
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from tqdm import tqdm
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_single_mask_generation(json_file: Path, output_dir: Path):
    """测试单个掩码的生成"""
    print(f"\n=== 测试单个完整多边形掩码生成 ===")
    print(f"JSON文件: {json_file.name}")
    
    # 创建解析器（现在使用完整多边形算法，严格连接每个关键点）
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  相机类型: {annotation_data['camera_type']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    for i, track in enumerate(annotation_data['tracks']):
        points_count = len(track['points'])
        print(f"    轨道{i+1}: {track['label']}, 类别ID: {track['class_id']}, {points_count} 个关键点")
        
        # 说明现在使用所有关键点
        if points_count >= 3:
            print(f"      将使用所有{points_count}个关键点形成边界（不使用凸包）")
        else:
            print(f"      警告：关键点不足3个，无法生成多边形")
    
    # 设置图像尺寸（根据原始图像）
    image_shape = (1080, 1920)  # 从诊断中获得的尺寸
    
    # 生成掩码（现在使用完整多边形算法）
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    print(f"生成的掩码:")
    print(f"  形状: {mask.shape}")
    print(f"  数据类型: {mask.dtype}")
    print(f"  各通道唯一值:")
    for c in range(mask.shape[2]):
        unique_vals = np.unique(mask[:, :, c])
        print(f"    通道{c}: {unique_vals}")
    
    # 转换为可视化友好的单通道掩码
    # 背景=0, 主轨道=85(绿色), 分叉轨道=170(红色)
    merged_mask = np.argmax(mask, axis=2)
    visual_mask = np.zeros_like(merged_mask, dtype=np.uint8)
    visual_mask[merged_mask == 1] = 85   # 主轨道(标签1) -> 绿色值
    visual_mask[merged_mask == 2] = 170  # 分叉轨道(标签2) -> 红色值
    # 背景保持为0
    
    print(f"可视化掩码:")
    print(f"  唯一值: {np.unique(visual_mask)}")
    print(f"  背景(0): {np.sum(visual_mask == 0)} 像素")
    print(f"  主轨道(85,标签1): {np.sum(visual_mask == 85)} 像素") 
    print(f"  分叉轨道(170,标签2): {np.sum(visual_mask == 170)} 像素")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 显示各个通道
    channel_names = ['背景', '主轨道', '分叉轨道']
    for c in range(3):
        row, col = c // 2, c % 2
        axes[row, col].imshow(mask[:, :, c], cmap='viridis')
        axes[row, col].set_title(f'{channel_names[c]} (通道{c})')
        axes[row, col].axis('off')
    
    # 合并显示 - 使用自定义颜色映射
    from matplotlib.colors import ListedColormap
    colors = ['black', 'green', 'red']  # 背景=黑色, 主轨道=绿色, 分叉轨道=红色
    cmap = ListedColormap(colors[:len(np.unique(merged_mask))])
    
    axes[1, 1].imshow(merged_mask, cmap=cmap, vmin=0, vmax=2)
    axes[1, 1].set_title('合并掩码\n(绿色=主轨道完整多边形, 红色=分叉轨道完整多边形)')
    axes[1, 1].axis('off')
    
    plt.suptitle(f'完整多边形掩码生成测试: {json_file.name}', fontsize=14)
    plt.tight_layout()
    
    # 保存测试结果
    test_output_path = output_dir / f'full_polygon_test_mask_{json_file.stem}.png'
    plt.savefig(test_output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"测试结果保存到: {test_output_path}")
    
    # 保存掩码文件（使用可视化友好的值）
    mask_output_path = output_dir / f'full_polygon_generated_mask_{json_file.stem}.png'
    cv2.imwrite(str(mask_output_path), visual_mask)
    
    print(f"生成的完整多边形掩码保存到: {mask_output_path}")
    
    return visual_mask, annotation_data


def regenerate_masks_batch(json_dir: Path, images_dir: Path, output_masks_dir: Path, 
                          max_files: int = None):
    """批量重新生成完整多边形掩码"""
    print(f"\n=== 批量重新生成完整多边形掩码 ===")
    print(f"JSON目录: {json_dir}")
    print(f"图像目录: {images_dir}")
    print(f"输出目录: {output_masks_dir}")
    print(f"算法: 完整多边形（严格连接每个关键点）")
    
    # 创建输出目录
    output_masks_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取JSON文件
    json_files = list(json_dir.glob('*.json'))
    if max_files:
        json_files = json_files[:max_files]
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 创建解析器（现在使用完整多边形算法）
    parser = RailwayAnnotationParser()
    
    successful_count = 0
    failed_count = 0
    total_main_points = 0
    total_fork_points = 0
    
    for json_file in tqdm(json_files, desc="生成完整多边形掩码"):
        try:
            # 解析JSON
            annotation_data = parser.parse_json_file(json_file)
            
            # 统计关键点使用情况
            main_points_count = 0
            fork_points_count = 0
            for track in annotation_data['tracks']:
                if track['label'] in ['Main_Left', 'Main_Right']:
                    main_points_count += len(track['points'])
                elif track['label'] in ['Fork_Left', 'Fork_Right']:
                    fork_points_count += len(track['points'])
            
            total_main_points += main_points_count
            total_fork_points += fork_points_count
            
            # 构建图像路径
            image_filename = annotation_data['filename']
            image_path = images_dir / image_filename
            
            # 检查图像是否存在
            if not image_path.exists():
                failed_count += 1
                continue
            
            # 读取图像获取尺寸
            image = cv2.imread(str(image_path))
            if image is None:
                failed_count += 1
                continue
            
            image_shape = image.shape[:2]
            
            # 生成完整多边形掩码
            mask = parser.create_segmentation_mask(annotation_data, image_shape)
            
            # 转换为可视化友好的单通道掩码
            # 背景=0, 主轨道=85(绿色), 分叉轨道=170(红色)
            merged_mask = np.argmax(mask, axis=2)
            visual_mask = np.zeros_like(merged_mask, dtype=np.uint8)
            visual_mask[merged_mask == 1] = 85   # 主轨道(标签1) -> 绿色值
            visual_mask[merged_mask == 2] = 170  # 分叉轨道(标签2) -> 红色值
            
            # 保存掩码
            mask_path = output_masks_dir / image_filename
            cv2.imwrite(str(mask_path), visual_mask)
            
            successful_count += 1
            
        except Exception as e:
            print(f"处理 {json_file.name} 时出错: {e}")
            failed_count += 1
    
    print(f"\n完整多边形掩码生成完成:")
    print(f"  成功: {successful_count}")
    print(f"  失败: {failed_count}")
    print(f"  总计使用的主轨道关键点: {total_main_points}")
    print(f"  总计使用的分叉轨道关键点: {total_fork_points}")
    print(f"  平均每个文件的主轨道关键点: {total_main_points/max(successful_count,1):.1f}")
    
    # 保存处理报告
    report_path = output_masks_dir.parent / 'processing_report.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("完整多边形掩码生成报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"算法: 完整多边形（严格连接每个关键点）\n")
        f.write(f"处理文件数: {len(json_files)}\n")
        f.write(f"成功生成: {successful_count}\n")
        f.write(f"失败: {failed_count}\n")
        f.write(f"成功率: {successful_count/max(len(json_files),1)*100:.1f}%\n\n")
        f.write(f"关键点统计:\n")
        f.write(f"  主轨道关键点总数: {total_main_points}\n")
        f.write(f"  分叉轨道关键点总数: {total_fork_points}\n")
        f.write(f"  平均每文件主轨道关键点: {total_main_points/max(successful_count,1):.1f}\n")
        f.write(f"  平均每文件分叉轨道关键点: {total_fork_points/max(successful_count,1):.1f}\n\n")
        f.write("特性:\n")
        f.write("- 使用所有关键点形成边界（不使用凸包）\n")
        f.write("- 主轨道和分叉轨道使用不同标签\n")
        f.write("- 左右轨道合并为统一区域\n")
        f.write("- 严格连接每个关键点\n")
    
    print(f"处理报告保存到: {report_path}")
    
    return successful_count, failed_count


def verify_regenerated_masks(masks_dir: Path, num_samples: int = 10):
    """验证重新生成的掩码"""
    print(f"\n=== 验证重新生成的掩码 ===")
    
    mask_files = list(masks_dir.glob('*.png'))
    sample_files = mask_files[:num_samples]
    
    print(f"验证 {len(sample_files)} 个掩码文件...")
    
    stats = []
    
    for mask_path in sample_files:
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if mask is None:
            continue
        
        stat = {
            'filename': mask_path.name,
            'shape': mask.shape,
            'unique_values': np.unique(mask).tolist(),
            'mean': mask.mean(),
            'sum': mask.sum(),
            'non_zero_pixels': np.count_nonzero(mask)
        }
        stats.append(stat)
        
        print(f"{mask_path.name}:")
        print(f"  唯一值: {stat['unique_values']}")
        print(f"  非零像素: {stat['non_zero_pixels']}")
        print(f"  均值: {stat['mean']:.4f}")
    
    # 检查多样性
    unique_sums = set(s['sum'] for s in stats)
    print(f"\n多样性检查:")
    print(f"  不同的像素总和数量: {len(unique_sums)}")
    
    if len(unique_sums) == 1:
        print("  ⚠️  所有掩码仍然相同")
    elif len(unique_sums) > len(stats) * 0.8:
        print("  ✅ 掩码显示良好的多样性")
    else:
        print("  ⚠️  掩码多样性较低")
    
    return stats


def create_comparison_visualization(original_masks_dir: Path, new_masks_dir: Path, 
                                   output_dir: Path, num_samples: int = 6):
    """创建对比可视化"""
    print(f"\n=== 创建对比可视化 ===")
    
    # 获取样本文件
    original_files = list(original_masks_dir.glob('*.png'))[:num_samples]
    
    fig, axes = plt.subplots(3, num_samples, figsize=(num_samples * 3, 9))
    
    # 创建自定义颜色映射
    from matplotlib.colors import ListedColormap
    colors = ['black', 'green', 'red']  # 背景=黑色, 主轨道=绿色, 分叉轨道=红色
    cmap = ListedColormap(colors)
    
    for i, original_path in enumerate(original_files):
        if i >= num_samples:
            break
        
        # 读取原始掩码
        original_mask = cv2.imread(str(original_path), cv2.IMREAD_GRAYSCALE)
        
        # 读取新掩码
        new_path = new_masks_dir / original_path.name
        if new_path.exists():
            new_mask = cv2.imread(str(new_path), cv2.IMREAD_GRAYSCALE)
        else:
            new_mask = np.zeros_like(original_mask) if original_mask is not None else None
        
        # 显示原始掩码
        if original_mask is not None:
            axes[0, i].imshow(original_mask, cmap='gray', vmin=0, vmax=255)
            axes[0, i].set_title(f'原始\n唯一值: {np.unique(original_mask)}')
        axes[0, i].axis('off')
        
        # 显示新掩码 - 使用自定义颜色映射
        if new_mask is not None:
            # 将像素值映射到类别索引
            display_mask = np.zeros_like(new_mask)
            display_mask[new_mask == 0] = 0    # 背景
            display_mask[new_mask == 85] = 1   # 主轨道 -> 绿色
            display_mask[new_mask == 170] = 2  # 分叉轨道 -> 红色
            
            axes[1, i].imshow(display_mask, cmap=cmap, vmin=0, vmax=2)
            unique_vals = np.unique(new_mask)
            track_pixels = np.sum(new_mask > 0)
            axes[1, i].set_title(f'重新生成\n轨道像素: {track_pixels}')
        axes[1, i].axis('off')
        
        # 显示差异
        if original_mask is not None and new_mask is not None:
            # 将新掩码转换为二值掩码来计算差异
            new_binary = (new_mask > 0).astype(np.uint8) * 255
            diff = np.abs(original_mask.astype(int) - new_binary.astype(int))
            axes[2, i].imshow(diff, cmap='hot')
            axes[2, i].set_title(f'差异\n最大差异: {diff.max()}')
        axes[2, i].axis('off')
    
    plt.suptitle('掩码对比：原始(灰度) vs 重新生成(绿=主轨道,红=分叉轨道)', fontsize=16)
    plt.tight_layout()
    
    comparison_path = output_dir / 'mask_comparison.png'
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"对比结果保存到: {comparison_path}")


def main():
    parser = argparse.ArgumentParser(description='修复掩码生成')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='JSON标注目录')
    parser.add_argument('--images-dir', type=str,
                       default='/home/<USER>/data/Download/railway_track_dataset/images/test',
                       help='图像目录')
    parser.add_argument('--original-masks-dir', type=str,
                       default='/home/<USER>/data/Download/railway_track_dataset/masks/test',
                       help='原始掩码目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/mask_fix',
                       help='输出目录')
    parser.add_argument('--max-files', type=int, default=50,
                       help='最大处理文件数（用于测试）')
    parser.add_argument('--test-single', action='store_true',
                       help='只测试单个文件')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    json_dir = Path(args.json_dir)
    images_dir = Path(args.images_dir)
    original_masks_dir = Path(args.original_masks_dir)
    
    print("掩码修复开始...")
    print(f"JSON目录: {json_dir}")
    print(f"图像目录: {images_dir}")
    print(f"原始掩码目录: {original_masks_dir}")
    print(f"输出目录: {output_dir}")
    
    if args.test_single:
        # 测试单个文件
        json_files = list(json_dir.glob('*.json'))
        if len(json_files) > 0:
            test_single_mask_generation(json_files[0], output_dir)
    else:
        # 批量处理
        new_masks_dir = output_dir / 'regenerated_masks'
        
        # 重新生成掩码
        successful_count, failed_count = regenerate_masks_batch(
            json_dir, images_dir, new_masks_dir, args.max_files
        )
        
        if successful_count > 0:
            # 验证结果
            verify_regenerated_masks(new_masks_dir)
            
            # 创建对比可视化
            create_comparison_visualization(
                original_masks_dir, new_masks_dir, output_dir
            )
        
        print(f"\n修复完成！")
        print(f"重新生成的掩码保存在: {new_masks_dir}")


if __name__ == '__main__':
    main() 