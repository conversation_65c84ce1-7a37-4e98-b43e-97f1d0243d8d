#!/usr/bin/env python
"""
对比新旧算法效果
展示平行轨道线算法 vs 凸包算法的差异
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import json

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def convex_hull_algorithm(points, image_shape, class_id):
    """旧算法：凸包算法"""
    mask = np.zeros(image_shape, dtype=np.uint8)
    if len(points) < 3:
        return mask
    
    points_int = np.array([(int(x), int(y)) for x, y in points], dtype=np.int32)
    hull = cv2.convexHull(points_int)
    cv2.fillPoly(mask, [hull], class_id)
    return mask


def parallel_lines_algorithm(left_points, right_points, image_shape, class_id):
    """新算法：平行轨道线算法"""
    mask = np.zeros(image_shape, dtype=np.uint8)
    if len(left_points) < 2 or len(right_points) < 2:
        return mask
    
    # 构建轨道多边形：左线 + 右线反向
    polygon_points = []
    polygon_points.extend(left_points)
    polygon_points.extend(reversed(right_points))
    
    points_int = np.array([(int(x), int(y)) for x, y in polygon_points], dtype=np.int32)
    cv2.fillPoly(mask, [points_int], class_id)
    return mask


def compare_algorithms():
    """对比新旧算法"""
    print("=== 对比新旧算法效果 ===")
    
    # 设置测试参数
    json_dir = Path('data/railway_annotation_6mm')
    output_dir = Path('outputs/algorithm_comparison')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 获取测试文件
    json_files = list(json_dir.glob('*.json'))
    test_file = json_files[0]
    print(f"测试文件: {test_file.name}")
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(test_file)
    
    # 提取轨道数据
    main_left = []
    main_right = []
    
    for track in annotation_data['tracks']:
        if track['label'] == 'Main_Left':
            main_left = track['points']
        elif track['label'] == 'Main_Right':
            main_right = track['points']
    
    if not main_left or not main_right:
        print("❌ 没有找到主轨道配对")
        return
    
    # 图像尺寸
    image_shape = (1080, 1920)
    
    print(f"轨道数据:")
    print(f"  Main_Left: {len(main_left)} 个关键点")
    print(f"  Main_Right: {len(main_right)} 个关键点")
    print()
    
    # 方案1：旧算法 - 合并所有关键点用凸包
    print("=== 方案1：旧算法（合并关键点 + 凸包）===")
    all_points = main_left + main_right
    old_mask = convex_hull_algorithm(all_points, image_shape, 1)
    old_pixels = np.sum(old_mask > 0)
    print(f"总关键点数: {len(all_points)}")
    print(f"凸包像素数: {old_pixels:,}")
    
    # 方案2：新算法 - 平行轨道线算法
    print("\n=== 方案2：新算法（平行轨道线）===")
    new_mask = parallel_lines_algorithm(main_left, main_right, image_shape, 1)
    new_pixels = np.sum(new_mask > 0)
    print(f"轨道多边形像素数: {new_pixels:,}")
    
    # 计算差异
    improvement = (new_pixels - old_pixels) / old_pixels * 100
    print(f"\n📊 算法对比:")
    print(f"  旧算法像素数: {old_pixels:,}")
    print(f"  新算法像素数: {new_pixels:,}")
    print(f"  像素增长: {improvement:+.1f}%")
    print(f"  覆盖率: {new_pixels/old_pixels:.1f}x")
    
    # 创建可视化对比
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 第一行：原始数据和算法构造
    # 原始轨道线
    axes[0, 0].set_xlim(0, 1920)
    axes[0, 0].set_ylim(1080, 0)
    axes[0, 0].set_title('Original Track Lines', fontsize=12)
    
    if main_left:
        left_x, left_y = zip(*main_left)
        axes[0, 0].plot(left_x, left_y, 'g-o', label=f'Main_Left ({len(main_left)} points)', markersize=4)
    
    if main_right:
        right_x, right_y = zip(*main_right)
        axes[0, 0].plot(right_x, right_y, 'b-o', label=f'Main_Right ({len(main_right)} points)', markersize=4)
    
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 旧算法构造
    axes[0, 1].set_xlim(0, 1920)
    axes[0, 1].set_ylim(1080, 0)
    axes[0, 1].set_title('Old Algorithm: Convex Hull', fontsize=12)
    
    # 显示所有点
    all_x, all_y = zip(*all_points)
    axes[0, 1].scatter(all_x, all_y, c='red', s=20, alpha=0.7, label=f'All Points ({len(all_points)})')
    
    # 显示凸包
    points_int = np.array([(int(x), int(y)) for x, y in all_points], dtype=np.int32)
    hull = cv2.convexHull(points_int)
    hull_points = [(p[0][0], p[0][1]) for p in hull]
    if hull_points:
        hull_x, hull_y = zip(*hull_points)
        axes[0, 1].plot(hull_x + (hull_x[0],), hull_y + (hull_y[0],), 'k-', linewidth=2, label=f'Convex Hull ({len(hull_points)} vertices)')
    
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 新算法构造
    axes[0, 2].set_xlim(0, 1920)
    axes[0, 2].set_ylim(1080, 0)
    axes[0, 2].set_title('New Algorithm: Parallel Lines', fontsize=12)
    
    # 显示原始线
    axes[0, 2].plot(left_x, left_y, 'g--', alpha=0.7, label='Main_Left')
    axes[0, 2].plot(right_x, right_y, 'b--', alpha=0.7, label='Main_Right')
    
    # 显示多边形边界
    polygon_points = list(main_left) + list(reversed(main_right))
    poly_x, poly_y = zip(*polygon_points)
    axes[0, 2].plot(poly_x + (poly_x[0],), poly_y + (poly_y[0],), 'k-', linewidth=2, 
                   label=f'Track Polygon ({len(polygon_points)} vertices)')
    
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 第二行：生成的掩码和统计
    # 旧算法掩码
    axes[1, 0].imshow(old_mask, cmap='Greens', vmin=0, vmax=1)
    axes[1, 0].set_title(f'Old Algorithm Mask\n{old_pixels:,} pixels', fontsize=12)
    axes[1, 0].axis('off')
    
    # 新算法掩码
    axes[1, 1].imshow(new_mask, cmap='Blues', vmin=0, vmax=1)
    axes[1, 1].set_title(f'New Algorithm Mask\n{new_pixels:,} pixels', fontsize=12)
    axes[1, 1].axis('off')
    
    # 差异统计
    algorithms = ['Old\n(Convex Hull)', 'New\n(Parallel Lines)']
    pixels = [old_pixels, new_pixels]
    colors = ['lightcoral', 'lightblue']
    
    bars = axes[1, 2].bar(algorithms, pixels, color=colors)
    axes[1, 2].set_title('Pixel Count Comparison', fontsize=12)
    axes[1, 2].set_ylabel('Pixels')
    
    # 添加数值标签
    for bar, pixel_count in zip(bars, pixels):
        height = bar.get_height()
        axes[1, 2].text(bar.get_x() + bar.get_width()/2., height + 2000,
                       f'{pixel_count:,}', ha='center', va='bottom')
    
    # 添加增长标签
    axes[1, 2].text(0.5, max(pixels) * 0.8, f'Improvement:\n{improvement:+.1f}%', 
                   ha='center', va='center', transform=axes[1, 2].transData,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.suptitle(f'Algorithm Comparison: {test_file.stem}', fontsize=16)
    plt.tight_layout()
    
    # 保存对比结果
    output_path = output_dir / f'algorithm_comparison_{test_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n对比结果保存到: {output_path}")
    
    # 保存详细报告
    report_path = output_dir / f'comparison_report_{test_file.stem}.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 算法对比报告\n\n")
        f.write(f"**测试文件**: {test_file.name}\n\n")
        f.write(f"## 轨道数据\n")
        f.write(f"- Main_Left: {len(main_left)} 个关键点\n")
        f.write(f"- Main_Right: {len(main_right)} 个关键点\n")
        f.write(f"- 总关键点: {len(all_points)} 个\n\n")
        f.write(f"## 算法对比\n\n")
        f.write(f"### 旧算法（凸包）\n")
        f.write(f"- 方法: 合并所有关键点，计算凸包\n")
        f.write(f"- 顶点数: {len(hull_points)} 个\n")
        f.write(f"- 像素数: {old_pixels:,}\n")
        f.write(f"- 覆盖率: {old_pixels/np.prod(image_shape)*100:.2f}%\n\n")
        f.write(f"### 新算法（平行轨道线）\n")
        f.write(f"- 方法: 从左右两条线构建轨道多边形\n")
        f.write(f"- 顶点数: {len(polygon_points)} 个\n")
        f.write(f"- 像素数: {new_pixels:,}\n")
        f.write(f"- 覆盖率: {new_pixels/np.prod(image_shape)*100:.2f}%\n\n")
        f.write(f"## 改进效果\n")
        f.write(f"- 像素增长: **{improvement:+.1f}%**\n")
        f.write(f"- 覆盖率倍数: **{new_pixels/old_pixels:.1f}x**\n")
        f.write(f"- 关键点利用率: **100%** (新算法使用所有关键点)\n")
        f.write(f"- 轨道表示: **更精确** (符合实际轨道几何形状)\n")
    
    print(f"详细报告保存到: {report_path}")
    
    return old_pixels, new_pixels, improvement


if __name__ == '__main__':
    compare_algorithms()
    print("\n✅ 算法对比完成！") 