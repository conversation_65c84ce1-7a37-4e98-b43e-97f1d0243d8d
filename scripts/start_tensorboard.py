#!/usr/bin/env python3
"""
启动TensorBoard服务的脚本
"""

import argparse
import subprocess
import sys
from pathlib import Path
import os


def parse_args():
    parser = argparse.ArgumentParser(description='启动TensorBoard服务')
    parser.add_argument(
        '--logdir',
        type=str,
        default='models/checkpoints',
        help='TensorBoard日志目录 (默认: models/checkpoints)'
    )
    parser.add_argument(
        '--port',
        type=int,
        default=6006,
        help='TensorBoard端口 (默认: 6006)'
    )
    parser.add_argument(
        '--host',
        type=str,
        default='localhost',
        help='TensorBoard主机地址 (默认: localhost)'
    )
    parser.add_argument(
        '--reload-interval',
        type=int,
        default=30,
        help='重新加载间隔（秒）(默认: 30)'
    )
    parser.add_argument(
        '--experiment',
        type=str,
        default=None,
        help='指定特定实验目录'
    )
    
    return parser.parse_args()


def find_tensorboard_logs(base_dir: Path, experiment: str = None) -> Path:
    """
    查找TensorBoard日志目录
    
    Args:
        base_dir: 基础目录
        experiment: 实验名称
        
    Returns:
        TensorBoard日志目录路径
    """
    if experiment:
        # 查找特定实验的tensorboard目录
        experiment_dir = base_dir / experiment
        if experiment_dir.exists():
            tensorboard_dir = experiment_dir / 'tensorboard'
            if tensorboard_dir.exists():
                return tensorboard_dir
            else:
                print(f"在 {experiment_dir} 中未找到tensorboard目录")
                return experiment_dir
        else:
            print(f"实验目录 {experiment_dir} 不存在")
            return base_dir
    else:
        # 查找所有tensorboard目录
        tensorboard_dirs = list(base_dir.glob('*/tensorboard'))
        if tensorboard_dirs:
            print(f"找到以下TensorBoard日志目录:")
            for i, tb_dir in enumerate(tensorboard_dirs):
                print(f"  {i+1}. {tb_dir}")
            return base_dir
        else:
            print(f"在 {base_dir} 中未找到任何tensorboard目录")
            return base_dir


def main():
    args = parse_args()
    
    # 检查logdir是否存在
    logdir = Path(args.logdir)
    if not logdir.exists():
        print(f"错误: 日志目录 {logdir} 不存在")
        sys.exit(1)
    
    # 查找TensorBoard日志
    tensorboard_logdir = find_tensorboard_logs(logdir, args.experiment)
    
    # 构建TensorBoard命令
    cmd = [
        'tensorboard',
        '--logdir', str(tensorboard_logdir),
        '--port', str(args.port),
        '--host', args.host,
        '--reload_interval', str(args.reload_interval),
        '--load_fast', 'false'  # 加载所有数据
    ]
    
    print(f"启动TensorBoard...")
    print(f"日志目录: {tensorboard_logdir}")
    print(f"访问地址: http://{args.host}:{args.port}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # 启动TensorBoard
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"启动TensorBoard失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nTensorBoard已停止")
    except FileNotFoundError:
        print("错误: 未找到tensorboard命令")
        print("请安装tensorboard: pip install tensorboard")
        sys.exit(1)


if __name__ == '__main__':
    main() 