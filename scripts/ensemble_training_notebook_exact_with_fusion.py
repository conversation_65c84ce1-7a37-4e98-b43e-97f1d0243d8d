#!/usr/bin/env python3
"""
集成训练脚本 - 增强版：训练 + 集成学习
基于 ensemble_training_notebook_exact.py，增加集成权重优化功能
完成单模型训练后自动进行集成学习
"""

import os
import sys
import torch
import torch.nn as nn
import segmentation_models_pytorch as smp
from pathlib import Path
import argparse
import numpy as np
import matplotlib.pyplot as plt
import dill
import time
import random
import gc
import json
from datetime import datetime
from collections import namedtuple
from torch.utils.data import DataLoader
import yaml
from tqdm import tqdm
import io

# 🔥 GPU利用率稳定化配置（兼容所有PyTorch版本）
os.environ.update({
    'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:128,roundup_power2_divisions:16',
    'CUDA_LAUNCH_BLOCKING': '0',  # 异步执行提高GPU利用率
    'TORCH_CUDNN_V8_API_ENABLED': '1',
    'OMP_NUM_THREADS': '6',  # 控制CPU线程数
    'MKL_NUM_THREADS': '6',
    'TOKENIZERS_PARALLELISM': 'false',
    'DISABLE_VISUALIZATION': '0'  # 🔥 启用可视化（已修复matplotlib错误）
})

# 🔥 CUDA优化设置
torch.backends.cudnn.benchmark = True  # 自动优化算法选择
torch.backends.cudnn.deterministic = False  # 允许非确定性以提高性能
torch.backends.cudnn.allow_tf32 = True
torch.backends.cuda.matmul.allow_tf32 = True

# 设置线程数
torch.set_num_threads(6)

# 添加项目根目录到系统路径（与其他脚本保持一致）
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 使用与当前目录一致的导入方式
from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.data.augmentations import get_train_transform, get_val_transform
from src.utils.metrics import iou_coef
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.utils import RunningAverage
from src.train import train

def safe_tensor_for_metrics(tensor):
    """
    🔥 安全地转换张量以兼容channels_last格式的metrics计算
    """
    if tensor.is_contiguous(memory_format=torch.channels_last):
        # 如果是channels_last格式，转换为连续格式以确保metrics计算正确
        return tensor.contiguous()
    return tensor

# 设置matplotlib样式（与notebook保持一致）
plt.style.use('ggplot')

# 定义EpochStats（与notebook保持一致）
EpochStats = namedtuple('EpochStats', 'epoch learning_rate train_loss val_loss val_jac time')

class CustomUnpickler(torch.serialization.pickle.Unpickler):
    """自定义反序列化器，用于处理模块路径变更"""
    
    def find_class(self, module, name):
        # 处理旧版本的 PAN 模块路径映射
        if module == 'segmentation_models_pytorch.pan':
            module = 'segmentation_models_pytorch.decoders.pan'
        elif module == 'segmentation_models_pytorch.pan.model':
            module = 'segmentation_models_pytorch.decoders.pan.model'
        elif module.startswith('segmentation_models_pytorch.pan.'):
            # 替换所有以 pan. 开头的子模块
            module = module.replace('segmentation_models_pytorch.pan.', 
                                   'segmentation_models_pytorch.decoders.pan.')
        
        # 处理 timm 库的路径变更
        elif module == 'timm.models.layers.conv2d_same':
            module = 'timm.layers.conv2d_same'
        elif module == 'timm.models.layers':
            module = 'timm.layers'
        elif module.startswith('timm.models.layers.'):
            # 替换所有 timm.models.layers. 为 timm.layers.
            module = module.replace('timm.models.layers.', 'timm.layers.')
        elif module == 'timm.models.efficientnet_blocks':
            # EfficientNet blocks 在新版本中的位置
            module = 'timm.models._efficientnet_blocks'
        elif module == 'timm.models.efficientnet_builder':
            module = 'timm.models._efficientnet_builder'
        
        return super().find_class(module, name)

def load_pretrained_model(model_path, device='cuda'):
    """
    加载预训练模型，自动修复模块路径问题
    
    Args:
        model_path: 模型文件路径
        device: 设备
        
    Returns:
        加载的模型
    """
    print(f"正在加载模型: {model_path}")
    
    try:
        # 方法1: 先尝试标准加载（适用于新格式）
        model = torch.load(model_path, map_location=device, weights_only=False)
        print("✅ 标准方法加载成功")
        return model
        
    except Exception as e:
        print(f"标准方法失败: {e}")
        
        if "No module named 'segmentation_models_pytorch.pan'" in str(e):
            print("🔧 检测到模块路径问题，尝试修复...")
            
            try:
                # 方法2: 使用自定义反序列化器
                # 首先尝试 ZIP 格式（PyTorch 1.6+）
                import zipfile
                
                # 检查是否是zip格式
                if zipfile.is_zipfile(model_path):
                    print("检测到ZIP格式，使用zip方法修复...")
                    
                    # 创建临时解压环境
                    with zipfile.ZipFile(model_path, 'r') as zip_ref:
                        # 读取 data.pkl
                        with zip_ref.open('archive/data.pkl') as pkl_file:
                            buffer = io.BytesIO(pkl_file.read())
                    
                    # 使用标准torch.load，但指定pickle_module
                    class PickleModule:
                        Unpickler = CustomUnpickler
                    
                    # 关闭zip文件，重新用torch.load加载
                    model = torch.load(model_path, map_location=device, 
                                     pickle_module=PickleModule, weights_only=False)
                    print("✅ ZIP格式修复成功!")
                    
                else:
                    print("检测到Legacy Pickle格式，使用pickle方法修复...")
                    # Legacy pickle 格式
                    with open(model_path, 'rb') as f:
                        buffer = io.BytesIO(f.read())
                    
                    unpickler = CustomUnpickler(buffer)
                    
                    # 处理持久化存储
                    def persistent_load(saved_id):
                        if isinstance(saved_id, tuple):
                            # PyTorch tensor 存储格式
                            if len(saved_id) == 2:
                                return torch._utils._rebuild_tensor_v2(*saved_id)
                            else:
                                return torch.storage._load_from_bytes(*saved_id)
                        return saved_id
                    
                    unpickler.persistent_load = persistent_load
                    model = unpickler.load()
                    
                    # 移动到正确设备
                    if hasattr(model, 'to'):
                        model = model.to(device)
                    
                    print("✅ Legacy格式修复成功!")
                
                return model
                
            except Exception as e2:
                print(f"自定义方法也失败: {e2}")
                
                try:
                    # 方法3: 尝试使用 encoding='latin-1'
                    print("尝试使用latin-1编码...")
                    
                    import pickle
                    with open(model_path, 'rb') as f:
                        model = pickle.load(f, encoding='latin-1')
                    
                    if hasattr(model, 'to'):
                        model = model.to(device)
                    
                    print("✅ latin-1编码加载成功!")
                    return model
                    
                except Exception as e3:
                    print(f"所有方法都失败: {e3}")
                    raise e3
        else:
            # 其他类型的错误直接抛出
            raise e

def setup_seed(seed=0):
    """设置随机种子（与notebook保持一致）"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    random.seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def train_with_checkpoint(model, optimizer, criterion, train_loader, val_loader, 
                         epochs, lr_scheduler, weights_path, 
                         checkpoint_dir=None, resume_from=None,
                         accumulate_every_n_epochs=2, clip_gradient=True,
                         model_name='model'):
    """
    训练函数 - 基于notebook的train函数，添加checkpoint功能
    
    每个epoch保存checkpoint，支持断点续训
    """
    device = torch.device('cuda')
    model.to(device)
    scaler = torch.cuda.amp.GradScaler()
    history = []
    best_val_jac = 0.0
    start_epoch = 0
    
    # 创建checkpoint目录
    if checkpoint_dir is None:
        checkpoint_dir = Path(weights_path).parent / 'checkpoints'
    checkpoint_dir = Path(checkpoint_dir)
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建可视化目录
    vis_dir = Path(weights_path).parent / 'visualizations' / model_name
    vis_dir.mkdir(parents=True, exist_ok=True)
    
    # 恢复训练
    if resume_from and Path(resume_from).exists():
        print(f'Resuming from checkpoint: {resume_from}')
        checkpoint = torch.load(resume_from, map_location=device)
        
        # 恢复模型状态
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # 恢复学习率调度器
        if lr_scheduler is not None and 'scheduler_state_dict' in checkpoint:
            lr_scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        # 恢复训练状态
        start_epoch = checkpoint['epoch']
        best_val_jac = checkpoint.get('best_val_jac', 0.0)
        history = checkpoint.get('history', [])
        
        # 恢复随机状态
        if 'random_state' in checkpoint:
            torch.set_rng_state(checkpoint['random_state']['torch'])
            np.random.set_state(checkpoint['random_state']['numpy'])
            random.setstate(checkpoint['random_state']['python'])
            torch.cuda.set_rng_state(checkpoint['random_state']['cuda'])
        
        print(f'Resumed from epoch {start_epoch}, best val IoU: {best_val_jac:.4f}')
    
    # 训练循环（与notebook保持一致）
    for e in range(start_epoch, epochs):
        loss_avg = RunningAverage()
        val_jac_avg = RunningAverage()
        val_loss_avg = RunningAverage()
        
        # 🔥 GPU利用率优化：减少频繁的内存清理
        if e == 0 or (e + 1) % 10 == 0:  # 只在第一个epoch和每10个epoch清理
            torch.cuda.empty_cache()
            gc.collect()

        model.train()
        with tqdm(total=len(train_loader), leave=False, file=sys.stdout) as t:
            if lr_scheduler is not None:
                stats_current_lr = lr_scheduler.get_last_lr()[0]
            else:
                stats_current_lr = optimizer.param_groups[0]['lr']
            t.set_description(f'Epoch {e + 1}, LR {stats_current_lr:.6f}')
            
            for batch_n, batch_data in enumerate(train_loader):
                train_batch, labels_batch = batch_data['image'], batch_data['mask']

                # 🔥 GPU利用率优化：异步数据传输和内存格式优化
                train_batch = train_batch.to(device, non_blocking=True)
                labels_batch = labels_batch.to(device, non_blocking=True)

                # 转换为channels_last格式以提高GPU利用率
                if train_batch.dim() == 4:
                    train_batch = train_batch.to(memory_format=torch.channels_last)
                if labels_batch.dim() == 4:
                    labels_batch = labels_batch.to(memory_format=torch.channels_last)

                with torch.autocast(device_type='cuda'):
                    output_batch = model(train_batch)
                    loss = criterion(output_batch, labels_batch)

                    # 🔥 修复数值不稳定：检查损失是否为NaN或无穷大
                    if not torch.isfinite(loss):
                        print(f'⚠️  检测到非有限损失值: {loss.item()}, 跳过此batch')
                        continue

                    loss_avg.update(loss.item())
                    loss /= accumulate_every_n_epochs

                scaler.scale(loss).backward()

                if (batch_n + 1) % accumulate_every_n_epochs == 0:
                    if clip_gradient:
                        torch.nn.utils.clip_grad_norm_(model.parameters(), 1)
                    scaler.step(optimizer)
                    scaler.update()
                    optimizer.zero_grad(set_to_none=True)  # 更高效的梯度清零
                
                t.set_postfix({'stats': f'train_loss: {loss_avg():.4f}'})
                t.update()
                stats_time_elapsed = t.format_interval(t.format_dict['elapsed'])
        
        if lr_scheduler is not None:
            lr_scheduler.step()
        
        # 验证（与notebook保持一致）
        model.eval()

        # 🔥 修复可视化：从整个验证集随机选择图像进行可视化
        num_vis_to_save = 5  # 每个epoch保存5张可视化图像
        total_val_samples = len(val_loader.dataset)

        # 为当前epoch随机选择要可视化的样本索引
        np.random.seed(e + 42)  # 每个epoch使用不同的随机种子
        vis_sample_indices = np.random.choice(total_val_samples, num_vis_to_save, replace=False)
        vis_sample_indices = set(vis_sample_indices)  # 转换为集合以便快速查找

        print(f'  🎨 本epoch将可视化样本索引: {sorted(vis_sample_indices)}')

        # 用于跟踪当前处理的样本索引和已保存的可视化
        current_sample_idx = 0
        saved_vis_count = 0
        
        with torch.no_grad():
            for batch_idx, batch_data in enumerate(val_loader):
                val_batch, val_labels_batch = batch_data['image'], batch_data['mask']

                # 🔥 GPU利用率优化：验证时也使用异步传输和内存格式优化
                val_batch = val_batch.to(device, non_blocking=True)
                val_labels_batch = val_labels_batch.to(device, non_blocking=True)

                # 转换为channels_last格式
                if val_batch.dim() == 4:
                    val_batch = val_batch.to(memory_format=torch.channels_last)
                if val_labels_batch.dim() == 4:
                    val_labels_batch = val_labels_batch.to(memory_format=torch.channels_last)

                with torch.autocast(device_type='cuda'):  # 验证时也使用混合精度
                    val_output_batch = model(val_batch)
                    val_loss = criterion(val_output_batch, val_labels_batch)
                val_loss_avg.update(val_loss.item())
                
                val_predicted = torch.nn.Sigmoid()(val_output_batch)

                # 🔥 修复IoU计算：需要二值化预测结果
                # IoU计算需要二值化的预测结果，而不是连续值
                val_predicted_binary = (val_predicted > 0.5).float()

                # 🔥 修复channels_last兼容性：安全地转换张量用于metrics计算
                val_jac_batch = iou_coef(
                    safe_tensor_for_metrics(val_labels_batch),
                    safe_tensor_for_metrics(val_predicted_binary)
                )
                val_jac_avg.update(val_jac_batch.item())

                # 🔥 调试信息：检查数据范围
                if batch_idx == 0:  # 只在第一个batch打印调试信息
                    print(f'    🔍 调试信息:')
                    print(f'      标签范围: [{val_labels_batch.min():.3f}, {val_labels_batch.max():.3f}]')
                    print(f'      预测范围: [{val_predicted.min():.3f}, {val_predicted.max():.3f}]')
                    print(f'      二值化预测范围: [{val_predicted_binary.min():.3f}, {val_predicted_binary.max():.3f}]')
                    print(f'      当前batch IoU: {val_jac_batch.item():.4f}')
                
                # 🔥 修复可视化：检查当前batch中是否有需要可视化的样本
                batch_size = val_batch.shape[0]

                # 检查当前batch中的每个样本是否需要可视化
                for batch_sample_idx in range(batch_size):
                    global_sample_idx = current_sample_idx + batch_sample_idx

                    # 如果当前样本是选中要可视化的样本，且还没保存够5张
                    if global_sample_idx in vis_sample_indices and saved_vis_count < num_vis_to_save:
                        vis_idx = batch_sample_idx  # 在当前batch中的索引
                        # 获取单个样本
                        image = val_batch[vis_idx].cpu()
                        true_mask = val_labels_batch[vis_idx].cpu()
                        pred_mask = val_predicted[vis_idx].cpu()
                        
                        # 🔥 修复matplotlib错误：正确处理数据类型和数值范围
                        # 确保张量是连续的并转换为标准格式
                        image = image.contiguous()
                        true_mask = true_mask.contiguous()
                        pred_mask = pred_mask.contiguous()

                        # 反归一化图像到[0,1]范围
                        image = image * torch.tensor([0.229, 0.224, 0.225]).reshape(3, 1, 1)
                        image = image + torch.tensor([0.485, 0.456, 0.406]).reshape(3, 1, 1)
                        image = torch.clamp(image, 0, 1)

                        # 转换为numpy数组并确保正确的数据类型
                        image_np = image.contiguous().permute(1, 2, 0).numpy().astype(np.float32)
                        true_mask_np = true_mask.contiguous().permute(1, 2, 0).numpy().astype(np.float32)
                        pred_mask_np = pred_mask.contiguous().permute(1, 2, 0).numpy().astype(np.float32)

                        # 确保数值范围正确
                        image_np = np.clip(image_np, 0, 1)
                        true_mask_np = np.clip(true_mask_np, 0, 1)
                        pred_mask_np = np.clip(pred_mask_np, 0, 1)
                        
                        # 创建可视化
                        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
                        
                        # 原始图像
                        axes[0].imshow(image_np)
                        axes[0].set_title('Original Image')
                        axes[0].axis('off')
                        
                        # 🔥 修复matplotlib错误：确保RGB数组数据类型和维度正确
                        # 真实掩码 - 安全创建RGB可视化
                        true_rgb = np.zeros((true_mask_np.shape[0], true_mask_np.shape[1], 3), dtype=np.float32)
                        if true_mask_np.shape[2] >= 2:
                            true_rgb[:, :, 0] = np.clip(true_mask_np[:, :, 1], 0, 1)  # 主轨道 - 红色
                        if true_mask_np.shape[2] >= 3:
                            true_rgb[:, :, 1] = np.clip(true_mask_np[:, :, 2], 0, 1)  # 分叉轨道 - 绿色
                        axes[1].imshow(true_rgb)
                        axes[1].set_title('Ground Truth')
                        axes[1].axis('off')

                        # 预测掩码 - 安全创建RGB可视化
                        pred_rgb = np.zeros((pred_mask_np.shape[0], pred_mask_np.shape[1], 3), dtype=np.float32)
                        if pred_mask_np.shape[2] >= 2:
                            pred_rgb[:, :, 0] = np.clip(pred_mask_np[:, :, 1], 0, 1)  # 主轨道 - 红色
                        if pred_mask_np.shape[2] >= 3:
                            pred_rgb[:, :, 1] = np.clip(pred_mask_np[:, :, 2], 0, 1)  # 分叉轨道 - 绿色
                        axes[2].imshow(pred_rgb)
                        axes[2].set_title('Prediction')
                        axes[2].axis('off')

                        # 叠加显示 - 确保数据类型一致
                        overlay = image_np.copy().astype(np.float32)
                        mask_overlay = pred_rgb.astype(np.float32) * 0.5
                        overlay = np.clip(overlay + mask_overlay, 0, 1)
                        axes[3].imshow(overlay)
                        axes[3].set_title('Overlay')
                        axes[3].axis('off')
                        
                        plt.suptitle(f'Epoch {e+1} - Validation Sample - IoU: {val_jac_batch.item():.4f}')
                        plt.tight_layout()
                        
                        # 🔥 修复matplotlib错误：安全保存可视化
                        vis_filename = vis_dir / f'epoch_{e+1:03d}_sample_{global_sample_idx:05d}.png'
                        try:
                            plt.savefig(vis_filename, dpi=150, bbox_inches='tight')
                            plt.close()
                            saved_vis_count += 1
                            print(f'  💾 可视化已保存 ({saved_vis_count}/{num_vis_to_save}): {vis_filename}')
                        except Exception as save_error:
                            print(f'  ⚠️ 可视化保存失败: {save_error}')
                            print(f'     错误详情: {type(save_error).__name__}: {save_error}')
                            plt.close()

                # 更新当前样本索引
                current_sample_idx += batch_size

                # 如果已经保存了足够的可视化，可以提前结束（可选优化）
                if saved_vis_count >= num_vis_to_save:
                    break
        
        stats_epoch = EpochStats(epoch=e + 1,
                               learning_rate=stats_current_lr,
                               train_loss=loss_avg(),
                               val_loss=val_loss_avg(),
                               val_jac=val_jac_avg(),
                               time=stats_time_elapsed)
        history.append(stats_epoch)
        
        print(f'Epoch {stats_epoch.epoch}. LR {stats_epoch.learning_rate:.6f}, '
              f'train_loss: {stats_epoch.train_loss:.4f}, '
              f'val_loss: {stats_epoch.val_loss:.4f}, '
              f'val_jac: {stats_epoch.val_jac:.4f}, '
              f'time: {stats_epoch.time}')
        
        # 保存最佳模型（与notebook保持一致）
        current_val_jac = val_jac_avg()
        print(f'  📊 当前验证IoU: {current_val_jac:.4f}, 历史最佳: {best_val_jac:.4f}')

        if current_val_jac > best_val_jac:
            torch.save(model, weights_path)
            best_val_jac = current_val_jac
            print(f'  ✅ 保存新的最佳模型! IoU: {best_val_jac:.4f} -> {weights_path}')
        else:
            print(f'  📈 当前IoU未超过最佳值，不保存')
        
        # 每个epoch保存checkpoint（新增功能）
        checkpoint_path = checkpoint_dir / f'checkpoint_epoch_{e + 1}.pth'
        checkpoint = {
            'epoch': e + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'best_val_jac': best_val_jac,
            'history': history,
            'random_state': {
                'torch': torch.get_rng_state(),
                'numpy': np.random.get_state(),
                'python': random.getstate(),
                'cuda': torch.cuda.get_rng_state()
            }
        }
        
        if lr_scheduler is not None:
            checkpoint['scheduler_state_dict'] = lr_scheduler.state_dict()
        
        torch.save(checkpoint, checkpoint_path)
        print(f'  -> Saved checkpoint: {checkpoint_path}')

        # 🔥 保存所有checkpoint，不删除任何文件
        # 注释掉删除逻辑，保留所有epoch的checkpoint
        # all_checkpoints = sorted(checkpoint_dir.glob('checkpoint_epoch_*.pth'))
        # if len(all_checkpoints) > 5:
        #     for old_checkpoint in all_checkpoints[:-5]:
        #         old_checkpoint.unlink()
        print(f'  📁 所有checkpoint都已保存到: {checkpoint_dir}')
        
        # 🔥 GPU利用率优化：智能内存管理
        if (e + 1) % 15 == 0:  # 进一步减少清理频率，每15个epoch清理一次
            torch.cuda.empty_cache()
            gc.collect()

        # 🔥 GPU利用率监控：显示GPU使用情况
        if torch.cuda.is_available() and (e + 1) % 5 == 0:  # 每5个epoch显示一次
            allocated = torch.cuda.memory_allocated() / 1024**3
            cached = torch.cuda.memory_reserved() / 1024**3

            # 尝试获取GPU利用率
            try:
                import subprocess
                result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'],
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    gpu_util = result.stdout.strip()
                    print(f'  📊 GPU状态: 利用率 {gpu_util}%, 内存 {allocated:.1f}GB/{cached:.1f}GB')
                else:
                    print(f'  📊 GPU内存: 已分配 {allocated:.1f}GB, 已缓存 {cached:.1f}GB')
            except:
                print(f'  📊 GPU内存: 已分配 {allocated:.1f}GB, 已缓存 {cached:.1f}GB')

    # 🔥 训练完成后保存最终checkpoint和模型
    print(f"\n🎉 训练完成！正在保存最终模型...")
    print(f"📊 最终训练状态 - 最佳验证IoU: {best_val_jac:.4f}")

    # 确保最佳模型已保存（以防最后一个epoch是最佳的）
    if not weights_path.exists():
        print(f"⚠️  最佳模型文件不存在，保存当前模型作为最佳模型")
        torch.save(model, weights_path)
    else:
        print(f"✅ 最佳模型已存在: {weights_path}")

    # 1. 保存最终checkpoint（包含完整训练状态）
    final_checkpoint_path = checkpoint_dir / 'final_checkpoint.pth'
    final_checkpoint = {
        'epoch': epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'best_val_jac': best_val_jac,
        'history': history,
        'training_completed': True,
        'final_model': True,
        'random_state': {
            'torch': torch.get_rng_state(),
            'numpy': np.random.get_state(),
            'python': random.getstate(),
            'cuda': torch.cuda.get_rng_state()
        }
    }

    if lr_scheduler is not None:
        final_checkpoint['scheduler_state_dict'] = lr_scheduler.state_dict()

    torch.save(final_checkpoint, final_checkpoint_path)
    print(f'✅ 最终checkpoint已保存: {final_checkpoint_path}')

    # 2. 保存最终完整模型（用于直接推理）
    final_model_path = checkpoint_dir / f'final_model_{model_name}.pth'
    torch.save(model, final_model_path)
    print(f'✅ 最终完整模型已保存: {final_model_path}')

    # 3. 保存最终模型状态字典（用于加载到新模型）
    final_state_dict_path = checkpoint_dir / f'final_state_dict_{model_name}.pth'
    torch.save(model.state_dict(), final_state_dict_path)
    print(f'✅ 最终状态字典已保存: {final_state_dict_path}')

    # 4. 统计保存的文件
    all_checkpoints = list(checkpoint_dir.glob('checkpoint_epoch_*.pth'))
    print(f'📊 训练总结:')
    print(f'   - 训练epoch数: {epochs}')
    print(f'   - 最佳验证IoU: {best_val_jac:.4f}')
    print(f'   - 保存的checkpoint数: {len(all_checkpoints)}')
    print(f'   - 最佳模型路径: {weights_path}')
    print(f'   - 最终模型路径: {final_model_path}')
    print(f'   - Checkpoint目录: {checkpoint_dir}')

    return history

class EnsembleOptimizer:
    """集成权重优化器 - 与现有数据处理保持一致"""
    
    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = torch.device(device)
        self.models_config = [
            {
                'name': 'efficientnetb4',
                'architecture': 'PAN',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': 'noisy-student',
                'epochs': 25
            },
            {
                'name': 'eca_nfnet_l2',
                'architecture': 'PAN',
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': 'imagenet',
                'epochs': 25
            },
            {
                'name': 'seresnet152d',
                'architecture': 'PAN',
                'encoder': 'tu-seresnet152d',
                'encoder_weights': 'imagenet',
                'epochs': 30
            }
        ]
    
    def load_model_from_path(self, model_config, weights_path):
        """加载模型（支持.pth.tar格式）"""
        print(f"加载模型: {model_config['name']} from {weights_path}")
        
        # 检查文件是否存在
        if not Path(weights_path).exists():
            raise FileNotFoundError(f"模型文件不存在: {weights_path}")
        
        # 尝试直接加载完整模型（.pth.tar格式）
        try:
            model = torch.load(weights_path, map_location=self.device, weights_only=False)
            if hasattr(model, 'eval'):
                model.eval()
                return model
        except Exception as e:
            print(f"完整模型加载失败，尝试状态字典格式: {e}")
        
        # 如果失败，尝试作为状态字典加载
        try:
            checkpoint = torch.load(weights_path, map_location=self.device, weights_only=False)
            
            # 创建模型架构
            model = smp.PAN(
                encoder_name=model_config['encoder'],
                encoder_weights=None,  # 不使用预训练权重
                in_channels=3,
                classes=3
            )
            
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            elif isinstance(checkpoint, dict):
                model.load_state_dict(checkpoint)
            else:
                raise ValueError("无法识别的模型格式")
            
            model.eval()
            return model
            
        except Exception as e:
            print(f"状态字典加载也失败: {e}")
            raise e
    
    def optimize_ensemble_weights(self, weights_paths, val_loader, num_iterations=1000):
        """
        优化集成权重 - 高性能版本（专门针对notebook fusion优化）

        Args:
            weights_paths: 模型权重文件路径列表
            val_loader: 验证数据加载器
            num_iterations: 搜索迭代次数

        Returns:
            优化后的权重字典
        """
        print(f"\n{'='*80}")
        print("🚀 开始高性能集成权重优化...")
        print(f"模型数量: {len(weights_paths)}")
        print(f"优化迭代次数: {num_iterations}")
        print(f"{'='*80}")

        # 🔥 性能优化1: 智能批次数量调整
        total_batches = len(val_loader)
        if total_batches > 150:
            max_val_batches = 120  # 大数据集使用更多批次
        elif total_batches > 80:
            max_val_batches = min(100, total_batches)  # 中等数据集
        else:
            max_val_batches = total_batches  # 小数据集全部使用

        print(f"📊 智能批次选择: {max_val_batches}/{total_batches} 个验证批次")

        # 加载所有模型
        models = []
        for i, (model_config, weights_path) in enumerate(zip(self.models_config, weights_paths)):
            model = self.load_model_from_path(model_config, weights_path)
            models.append(model)
            print(f"✅ 加载模型 {i+1}/{len(weights_paths)}: {model_config['name']}")

        # 🔥 性能优化2: 高效预测收集（批量处理）
        print("\n📊 收集模型预测结果（高性能模式）...")
        all_predictions, all_targets = self._collect_predictions_optimized(
            models, val_loader, max_val_batches
        )

        print(f"✅ 缓存完成: {all_targets.shape[0]} 样本")

        # 🔥 性能优化3: 向量化权重搜索
        best_weights = self._optimize_weights_vectorized(
            all_predictions, all_targets, num_iterations
        )

        # 清理缓存的预测结果
        del all_predictions, all_targets, models
        torch.cuda.empty_cache()
        gc.collect()

        print(f"\n💾 权重优化完成，使用了 {max_val_batches} 个验证批次")
        return best_weights

    def _collect_predictions_optimized(self, models, val_loader, max_val_batches):
        """高效收集预测结果"""
        all_predictions = [[] for _ in range(len(models))]
        all_targets = []

        # 🔥 使用混合精度加速
        use_amp = torch.cuda.is_available()

        with torch.no_grad():
            batch_count = 0
            for batch in tqdm(val_loader, desc="收集预测", total=max_val_batches):
                if batch_count >= max_val_batches:
                    break

                images = batch['image'].to(self.device)
                targets = batch['mask'].to(self.device)

                # 🔥 批量处理所有模型，减少GPU-CPU传输
                batch_predictions = []

                if use_amp:
                    with torch.cuda.amp.autocast():
                        for model in models:
                            pred = model(images)
                            pred = torch.sigmoid(pred)
                            batch_predictions.append(pred.cpu())
                else:
                    for model in models:
                        pred = model(images)
                        pred = torch.sigmoid(pred)
                        batch_predictions.append(pred.cpu())

                # 存储结果
                for i, pred in enumerate(batch_predictions):
                    all_predictions[i].append(pred)

                all_targets.append(targets.cpu())
                batch_count += 1

                # 🔥 减少内存清理频率
                if batch_count % 25 == 0:
                    torch.cuda.empty_cache()

        # 合并所有批次
        print("🔗 合并预测结果...")
        for i in range(len(models)):
            all_predictions[i] = torch.cat(all_predictions[i], dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        return all_predictions, all_targets

    def _optimize_weights_vectorized(self, all_predictions, all_targets, num_iterations):
        """向量化权重优化 - 核心性能提升"""
        best_weights = {}
        num_classes = 3
        class_names = ['背景', '主轨道', '分叉轨道']

        print(f"\n🎯 开始向量化权重优化...")

        for class_idx in range(num_classes):
            class_name = class_names[class_idx]
            print(f"\n🔍 优化类别 {class_idx} ({class_name})...")

            # 预提取当前类别的数据
            target_class = all_targets[:, class_idx:class_idx+1]
            class_predictions = [pred[:, class_idx:class_idx+1] for pred in all_predictions]

            best_iou = 0.0
            best_class_weights = None
            no_improvement_count = 0

            # 🔥 核心优化: 批量向量化搜索
            batch_size = min(100, num_iterations // 10)  # 动态批量大小

            with tqdm(total=num_iterations, desc=f"类别 {class_idx}") as pbar:
                for start_idx in range(0, num_iterations, batch_size):
                    end_idx = min(start_idx + batch_size, num_iterations)
                    current_batch_size = end_idx - start_idx

                    # 批量生成权重
                    weights_batch = np.random.dirichlet(
                        np.ones(len(all_predictions)), size=current_batch_size
                    )

                    # 向量化计算IoU
                    for weights in weights_batch:
                        # 计算加权预测
                        weighted_pred = torch.zeros_like(class_predictions[0])
                        for i, weight in enumerate(weights):
                            weighted_pred += weight * class_predictions[i]

                        # 计算IoU
                        iou = iou_coef(target_class, weighted_pred).item()

                        # 更新最佳权重
                        if iou > best_iou:
                            best_iou = iou
                            best_class_weights = weights.copy()
                            no_improvement_count = 0
                        else:
                            no_improvement_count += 1

                    pbar.update(current_batch_size)

                    # 🔥 早停机制
                    if no_improvement_count >= 200:  # 200次无改善则早停
                        print(f"   早停: {no_improvement_count} 次无改善")
                        break

            # 保存结果
            best_weights[class_idx] = {
                'weights': best_class_weights.tolist(),
                'iou': best_iou,
                'class_name': class_name
            }

            print(f"✅ 类别 {class_idx} ({class_name}) - IoU: {best_iou:.4f}")
            print(f"   权重: {best_class_weights}")

        return best_weights
    
    def save_ensemble_config(self, best_weights, weights_paths, output_dir):
        """保存集成配置"""
        ensemble_config = {
            'timestamp': datetime.now().isoformat(),
            'model_paths': weights_paths,
            'models_config': self.models_config,
            'fusion_weights': best_weights,
            'device': str(self.device)
        }
        
        config_path = Path(output_dir) / 'ensemble_config.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(ensemble_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 集成配置已保存: {config_path}")
        return config_path

def main():
    """主函数 - 增强版：训练 + 集成学习"""
    parser = argparse.ArgumentParser(description='集成训练脚本 - 增强版：训练+集成学习')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, 
                       help='数据集根目录')
    parser.add_argument('--output-dir', type=str, default='weights',
                      help='模型权重输出目录')
    parser.add_argument('--resume', action='store_true',
                      help='从checkpoint恢复训练')
    parser.add_argument('--pretrained-efficientnet', type=str, default=None,
                      help='预训练的EfficientNet模型路径（.pth.tar文件）')
    parser.add_argument('--pretrained-nfnet', type=str, default=None,
                      help='预训练的NFNet模型路径（.pth.tar文件）')
    parser.add_argument('--pretrained-resnet', type=str, default=None,
                      help='预训练的ResNet模型路径（.pth.tar文件）')
    parser.add_argument('--seed', type=int, default=0,
                      help='随机种子')
    
    # 新增模型选择参数
    parser.add_argument('--models', type=str, nargs='*', default=None,
                      help='指定要训练的模型列表 (可选: efficientnetb4, eca_nfnet_l2, seresnet152d)')
    parser.add_argument('--train-all', action='store_true',
                      help='训练所有可用模型（默认行为）')
    parser.add_argument('--list-models', action='store_true',
                      help='显示所有可用模型并退出')
    
    # 集成学习相关参数
    parser.add_argument('--skip-ensemble', action='store_true',
                      help='跳过集成权重优化')
    parser.add_argument('--ensemble-iterations', type=int, default=1000,
                      help='集成权重优化迭代次数（默认：1000）')
    
    args = parser.parse_args()
    
    # 定义所有可用的模型配置
    all_models_config = {
        'efficientnetb4': {
            'name': 'efficientnetb4',
            'architecture': 'PAN',
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'encoder_weights': 'noisy-student',
            'epochs': 25,
            'pretrained_path': args.pretrained_efficientnet,
            'description': 'EfficientNet-B4 with Noisy Student weights'
        },
        'eca_nfnet_l2': {
            'name': 'eca_nfnet_l2',
            'architecture': 'PAN',
            'encoder': 'tu-eca_nfnet_l2',
            'encoder_weights': 'imagenet',
            'epochs': 25,
            'pretrained_path': args.pretrained_nfnet,
            'description': 'ECA-NFNet-L2 with ImageNet weights'
        },
        'seresnet152d': {
            'name': 'seresnet152d',
            'architecture': 'PAN',
            'encoder': 'tu-seresnet152d',
            'encoder_weights': 'imagenet',
            'epochs': 30,
            'pretrained_path': args.pretrained_resnet,
            'description': 'SE-ResNet-152D with ImageNet weights'
        }
    }
    
    # 如果用户请求列出模型，显示并退出
    if args.list_models:
        print("可用的模型列表:")
        print("-" * 60)
        for model_key, model_info in all_models_config.items():
            print(f"  {model_key:15} - {model_info['description']}")
            print(f"  {'':15}   架构: {model_info['architecture']}, 编码器: {model_info['encoder']}")
            print(f"  {'':15}   训练轮数: {model_info['epochs']}")
            print()
        print("使用示例:")
        print("  python scripts/ensemble_training_notebook_exact_with_fusion.py --data-dir /path/to/data --models efficientnetb4 seresnet152d")
        print("  python scripts/ensemble_training_notebook_exact_with_fusion.py --data-dir /path/to/data --train-all")
        return
    
    # 检查 data-dir 参数（只有在非 list-models 模式下才需要）
    if not args.data_dir:
        parser.error("--data-dir 参数是必需的（除非使用 --list-models）")
    
    # 确定要训练的模型
    if args.models is not None:
        # 用户指定了特定模型
        selected_models = []
        invalid_models = []
        
        for model_name in args.models:
            if model_name in all_models_config:
                selected_models.append(model_name)
            else:
                invalid_models.append(model_name)
        
        if invalid_models:
            print(f"错误: 无效的模型名称: {invalid_models}")
            print(f"可用模型: {list(all_models_config.keys())}")
            print("使用 --list-models 查看详细信息")
            return
        
        models_config = [all_models_config[name] for name in selected_models]
        print(f"将训练指定的 {len(models_config)} 个模型: {selected_models}")
        
    elif args.train_all:
        # 用户明确指定训练所有模型
        models_config = list(all_models_config.values())
        print(f"将训练所有 {len(models_config)} 个模型: {list(all_models_config.keys())}")
        
    else:
        # 默认行为：训练所有模型
        models_config = list(all_models_config.values())
        print(f"默认训练所有 {len(models_config)} 个模型: {list(all_models_config.keys())}")
        print("提示: 使用 --models 参数可以指定特定模型，使用 --list-models 查看可用模型")
    
    # 设置随机种子（与notebook保持一致）
    setup_seed(args.seed)
    
    # 加载配置文件（与当前目录方式一致）
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径
    config['data']['train_path'] = args.data_dir
    config['data']['val_path'] = args.data_dir
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("=" * 80)
    print("增强版集成训练脚本 - 训练 + 集成学习")
    print("=" * 80)
    print(f"数据目录: {args.data_dir}")
    print(f"配置文件: {args.config}")
    print(f"设备: {device}")
    print(f"要训练的模型: {[model['name'] for model in models_config]}")
    if not args.skip_ensemble:
        print(f"集成优化迭代次数: {args.ensemble_iterations}")
    
    # 创建数据集（使用与当前目录一致的方式）
    print("\n创建数据集...")
    
    # 获取数据变换
    train_transform = get_train_transform(config)
    val_transform = get_val_transform(config)
    
    # 创建训练和验证数据集
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val',
        config=config,
        transform=val_transform
    )
    
    # 🔥 GPU利用率优化：从配置获取并优化参数
    batch_size = config['data']['batch_size']['train']
    val_batch_size = config['data']['batch_size']['val']

    # 优化数据加载参数以提高GPU利用率稳定性
    num_workers = config.get('project', {}).get('num_workers', 6)  # 增加默认工作进程
    prefetch_factor = config.get('project', {}).get('gpu_optimization', {}).get('prefetch_factor', 3)
    persistent_workers = config.get('project', {}).get('gpu_optimization', {}).get('persistent_workers', True)

    print(f"🔧 数据加载优化: workers={num_workers}, prefetch={prefetch_factor}, persistent={persistent_workers}")
    
    # 🔥 GPU利用率优化：检查GPU内存并智能调整配置
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        gpu_name = torch.cuda.get_device_name()
        print(f"🔍 GPU检测: {gpu_name} ({gpu_memory:.1f}GB)")

        # 智能调整batch size以平衡内存使用和GPU利用率
        if gpu_memory < 8:
            print("⚠️  GPU内存较小，优化batch size以提高利用率")
            batch_size = min(batch_size, 2)  # 小内存GPU使用较小batch size
            val_batch_size = min(val_batch_size, 4)
            num_workers = min(num_workers, 4)  # 减少工作进程
        elif gpu_memory < 12:
            print("✅ GPU内存中等，平衡batch size和利用率")
            batch_size = min(batch_size, 4)  # 恢复到正常的batch size
            val_batch_size = min(val_batch_size, 6)
            num_workers = min(num_workers, 6)
        else:
            print("✅ GPU内存充足，优化for最大利用率")
            # 对于大内存GPU，确保batch size足够大以充分利用GPU
            batch_size = max(batch_size, 8)  # 确保最小batch size
            val_batch_size = max(val_batch_size, 12)
            num_workers = min(num_workers, 8)
    
    # 从配置获取batch_size（已经过内存优化调整）
    print(f"📊 内存优化后的批量大小: train={batch_size}, val={val_batch_size}")
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    # 🔥 GPU利用率优化：创建高效数据加载器
    loaders = {
        'train': DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            persistent_workers=persistent_workers if num_workers > 0 else False,
            prefetch_factor=prefetch_factor if num_workers > 0 else 2,
            drop_last=True,  # 确保批次大小一致，提高GPU利用率稳定性
            multiprocessing_context='spawn' if num_workers > 0 else None
        ),
        'val': DataLoader(
            val_dataset,
            batch_size=val_batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            persistent_workers=persistent_workers if num_workers > 0 else False,
            prefetch_factor=prefetch_factor if num_workers > 0 else 2,
            drop_last=False,  # 验证时保留所有数据
            multiprocessing_context='spawn' if num_workers > 0 else None
        )
    }
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 训练选定的模型
    trained_models = []
    skipped_models = []
    trained_weights_paths = []
    
    for i, model_config in enumerate(models_config, 1):
        print(f"\n{'=' * 80}")
        print(f"训练模型 {i}/{len(models_config)}: {model_config['name']}")
        print(f"描述: {model_config.get('description', 'N/A')}")
        print(f"{'=' * 80}")
        
        weights_path = output_dir / f"{model_config['name']}.pth.tar"
        history_path = output_dir / f"history_{model_config['name']}"
        checkpoint_dir = output_dir / 'checkpoints' / model_config['name']
        
        # 检查是否已有训练好的模型（与notebook保持一致）
        if weights_path.exists() and history_path.exists() and not args.resume:
            print(f'跳过训练 - 加载已有权重: {weights_path}')
            with open(history_path, 'rb') as handle:
                history = dill.load(handle)
            skipped_models.append(model_config['name'])
            trained_weights_paths.append(str(weights_path))
            continue
        
        print('开始训练...')
        
        # 创建模型（与notebook保持一致）
        if model_config['pretrained_path'] and Path(model_config['pretrained_path']).exists():
            # 加载预训练模型
            print(f'加载预训练模型: {model_config["pretrained_path"]}')
            try:
                # 首先尝试加载转换后的格式（只包含state_dict）
                checkpoint = torch.load(model_config['pretrained_path'], map_location=device)
                
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    # 这是转换后的格式
                    print("检测到转换后的模型格式，创建模型并加载权重...")
                    model = smp.PAN(
                        encoder_name=model_config['encoder'],
                        encoder_weights=None,  # 不使用预训练权重，我们会加载自己的
                        in_channels=3,
                        classes=3
                    )
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"✓ 成功从转换后的checkpoint加载模型")
                else:
                    # 尝试作为完整模型加载
                    model = load_pretrained_model(model_config['pretrained_path'], device=device)
                    
                    # 验证模型架构
                    if not hasattr(model, 'encoder'):
                        raise ValueError(f"预训练模型缺少 'encoder' 属性，可能不是有效的分割模型")
                    
                    if not hasattr(model, 'decoder'):
                        raise ValueError(f"预训练模型缺少 'decoder' 属性，可能不是有效的分割模型")
                    
                    # 验证输出类别数
                    if hasattr(model, 'segmentation_head'):
                        out_channels = model.segmentation_head[0].out_channels
                        if out_channels != 3:
                            raise ValueError(f"预训练模型输出类别数为 {out_channels}，但期望为 3")
                    
                    print(f"✓ 成功加载预训练模型，编码器: {model.encoder.name if hasattr(model.encoder, 'name') else 'unknown'}")
                
            except Exception as e:
                print(f"\n错误: 无法加载预训练模型 '{model_config['pretrained_path']}'")
                print(f"错误信息: {str(e)}")
                print("\n解决方案:")
                print("1. 如果这是旧版本的模型，请先使用convert_old_models.py转换:")
                print(f"   python scripts/convert_old_models.py {model_config['pretrained_path']} --type {model_config['name']}")
                print("2. 然后使用转换后的模型文件")
                print("3. 或者不指定 --pretrained-* 参数从头训练")
                sys.exit(1)
        else:
            # 创建新模型
            if model_config['pretrained_path']:
                # 用户指定了预训练路径但文件不存在
                print(f"\n错误: 找不到预训练模型文件: {model_config['pretrained_path']}")
                print("请检查文件路径是否正确")
                sys.exit(1)
            
            print(f"创建新模型: {model_config['name']}")
            model = smp.PAN(
                encoder_name=model_config['encoder'],
                encoder_weights=model_config['encoder_weights'],
                in_channels=3,
                classes=3
            )
            print(f"✓ 使用 {model_config['encoder_weights']} 预训练权重初始化编码器")
        
        # 创建损失函数（使用与当前目录一致的方式）
        loss_config = config.get('loss', {})
        alpha_weights = loss_config.get('alpha', [0.1, 0.3, 0.6])
        
        criterion = MultiLabelSegmentationLoss(
            jaccard_weight=loss_config.get('jaccard_weight', 0.5),
            focal_weight=loss_config.get('focal_weight', 0.5),
            alpha=alpha_weights,
            gamma=loss_config.get('gamma', 2.0)
        )
        
        # 🔥 修复数值不稳定：降低学习率，添加权重衰减
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.0001, weight_decay=0.01)  # 降低学习率

        # 创建学习率调度器
        lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=model_config['epochs'], eta_min=1e-7  # 降低最小学习率
        )
        
        # 确定恢复点
        resume_from = None
        if args.resume:
            latest_checkpoint = checkpoint_dir / 'latest_checkpoint.pth'
            if latest_checkpoint.exists():
                resume_from = latest_checkpoint
                print(f'将从 {resume_from} 恢复训练')
        
        # 开始训练
        history = train_with_checkpoint(
            model=model,
            optimizer=optimizer,
            criterion=criterion,
            train_loader=loaders['train'],
            val_loader=loaders['val'],
            epochs=model_config['epochs'],
            lr_scheduler=lr_scheduler,
            weights_path=weights_path,
            checkpoint_dir=checkpoint_dir,
            resume_from=resume_from,
            model_name=model_config['name']
        )
        
        # 保存训练历史（与notebook保持一致）
        with open(history_path, 'wb') as handle:
            dill.dump(history, handle)
        
        print(f'模型训练完成: {model_config["name"]}')
        print(f'权重已保存: {weights_path}')
        print(f'历史已保存: {history_path}')
        trained_models.append(model_config['name'])
        trained_weights_paths.append(str(weights_path))
        
        # 可视化训练曲线
        if history:
            # 提取数据
            epochs = [stat.epoch for stat in history]
            train_loss = [stat.train_loss for stat in history]
            val_loss = [stat.val_loss for stat in history]
            val_jac = [stat.val_jac for stat in history]
            
            x = np.arange(1, len(epochs) + 1)
            
            # 损失曲线
            plt.figure(figsize=(12, 6))
            plt.plot(x, train_loss, label='Training Set Loss')
            plt.plot(x, val_loss, label='Validation Set Loss')
            plt.title(f'Loss values during training - {model_config["name"]}')
            plt.xlabel('Epochs')
            plt.ylabel('Loss')
            plt.legend()
            # 🔥 修复matplotlib错误：安全保存损失曲线
            try:
                plt.savefig(output_dir / f'loss_curve_{model_config["name"]}.png', dpi=150, bbox_inches='tight')
                plt.close()
                print(f'📊 损失曲线已保存: loss_curve_{model_config["name"]}.png')
            except Exception as save_error:
                print(f'⚠️ 损失曲线保存失败: {save_error}')
                print(f'   错误详情: {type(save_error).__name__}: {save_error}')
                plt.close()

            # IoU曲线
            plt.figure(figsize=(12, 6))
            plt.plot(x, val_jac, label='IoU')
            plt.title(f'Validation Set IoU - {model_config["name"]}')
            plt.xlabel('Epochs')
            plt.ylabel('Metric')

            # 🔥 修复matplotlib错误：安全保存IoU曲线
            try:
                plt.savefig(output_dir / f'iou_curve_{model_config["name"]}.png', dpi=150, bbox_inches='tight')
                plt.close()
                print(f'📊 IoU曲线已保存: iou_curve_{model_config["name"]}.png')
            except Exception as save_error:
                print(f'⚠️ IoU曲线保存失败: {save_error}')
                print(f'   错误详情: {type(save_error).__name__}: {save_error}')
                plt.close()
    
    print("\n" + "=" * 80)
    print("单模型训练完成!")
    print("=" * 80)
    
    # 显示训练结果总结
    if trained_models:
        print(f"✅ 成功训练的模型 ({len(trained_models)}个): {', '.join(trained_models)}")
    
    if skipped_models:
        print(f"⏭️  跳过的模型 ({len(skipped_models)}个): {', '.join(skipped_models)}")
    
    # 保存训练配置
    config_path = output_dir / 'training_config.json'
    with open(config_path, 'w') as f:
        json.dump({
            'data_dir': args.data_dir,
            'config_file': args.config,
            'seed': args.seed,
            'requested_models': [model['name'] for model in models_config],
            'trained_models': trained_models,
            'skipped_models': skipped_models,
            'models_config': models_config,
            'timestamp': datetime.now().isoformat()
        }, f, indent=2)
    
    print(f"\n📝 训练配置已保存至: {config_path}")
    
    # === 集成学习部分 ===
    if args.skip_ensemble:
        print("\n⏭️  跳过集成权重优化")
        print("如需手动进行集成学习，请运行:")
        print(f"python scripts/ensemble_training.py --data-dir {args.data_dir} --skip-training")
        return
    
    if len(trained_weights_paths) < 2:
        print("\n⚠️  需要至少2个模型才能进行集成学习")
        print(f"当前只有 {len(trained_weights_paths)} 个模型")
        return
    
    print(f"\n{'='*80}")
    print("开始集成学习阶段...")
    print(f"{'='*80}")
    
    # 🔥 集成学习前的内存安全检查
    if torch.cuda.is_available():
        # 清理之前训练的内存残留
        torch.cuda.empty_cache()
        gc.collect()
        
        # 检查可用内存
        free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()
        free_memory_gb = free_memory / 1024**3
        
        print(f"🔍 可用GPU内存: {free_memory_gb:.1f}GB")
        
        if free_memory_gb < 2:
            print("⚠️  GPU内存不足，建议:")
            print("   1. 减少验证集batch size")
            print("   2. 减少集成优化迭代次数")
            print("   3. 使用更少的验证数据")
            
            # 自动调整参数
            original_iterations = args.ensemble_iterations
            args.ensemble_iterations = min(args.ensemble_iterations, 300)
            print(f"   🔧 自动调整迭代次数: {original_iterations} -> {args.ensemble_iterations}")
    
    # 创建集成优化器
    ensemble_optimizer = EnsembleOptimizer(config, device=device)
    
    # 优化集成权重
    best_weights = ensemble_optimizer.optimize_ensemble_weights(
        trained_weights_paths, 
        loaders['val'],
        num_iterations=args.ensemble_iterations
    )
    
    # 保存集成配置
    ensemble_config_path = ensemble_optimizer.save_ensemble_config(
        best_weights, 
        trained_weights_paths, 
        output_dir
    )
    
    print(f"\n{'='*80}")
    print("集成学习完成!")
    print(f"{'='*80}")
    
    # 显示最终结果
    print("\n🎯 最优集成权重:")
    for class_idx, weight_info in best_weights.items():
        print(f"  {weight_info['class_name']:8}: {weight_info['weights']} (IoU: {weight_info['iou']:.4f})")
    
    print(f"\n📂 输出文件:")
    print(f"  - 模型权重: {output_dir}")
    print(f"  - 集成配置: {ensemble_config_path}")
    print(f"  - 训练配置: {config_path}")
    
    print(f"\n🚀 使用集成模型进行预测:")
    print(f"python scripts/ensemble_prediction.py \\")
    print(f"    --weights-dir {output_dir} \\")
    print(f"    --config {args.config} \\")
    print(f"    --input /path/to/image.jpg \\")
    print(f"    --output predictions/")

if __name__ == '__main__':
    main() 