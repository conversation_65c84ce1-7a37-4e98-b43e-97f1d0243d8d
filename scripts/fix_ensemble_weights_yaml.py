#!/usr/bin/env python3
"""
修复ensemble_weights.yaml文件工具
将包含numpy对象的YAML文件转换为标准格式
"""

import sys
import yaml
import numpy as np
from pathlib import Path
import argparse
import shutil
from datetime import datetime


def fix_ensemble_weights_yaml(yaml_path: str, backup: bool = True):
    """
    修复ensemble_weights.yaml文件
    
    Args:
        yaml_path: YAML文件路径
        backup: 是否创建备份
    """
    yaml_path = Path(yaml_path)
    
    if not yaml_path.exists():
        print(f"❌ 文件不存在: {yaml_path}")
        return False
    
    print(f"🔧 修复YAML文件: {yaml_path}")
    
    # 创建备份
    if backup:
        backup_path = yaml_path.with_suffix(f'.yaml.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        shutil.copy2(yaml_path, backup_path)
        print(f"📦 备份已创建: {backup_path}")
    
    try:
        # 尝试使用UnsafeLoader加载numpy对象
        with open(yaml_path, 'r', encoding='utf-8') as f:
            weights_config = yaml.load(f, Loader=yaml.UnsafeLoader)
        
        print("✅ 成功加载包含numpy对象的YAML文件")
        
        # 转换numpy对象为普通Python类型
        fixed_config = {}
        
        for class_idx, info in weights_config.items():
            if isinstance(class_idx, (int, np.integer)):
                class_idx = int(class_idx)
                
                if isinstance(info, dict):
                    fixed_info = {}
                    for key, value in info.items():
                        if isinstance(value, np.ndarray):
                            fixed_info[key] = value.tolist()
                        elif isinstance(value, (np.floating, np.integer)):
                            fixed_info[key] = float(value) if isinstance(value, np.floating) else int(value)
                        else:
                            fixed_info[key] = value
                    fixed_config[class_idx] = fixed_info
                    
                elif isinstance(info, np.ndarray):
                    fixed_config[class_idx] = info.tolist()
                    
                elif isinstance(info, (list, tuple)):
                    # 转换列表中的numpy对象
                    fixed_list = []
                    for item in info:
                        if isinstance(item, (np.floating, np.integer)):
                            fixed_list.append(float(item) if isinstance(item, np.floating) else int(item))
                        else:
                            fixed_list.append(item)
                    fixed_config[class_idx] = fixed_list
                    
                else:
                    fixed_config[class_idx] = info
        
        # 保存修复后的文件
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(fixed_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ 修复完成，文件已保存: {yaml_path}")
        
        # 验证修复结果
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                test_config = yaml.safe_load(f)
            print("✅ 验证成功：文件现在可以使用safe_load加载")
            
            # 显示修复后的内容
            print("\n📊 修复后的权重配置:")
            for class_idx, info in test_config.items():
                if isinstance(info, dict) and 'weights' in info:
                    weights = info['weights']
                    iou = info.get('iou', 'N/A')
                    print(f"  类别 {class_idx}: 权重={weights}, IoU={iou}")
                else:
                    print(f"  类别 {class_idx}: {info}")
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False


def find_ensemble_weights_files(search_dir: str):
    """查找所有ensemble_weights.yaml文件"""
    search_dir = Path(search_dir)
    yaml_files = list(search_dir.rglob('ensemble_weights.yaml'))
    
    print(f"🔍 在 {search_dir} 中找到 {len(yaml_files)} 个ensemble_weights.yaml文件:")
    for i, yaml_file in enumerate(yaml_files, 1):
        print(f"  {i}. {yaml_file}")
    
    return yaml_files


def main():
    parser = argparse.ArgumentParser(description='修复ensemble_weights.yaml文件中的numpy对象')
    
    parser.add_argument('--file', type=str, 
                       help='指定要修复的YAML文件路径')
    parser.add_argument('--search-dir', type=str, default='.',
                       help='搜索ensemble_weights.yaml文件的目录')
    parser.add_argument('--no-backup', action='store_true',
                       help='不创建备份文件')
    parser.add_argument('--auto-fix-all', action='store_true',
                       help='自动修复找到的所有文件')
    
    args = parser.parse_args()
    
    print("🛠️  集成权重YAML文件修复工具")
    print("=" * 50)
    
    if args.file:
        # 修复指定文件
        success = fix_ensemble_weights_yaml(args.file, backup=not args.no_backup)
        if success:
            print("🎉 修复完成！")
        else:
            print("❌ 修复失败！")
            sys.exit(1)
    
    else:
        # 搜索并修复文件
        yaml_files = find_ensemble_weights_files(args.search_dir)
        
        if not yaml_files:
            print("❌ 未找到ensemble_weights.yaml文件")
            sys.exit(1)
        
        if args.auto_fix_all:
            # 自动修复所有文件
            print(f"\n🚀 自动修复 {len(yaml_files)} 个文件...")
            success_count = 0
            
            for yaml_file in yaml_files:
                print(f"\n修复文件: {yaml_file}")
                if fix_ensemble_weights_yaml(yaml_file, backup=not args.no_backup):
                    success_count += 1
            
            print(f"\n📊 修复结果: {success_count}/{len(yaml_files)} 个文件修复成功")
            
        else:
            # 交互式修复
            for i, yaml_file in enumerate(yaml_files, 1):
                print(f"\n文件 {i}: {yaml_file}")
                
                # 检查文件是否需要修复
                needs_fix = False
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    print("  ✅ 文件正常，无需修复")
                except yaml.constructor.ConstructorError:
                    print("  ⚠️  文件包含numpy对象，需要修复")
                    needs_fix = True
                except Exception as e:
                    print(f"  ❌ 文件读取失败: {e}")
                    continue
                
                if needs_fix:
                    response = input(f"  是否修复此文件？(y/n): ")
                    if response.lower() in ['y', 'yes']:
                        fix_ensemble_weights_yaml(yaml_file, backup=not args.no_backup)
        
        print("\n🎉 处理完成！")


if __name__ == '__main__':
    main() 