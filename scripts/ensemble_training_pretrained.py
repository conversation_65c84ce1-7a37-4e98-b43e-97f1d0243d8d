#!/usr/bin/env python3
"""
集成学习训练脚本 - 支持预训练权重加载
基于原始集成训练脚本，增加对.pth.tar预训练权重文件的支持
"""

import os
import sys
import yaml
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import segmentation_models_pytorch as smp

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.data.augmentations import get_train_transform, get_val_transform
from src.models.segmentation_model import create_model
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.utils.metrics import iou_coef


class PretrainedEnsembleTrainer:
    """支持预训练权重的集成学习训练器"""
    
    def __init__(self, config: Dict[str, Any], pretrained_dir: Optional[str] = None):
        """
        初始化训练器
        
        Args:
            config: 配置字典
            pretrained_dir: 预训练权重文件目录路径
        """
        self.config = config
        self.device = torch.device(config['project']['device'])
        self.pretrained_dir = Path(pretrained_dir) if pretrained_dir else Path(".")
        
        # 模型配置 - 与预训练权重文件对应
        self.models_config = [
            {
                'name': 'efficientnet_b4',
                'architecture': 'pan',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': 'noisy-student',
                'pretrained_file': 'efficientnetb4.pth.tar',
                'epochs': 25
            },
            {
                'name': 'eca_nfnet_l2',
                'architecture': 'pan', 
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': 'imagenet',
                'pretrained_file': 'eca_nfnet_l2.pth.tar',
                'epochs': 25
            },
            {
                'name': 'seresnet152d',
                'architecture': 'pan',
                'encoder': 'tu-seresnet152d', 
                'encoder_weights': 'imagenet',
                'pretrained_file': 'seresnet152d.pth.tar',
                'epochs': 30
            }
        ]
        
        # 创建输出目录
        checkpoint_root = config.get('training', {}).get(
            'save_dir',
            config.get('checkpointing', {}).get('save_dir', 'outputs')
        )
        self.weights_dir = Path(checkpoint_root) / 'ensemble_weights_pretrained'
        self.weights_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.weights_dir / f'ensemble_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_model(self, model_config: Dict[str, Any]) -> nn.Module:
        """创建单个模型"""
        try:
            model = create_model(
                architecture=model_config['architecture'],
                backbone=model_config['encoder'],
                num_classes=self.config['data']['num_classes'],
                pretrained=True
            )
            return model.to(self.device)
        except Exception as e:
            self.logger.warning(f"使用create_model失败: {e}，回退到segmentation_models_pytorch")
            
            # 回退方案
            params = {
                'encoder_name': model_config['encoder'],
                'encoder_weights': model_config['encoder_weights'],
                'in_channels': 3,
                'classes': self.config['model']['classes'],
                'activation': None
            }
            
            if model_config['architecture'].lower() == 'pan':
                model = smp.PAN(**params)
            elif model_config['architecture'].lower() == 'unet':
                model = smp.Unet(**params)
            elif model_config['architecture'].lower() == 'fpn':
                model = smp.FPN(**params)
            else:
                raise ValueError(f"不支持的架构: {model_config['architecture']}")
            
            return model.to(self.device)
    
    def load_pretrained_weights(self, model: nn.Module, pretrained_file: str) -> bool:
        """
        加载预训练权重
        
        Args:
            model: 模型实例
            pretrained_file: 预训练权重文件名
            
        Returns:
            是否成功加载
        """
        pretrained_path = self.pretrained_dir / pretrained_file
        
        if not pretrained_path.exists():
            self.logger.warning(f"预训练权重文件不存在: {pretrained_path}")
            return False
        
        try:
            self.logger.info(f"加载预训练权重: {pretrained_path}")
            
            # 加载权重
            checkpoint = torch.load(pretrained_path, map_location=self.device)
            
            # 处理不同的权重格式
            if isinstance(checkpoint, dict):
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint
            
            # 加载权重到模型
            missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
            
            if missing_keys:
                self.logger.warning(f"缺失的权重键: {missing_keys}")
            if unexpected_keys:
                self.logger.warning(f"意外的权重键: {unexpected_keys}")
            
            self.logger.info(f"成功加载预训练权重: {pretrained_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载预训练权重失败: {e}")
            return False
    
    def train_single_model(self, 
                          model_config: Dict[str, Any],
                          train_loader: DataLoader,
                          val_loader: DataLoader,
                          use_pretrained: bool = True,
                          fine_tune_only: bool = False) -> str:
        """
        训练单个模型
        
        Args:
            model_config: 模型配置
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            use_pretrained: 是否使用预训练权重
            fine_tune_only: 是否只进行微调（较少的epoch）
            
        Returns:
            保存的权重文件路径
        """
        model_name = model_config['name']
        epochs = model_config['epochs'] // 2 if fine_tune_only else model_config['epochs']
        
        self.logger.info(f"\n开始训练模型: {model_name}")
        self.logger.info(f"编码器: {model_config['encoder']}")
        self.logger.info(f"训练轮数: {epochs}")
        self.logger.info(f"使用预训练权重: {use_pretrained}")
        self.logger.info(f"微调模式: {fine_tune_only}")
        
        # 检查是否已存在训练后的权重文件
        weights_path = self.weights_dir / f"{model_name}.pth"
        if weights_path.exists():
            self.logger.info(f"权重文件已存在，跳过训练: {weights_path}")
            return str(weights_path)
        
        # 创建模型
        model = self.create_model(model_config)
        
        # 加载预训练权重
        if use_pretrained:
            success = self.load_pretrained_weights(model, model_config['pretrained_file'])
            if not success:
                self.logger.warning("预训练权重加载失败，使用随机初始化")
        
        # 损失函数
        loss_config = self.config.get('loss', {})
        alpha_weights = loss_config.get('alpha', [0.1, 0.3, 0.6])
        
        criterion = MultiLabelSegmentationLoss(
            jaccard_weight=loss_config.get('dice_weight', 0.5),
            focal_weight=loss_config.get('ce_weight', 0.5), 
            alpha=alpha_weights,
            gamma=loss_config.get('focal_gamma', 2.0)
        )
        
        # 优化器 - 微调模式使用更小的学习率
        training_config = self.config.get('training', {})
        optimizer_config = self.config.get('optimizer', {})
        
        base_lr = training_config.get('learning_rate', 0.0005)
        lr = base_lr / 5 if fine_tune_only else base_lr  # 微调时使用1/5的学习率
        weight_decay = optimizer_config.get('weight_decay', 0.0001)
        optimizer_type = optimizer_config.get('type', 'adamw').lower()
        
        if optimizer_type == 'adamw':
            betas = optimizer_config.get('betas', [0.9, 0.999])
            optimizer = torch.optim.AdamW(
                model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=betas
            )
        elif optimizer_type == 'sgd':
            momentum = optimizer_config.get('momentum', 0.9)
            optimizer = torch.optim.SGD(
                model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                momentum=momentum
            )
        else:
            optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        
        # 学习率调度器
        scheduler_config = self.config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'cosine').lower()
        
        if scheduler_type == 'cosine':
            min_lr = scheduler_config.get('min_lr', 1e-6)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs, eta_min=min_lr
            )
        elif scheduler_type == 'step':
            step_size = scheduler_config.get('step_size', epochs // 3)
            gamma = scheduler_config.get('gamma', 0.1)
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer, step_size=step_size, gamma=gamma
            )
        else:
            min_lr = scheduler_config.get('min_lr', 1e-6)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs, eta_min=min_lr
            )
        
        # 训练循环
        best_iou = 0.0
        train_losses = []
        val_losses = []
        val_ious = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_batches = 0
            
            train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} - 训练")
            for batch in train_pbar:
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device)
                
                optimizer.zero_grad()
                
                outputs = model(images)
                loss = criterion(outputs, masks)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
                
                train_pbar.set_postfix({'loss': f'{loss.item():.4f}'})
            
            avg_train_loss = train_loss / train_batches
            train_losses.append(avg_train_loss)
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_iou = 0.0
            val_batches = 0
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} - 验证")
                for batch in val_pbar:
                    images = batch['image'].to(self.device)
                    masks = batch['mask'].to(self.device)
                    
                    outputs = model(images)
                    loss = criterion(outputs, masks)
                    
                    outputs_prob = torch.sigmoid(outputs)
                    iou = iou_coef(masks, outputs_prob)
                    
                    val_loss += loss.item()
                    val_iou += iou.item()
                    val_batches += 1
                    
                    val_pbar.set_postfix({'loss': f'{loss.item():.4f}', 'iou': f'{iou.item():.4f}'})
            
            avg_val_loss = val_loss / val_batches
            avg_val_iou = val_iou / val_batches
            
            val_losses.append(avg_val_loss)
            val_ious.append(avg_val_iou)
            
            scheduler.step()
            
            # 保存最佳模型
            if avg_val_iou > best_iou:
                best_iou = avg_val_iou
                torch.save(model.state_dict(), weights_path)
                self.logger.info(f"保存最佳模型，IoU: {best_iou:.4f}")
            
            self.logger.info(f"Epoch {epoch+1}/{epochs} - "
                           f"训练损失: {avg_train_loss:.4f}, "
                           f"验证损失: {avg_val_loss:.4f}, "
                           f"验证IoU: {avg_val_iou:.4f}, "
                           f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        self.logger.info(f"模型 {model_name} 训练完成，最佳IoU: {best_iou:.4f}")
        
        # 清理内存
        del model
        torch.cuda.empty_cache()
        
        return str(weights_path)
    
    def train_all_models(self, 
                        train_loader: DataLoader,
                        val_loader: DataLoader,
                        use_pretrained: bool = True,
                        fine_tune_only: bool = False) -> List[str]:
        """训练所有模型"""
        weights_paths = []
        
        for model_config in self.models_config:
            # 检查预训练权重文件是否存在
            pretrained_path = self.pretrained_dir / model_config['pretrained_file']
            if use_pretrained and not pretrained_path.exists():
                self.logger.warning(f"预训练权重文件不存在: {pretrained_path}")
                self.logger.warning("将跳过此模型的预训练权重加载")
            
            weights_path = self.train_single_model(
                model_config, train_loader, val_loader, 
                use_pretrained, fine_tune_only
            )
            weights_paths.append(weights_path)
        
        return weights_paths
    
    def load_models_for_ensemble(self, weights_paths: List[str]) -> List[nn.Module]:
        """加载所有模型用于集成"""
        models = []
        
        for i, (model_config, weights_path) in enumerate(zip(self.models_config, weights_paths)):
            model = self.create_model(model_config)
            model.load_state_dict(torch.load(weights_path, map_location=self.device))
            model.eval()
            models.append(model)
            self.logger.info(f"加载模型 {i+1}/{len(weights_paths)}: {model_config['name']}")
        
        return models
    
    def create_ensemble_from_pretrained(self, 
                                      val_loader: DataLoader) -> tuple:
        """
        直接从预训练权重创建集成模型（跳过训练）
        
        Args:
            val_loader: 验证数据加载器
            
        Returns:
            (models, weights_paths) 元组
        """
        self.logger.info("直接从预训练权重创建集成模型...")
        
        models = []
        weights_paths = []
        
        for model_config in self.models_config:
            # 创建模型
            model = self.create_model(model_config)
            
            # 加载预训练权重
            success = self.load_pretrained_weights(model, model_config['pretrained_file'])
            if not success:
                raise RuntimeError(f"无法加载预训练权重: {model_config['pretrained_file']}")
            
            model.eval()
            models.append(model)
            
            # 保存权重路径（指向预训练文件）
            weights_path = str(self.pretrained_dir / model_config['pretrained_file'])
            weights_paths.append(weights_path)
            
            self.logger.info(f"加载预训练模型: {model_config['name']}")
        
        return models, weights_paths
    
    def optimize_ensemble_weights(self, 
                                 weights_paths: List[str],
                                 val_loader: DataLoader) -> Dict[int, Dict[str, float]]:
        """优化集成权重"""
        self.logger.info("\n开始优化集成权重...")
        
        # 加载所有模型
        models = self.load_models_for_ensemble(weights_paths)
        
        # 收集预测结果
        all_predictions = [[] for _ in range(len(models))]
        all_targets = []
        
        self.logger.info("收集模型预测结果...")
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="预测"):
                images = batch['image'].to(self.device)
                targets = batch['mask'].to(self.device)
                
                for i, model in enumerate(models):
                    pred = model(images)
                    pred = torch.sigmoid(pred)
                    all_predictions[i].append(pred.cpu())
                
                all_targets.append(targets.cpu())
        
        # 合并批次
        for i in range(len(models)):
            all_predictions[i] = torch.cat(all_predictions[i], dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        
        # 优化权重
        best_weights = {}
        num_classes = self.config['model']['classes']
        
        for class_idx in range(num_classes):
            self.logger.info(f"优化类别 {class_idx} 的权重...")
            
            best_iou = 0.0
            best_class_weights = None
            
            # 网格搜索
            with tqdm(total=9801, desc=f"类别 {class_idx}") as pbar:
                for alpha in np.arange(0.01, 1.00, 0.01):
                    for beta in np.arange(0.01, 1.00, 0.01):
                        gamma = 1.0 - alpha - beta
                        if gamma <= 0:
                            pbar.update(1)
                            continue
                        
                        weights = [alpha, beta, gamma]
                        weighted_pred = torch.zeros_like(all_predictions[0][:, class_idx:class_idx+1])
                        
                        for i, weight in enumerate(weights):
                            if i < len(all_predictions):
                                weighted_pred += weight * all_predictions[i][:, class_idx:class_idx+1]
                        
                        target_class = all_targets[:, class_idx:class_idx+1]
                        iou = iou_coef(target_class, weighted_pred)
                        
                        if iou > best_iou:
                            best_iou = iou
                            best_class_weights = weights.copy()
                        
                        pbar.update(1)
            
            best_weights[class_idx] = {
                'weights': best_class_weights,
                'iou': best_iou.item() if torch.is_tensor(best_iou) else best_iou
            }
            
            self.logger.info(f"类别 {class_idx} 最佳权重: {best_class_weights}")
            self.logger.info(f"类别 {class_idx} 最佳IoU: {best_iou:.4f}")
        
        # 保存权重配置
        weights_config_path = self.weights_dir / 'ensemble_weights.yaml'
        with open(weights_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(best_weights, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"集成权重已保存到: {weights_config_path}")
        
        # 清理内存
        del models
        torch.cuda.empty_cache()
        
        return best_weights


def main():
    """主函数"""
    print("=== 支持预训练权重的集成学习训练脚本 ===")
    print("✅ 支持加载.pth.tar预训练权重文件")
    print("✅ 支持微调模式（更少epoch + 更小学习率）")
    print("✅ 支持直接从预训练权重创建集成（跳过训练）")
    print("✅ 完整的日志记录")
    print()
    
    parser = argparse.ArgumentParser(description='支持预训练权重的集成学习训练')
    parser.add_argument('--config', type=str, 
                       default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据集根目录')
    parser.add_argument('--pretrained-dir', type=str, default='.',
                       help='预训练权重文件目录（包含.pth.tar文件）')
    parser.add_argument('--mode', type=str, 
                       choices=['train', 'finetune', 'ensemble_only'], 
                       default='finetune',
                       help='运行模式：train(完整训练), finetune(微调), ensemble_only(仅集成)')
    parser.add_argument('--skip-training', action='store_true',
                       help='跳过训练，直接优化集成权重')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径
    config['data']['train_path'] = args.data_dir
    config['data']['val_path'] = args.data_dir
    
    # 创建训练器
    trainer = PretrainedEnsembleTrainer(config, args.pretrained_dir)
    
    # 创建数据集
    print("创建数据集...")
    train_transform = get_train_transform(config)
    val_transform = get_val_transform(config)
    
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val',
        config=config,
        transform=val_transform
    )
    
    # 使用配置文件中的batch_size
    config_train_batch = config['data']['batch_size']['train']
    config_val_batch = config['data']['batch_size']['val']
    
    ensemble_batch_size = config_train_batch
    val_batch_size = config_val_batch
    
    print(f"使用配置文件中的batch_size: train={ensemble_batch_size}, val={val_batch_size}")
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=ensemble_batch_size,
        shuffle=True,
        num_workers=config['project']['num_workers'],
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=val_batch_size,
        shuffle=False,
        num_workers=config['project']['num_workers'],
        pin_memory=True
    )
    
    # 根据模式执行不同的操作
    if args.mode == 'ensemble_only' or args.skip_training:
        print("直接从预训练权重创建集成...")
        models, weights_paths = trainer.create_ensemble_from_pretrained(val_loader)
    elif args.mode == 'finetune':
        print("微调模式：加载预训练权重进行微调...")
        weights_paths = trainer.train_all_models(
            train_loader, val_loader, 
            use_pretrained=True, fine_tune_only=True
        )
    else:  # args.mode == 'train'
        print("完整训练模式：加载预训练权重进行完整训练...")
        weights_paths = trainer.train_all_models(
            train_loader, val_loader, 
            use_pretrained=True, fine_tune_only=False
        )
    
    # 优化集成权重
    print("\n开始优化集成权重...")
    best_weights = trainer.optimize_ensemble_weights(weights_paths, val_loader)
    
    print("\n=== 集成学习完成 ===")
    print(f"模式: {args.mode}")
    print(f"权重保存目录: {trainer.weights_dir}")
    
    # 打印结果
    print("\n最优集成权重:")
    for class_idx, weight_info in best_weights.items():
        weights_str = [f"{w:.3f}" for w in weight_info['weights']]
        print(f"类别 {class_idx}: [{', '.join(weights_str)}] (IoU: {weight_info['iou']:.4f})")
    
    print(f"\n权重配置已保存: {trainer.weights_dir / 'ensemble_weights.yaml'}")


if __name__ == '__main__':
    main() 