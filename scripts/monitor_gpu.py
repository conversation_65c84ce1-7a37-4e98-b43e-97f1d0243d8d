#!/usr/bin/env python3
"""GPU监控脚本"""

import GPUtil
import time
import sys

def monitor_gpu():
    """监控GPU使用情况"""
    print("GPU监控开始... (Ctrl+C退出)")
    print("-" * 80)
    
    try:
        while True:
            gpus = GPUtil.getGPUs()
            
            for gpu in gpus:
                print(f"\rGPU {gpu.id}: {gpu.name}")
                print(f"  显存: {gpu.memoryUsed:.0f}/{gpu.memoryTotal:.0f} MB ({gpu.memoryUtil*100:.1f}%)")
                print(f"  温度: {gpu.temperature}°C")
                print(f"  使用率: {gpu.load*100:.1f}%")
            
            sys.stdout.write("\033[F" * 5)  # 回到开始位置
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n监控结束")

if __name__ == "__main__":
    monitor_gpu()