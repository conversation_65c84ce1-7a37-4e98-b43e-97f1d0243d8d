#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分叉轨道检测效果
"""

import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.predict_inference import RailwaySegmentationPredictor
import numpy as np

def test_fork_detection():
    """测试分叉轨道检测"""
    print("🧪 测试分叉轨道检测效果...")
    
    # 配置路径
    config_path = "configs/railway_track_config.yaml"
    checkpoint_path = "models/checkpoints/railway_track_config/best_model.pth"
    
    # 包含分叉轨道的测试图像
    test_image = "data/railway_annotation_6mm/20250118164602376.near.avi_frame_1135.png"
    
    if not Path(test_image).exists():
        print("❌ 测试图像不存在，请检查路径")
        return
    
    try:
        # 创建预测器
        predictor = RailwaySegmentationPredictor(config_path, checkpoint_path)
        
        # 使用不同阈值进行预测
        thresholds = [0.1, 0.3, 0.5, 0.7]
        
        for threshold in thresholds:
            print(f"\n测试阈值: {threshold}")
            
            prediction_mask, prediction_probs = predictor.predict_single(test_image, threshold)
            
            # 统计预测结果
            unique_labels = np.unique(prediction_mask)
            
            for label in unique_labels:
                count = np.sum(prediction_mask == label)
                percentage = count / prediction_mask.size * 100
                
                if label == 0:
                    label_name = "背景"
                elif label == 1:
                    label_name = "主轨道"
                elif label == 2:
                    label_name = "分叉轨道"
                else:
                    label_name = f"标签{label}"
                
                print(f"  {label_name}: {count} 像素 ({percentage:.2f}%)")
            
            if 2 in unique_labels:
                print(f"  ✅ 检测到分叉轨道！")
            else:
                print(f"  ❌ 未检测到分叉轨道")
        
        print("\n🎯 建议使用阈值0.3进行分叉轨道检测")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_fork_detection()
