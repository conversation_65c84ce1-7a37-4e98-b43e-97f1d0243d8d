#!/usr/bin/env python
"""
详细评估脚本
深入分析模型评估结果的异常情况
"""

import argparse
import os
import sys
from pathlib import Path
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core import load_config
from src.core.registry import MODEL_REGISTRY
from src.data import create_dataloaders
from src.utils import setup_logger


def analyze_model_vs_data(model, dataloader, device, logger, num_samples=10):
    """
    分析模型预测与数据的关系
    
    Args:
        model: 模型
        dataloader: 数据加载器
        device: 设备
        logger: 日志记录器
        num_samples: 分析样本数量
    """
    model.eval()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(dataloader):
            if batch_idx >= num_samples:
                break
                
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            filenames = batch.get('filename', [f'sample_{batch_idx}'])
            
            # 模型预测
            outputs = model(images)
            
            # 检查输出范围
            if outputs.min() < 0 or outputs.max() > 1:
                predictions = torch.sigmoid(outputs)
            else:
                predictions = outputs
            
            # 逐样本分析
            for i in range(images.size(0)):
                logger.info(f"\n=== 分析样本 {batch_idx}-{i}: {filenames[i]} ===")
                
                img = images[i]
                mask = masks[i]
                pred = predictions[i]
                
                # 统计信息
                mask_stats = {
                    'min': mask.min().item(),
                    'max': mask.max().item(),
                    'mean': mask.mean().item(),
                    'unique_values': torch.unique(mask).cpu().numpy()
                }
                
                pred_stats = {
                    'min': pred.min().item(),
                    'max': pred.max().item(),
                    'mean': pred.mean().item()
                }
                
                logger.info(f"掩码统计: {mask_stats}")
                logger.info(f"预测统计: {pred_stats}")
                
                # 检查是否完全相同
                mask_binary = (mask > 0.5).float()
                pred_binary = (pred > 0.5).float()
                
                diff = torch.abs(mask_binary - pred_binary)
                diff_ratio = diff.sum() / diff.numel()
                
                logger.info(f"二值化差异比例: {diff_ratio.item():.6f}")
                
                if diff_ratio < 1e-6:
                    logger.warning("⚠️  预测与真实掩码几乎完全相同！")
                
                # 计算逐像素相关性
                mask_flat = mask.view(-1)
                pred_flat = pred.view(-1)
                correlation = torch.corrcoef(torch.stack([mask_flat, pred_flat]))[0, 1]
                logger.info(f"像素级相关性: {correlation.item():.6f}")
                
                if correlation > 0.99:
                    logger.warning("⚠️  预测与真实掩码高度相关！")


def check_data_integrity(dataloader, logger, num_samples=5):
    """
    检查数据完整性
    
    Args:
        dataloader: 数据加载器
        logger: 日志记录器
        num_samples: 检查样本数量
    """
    logger.info("\n=== 数据完整性检查 ===")
    
    all_filenames = []
    sample_checksums = []
    
    for batch_idx, batch in enumerate(dataloader):
        if batch_idx >= num_samples:
            break
            
        images = batch['image']
        masks = batch['mask']
        filenames = batch.get('filename', [f'sample_{batch_idx}'])
        
        for i in range(images.size(0)):
            filename = filenames[i] if isinstance(filenames, list) else filenames
            all_filenames.append(filename)
            
            # 计算图像和掩码的校验和
            img_checksum = torch.sum(images[i]).item()
            mask_checksum = torch.sum(masks[i]).item()
            
            sample_checksums.append((filename, img_checksum, mask_checksum))
            
            logger.info(f"样本 {filename}:")
            logger.info(f"  图像校验和: {img_checksum:.2f}")
            logger.info(f"  掩码校验和: {mask_checksum:.2f}")
            logger.info(f"  图像形状: {images[i].shape}")
            logger.info(f"  掩码形状: {masks[i].shape}")
            logger.info(f"  掩码唯一值: {torch.unique(masks[i]).cpu().numpy()}")
    
    # 检查重复文件名
    unique_filenames = set(all_filenames)
    if len(unique_filenames) != len(all_filenames):
        logger.warning("⚠️  发现重复的文件名！")
        
    return sample_checksums


def compare_with_random_model(dataloader, device, logger, num_samples=5):
    """
    与随机模型对比
    
    Args:
        dataloader: 数据加载器
        device: 设备
        logger: 日志记录器
        num_samples: 对比样本数量
    """
    logger.info("\n=== 随机模型对比 ===")
    
    # 创建随机模型（相同架构但随机权重）
    sample_batch = next(iter(dataloader))
    input_shape = sample_batch['image'].shape
    output_shape = sample_batch['mask'].shape
    
    class RandomModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = torch.nn.Conv2d(input_shape[1], output_shape[1], 1)
            
        def forward(self, x):
            return torch.sigmoid(self.conv(x))
    
    random_model = RandomModel().to(device)
    random_model.eval()
    
    random_metrics = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(dataloader):
            if batch_idx >= num_samples:
                break
                
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # 随机预测
            random_pred = random_model(images)
            
            # 计算IoU
            pred_binary = (random_pred > 0.5).float()
            mask_binary = (masks > 0.5).float()
            
            intersection = (pred_binary * mask_binary).sum()
            union = pred_binary.sum() + mask_binary.sum() - intersection
            iou = (intersection / (union + 1e-8)).item()
            
            random_metrics.append(iou)
            logger.info(f"随机模型批次 {batch_idx} IoU: {iou:.4f}")
    
    avg_random_iou = np.mean(random_metrics)
    logger.info(f"随机模型平均IoU: {avg_random_iou:.4f}")
    
    if avg_random_iou < 0.1:
        logger.info("✅ 随机模型IoU较低，这是正常的")
    else:
        logger.warning("⚠️  随机模型IoU异常高，可能存在数据问题")
    
    return avg_random_iou


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='详细评估分析')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--output-dir', type=str, default='outputs/detailed_analysis', help='输出目录')
    parser.add_argument('--gpu', type=str, default='0', help='GPU编号')
    parser.add_argument('--num-samples', type=int, default=10, help='分析样本数量')
    
    args = parser.parse_args()
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logger = setup_logger(
        'detailed_analysis',
        output_dir / 'detailed_analysis.log',
        console_level='INFO'
    )
    
    logger.info("开始详细评估分析...")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"检查点: {args.checkpoint}")
    logger.info(f"设备: {device}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建数据加载器
    dataloaders = create_dataloaders(config.to_dict())
    test_loader = dataloaders['test']
    
    # 加载模型
    model_config = config.model.to_dict()
    model = MODEL_REGISTRY.build(model_config)
    checkpoint = torch.load(args.checkpoint, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    
    # 1. 检查数据完整性
    check_data_integrity(test_loader, logger, args.num_samples)
    
    # 2. 分析模型预测与真实数据的关系
    analyze_model_vs_data(model, test_loader, device, logger, args.num_samples)
    
    # 3. 与随机模型对比
    compare_with_random_model(test_loader, device, logger, args.num_samples)
    
    logger.info("详细分析完成！")


if __name__ == '__main__':
    main() 