#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分叉轨道检测诊断脚本
分析无法检测分叉轨道的原因
"""

import torch
import cv2
import numpy as np
import argparse
import yaml
import os
import json
from pathlib import Path
from typing import Tuple, Dict, List, Optional, Union
import matplotlib.pyplot as plt
from collections import defaultdict
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

import sys
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 可选导入seaborn
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

from scripts.predict_inference import RailwaySegmentationPredictor
from src.data.preprocessing import RailwayAnnotationParser


class ForkDetectionDiagnostic:
    """分叉轨道检测诊断器"""
    
    def __init__(self, config_path: str, checkpoint_path: str):
        """
        初始化诊断器
        
        Args:
            config_path: 配置文件路径
            checkpoint_path: 模型权重文件路径
        """
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        
        # 创建预测器
        try:
            self.predictor = RailwaySegmentationPredictor(config_path, checkpoint_path)
            logger.info("✅ 预测器创建成功")
        except Exception as e:
            logger.error(f"❌ 预测器创建失败: {e}")
            self.predictor = None
        
        # 创建数据解析器
        self.parser = RailwayAnnotationParser()
    
    def check_data_availability(self, json_dir: str) -> Dict[str, any]:
        """
        检查数据中是否包含分叉轨道标注
        
        Args:
            json_dir: JSON标注文件目录
            
        Returns:
            数据检查结果
        """
        logger.info("=== 检查数据可用性 ===")
        
        json_files = list(Path(json_dir).glob('*.json'))
        logger.info(f"找到 {len(json_files)} 个JSON文件")
        
        stats = {
            'total_files': len(json_files),
            'files_with_main_tracks': 0,
            'files_with_fork_tracks': 0,
            'files_with_complete_fork_pairs': 0,
            'main_track_annotations': 0,
            'fork_track_annotations': 0,
            'fork_track_details': []
        }
        
        for json_file in json_files[:20]:  # 检查前20个文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                annotation_data = self.parser.parse_json_file(json_file)
                
                has_main = False
                has_fork = False
                fork_left_count = 0
                fork_right_count = 0
                
                for track in annotation_data['tracks']:
                    if track['label'] in ['Main_Left', 'Main_Right']:
                        has_main = True
                        stats['main_track_annotations'] += 1
                    elif track['label'] in ['Fork_Left', 'Fork_Right']:
                        has_fork = True
                        stats['fork_track_annotations'] += 1
                        if track['label'] == 'Fork_Left':
                            fork_left_count = len(track['points'])
                        elif track['label'] == 'Fork_Right':
                            fork_right_count = len(track['points'])
                
                if has_main:
                    stats['files_with_main_tracks'] += 1
                if has_fork:
                    stats['files_with_fork_tracks'] += 1
                    
                if fork_left_count > 0 and fork_right_count > 0:
                    stats['files_with_complete_fork_pairs'] += 1
                    stats['fork_track_details'].append({
                        'filename': json_file.name,
                        'fork_left_points': fork_left_count,
                        'fork_right_points': fork_right_count
                    })
                
            except Exception as e:
                logger.warning(f"处理文件 {json_file.name} 时出错: {e}")
        
        # 打印统计结果
        logger.info(f"数据统计结果:")
        logger.info(f"  总文件数: {stats['total_files']}")
        logger.info(f"  包含主轨道的文件: {stats['files_with_main_tracks']}")
        logger.info(f"  包含分叉轨道的文件: {stats['files_with_fork_tracks']}")
        logger.info(f"  包含完整分叉轨道对的文件: {stats['files_with_complete_fork_pairs']}")
        logger.info(f"  主轨道标注总数: {stats['main_track_annotations']}")
        logger.info(f"  分叉轨道标注总数: {stats['fork_track_annotations']}")
        
        if stats['files_with_fork_tracks'] == 0:
            logger.warning("⚠️  数据中没有分叉轨道标注！这可能是检测失败的主要原因。")
        elif stats['files_with_complete_fork_pairs'] == 0:
            logger.warning("⚠️  数据中没有完整的分叉轨道对（需要同时有Fork_Left和Fork_Right）！")
        else:
            logger.info("✅ 数据中包含分叉轨道标注")
        
        return stats
    
    def check_mask_generation(self, json_file: str, output_dir: str) -> Dict[str, any]:
        """
        检查掩码生成过程
        
        Args:
            json_file: JSON标注文件路径
            output_dir: 输出目录
            
        Returns:
            掩码生成检查结果
        """
        logger.info(f"=== 检查掩码生成: {Path(json_file).name} ===")
        
        try:
            # 解析JSON文件
            annotation_data = self.parser.parse_json_file(json_file)
            
            # 生成掩码
            image_shape = (1080, 1920)  # 默认图像尺寸
            mask = self.parser.create_segmentation_mask(annotation_data, image_shape)
            
            # 分析掩码
            unique_labels = np.unique(mask)
            label_counts = {label: np.sum(mask == label) for label in unique_labels}
            
            logger.info(f"生成的掩码统计:")
            for label, count in label_counts.items():
                percentage = count / mask.size * 100
                if label == 0:
                    label_name = "背景"
                elif label == 1:
                    label_name = "主轨道"
                elif label == 2:
                    label_name = "分叉轨道"
                else:
                    label_name = f"标签{label}"
                logger.info(f"  {label_name}(标签{label}): {count} 像素 ({percentage:.2f}%)")
            
            # 可视化掩码
            plt.figure(figsize=(15, 10))
            
            # 1. 显示原始掩码
            plt.subplot(2, 3, 1)
            plt.imshow(mask, cmap='viridis', vmin=0, vmax=2)
            plt.title('原始掩码')
            plt.colorbar()
            
            # 2. 分别显示各类别
            for i, (label, name) in enumerate([(0, '背景'), (1, '主轨道'), (2, '分叉轨道')]):
                plt.subplot(2, 3, i + 2)
                class_mask = (mask == label).astype(np.uint8) * 255
                plt.imshow(class_mask, cmap='gray')
                plt.title(f'{name} (标签{label})\n{label_counts.get(label, 0)} 像素')
                
            # 3. 显示轨道线条（如果有的话）
            plt.subplot(2, 3, 5)
            track_visualization = np.zeros((*image_shape, 3), dtype=np.uint8)
            
            for track in annotation_data['tracks']:
                if track['label'] in ['Main_Left', 'Main_Right']:
                    color = (0, 255, 0)  # 绿色 - 主轨道
                elif track['label'] in ['Fork_Left', 'Fork_Right']:
                    color = (255, 0, 0)  # 红色 - 分叉轨道
                else:
                    continue
                
                points = np.array(track['points'], dtype=np.int32)
                if len(points) > 1:
                    cv2.polylines(track_visualization, [points], False, color, 3)
            
            plt.imshow(track_visualization)
            plt.title('轨道线条\n绿=主轨道, 红=分叉轨道')
            
            # 6. 统计图
            plt.subplot(2, 3, 6)
            labels = list(label_counts.keys())
            counts = list(label_counts.values())
            colors = ['black', 'green', 'red'][:len(labels)]
            plt.bar(labels, counts, color=colors)
            plt.title('像素统计')
            plt.xlabel('标签')
            plt.ylabel('像素数量')
            
            plt.tight_layout()
            
            # 保存结果
            save_path = Path(output_dir) / f'mask_generation_check_{Path(json_file).stem}.png'
            save_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            logger.info(f"掩码生成检查结果保存到: {save_path}")
            
            result = {
                'unique_labels': unique_labels.tolist(),
                'label_counts': label_counts,
                'has_fork_track': 2 in unique_labels,
                'fork_track_pixels': label_counts.get(2, 0),
                'visualization_path': str(save_path)
            }
            
            if not result['has_fork_track']:
                logger.warning("⚠️  生成的掩码中没有分叉轨道像素！")
            else:
                logger.info("✅ 掩码生成正常，包含分叉轨道")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 掩码生成检查失败: {e}")
            return {'error': str(e)}
    
    def check_model_prediction(self, image_path: str, json_file: str, output_dir: str) -> Dict[str, any]:
        """
        检查模型预测过程
        
        Args:
            image_path: 图像路径
            json_file: 对应的JSON标注文件
            output_dir: 输出目录
            
        Returns:
            模型预测检查结果
        """
        logger.info(f"=== 检查模型预测: {Path(image_path).name} ===")
        
        if self.predictor is None:
            logger.error("❌ 预测器未初始化")
            return {'error': '预测器未初始化'}
        
        try:
            # 进行预测
            prediction_mask, prediction_probs = self.predictor.predict_single(image_path, threshold=0.1)  # 降低阈值
            
            # 分析预测结果
            pred_unique = np.unique(prediction_mask)
            pred_counts = {label: np.sum(prediction_mask == label) for label in pred_unique}
            
            logger.info(f"预测结果统计:")
            for label, count in pred_counts.items():
                percentage = count / prediction_mask.size * 100
                if label == 0:
                    label_name = "背景"
                elif label == 1:
                    label_name = "主轨道"
                elif label == 2:
                    label_name = "分叉轨道"
                else:
                    label_name = f"标签{label}"
                logger.info(f"  {label_name}(标签{label}): {count} 像素 ({percentage:.2f}%)")
            
            # 分析概率分布
            logger.info(f"概率分布分析:")
            for c in range(prediction_probs.shape[2]):
                channel_probs = prediction_probs[:, :, c]
                if c == 0:
                    channel_name = "背景"
                elif c == 1:
                    channel_name = "主轨道"
                elif c == 2:
                    channel_name = "分叉轨道"
                else:
                    channel_name = f"通道{c}"
                
                logger.info(f"  {channel_name}: 平均={channel_probs.mean():.4f}, "
                           f"最大={channel_probs.max():.4f}, "
                           f"最小={channel_probs.min():.4f}, "
                           f"标准差={channel_probs.std():.4f}")
            
            # 生成真实掩码用于比较
            annotation_data = self.parser.parse_json_file(json_file)
            gt_mask = self.parser.create_segmentation_mask(annotation_data, prediction_mask.shape)
            
            # 可视化比较
            plt.figure(figsize=(20, 15))
            
            # 1. 原图
            image = cv2.imread(image_path)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            plt.subplot(3, 4, 1)
            plt.imshow(image_rgb)
            plt.title('原图')
            plt.axis('off')
            
            # 2. 真实掩码
            plt.subplot(3, 4, 2)
            plt.imshow(gt_mask, cmap='viridis', vmin=0, vmax=2)
            plt.title('真实掩码')
            plt.colorbar()
            
            # 3. 预测掩码
            plt.subplot(3, 4, 3)
            plt.imshow(prediction_mask, cmap='viridis', vmin=0, vmax=2)
            plt.title('预测掩码')
            plt.colorbar()
            
            # 4. 概率热图 - 背景
            plt.subplot(3, 4, 4)
            plt.imshow(prediction_probs[:, :, 0], cmap='Blues')
            plt.title('背景概率')
            plt.colorbar()
            
            # 5. 概率热图 - 主轨道
            plt.subplot(3, 4, 5)
            plt.imshow(prediction_probs[:, :, 1], cmap='Greens')
            plt.title('主轨道概率')
            plt.colorbar()
            
            # 6. 概率热图 - 分叉轨道
            plt.subplot(3, 4, 6)
            if prediction_probs.shape[2] > 2:
                plt.imshow(prediction_probs[:, :, 2], cmap='Reds')
                plt.title('分叉轨道概率')
            else:
                plt.text(0.5, 0.5, '无分叉轨道通道', ha='center', va='center')
                plt.title('分叉轨道概率 (不存在)')
            plt.colorbar()
            
            # 7. 真实vs预测 - 主轨道
            plt.subplot(3, 4, 7)
            comparison = np.zeros((*prediction_mask.shape, 3))
            comparison[gt_mask == 1] = [0, 1, 0]  # 绿色 - 真实主轨道
            comparison[prediction_mask == 1] += [1, 0, 0]  # 红色 - 预测主轨道
            plt.imshow(np.clip(comparison, 0, 1))
            plt.title('主轨道对比\n绿=真实,红=预测,黄=重叠')
            plt.axis('off')
            
            # 8. 真实vs预测 - 分叉轨道
            plt.subplot(3, 4, 8)
            comparison = np.zeros((*prediction_mask.shape, 3))
            comparison[gt_mask == 2] = [0, 1, 0]  # 绿色 - 真实分叉轨道
            comparison[prediction_mask == 2] += [1, 0, 0]  # 红色 - 预测分叉轨道
            plt.imshow(np.clip(comparison, 0, 1))
            plt.title('分叉轨道对比\n绿=真实,红=预测,黄=重叠')
            plt.axis('off')
            
            # 9-12. 不同阈值下的预测结果
            thresholds = [0.1, 0.3, 0.5, 0.7]
            for i, thresh in enumerate(thresholds):
                plt.subplot(3, 4, 9 + i)
                thresh_mask = self.predictor.post_process_prediction(prediction_probs, threshold=thresh)
                plt.imshow(thresh_mask, cmap='viridis', vmin=0, vmax=2)
                plt.title(f'阈值={thresh}\n分叉像素:{np.sum(thresh_mask == 2)}')
                plt.colorbar()
            
            plt.tight_layout()
            
            # 保存结果
            save_path = Path(output_dir) / f'model_prediction_check_{Path(image_path).stem}.png'
            save_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            logger.info(f"模型预测检查结果保存到: {save_path}")
            
            # 计算评估指标
            gt_fork = (gt_mask == 2).astype(np.float32)
            pred_fork = (prediction_mask == 2).astype(np.float32)
            
            if gt_fork.sum() > 0:
                intersection = (gt_fork * pred_fork).sum()
                union = gt_fork.sum() + pred_fork.sum() - intersection
                iou = intersection / union if union > 0 else 0
                
                precision = intersection / pred_fork.sum() if pred_fork.sum() > 0 else 0
                recall = intersection / gt_fork.sum() if gt_fork.sum() > 0 else 0
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            else:
                iou = precision = recall = f1 = 0
            
            result = {
                'predicted_labels': pred_unique.tolist(),
                'predicted_counts': pred_counts,
                'has_predicted_fork': 2 in pred_unique,
                'fork_pixels_predicted': pred_counts.get(2, 0),
                'fork_pixels_ground_truth': int(np.sum(gt_mask == 2)),
                'fork_track_metrics': {
                    'iou': float(iou),
                    'precision': float(precision),
                    'recall': float(recall),
                    'f1': float(f1)
                },
                'probability_stats': {
                    'fork_channel_mean': float(prediction_probs[:, :, 2].mean()) if prediction_probs.shape[2] > 2 else 0,
                    'fork_channel_max': float(prediction_probs[:, :, 2].max()) if prediction_probs.shape[2] > 2 else 0
                },
                'visualization_path': str(save_path)
            }
            
            if not result['has_predicted_fork']:
                logger.warning("⚠️  模型没有预测出分叉轨道！")
            else:
                logger.info("✅ 模型预测了分叉轨道")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 模型预测检查失败: {e}")
            return {'error': str(e)}
    
    def run_comprehensive_diagnostic(self, 
                                    json_dir: str, 
                                    image_dir: str, 
                                    output_dir: str,
                                    max_samples: int = 5) -> Dict[str, any]:
        """
        运行综合诊断
        
        Args:
            json_dir: JSON标注文件目录
            image_dir: 图像文件目录  
            output_dir: 输出目录
            max_samples: 最大样本数
            
        Returns:
            综合诊断结果
        """
        logger.info("🔍 开始分叉轨道检测综合诊断")
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        results = {
            'data_check': {},
            'mask_generation_checks': [],
            'model_prediction_checks': [],
            'summary': {},
            'recommendations': []
        }
        
        # 1. 检查数据可用性
        logger.info("步骤1: 检查数据可用性")
        results['data_check'] = self.check_data_availability(json_dir)
        
        # 2. 检查掩码生成
        logger.info("步骤2: 检查掩码生成")
        json_files = list(Path(json_dir).glob('*.json'))
        fork_files = []
        
        for json_file in json_files[:max_samples * 2]:  # 多检查一些以找到包含分叉轨道的文件
            try:
                annotation_data = self.parser.parse_json_file(json_file)
                has_fork = any(track['label'] in ['Fork_Left', 'Fork_Right'] 
                              for track in annotation_data['tracks'])
                if has_fork:
                    fork_files.append(json_file)
                    if len(fork_files) >= max_samples:
                        break
            except:
                continue
        
        if not fork_files:
            # 如果没有包含分叉轨道的文件，随机选择一些文件进行检查
            fork_files = json_files[:max_samples]
            logger.warning("⚠️  没有找到包含分叉轨道的文件，将检查普通文件")
        
        for json_file in fork_files:
            mask_check = self.check_mask_generation(str(json_file), output_dir)
            mask_check['filename'] = json_file.name
            results['mask_generation_checks'].append(mask_check)
        
        # 3. 检查模型预测（如果有对应的图像）
        if self.predictor is not None:
            logger.info("步骤3: 检查模型预测")
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            
            for json_file in fork_files[:max_samples]:
                # 尝试找到对应的图像文件
                image_name = json_file.stem
                image_file = None
                
                for ext in image_extensions:
                    potential_path = Path(image_dir) / f"{image_name}{ext}"
                    if potential_path.exists():
                        image_file = potential_path
                        break
                
                if image_file and image_file.exists():
                    pred_check = self.check_model_prediction(str(image_file), str(json_file), output_dir)
                    pred_check['filename'] = image_file.name
                    results['model_prediction_checks'].append(pred_check)
                else:
                    logger.warning(f"未找到对应图像: {image_name}")
        
        # 4. 生成诊断总结
        logger.info("步骤4: 生成诊断总结")
        results['summary'] = self._generate_summary(results)
        results['recommendations'] = self._generate_recommendations(results)
        
        # 5. 保存详细报告
        self._save_diagnostic_report(results, output_dir)
        
        logger.info("🎯 综合诊断完成")
        return results
    
    def _generate_summary(self, results: Dict) -> Dict:
        """生成诊断总结"""
        summary = {}
        
        # 数据问题总结
        data_check = results['data_check']
        summary['data_issues'] = []
        
        if data_check.get('files_with_fork_tracks', 0) == 0:
            summary['data_issues'].append("数据中没有分叉轨道标注")
        elif data_check.get('files_with_complete_fork_pairs', 0) == 0:
            summary['data_issues'].append("数据中没有完整的分叉轨道对")
        
        # 掩码生成问题总结
        mask_checks = results['mask_generation_checks']
        summary['mask_issues'] = []
        
        fork_mask_generated = sum(1 for check in mask_checks if check.get('has_fork_track', False))
        if fork_mask_generated == 0:
            summary['mask_issues'].append("掩码生成过程中没有生成分叉轨道像素")
        
        # 模型预测问题总结
        pred_checks = results['model_prediction_checks']
        summary['model_issues'] = []
        
        fork_predicted = sum(1 for check in pred_checks if check.get('has_predicted_fork', False))
        if fork_predicted == 0:
            summary['model_issues'].append("模型没有预测出分叉轨道")
        
        # 整体问题严重程度
        total_issues = len(summary['data_issues']) + len(summary['mask_issues']) + len(summary['model_issues'])
        if total_issues == 0:
            summary['severity'] = 'low'
        elif total_issues <= 2:
            summary['severity'] = 'medium'
        else:
            summary['severity'] = 'high'
        
        return summary
    
    def _generate_recommendations(self, results: Dict) -> List[str]:
        """生成诊断建议"""
        recommendations = []
        summary = results['summary']
        
        # 数据问题的建议
        if "数据中没有分叉轨道标注" in summary.get('data_issues', []):
            recommendations.append("1. 检查JSON标注文件是否包含Fork_Left和Fork_Right标签")
            recommendations.append("2. 确认数据集是否包含分叉轨道的场景")
            recommendations.append("3. 考虑补充包含分叉轨道的训练数据")
        
        if "数据中没有完整的分叉轨道对" in summary.get('data_issues', []):
            recommendations.append("4. 确保每个分叉轨道都有对应的左右两条边界线标注")
            recommendations.append("5. 检查标注质量，确保Fork_Left和Fork_Right成对出现")
        
        # 掩码生成问题的建议
        if "掩码生成过程中没有生成分叉轨道像素" in summary.get('mask_issues', []):
            recommendations.append("6. 检查RailwayAnnotationParser的create_segmentation_mask方法")
            recommendations.append("7. 验证分叉轨道的多边形生成算法")
            recommendations.append("8. 调整线条厚度参数或多边形生成策略")
        
        # 模型预测问题的建议
        if "模型没有预测出分叉轨道" in summary.get('model_issues', []):
            recommendations.append("9. 检查模型权重是否正确加载")
            recommendations.append("10. 验证模型是否在包含分叉轨道的数据上训练")
            recommendations.append("11. 调整预测阈值，尝试更低的阈值")
            recommendations.append("12. 检查损失函数中分叉轨道的类别权重设置")
            recommendations.append("13. 考虑重新训练模型，增加分叉轨道样本的权重")
        
        # 通用建议
        recommendations.append("14. 使用scripts/test_track_labels.py单独测试轨道标签生成")
        recommendations.append("15. 检查模型训练时的类别分布和损失函数配置")
        
        return recommendations
    
    def _save_diagnostic_report(self, results: Dict, output_dir: str):
        """保存诊断报告"""
        report_path = Path(output_dir) / 'fork_detection_diagnostic_report.md'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 分叉轨道检测诊断报告\n\n")
            
            # 数据检查结果
            f.write("## 1. 数据可用性检查\n\n")
            data_check = results['data_check']
            f.write(f"- 总文件数: {data_check.get('total_files', 0)}\n")
            f.write(f"- 包含主轨道的文件: {data_check.get('files_with_main_tracks', 0)}\n")
            f.write(f"- 包含分叉轨道的文件: {data_check.get('files_with_fork_tracks', 0)}\n")
            f.write(f"- 包含完整分叉轨道对的文件: {data_check.get('files_with_complete_fork_pairs', 0)}\n")
            
            # 掩码生成检查结果
            f.write("\n## 2. 掩码生成检查\n\n")
            for i, check in enumerate(results['mask_generation_checks']):
                f.write(f"### 文件 {i+1}: {check.get('filename', 'unknown')}\n")
                f.write(f"- 是否包含分叉轨道: {'是' if check.get('has_fork_track', False) else '否'}\n")
                f.write(f"- 分叉轨道像素数: {check.get('fork_track_pixels', 0)}\n")
                f.write(f"- 标签分布: {check.get('label_counts', {})}\n\n")
            
            # 模型预测检查结果
            f.write("\n## 3. 模型预测检查\n\n")
            for i, check in enumerate(results['model_prediction_checks']):
                f.write(f"### 文件 {i+1}: {check.get('filename', 'unknown')}\n")
                f.write(f"- 是否预测出分叉轨道: {'是' if check.get('has_predicted_fork', False) else '否'}\n")
                f.write(f"- 预测的分叉轨道像素数: {check.get('fork_pixels_predicted', 0)}\n")
                f.write(f"- 真实的分叉轨道像素数: {check.get('fork_pixels_ground_truth', 0)}\n")
                if 'fork_track_metrics' in check:
                    metrics = check['fork_track_metrics']
                    f.write(f"- 分叉轨道IoU: {metrics.get('iou', 0):.4f}\n")
                    f.write(f"- 分叉轨道精确率: {metrics.get('precision', 0):.4f}\n")
                    f.write(f"- 分叉轨道召回率: {metrics.get('recall', 0):.4f}\n")
                f.write("\n")
            
            # 问题总结
            f.write("\n## 4. 问题总结\n\n")
            summary = results['summary']
            for issue_type in ['data_issues', 'mask_issues', 'model_issues']:
                if issue_type in summary and summary[issue_type]:
                    f.write(f"### {issue_type.replace('_', ' ').title()}\n")
                    for issue in summary[issue_type]:
                        f.write(f"- {issue}\n")
                    f.write("\n")
            
            # 建议
            f.write("\n## 5. 改进建议\n\n")
            for rec in results['recommendations']:
                f.write(f"{rec}\n")
            f.write("\n")
        
        logger.info(f"详细诊断报告保存到: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分叉轨道检测诊断')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml', help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, default='models/checkpoints/railway_track_config/best_model.pth', help='模型权重路径')
    parser.add_argument('--json-dir', type=str, required=True, help='JSON标注文件目录')
    parser.add_argument('--image-dir', type=str, help='图像文件目录（可选）')
    parser.add_argument('--output', type=str, default='outputs/fork_diagnostic', help='输出目录')
    parser.add_argument('--max-samples', type=int, default=5, help='最大检查样本数')
    
    args = parser.parse_args()
    
    # 创建诊断器
    diagnostic = ForkDetectionDiagnostic(args.config, args.checkpoint)
    
    # 设置图像目录（如果未提供，尝试从JSON文件同目录查找）
    image_dir = args.image_dir or args.json_dir
    
    # 运行综合诊断
    results = diagnostic.run_comprehensive_diagnostic(
        args.json_dir,
        image_dir,
        args.output,
        args.max_samples
    )
    
    # 打印关键结果
    print("\n" + "="*60)
    print("🎯 分叉轨道检测诊断结果总结")
    print("="*60)
    
    summary = results['summary']
    
    print(f"\n📊 数据统计:")
    data_check = results['data_check']
    print(f"  - 包含分叉轨道的文件: {data_check.get('files_with_fork_tracks', 0)}/{data_check.get('total_files', 0)}")
    print(f"  - 完整分叉轨道对: {data_check.get('files_with_complete_fork_pairs', 0)}")
    
    print(f"\n⚠️  发现的问题:")
    all_issues = summary.get('data_issues', []) + summary.get('mask_issues', []) + summary.get('model_issues', [])
    if all_issues:
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("  无重大问题发现")
    
    print(f"\n💡 前3个重要建议:")
    recommendations = results['recommendations'][:3]
    for rec in recommendations:
        print(f"  • {rec}")
    
    print(f"\n📁 详细报告和可视化结果保存在: {args.output}")
    print("="*60)


if __name__ == "__main__":
    main() 