#!/usr/bin/env python3
"""
Convert existing NPY mask files to compressed NPZ format to save disk space
"""

import argparse
import numpy as np
from pathlib import Path
from tqdm import tqdm
import shutil


def convert_npy_to_npz(npy_path, delete_original=False):
    """
    Convert a single NPY file to NPZ format
    
    Args:
        npy_path: Path to NPY file
        delete_original: Whether to delete the original NPY file
        
    Returns:
        (original_size, new_size, success)
    """
    try:
        # Check if NPY file exists and is not empty
        if not npy_path.exists() or npy_path.stat().st_size == 0:
            return 0, 0, False
            
        # Load the NPY file
        mask = np.load(str(npy_path))
        original_size = npy_path.stat().st_size
        
        # Convert float32 to uint8 if needed
        if mask.dtype == np.float32 or mask.dtype == np.float64:
            # Assuming values are 0.0 or 1.0
            mask_uint8 = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
        else:
            mask_uint8 = mask.astype(np.uint8)
        
        # Save as compressed NPZ
        npz_path = npy_path.with_suffix('.npz')
        np.savez_compressed(str(npz_path), mask=mask_uint8)
        
        new_size = npz_path.stat().st_size
        
        # Delete original if requested
        if delete_original:
            npy_path.unlink()
            
        return original_size, new_size, True
        
    except Exception as e:
        print(f"Error converting {npy_path}: {e}")
        return 0, 0, False


def main():
    parser = argparse.ArgumentParser(description='Convert NPY files to compressed NPZ format')
    parser.add_argument('--data-dir', type=str, required=True, 
                       help='Root directory containing NPY files')
    parser.add_argument('--delete-original', action='store_true',
                       help='Delete original NPY files after conversion')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without actually converting')
    parser.add_argument('--backup-dir', type=str,
                       help='Directory to backup original NPY files before deletion')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    
    # Find all NPY files
    npy_files = list(data_dir.rglob('masks/*.npy'))
    
    # Filter out empty files
    npy_files = [f for f in npy_files if f.stat().st_size > 0]
    
    print(f"Found {len(npy_files)} non-empty NPY files")
    
    if len(npy_files) == 0:
        print("No NPY files to convert!")
        return
    
    # Calculate total size
    total_original_size = sum(f.stat().st_size for f in npy_files)
    print(f"Total NPY size: {total_original_size / 1024 / 1024 / 1024:.2f} GB")
    
    if args.dry_run:
        # Sample a few files to estimate compression
        sample_size = min(5, len(npy_files))
        sample_files = npy_files[:sample_size]
        
        print(f"\nDry run - testing {sample_size} files:")
        sample_original = 0
        sample_new = 0
        
        for npy_path in sample_files:
            try:
                mask = np.load(str(npy_path))
                original = npy_path.stat().st_size
                
                # Simulate compression
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.npz') as tmp:
                    if mask.dtype == np.float32:
                        mask_uint8 = (mask * 255).astype(np.uint8) if mask.max() <= 1 else mask.astype(np.uint8)
                    else:
                        mask_uint8 = mask.astype(np.uint8)
                    np.savez_compressed(tmp.name, mask=mask_uint8)
                    new_size = Path(tmp.name).stat().st_size
                    
                print(f"  {npy_path.name}: {original / 1024 / 1024:.2f} MB -> {new_size / 1024 / 1024:.2f} MB")
                sample_original += original
                sample_new += new_size
            except Exception as e:
                print(f"  Error with {npy_path.name}: {e}")
        
        if sample_original > 0:
            compression_ratio = sample_original / sample_new
            estimated_new_size = total_original_size / compression_ratio
            print(f"\nEstimated compression ratio: {compression_ratio:.1f}x")
            print(f"Estimated total NPZ size: {estimated_new_size / 1024 / 1024 / 1024:.2f} GB")
            print(f"Estimated space saved: {(total_original_size - estimated_new_size) / 1024 / 1024 / 1024:.2f} GB")
        
        return
    
    # Create backup directory if specified
    if args.backup_dir and args.delete_original:
        backup_dir = Path(args.backup_dir)
        backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"Backing up NPY files to: {backup_dir}")
    
    # Convert files
    print(f"\nConverting {len(npy_files)} files...")
    
    total_original = 0
    total_new = 0
    success_count = 0
    
    for npy_path in tqdm(npy_files, desc="Converting"):
        # Backup if requested
        if args.backup_dir and args.delete_original:
            rel_path = npy_path.relative_to(data_dir)
            backup_path = backup_dir / rel_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(npy_path, backup_path)
        
        # Convert
        orig_size, new_size, success = convert_npy_to_npz(npy_path, args.delete_original)
        
        if success:
            total_original += orig_size
            total_new += new_size
            success_count += 1
    
    # Summary
    print(f"\nConversion complete!")
    print(f"Successfully converted: {success_count}/{len(npy_files)} files")
    
    if success_count > 0:
        print(f"Original total size: {total_original / 1024 / 1024 / 1024:.2f} GB")
        print(f"New total size: {total_new / 1024 / 1024 / 1024:.2f} GB")
        print(f"Compression ratio: {total_original / total_new:.1f}x")
        print(f"Space saved: {(total_original - total_new) / 1024 / 1024 / 1024:.2f} GB")
        
        if args.delete_original:
            print(f"\nOriginal NPY files have been deleted.")
            if args.backup_dir:
                print(f"Backups are available in: {args.backup_dir}")


if __name__ == '__main__':
    main()