#!/usr/bin/env python
"""
测试轨道标签分离脚本
验证主轨道(标签1)和分叉轨道(标签2)使用不同标签的功能
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_track_labels(json_file: Path, output_dir: Path):
    """测试轨道标签分离功能"""
    print(f"\n=== 测试轨道标签分离 ===")
    print(f"JSON文件: {json_file.name}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 统计轨道类型
    main_count = 0
    fork_count = 0
    main_points = []
    fork_points = []
    
    for track in annotation_data['tracks']:
        if track['label'] in ['Main_Left', 'Main_Right']:
            main_count += 1
            main_points.extend(track['points'])
            print(f"    主轨道: {track['label']}, {len(track['points'])} 个点")
        elif track['label'] in ['Fork_Left', 'Fork_Right']:
            fork_count += 1
            fork_points.extend(track['points'])
            print(f"    分叉轨道: {track['label']}, {len(track['points'])} 个点")
    
    print(f"\n轨道类型统计:")
    print(f"  主轨道: {main_count} 条，总计 {len(main_points)} 个关键点")
    print(f"  分叉轨道: {fork_count} 条，总计 {len(fork_points)} 个关键点")
    
    # 设置图像尺寸
    image_shape = (1080, 1920)
    
    # 生成分割掩码
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    print(f"\n生成的掩码信息:")
    print(f"  掩码形状: {mask.shape}")
    print(f"  数据类型: {mask.dtype}")
    
    # 检查各通道的内容
    for i in range(mask.shape[2]):
        unique_vals = np.unique(mask[:, :, i])
        non_zero_count = np.count_nonzero(mask[:, :, i])
        if i == 0:
            channel_name = "背景"
        elif i == 1:
            channel_name = "主轨道(标签1)"
        elif i == 2:
            channel_name = "分叉轨道(标签2)"
        else:
            channel_name = f"通道{i}"
        
        print(f"  {channel_name}: 唯一值{unique_vals}, 非零像素{non_zero_count}")
    
    # 转换为单通道掩码以便分析
    merged_mask = np.argmax(mask, axis=2)
    unique_labels = np.unique(merged_mask)
    
    print(f"\n最终掩码标签:")
    print(f"  唯一标签: {unique_labels}")
    for label in unique_labels:
        count = np.sum(merged_mask == label)
        if label == 0:
            print(f"    标签0(背景): {count} 像素")
        elif label == 1:
            print(f"    标签1(主轨道): {count} 像素")
        elif label == 2:
            print(f"    标签2(分叉轨道): {count} 像素")
        else:
            print(f"    标签{label}: {count} 像素")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 显示背景通道
    axes[0, 0].imshow(mask[:, :, 0], cmap='gray')
    axes[0, 0].set_title('背景通道\n(标签0)')
    axes[0, 0].axis('off')
    
    # 2. 显示主轨道通道
    axes[0, 1].imshow(mask[:, :, 1], cmap='Greens')
    axes[0, 1].set_title(f'主轨道通道\n(标签1) - {np.count_nonzero(mask[:, :, 1])} 像素')
    axes[0, 1].axis('off')
    
    # 3. 显示分叉轨道通道
    if mask.shape[2] > 2:
        axes[0, 2].imshow(mask[:, :, 2], cmap='Reds')
        fork_pixels = np.count_nonzero(mask[:, :, 2])
        axes[0, 2].set_title(f'分叉轨道通道\n(标签2) - {fork_pixels} 像素')
    else:
        axes[0, 2].text(0.5, 0.5, '无分叉轨道通道', ha='center', va='center', 
                       transform=axes[0, 2].transAxes)
        axes[0, 2].set_title('分叉轨道通道\n(标签2) - 不存在')
    axes[0, 2].axis('off')
    
    # 4. 显示合并后的标签掩码
    colors = ['black', 'green', 'red', 'blue']  # 背景、主轨道、分叉轨道、其他
    cmap = ListedColormap(colors[:len(unique_labels)])
    
    axes[1, 0].imshow(merged_mask, cmap=cmap, vmin=0, vmax=max(unique_labels))
    axes[1, 0].set_title('合并标签掩码\n黑=背景(0), 绿=主轨道(1), 红=分叉轨道(2)')
    axes[1, 0].axis('off')
    
    # 5. 显示标签统计
    label_stats = []
    for label in unique_labels:
        count = np.sum(merged_mask == label)
        percentage = count / merged_mask.size * 100
        if label == 0:
            name = "背景"
        elif label == 1:
            name = "主轨道"
        elif label == 2:
            name = "分叉轨道"
        else:
            name = f"标签{label}"
        label_stats.append(f"{name}(标签{label}): {count}像素 ({percentage:.1f}%)")
    
    stats_text = "\n".join(label_stats)
    axes[1, 1].text(0.1, 0.5, stats_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='center')
    axes[1, 1].set_title('标签统计')
    axes[1, 1].axis('off')
    
    # 6. 显示可视化版本（使用颜色映射值）
    visual_mask = np.zeros_like(merged_mask, dtype=np.uint8)
    visual_mask[merged_mask == 1] = 85   # 主轨道 -> 绿色值
    visual_mask[merged_mask == 2] = 170  # 分叉轨道 -> 红色值
    
    # 显示原始像素值分布
    visual_unique = np.unique(visual_mask)
    visual_text = f"可视化掩码像素值:\n"
    for val in visual_unique:
        count = np.sum(visual_mask == val)
        if val == 0:
            visual_text += f"背景(0): {count}\n"
        elif val == 85:
            visual_text += f"主轨道(85): {count}\n"
        elif val == 170:
            visual_text += f"分叉轨道(170): {count}\n"
        else:
            visual_text += f"值{val}: {count}\n"
    
    axes[1, 2].text(0.1, 0.5, visual_text, transform=axes[1, 2].transAxes, 
                   fontsize=10, verticalalignment='center')
    axes[1, 2].set_title('可视化掩码值')
    axes[1, 2].axis('off')
    
    plt.suptitle(f'轨道标签分离测试: {json_file.name}', fontsize=16)
    plt.tight_layout()
    
    # 保存结果
    output_path = output_dir / f'track_labels_test_{json_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果保存到: {output_path}")
    
    # 验证标签正确性
    if 1 in unique_labels and main_count > 0:
        print("✅ 主轨道标签(1)正确生成")
    elif main_count > 0:
        print("❌ 主轨道存在但标签(1)未生成")
    
    if 2 in unique_labels and fork_count > 0:
        print("✅ 分叉轨道标签(2)正确生成")
    elif fork_count > 0:
        print("❌ 分叉轨道存在但标签(2)未生成")
    elif fork_count == 0:
        print("ℹ️  无分叉轨道数据")
    
    return merged_mask, annotation_data


def batch_test_track_labels(json_dir: Path, output_dir: Path, max_files: int = 5):
    """批量测试轨道标签"""
    print(f"\n=== 批量测试轨道标签 ===")
    
    json_files = list(json_dir.glob('*.json'))[:max_files]
    results = []
    
    for json_file in json_files:
        try:
            print(f"\n处理: {json_file.name}")
            merged_mask, annotation_data = test_track_labels(json_file, output_dir)
            
            unique_labels = np.unique(merged_mask)
            main_pixels = np.sum(merged_mask == 1) if 1 in unique_labels else 0
            fork_pixels = np.sum(merged_mask == 2) if 2 in unique_labels else 0
            
            results.append({
                'filename': json_file.name,
                'tracks': len(annotation_data['tracks']),
                'unique_labels': unique_labels.tolist(),
                'main_pixels': main_pixels,
                'fork_pixels': fork_pixels
            })
            
        except Exception as e:
            print(f"处理 {json_file.name} 时出错: {e}")
    
    # 保存批量测试报告
    report_path = output_dir / 'track_labels_batch_report.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("轨道标签分离批量测试报告\n")
        f.write("=" * 50 + "\n\n")
        
        for result in results:
            f.write(f"文件: {result['filename']}\n")
            f.write(f"  轨道数量: {result['tracks']}\n")
            f.write(f"  生成的标签: {result['unique_labels']}\n")
            f.write(f"  主轨道像素(标签1): {result['main_pixels']}\n")
            f.write(f"  分叉轨道像素(标签2): {result['fork_pixels']}\n\n")
    
    print(f"\n批量测试报告保存到: {report_path}")


def main():
    parser = argparse.ArgumentParser(description='测试轨道标签分离')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='JSON标注目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/track_labels_test',
                       help='输出目录')
    parser.add_argument('--test-single', action='store_true',
                       help='只测试单个文件')
    parser.add_argument('--max-files', type=int, default=5,
                       help='最大测试文件数')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    json_dir = Path(args.json_dir)
    
    print("轨道标签分离测试开始...")
    print(f"JSON目录: {json_dir}")
    print(f"输出目录: {output_dir}")
    
    json_files = list(json_dir.glob('*.json'))
    if len(json_files) == 0:
        print("错误：没有找到JSON文件")
        return
    
    if args.test_single:
        # 测试单个文件
        json_file = json_files[0]
        test_track_labels(json_file, output_dir)
    else:
        # 批量测试
        batch_test_track_labels(json_dir, output_dir, args.max_files)
    
    print(f"\n测试完成！结果保存在: {output_dir}")


if __name__ == '__main__':
    main() 