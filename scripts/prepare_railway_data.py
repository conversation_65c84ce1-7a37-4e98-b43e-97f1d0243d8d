#!/usr/bin/env python
"""
铁路轨道数据预处理脚本
处理JSON标注文件，生成语义分割数据集
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data import preprocess_json_annotations, create_data_splits


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='预处理铁路轨道标注数据')
    
    parser.add_argument(
        '--json-dir',
        type=str,
        required=True,
        help='JSON标注文件目录'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default='data/processed',
        help='输出目录'
    )
    parser.add_argument(
        '--camera-type',
        type=str,
        choices=['6mm', '25mm', None],
        default=None,
        help='相机类型筛选'
    )
    parser.add_argument(
        '--download-images',
        action='store_true',
        help='是否从URL下载图像'
    )
    parser.add_argument(
        '--split-ratio',
        nargs=3,
        type=float,
        default=[0.7, 0.15, 0.15],
        help='训练集、验证集、测试集的比例'
    )
    parser.add_argument(
        '--k-folds',
        type=int,
        default=None,
        help='K折交叉验证的折数'
    )
    parser.add_argument(
        '--random-seed',
        type=int,
        default=42,
        help='随机种子'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 路径转换
    json_dir = Path(args.json_dir)
    output_dir = Path(args.output_dir)
    
    # 检查输入目录
    if not json_dir.exists():
        print(f"错误: JSON目录不存在: {json_dir}")
        return
    
    print(f"JSON标注目录: {json_dir}")
    print(f"输出目录: {output_dir}")
    
    # 步骤1: 预处理JSON标注
    print("\n步骤1: 预处理JSON标注文件...")
    preprocess_json_annotations(
        json_dir=json_dir,
        output_dir=output_dir,
        download_images=args.download_images,
        camera_type=args.camera_type
    )
    
    # 步骤2: 创建数据集划分
    print("\n步骤2: 创建数据集划分...")
    
    # 检查处理后的数据
    images_dir = output_dir / 'images'
    masks_dir = output_dir / 'masks'
    
    if not images_dir.exists() or not masks_dir.exists():
        print("错误: 预处理失败，未找到图像或掩码目录")
        return
    
    # 创建数据划分
    split_config = {
        'train': args.split_ratio[0],
        'val': args.split_ratio[1],
        'test': args.split_ratio[2]
    }
    
    splits = create_data_splits(
        images_dir=images_dir,
        masks_dir=masks_dir,
        split_config=split_config,
        k_folds=args.k_folds,
        random_state=args.random_seed,
        save_dir=output_dir / 'splits'
    )
    
    # 步骤3: 组织数据到标准目录结构
    print("\n步骤3: 组织数据到标准目录结构...")
    
    import shutil
    
    if args.k_folds is None:
        # 普通划分
        for split_name, split_data in splits.items():
            split_images_dir = output_dir / 'images' / split_name
            split_masks_dir = output_dir / 'masks' / split_name
            
            split_images_dir.mkdir(parents=True, exist_ok=True)
            split_masks_dir.mkdir(parents=True, exist_ok=True)
            
            for image_path, mask_path in split_data:
                # 复制图像
                shutil.copy2(image_path, split_images_dir / image_path.name)
                # 复制掩码
                shutil.copy2(mask_path, split_masks_dir / mask_path.name)
            
            print(f"{split_name}: {len(split_data)} 个样本")
    else:
        # K折交叉验证
        for fold_key, fold_splits in splits.items():
            print(f"\n{fold_key}:")
            for split_name, split_data in fold_splits.items():
                split_images_dir = output_dir / fold_key / 'images' / split_name
                split_masks_dir = output_dir / fold_key / 'masks' / split_name
                
                split_images_dir.mkdir(parents=True, exist_ok=True)
                split_masks_dir.mkdir(parents=True, exist_ok=True)
                
                for image_path, mask_path in split_data:
                    # 复制图像
                    shutil.copy2(image_path, split_images_dir / image_path.name)
                    # 复制掩码
                    shutil.copy2(mask_path, split_masks_dir / mask_path.name)
                
                print(f"  {split_name}: {len(split_data)} 个样本")
    
    # 清理原始图像和掩码目录（可选）
    print("\n清理临时文件...")
    if (output_dir / 'images').exists() and not any((output_dir / 'images').iterdir()):
        shutil.rmtree(output_dir / 'images')
    if (output_dir / 'masks').exists() and not any((output_dir / 'masks').iterdir()):
        shutil.rmtree(output_dir / 'masks')
    
    print("\n数据预处理完成！")
    
    # 显示数据集统计信息
    print("\n数据集统计信息:")
    metadata_path = output_dir / 'metadata.json'
    if metadata_path.exists():
        import json
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        print(f"类别数: {metadata['num_classes']}")
        print(f"类别名称: {metadata['classes']}")
        print(f"支持的相机类型: {metadata['camera_types']}")
        print(f"处理的文件数: {metadata['processed_files']}")


if __name__ == '__main__':
    main() 