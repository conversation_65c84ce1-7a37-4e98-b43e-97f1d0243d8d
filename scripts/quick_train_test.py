#!/usr/bin/env python
"""
快速训练测试脚本
用于验证数据修复后是否能正常运行训练流程
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import yaml
from src.data.dataloader import get_dataloader
from src.data.transforms import get_train_transforms, get_val_transforms


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def test_data_loading(config: dict, data_root: str):
    """测试数据加载"""
    print("测试数据加载...")
    
    try:
        # 获取数据变换
        train_transform = get_train_transforms(config)
        val_transform = get_val_transforms(config)
        
        # 创建数据加载器
        train_loader = get_dataloader(
            data_root=data_root,
            split='train',
            config=config,
            transform=train_transform,
            camera_type='6mm'  # 测试6mm相机数据
        )
        
        val_loader = get_dataloader(
            data_root=data_root,
            split='val',
            config=config,
            transform=val_transform,
            camera_type='6mm'
        )
        
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        # 测试数据加载
        print("测试训练数据加载...")
        for i, batch in enumerate(train_loader):
            print(f"批次 {i}:")
            print(f"  图像形状: {batch['image'].shape}")
            if 'mask' in batch:
                print(f"  掩码形状: {batch['mask'].shape}")
            print(f"  文件名: {batch['filename'][:3]}...")  # 显示前3个文件名
            
            if i >= 2:  # 只测试前3个批次
                break
        
        print("数据加载测试成功！")
        
        return train_loader, val_loader
        
    except Exception as e:
        print(f"数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def test_model_creation(config: dict):
    """测试模型创建"""
    print("测试模型创建...")
    
    try:
        # 简单的模型创建（不依赖复杂的注册机制）
        import segmentation_models_pytorch as smp
        
        model = smp.PAN(
            encoder_name=config['model']['encoder'],
            encoder_weights=config['model']['encoder_weights'],
            classes=config['model']['classes'],
            activation=config['model']['activation']
        )
        
        print(f"模型创建成功")
        print(f"参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return model
        
    except Exception as e:
        print(f"模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_forward_pass(model, train_loader):
    """测试前向传播"""
    print("测试前向传播...")
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()
        
        # 获取一个批次的数据
        batch = next(iter(train_loader))
        images = batch['image'].to(device)
        
        print(f"输入图像形状: {images.shape}")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(images)
        
        print(f"输出形状: {outputs.shape}")
        print("前向传播测试成功！")
        
        return True
        
    except Exception as e:
        print(f"前向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速训练测试')
    parser.add_argument(
        '--config',
        type=str,
        default='configs/railway_track_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--data-root',
        type=str,
        default='data/railway_track_dataset',
        help='数据根目录'
    )
    
    args = parser.parse_args()
    
    # 检查数据目录是否存在
    data_root = Path(args.data_root)
    if not data_root.exists():
        print(f"错误: 数据目录不存在: {data_root}")
        print("请先运行数据修复脚本:")
        print("python scripts/fix_data_structure.py")
        return
    
    # 检查必要的子目录
    required_dirs = [
        data_root / 'images' / 'train',
        data_root / 'images' / 'val', 
        data_root / 'masks' / 'train',
        data_root / 'masks' / 'val'
    ]
    
    missing_dirs = [d for d in required_dirs if not d.exists()]
    if missing_dirs:
        print("错误: 缺少必要的数据目录:")
        for d in missing_dirs:
            print(f"  {d}")
        print("请先运行数据修复脚本:")
        print("python scripts/fix_data_structure.py")
        return
    
    # 加载配置
    print(f"加载配置文件: {args.config}")
    config = load_config(args.config)
    
    print("=== 快速训练测试 ===")
    
    # 步骤1: 测试数据加载
    train_loader, val_loader = test_data_loading(config, str(data_root))
    if train_loader is None:
        print("数据加载测试失败，停止测试")
        return
    
    # 步骤2: 测试模型创建
    model = test_model_creation(config)
    if model is None:
        print("模型创建测试失败，停止测试")
        return
    
    # 步骤3: 测试前向传播
    success = test_forward_pass(model, train_loader)
    if not success:
        print("前向传播测试失败")
        return
    
    print("\n=== 所有测试通过！ ===")
    print("数据和模型配置正确，可以开始正式训练")
    print("运行以下命令开始训练:")
    print(f"python scripts/train.py --config {args.config}")


if __name__ == '__main__':
    main() 