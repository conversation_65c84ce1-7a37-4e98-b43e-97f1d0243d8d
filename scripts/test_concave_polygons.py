#!/usr/bin/env python3
"""
测试cv2.fillPoly对于凹陷多边形的处理
演示各种复杂形状的填充效果
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path

def test_concave_polygons():
    """测试凹陷多边形的填充效果"""
    print("=== cv2.fillPoly 凹陷多边形处理测试 ===")
    
    # 创建图像
    image_shape = (400, 600)
    
    # 测试用例1：简单凹陷多边形
    print("\n1. 简单凹陷多边形")
    concave_points = np.array([
        [100, 100],  # 左上
        [300, 100],  # 右上
        [300, 300],  # 右下
        [200, 200],  # 凹陷点
        [100, 300]   # 左下
    ], dtype=np.int32)
    
    # 测试用例2：复杂凹陷多边形（星形）
    print("2. 复杂凹陷多边形（星形）")
    star_points = np.array([
        [300, 50],   # 顶部
        [350, 200],  # 右上
        [500, 200],  # 右上外
        [400, 300],  # 右下
        [450, 450],  # 右下外
        [300, 350],  # 底部
        [150, 450],  # 左下外
        [200, 300],  # 左下
        [100, 200],  # 左上外
        [250, 200]   # 左上
    ], dtype=np.int32)
    
    # 测试用例3：自相交多边形
    print("3. 自相交多边形")
    self_intersecting_points = np.array([
        [100, 100],  # 起点
        [400, 100],  # 右上
        [100, 300],  # 左下
        [400, 300]   # 右下
    ], dtype=np.int32)
    
    # 测试用例4：轨道形状（模拟实际场景）
    print("4. 轨道形状（模拟实际场景）")
    track_points = np.array([
        [100, 150],  # 左边线起点
        [200, 180],  # 左边线中间
        [300, 200],  # 左边线终点
        [350, 200],  # 右边线终点
        [250, 180],  # 右边线中间
        [150, 150]   # 右边线起点
    ], dtype=np.int32)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 简单凹陷多边形
    mask1 = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(mask1, [concave_points], 255)
    
    axes[0, 0].set_title('简单凹陷多边形')
    axes[0, 0].imshow(mask1, cmap='viridis')
    axes[0, 0].axis('off')
    
    # 绘制边界
    boundary1 = np.zeros(image_shape, dtype=np.uint8)
    cv2.polylines(boundary1, [concave_points], True, 255, 2)
    
    # 2. 复杂凹陷多边形（星形）
    mask2 = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(mask2, [star_points], 255)
    
    axes[0, 1].set_title('复杂凹陷多边形（星形）')
    axes[0, 1].imshow(mask2, cmap='viridis')
    axes[0, 1].axis('off')
    
    # 3. 自相交多边形
    mask3 = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(mask3, [self_intersecting_points], 255)
    
    axes[1, 0].set_title('自相交多边形')
    axes[1, 0].imshow(mask3, cmap='viridis')
    axes[1, 0].axis('off')
    
    # 4. 轨道形状
    mask4 = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(mask4, [track_points], 255)
    
    axes[1, 1].set_title('轨道形状')
    axes[1, 1].imshow(mask4, cmap='viridis')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('outputs/concave_polygons_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 统计结果
    print(f"\n填充结果统计:")
    print(f"  简单凹陷多边形: {np.sum(mask1 > 0)} 像素")
    print(f"  复杂凹陷多边形: {np.sum(mask2 > 0)} 像素")
    print(f"  自相交多边形: {np.sum(mask3 > 0)} 像素")
    print(f"  轨道形状: {np.sum(mask4 > 0)} 像素")
    
    return mask1, mask2, mask3, mask4

def test_fillPoly_algorithm():
    """测试fillPoly算法的具体行为"""
    print("\n=== fillPoly算法行为分析 ===")
    
    # 创建一个简单的凹陷多边形来演示算法
    points = np.array([
        [200, 100],  # 顶部
        [300, 150],  # 右上
        [250, 200],  # 凹陷
        [150, 200],  # 左下
        [100, 150]   # 左上
    ], dtype=np.int32)
    
    image_shape = (300, 400)
    mask = np.zeros(image_shape, dtype=np.uint8)
    
    # 填充多边形
    cv2.fillPoly(mask, [points], 255)
    
    # 创建详细可视化
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 1. 显示原始点和边界
    axes[0].set_title('原始多边形边界')
    axes[0].set_xlim(0, 400)
    axes[0].set_ylim(300, 0)
    
    # 绘制边界
    boundary_x = np.append(points[:, 0], points[0, 0])
    boundary_y = np.append(points[:, 1], points[0, 1])
    axes[0].plot(boundary_x, boundary_y, 'r-', linewidth=2, label='边界')
    
    # 绘制点
    axes[0].scatter(points[:, 0], points[:, 1], c='red', s=100, zorder=5)
    
    # 标注点
    for i, (x, y) in enumerate(points):
        axes[0].annotate(f'P{i}', (x+10, y+10), fontsize=12)
    
    axes[0].legend()
    axes[0].grid(True)
    
    # 2. 显示填充结果
    axes[1].set_title('fillPoly填充结果')
    axes[1].imshow(mask, cmap='viridis')
    axes[1].axis('off')
    
    # 3. 显示边界和填充的对比
    axes[2].set_title('边界 vs 填充对比')
    
    # 创建边界图像
    boundary_mask = np.zeros(image_shape, dtype=np.uint8)
    cv2.polylines(boundary_mask, [points], True, 255, 2)
    
    # 显示对比
    comparison = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    comparison[:, :, 0] = boundary_mask  # 蓝色：边界
    comparison[:, :, 1] = mask  # 绿色：填充
    
    axes[2].imshow(comparison)
    axes[2].set_title('蓝色=边界, 绿色=填充内部')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig('outputs/fillpoly_algorithm_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"凹陷多边形填充像素数: {np.sum(mask > 0)}")
    print(f"边界像素数: {np.sum(boundary_mask > 0)}")
    
    return mask, boundary_mask

def test_railway_track_concave():
    """测试轨道形状中的凹陷情况"""
    print("\n=== 轨道形状凹陷测试 ===")
    
    # 模拟轨道形状，包含凹陷
    left_track = np.array([
        [100, 150],  # 左边线起点
        [200, 180],  # 左边线中间
        [300, 200],  # 左边线终点
    ], dtype=np.int32)
    
    right_track = np.array([
        [150, 150],  # 右边线起点
        [250, 180],  # 右边线中间（凹陷）
        [350, 200],  # 右边线终点
    ], dtype=np.int32)
    
    # 构造多边形：左边线 + 右边线反向
    polygon_points = []
    polygon_points.extend(left_track)
    polygon_points.extend(reversed(right_track))
    
    polygon_array = np.array(polygon_points, dtype=np.int32)
    
    image_shape = (300, 400)
    mask = np.zeros(image_shape, dtype=np.uint8)
    
    # 填充多边形
    cv2.fillPoly(mask, [polygon_array], 255)
    
    # 可视化
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # 1. 显示轨道线和多边形
    axes[0].set_title('轨道形状构造')
    axes[0].set_xlim(0, 400)
    axes[0].set_ylim(300, 0)
    
    # 绘制左边线
    axes[0].plot(left_track[:, 0], left_track[:, 1], 'g-o', label='Left Track', linewidth=2, markersize=8)
    
    # 绘制右边线
    axes[0].plot(right_track[:, 0], right_track[:, 1], 'b-o', label='Right Track', linewidth=2, markersize=8)
    
    # 绘制多边形边界
    boundary_x = np.append(polygon_array[:, 0], polygon_array[0, 0])
    boundary_y = np.append(polygon_array[:, 1], polygon_array[0, 1])
    axes[0].plot(boundary_x, boundary_y, 'r--', linewidth=2, label='Polygon Boundary')
    
    axes[0].legend()
    axes[0].grid(True)
    
    # 2. 显示填充结果
    axes[1].set_title('轨道形状填充结果')
    axes[1].imshow(mask, cmap='viridis')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('outputs/railway_track_concave_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"轨道形状填充像素数: {np.sum(mask > 0)}")
    
    return mask

if __name__ == "__main__":
    # 创建输出目录
    output_dir = Path('outputs')
    output_dir.mkdir(exist_ok=True)
    
    # 运行测试
    test_concave_polygons()
    test_fillPoly_algorithm()
    test_railway_track_concave()
    
    print("\n✅ 凹陷多边形测试完成！") 