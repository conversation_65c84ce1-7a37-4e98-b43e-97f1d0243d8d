#!/usr/bin/env python3
"""
测试多标签配置
验证集成学习的多标签实现是否与notebook一致
"""

import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.multilabel_losses import MultilabelJaccardLoss, MultilabelFocalLoss, MultilabelCombinedLoss


def test_multilabel_configuration():
    """测试多标签配置"""
    
    print("=== 多标签配置测试 ===\n")
    
    # 1. 读取配置文件
    config_path = project_root / 'configs' / 'railway_track_config.yaml'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("1. 损失函数配置:")
    loss_config = config['loss']
    print(f"   - 类型: {loss_config['type']}")
    print(f"   - <PERSON><PERSON><PERSON>权重: {loss_config.get('jaccard_weight', 0.5)}")
    print(f"   - Focal权重: {loss_config.get('focal_weight', 0.5)}")
    print(f"   - Alpha权重: {loss_config.get('alpha', [0.1, 0.3, 0.6])}")
    print(f"   - Gamma: {loss_config.get('gamma', 2.0)}")
    print()
    
    # 2. 类别配置
    print("2. 类别配置:")
    classes = config['data']['classes']
    print(f"   - 类别数量: {len(classes)}")
    for i, cls in enumerate(classes):
        alpha = loss_config.get('alpha', [0.1, 0.3, 0.6])[i]
        print(f"   - 类别{i} ({cls}): alpha权重 = {alpha}")
    print()
    
    # 3. 模型配置
    print("3. 模型配置:")
    model_config = config['model']
    print(f"   - 激活函数: {model_config.get('activation', 'sigmoid')}")
    print(f"   - 类别数: {model_config.get('classes', 3)}")
    print()
    
    # 4. 集成配置
    print("4. 集成学习配置:")
    print("   - 模型1: EfficientNet-B4 (PAN)")
    print("   - 模型2: ECA-NFNet-L2 (PAN)")
    print("   - 模型3: SE-ResNet-152d (PAN)")
    print()
    
    # 5. 验证损失函数
    print("5. 验证损失函数创建:")
    try:
        # 创建多标签Jaccard损失
        jaccard_loss = MultilabelJaccardLoss(mode='multilabel')
        print("   ✓ MultilabelJaccardLoss 创建成功")
        
        # 创建多标签Focal损失
        focal_loss = MultilabelFocalLoss(
            alpha=loss_config.get('alpha', [0.1, 0.3, 0.6]),
            gamma=loss_config.get('gamma', 2.0)
        )
        print("   ✓ MultilabelFocalLoss 创建成功")
        
        # 创建组合损失
        combined_loss = MultilabelCombinedLoss(
            jaccard_weight=loss_config.get('jaccard_weight', 0.5),
            focal_weight=loss_config.get('focal_weight', 0.5),
            alpha=loss_config.get('alpha', [0.1, 0.3, 0.6]),
            gamma=loss_config.get('gamma', 2.0)
        )
        print("   ✓ MultilabelCombinedLoss 创建成功")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 6. 与notebook对比
    print("6. 与Notebook实现对比:")
    print("   Notebook实现:")
    print("   - 损失函数: smp.losses.JaccardLoss(mode='multilabel')")
    print("   - 激活函数: Sigmoid (每个类别独立)")
    print("   - 集成权重 (per-class):")
    print("     * Class 0: [0.41, 0.50, 0.09]")
    print("     * Class 1: [0.15, 0.44, 0.41]")
    print("     * Class 2: [0.11, 0.50, 0.39]")
    print()
    print("   当前实现:")
    print("   - 损失函数: MultilabelCombinedLoss (Jaccard + Focal)")
    print("   - 激活函数: Sigmoid (与notebook一致)")
    print("   - Focal Loss权重:")
    print("     * 背景: 0.1")
    print("     * 主轨道: 0.3")
    print("     * 分叉轨道: 0.6")
    print()
    
    print("=== 配置验证完成 ===")
    print("\n关键改进:")
    print("1. 支持多标签分割（像素可同时属于多个类别）")
    print("2. 使用Focal Loss处理类别不平衡")
    print("3. 每个类别有独立的权重控制")
    print("4. 后处理使用阈值而非argmax，保持多标签特性")


if __name__ == '__main__':
    test_multilabel_configuration()