#!/usr/bin/env python3
"""
测试多边形生成逻辑
可视化点围成内部的填充过程
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path

def visualize_polygon_logic():
    """可视化多边形生成逻辑"""
    print("=== 多边形生成逻辑可视化 ===")
    
    # 创建测试数据
    # 左边线：从左到右
    left_points = [(100, 100), (200, 150), (300, 200), (400, 250)]
    # 右边线：从左到右（同向）
    right_points = [(150, 100), (250, 150), (350, 200), (450, 250)]
    
    # 构造多边形
    polygon_points = []
    polygon_points.extend(left_points)  # 左边线正向
    polygon_points.extend(reversed(right_points))  # 右边线反向
    
    print(f"多边形构造过程:")
    print(f"  左边线点数: {len(left_points)}")
    print(f"  右边线点数: {len(right_points)}")
    print(f"  多边形总点数: {len(polygon_points)}")
    
    # 创建图像
    image_shape = (400, 600)
    mask = np.zeros(image_shape, dtype=np.uint8)
    
    # 转换为整数坐标
    points_int = np.array([(int(x), int(y)) for x, y in polygon_points], dtype=np.int32)
    
    # 填充多边形
    cv2.fillPoly(mask, [points_int], 255)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 显示原始轨道线
    axes[0, 0].set_title('原始轨道线')
    axes[0, 0].set_xlim(0, 600)
    axes[0, 0].set_ylim(400, 0)
    
    # 绘制左边线
    left_x, left_y = zip(*left_points)
    axes[0, 0].plot(left_x, left_y, 'g-o', label='Left Track', markersize=8, linewidth=2)
    
    # 绘制右边线
    right_x, right_y = zip(*right_points)
    axes[0, 0].plot(right_x, right_y, 'b-o', label='Right Track', markersize=8, linewidth=2)
    
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 2. 显示多边形边界
    axes[0, 1].set_title('多边形边界构造')
    axes[0, 1].set_xlim(0, 600)
    axes[0, 1].set_ylim(400, 0)
    
    # 绘制多边形边界
    polygon_x, polygon_y = zip(*polygon_points)
    axes[0, 1].plot(polygon_x, polygon_y, 'r-', linewidth=3, label='Polygon Boundary')
    axes[0, 1].scatter(polygon_x, polygon_y, c='red', s=50, zorder=5)
    
    # 标注点的顺序
    for i, (x, y) in enumerate(polygon_points):
        axes[0, 1].annotate(str(i), (x+10, y+10), fontsize=12, color='red')
    
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 3. 显示填充的多边形
    axes[1, 0].set_title('填充的多边形（内部）')
    axes[1, 0].imshow(mask, cmap='viridis')
    axes[1, 0].axis('off')
    
    # 4. 显示边界和填充的对比
    axes[1, 1].set_title('边界 vs 填充对比')
    
    # 创建边界图像
    boundary_mask = np.zeros(image_shape, dtype=np.uint8)
    cv2.polylines(boundary_mask, [points_int], True, 255, 3)
    
    # 显示对比
    comparison = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    comparison[:, :, 0] = boundary_mask  # 蓝色：边界
    comparison[:, :, 1] = mask  # 绿色：填充
    
    axes[1, 1].imshow(comparison)
    axes[1, 1].set_title('蓝色=边界, 绿色=填充内部')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('outputs/polygon_logic_visualization.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\n多边形统计:")
    print(f"  边界像素数: {np.sum(boundary_mask > 0)}")
    print(f"  填充像素数: {np.sum(mask > 0)}")
    print(f"  内部区域: {np.sum(mask > 0)} 像素")
    
    return mask, boundary_mask

def test_direction_adjustment():
    """测试方向调整功能"""
    print("\n=== 方向调整测试 ===")
    
    # 测试用例1：同向轨道
    print("1. 同向轨道测试")
    left_points = [(100, 100), (200, 150), (300, 200)]
    right_points = [(150, 100), (250, 150), (350, 200)]
    
    polygon_points = []
    polygon_points.extend(left_points)
    polygon_points.extend(reversed(right_points))
    
    print(f"  左边线: {left_points}")
    print(f"  右边线: {right_points}")
    print(f"  多边形: {polygon_points}")
    
    # 测试用例2：反向轨道
    print("\n2. 反向轨道测试")
    left_points = [(100, 100), (200, 150), (300, 200)]
    right_points = [(350, 200), (250, 150), (150, 100)]  # 反向
    
    polygon_points = []
    polygon_points.extend(left_points)
    polygon_points.extend(reversed(right_points))  # 自动调整
    
    print(f"  左边线: {left_points}")
    print(f"  右边线: {right_points}")
    print(f"  多边形: {polygon_points}")

if __name__ == "__main__":
    # 创建输出目录
    output_dir = Path('outputs')
    output_dir.mkdir(exist_ok=True)
    
    # 运行测试
    visualize_polygon_logic()
    test_direction_adjustment()
    
    print("\n✅ 多边形逻辑测试完成！") 