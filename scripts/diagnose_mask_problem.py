#!/usr/bin/env python
"""
掩码问题诊断脚本
检查掩码文件是否完全相同，并找到问题根源
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import matplotlib.pyplot as plt
import hashlib
from collections import defaultdict
import json
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def calculate_file_hash(file_path: Path) -> str:
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def analyze_mask_files(masks_dir: Path, num_samples: int = 20):
    """分析掩码文件"""
    print(f"分析掩码目录: {masks_dir}")
    
    # 获取所有掩码文件
    mask_files = list(masks_dir.glob("*.png")) + list(masks_dir.glob("*.jpg"))
    print(f"找到 {len(mask_files)} 个掩码文件")
    
    if len(mask_files) == 0:
        print("错误: 没有找到掩码文件")
        return
    
    # 限制分析数量
    sample_files = mask_files[:num_samples]
    
    # 统计信息
    hash_groups = defaultdict(list)
    mask_stats = []
    
    print(f"分析前 {len(sample_files)} 个掩码文件...")
    
    for i, mask_path in enumerate(sample_files):
        try:
            # 计算文件哈希
            file_hash = calculate_file_hash(mask_path)
            hash_groups[file_hash].append(mask_path.name)
            
            # 读取掩码
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"警告: 无法读取掩码 {mask_path.name}")
                continue
            
            # 统计信息
            stats = {
                'filename': mask_path.name,
                'hash': file_hash,
                'shape': mask.shape,
                'min': mask.min(),
                'max': mask.max(),
                'mean': mask.mean(),
                'unique_values': np.unique(mask).tolist(),
                'sum': mask.sum(),
                'std': mask.std()
            }
            
            mask_stats.append(stats)
            
            print(f"{i+1:2d}. {mask_path.name}")
            print(f"    哈希: {file_hash[:12]}...")
            print(f"    形状: {stats['shape']}")
            print(f"    范围: [{stats['min']}, {stats['max']}]")
            print(f"    均值: {stats['mean']:.6f}")
            print(f"    唯一值: {stats['unique_values']}")
            print(f"    总和: {stats['sum']}")
            
        except Exception as e:
            print(f"处理 {mask_path.name} 时出错: {e}")
    
    # 分析哈希分组
    print(f"\n=== 哈希分组分析 ===")
    print(f"总共 {len(hash_groups)} 个不同的哈希值")
    
    for hash_val, files in hash_groups.items():
        print(f"哈希 {hash_val[:12]}... : {len(files)} 个文件")
        if len(files) > 1:
            print(f"  相同文件: {files}")
    
    # 检查是否所有掩码都相同
    if len(hash_groups) == 1:
        print("🚨 严重问题: 所有掩码文件完全相同！")
    elif len(hash_groups) < len(sample_files) * 0.5:
        print("⚠️  警告: 大量掩码文件重复")
    else:
        print("✅ 掩码文件看起来正常多样化")
    
    return mask_stats, hash_groups


def check_original_json_data(json_dir: Path):
    """检查原始JSON标注文件"""
    print(f"\n=== 检查原始JSON数据 ===")
    print(f"JSON目录: {json_dir}")
    
    if not json_dir.exists():
        print(f"错误: JSON目录不存在: {json_dir}")
        return
    
    json_files = list(json_dir.glob("*.json"))
    print(f"找到 {len(json_files)} 个JSON文件")
    
    if len(json_files) == 0:
        print("错误: 没有找到JSON文件")
        return
    
    # 分析前几个JSON文件
    for i, json_file in enumerate(json_files[:5]):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n{i+1}. {json_file.name}")
            print(f"  JSON字段: {list(data.keys())}")
            
            # 检查标注数据
            labels = data.get('labels', [])
            print(f"  标注数量: {len(labels)}")
            
            if len(labels) > 0:
                for j, label in enumerate(labels):
                    label_type = label.get('label', 'unknown')
                    points = label.get('points', [])
                    print(f"    标注{j+1}: {label_type}, {len(points)//2} 个点")
                    if len(points) >= 4:
                        print(f"      首点: ({points[0]:.1f}, {points[1]:.1f})")
                        print(f"      末点: ({points[-2]:.1f}, {points[-1]:.1f})")
            else:
                print("  ⚠️  没有找到标注数据")
            
        except Exception as e:
            print(f"处理JSON文件 {json_file.name} 时出错: {e}")


def visualize_mask_samples(masks_dir: Path, output_dir: Path, num_samples: int = 6):
    """可视化掩码样本"""
    print(f"\n=== 可视化掩码样本 ===")
    
    mask_files = list(masks_dir.glob("*.png"))[:num_samples]
    
    if len(mask_files) == 0:
        print("错误: 没有找到掩码文件")
        return
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, mask_path in enumerate(mask_files):
        if i >= 6:
            break
            
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if mask is None:
            continue
        
        axes[i].imshow(mask, cmap='viridis')
        axes[i].set_title(f'{mask_path.name}\n唯一值: {np.unique(mask)}')
        axes[i].axis('off')
    
    # 隐藏空的子图
    for i in range(len(mask_files), 6):
        axes[i].axis('off')
    
    plt.suptitle('掩码样本可视化', fontsize=16)
    plt.tight_layout()
    
    output_path = output_dir / 'mask_samples_visualization.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"可视化结果保存到: {output_path}")


def create_diagnosis_report(mask_stats, hash_groups, output_dir: Path):
    """创建诊断报告"""
    report_path = output_dir / 'mask_diagnosis_report.json'
    
    report = {
        'total_analyzed': len(mask_stats),
        'unique_hashes': len(hash_groups),
        'duplicate_groups': {hash_val: files for hash_val, files in hash_groups.items() if len(files) > 1},
        'mask_statistics': mask_stats,
        'analysis_summary': {
            'all_identical': len(hash_groups) == 1,
            'high_duplication': len(hash_groups) < len(mask_stats) * 0.5,
            'problem_detected': len(hash_groups) == 1 or len(hash_groups) < len(mask_stats) * 0.5
        }
    }
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"诊断报告保存到: {report_path}")
    return report


def main():
    parser = argparse.ArgumentParser(description='诊断掩码问题')
    parser.add_argument('--masks-dir', type=str, 
                       default='/home/<USER>/data/Download/railway_track_dataset/masks/test',
                       help='掩码目录')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='原始JSON目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/mask_diagnosis',
                       help='输出目录')
    parser.add_argument('--num-samples', type=int, default=20,
                       help='分析的样本数量')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("掩码问题诊断开始...")
    print(f"掩码目录: {args.masks_dir}")
    print(f"JSON目录: {args.json_dir}")
    print(f"输出目录: {output_dir}")
    
    # 1. 分析掩码文件
    masks_dir = Path(args.masks_dir)
    mask_stats, hash_groups = analyze_mask_files(masks_dir, args.num_samples)
    
    # 2. 检查原始JSON数据
    json_dir = Path(args.json_dir)
    check_original_json_data(json_dir)
    
    # 3. 可视化掩码样本
    visualize_mask_samples(masks_dir, output_dir, 6)
    
    # 4. 创建诊断报告
    report = create_diagnosis_report(mask_stats, hash_groups, output_dir)
    
    # 5. 输出结论
    print(f"\n=== 诊断结论 ===")
    if report['analysis_summary']['all_identical']:
        print("🚨 严重问题: 所有掩码文件完全相同！")
        print("建议: 重新运行数据预处理管道")
    elif report['analysis_summary']['high_duplication']:
        print("⚠️  警告: 存在大量重复掩码")
        print("建议: 检查数据预处理逻辑")
    else:
        print("✅ 掩码文件看起来正常")
    
    print(f"\n详细报告已保存到: {output_dir}")


if __name__ == '__main__':
    main() 