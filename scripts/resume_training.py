#!/usr/bin/env python
"""
恢复训练脚本
便捷地恢复中断的训练
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from scripts.train import find_latest_checkpoint, show_checkpoint_info, main as train_main


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='恢复中断的训练',
        epilog="""
使用示例:
  # 恢复训练（自动查找最新检查点）
  python scripts/resume_training.py --experiment-dir models/checkpoints/railway_track_config
  
  # 恢复折叠训练
  python scripts/resume_training.py --experiment-dir models/checkpoints/railway_track_config/fold_1
  
  # 恢复相机特定训练
  python scripts/resume_training.py --experiment-dir models/checkpoints/railway_track_config_6mm
  
  # 指定检查点文件
  python scripts/resume_training.py \\
    --experiment-dir models/checkpoints/railway_track_config \\
    --checkpoint models/checkpoints/railway_track_config/checkpoint_step_1500.pth
  
  # 继续训练更多epoch
  python scripts/resume_training.py \\
    --experiment-dir models/checkpoints/railway_track_config \\
    --continue-epochs 200

注意：
  - 实验目录应该与训练脚本生成的目录结构一致
  - 脚本会自动解析实验名称、相机类型、fold信息
  - 优先使用实验目录中保存的config.yaml，否则查找configs/目录
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--experiment-dir',
        type=str,
        required=True,
        help='实验输出目录路径（由训练脚本生成的目录）'
    )
    parser.add_argument(
        '--checkpoint',
        type=str,
        default=None,
        help='指定检查点文件路径（可选，默认使用最新的）'
    )
    parser.add_argument(
        '--gpu',
        type=str,
        default='0',
        help='使用的GPU编号'
    )
    parser.add_argument(
        '--continue-epochs',
        type=int,
        default=None,
        help='继续训练的epoch数（覆盖配置文件）'
    )
    
    return parser.parse_args()


def parse_experiment_info(experiment_dir: Path):
    """
    从实验目录路径解析实验信息
    
    Args:
        experiment_dir: 实验目录路径
        
    Returns:
        dict: 包含实验名称、相机类型、fold等信息
    """
    path_parts = experiment_dir.parts
    experiment_name = experiment_dir.name
    camera_type = None
    fold = None
    base_experiment_name = experiment_name
    
    # 检查是否包含fold信息
    if experiment_name.startswith('fold_'):
        # 情况：models/checkpoints/experiment_name/fold_N
        fold = int(experiment_name.split('_')[1])
        # 获取父目录作为基础实验名
        base_experiment_name = experiment_dir.parent.name
        experiment_name = base_experiment_name
    elif 'fold_' in experiment_name:
        # 情况：models/checkpoints/experiment_name_fold_N
        parts = experiment_name.split('_fold_')
        if len(parts) == 2:
            base_experiment_name = parts[0]
            fold = int(parts[1])
    
    # 检查是否包含相机类型信息
    for camera in ['6mm', '25mm']:
        if camera in base_experiment_name:
            camera_type = camera
            # 移除相机类型后缀获取真正的实验名
            base_experiment_name = base_experiment_name.replace(f'_{camera}', '')
            break
    
    return {
        'experiment_name': base_experiment_name,
        'full_experiment_name': experiment_name,
        'camera_type': camera_type,
        'fold': fold
    }


def find_experiment_config(experiment_dir: Path, experiment_info: dict) -> Path:
    """查找实验配置文件"""
    # 首先检查实验目录中是否有保存的配置
    config_file = experiment_dir / 'config.yaml'
    if config_file.exists():
        return config_file
    
    # 如果没有保存的配置，尝试在configs目录查找
    experiment_name = experiment_info['experiment_name']
    possible_configs = [
        Path('configs') / f'{experiment_name}.yaml',
        Path('configs') / f'{experiment_name}_config.yaml',
        Path('configs') / 'railway_track_config.yaml'
    ]
    
    for config_path in possible_configs:
        if config_path.exists():
            return config_path
    
    return None


def validate_directory_structure(experiment_dir: Path):
    """
    验证实验目录结构是否符合训练脚本的期望
    
    Args:
        experiment_dir: 实验目录
        
    Returns:
        bool: 是否有效
    """
    # 检查必要的文件和目录
    required_patterns = [
        'checkpoint_*.pth',  # 至少有一个检查点文件
        'last_checkpoint.pth',  # 或者有最新检查点
    ]
    
    has_checkpoint = False
    for pattern in required_patterns:
        if list(experiment_dir.glob(pattern)):
            has_checkpoint = True
            break
    
    if not has_checkpoint:
        print(f"⚠️  警告: 在 {experiment_dir} 中未找到检查点文件")
        return False
    
    return True


def main():
    """主函数"""
    args = parse_args()
    
    experiment_dir = Path(args.experiment_dir)
    if not experiment_dir.exists():
        print(f"❌ 实验目录不存在: {experiment_dir}")
        return 1
    
    print("🔄 恢复训练工具")
    print("=" * 50)
    print(f"实验目录: {experiment_dir}")
    
    # 验证目录结构
    if not validate_directory_structure(experiment_dir):
        print("❌ 实验目录结构无效")
        return 1
    
    # 解析实验信息
    experiment_info = parse_experiment_info(experiment_dir)
    print(f"📋 实验信息:")
    print(f"  实验名称: {experiment_info['experiment_name']}")
    if experiment_info['camera_type']:
        print(f"  相机类型: {experiment_info['camera_type']}")
    if experiment_info['fold'] is not None:
        print(f"  交叉验证折数: {experiment_info['fold']}")
    
    # 查找检查点
    if args.checkpoint:
        checkpoint_path = Path(args.checkpoint)
        if not checkpoint_path.exists():
            print(f"❌ 指定的检查点不存在: {checkpoint_path}")
            return 1
    else:
        checkpoint_path = find_latest_checkpoint(experiment_dir)
        if not checkpoint_path:
            print("❌ 在实验目录中未找到检查点文件")
            print("提示：请检查以下文件是否存在：")
            print("  - last_checkpoint.pth")
            print("  - checkpoint_epoch_*.pth")
            print("  - checkpoint_step_*.pth")
            return 1
    
    print(f"📁 找到检查点: {checkpoint_path}")
    
    # 显示检查点信息
    class DummyLogger:
        def info(self, msg): print(f"ℹ️  {msg}")
        def error(self, msg): print(f"❌ {msg}")
        def warning(self, msg): print(f"⚠️  {msg}")
    
    checkpoint_info = show_checkpoint_info(checkpoint_path, DummyLogger())
    if not checkpoint_info:
        print("❌ 无法读取检查点信息")
        return 1
    
    # 查找配置文件
    config_file = find_experiment_config(experiment_dir, experiment_info)
    if not config_file:
        print("❌ 无法找到实验配置文件")
        print("请确保以下文件之一存在:")
        print(f"  - {experiment_dir}/config.yaml")
        print(f"  - configs/{experiment_info['experiment_name']}.yaml")
        print(f"  - configs/{experiment_info['experiment_name']}_config.yaml")
        print("  - configs/railway_track_config.yaml")
        return 1
    
    print(f"📋 使用配置文件: {config_file}")
    
    # 构建训练命令参数
    train_args = [
        '--config', str(config_file),
        '--resume', str(checkpoint_path),
        '--gpu', args.gpu,
        '--experiment-name', experiment_info['experiment_name']
    ]
    
    # 添加相机类型参数
    if experiment_info['camera_type']:
        train_args.extend(['--camera-type', experiment_info['camera_type']])
    
    # 添加fold参数
    if experiment_info['fold'] is not None:
        train_args.extend(['--fold', str(experiment_info['fold'])])
    
    # 添加继续训练的epoch数
    if args.continue_epochs:
        train_args.extend(['--epochs', str(args.continue_epochs)])
    
    print("\n🚀 恢复训练...")
    print("完整命令行参数:", ' '.join(train_args))
    print("=" * 50)
    
    # 修改sys.argv以模拟命令行调用
    original_argv = sys.argv.copy()
    sys.argv = ['train.py'] + train_args
    
    try:
        # 调用训练主函数
        return train_main()
    except Exception as e:
        print(f"❌ 恢复训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 恢复原始argv
        sys.argv = original_argv


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code or 0) 