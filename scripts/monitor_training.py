#!/usr/bin/env python3
"""
训练监控脚本
实时查看训练进度和指标
"""

import json
import time
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, Any

def load_training_history(log_dir: Path) -> List[Dict[str, Any]]:
    """加载训练历史"""
    history_file = log_dir / 'history.json'
    if history_file.exists():
        with open(history_file, 'r') as f:
            return json.load(f)
    return []

def parse_log_file(log_file: Path) -> Dict[str, Any]:
    """解析日志文件获取最新状态"""
    if not log_file.exists():
        return {}
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    # 查找最新的训练状态
    latest_info = {}
    for line in reversed(lines):
        if 'Epoch' in line and '|' in line:
            # 解析epoch信息
            parts = line.split('|')
            for part in parts:
                if ':' in part:
                    key, value = part.strip().split(':', 1)
                    try:
                        latest_info[key.strip()] = float(value.strip())
                    except:
                        latest_info[key.strip()] = value.strip()
            break
    
    return latest_info

def display_status(log_dir: Path):
    """显示训练状态"""
    print(f"\n{'='*60}")
    print(f"训练监控 - {log_dir}")
    print(f"{'='*60}")
    
    # 加载训练历史
    history = load_training_history(log_dir)
    if history:
        latest = history[-1]
        print(f"总Epoch数: {len(history)}")
        print(f"当前Epoch: {latest.get('epoch', 'N/A')}")
        print(f"训练损失: {latest.get('train_loss', 'N/A'):.4f}")
        print(f"验证损失: {latest.get('val_loss', 'N/A'):.4f}")
        print(f"验证IoU: {latest.get('val_iou', 'N/A'):.4f}")
        print(f"学习率: {latest.get('lr', 'N/A'):.6f}")
        
        # 计算最佳性能
        best_epoch = max(history, key=lambda x: x.get('val_iou', 0))
        print(f"\n最佳性能:")
        print(f"  Epoch: {best_epoch.get('epoch', 'N/A')}")
        print(f"  IoU: {best_epoch.get('val_iou', 'N/A'):.4f}")
    
    # 解析日志文件
    log_file = log_dir / 'train.log'
    latest_info = parse_log_file(log_file)
    if latest_info:
        print(f"\n最新状态:")
        for key, value in latest_info.items():
            print(f"  {key}: {value}")

def plot_metrics(log_dir: Path, save_path: str = None):
    """绘制训练指标"""
    history = load_training_history(log_dir)
    if not history:
        print("没有找到训练历史")
        return
    
    epochs = [h['epoch'] for h in history]
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    # 损失曲线
    if 'train_loss' in history[0]:
        train_losses = [h['train_loss'] for h in history]
        axes[0].plot(epochs, train_losses, 'b-', label='训练损失')
    if 'val_loss' in history[0]:
        val_losses = [h.get('val_loss', 0) for h in history]
        axes[0].plot(epochs, val_losses, 'r-', label='验证损失')
    axes[0].set_title('损失曲线')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # IoU曲线
    if 'val_iou' in history[0]:
        val_ious = [h.get('val_iou', 0) for h in history]
        axes[1].plot(epochs, val_ious, 'g-', label='IoU')
        axes[1].set_title('IoU曲线')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('IoU')
        axes[1].legend()
        axes[1].grid(True)
    
    # 学习率曲线
    if 'lr' in history[0]:
        lrs = [h['lr'] for h in history]
        axes[2].plot(epochs, lrs, 'orange', label='学习率')
        axes[2].set_title('学习率曲线')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('Learning Rate')
        axes[2].set_yscale('log')
        axes[2].legend()
        axes[2].grid(True)
    
    # 其他指标
    metrics = ['val_dice', 'val_f1', 'val_precision', 'val_recall']
    for metric in metrics:
        if metric in history[0]:
            values = [h.get(metric, 0) for h in history]
            axes[3].plot(epochs, values, label=metric.replace('val_', ''))
    axes[3].set_title('其他指标')
    axes[3].set_xlabel('Epoch')
    axes[3].set_ylabel('Score')
    axes[3].legend()
    axes[3].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    else:
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='训练监控工具')
    parser.add_argument('--log-dir', type=str, required=True,
                       help='训练日志目录')
    parser.add_argument('--watch', action='store_true',
                       help='持续监控模式')
    parser.add_argument('--plot', action='store_true',
                       help='绘制训练曲线')
    parser.add_argument('--save-plot', type=str,
                       help='保存图表路径')
    
    args = parser.parse_args()
    log_dir = Path(args.log_dir)
    
    if args.plot:
        plot_metrics(log_dir, args.save_plot)
        return
    
    if args.watch:
        print("开始持续监控训练进度...")
        print("按 Ctrl+C 退出")
        try:
            while True:
                display_status(log_dir)
                time.sleep(30)  # 每30秒更新一次
        except KeyboardInterrupt:
            print("\n监控已停止")
    else:
        display_status(log_dir)

if __name__ == '__main__':
    main() 