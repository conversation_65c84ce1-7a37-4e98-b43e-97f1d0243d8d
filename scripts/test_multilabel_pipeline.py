#!/usr/bin/env python3
"""
测试多标签数据Pipeline
验证从数据加载到训练的完整流程
"""

import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import yaml
from pathlib import Path
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data import create_dataloaders, verify_multilabel_format
from src.models.segmentation import SegmentationModel
from src.models.multilabel_losses import MultilabelCombinedLoss
from src.utils.visualization import visualize_predictions
from src.utils.metrics import compute_metrics, compute_metrics_per_class


def test_data_loading(config):
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    # 创建数据加载器
    train_loader, val_loader = create_dataloaders(config)
    
    print(f"训练集大小: {len(train_loader.dataset)} 样本")
    print(f"验证集大小: {len(val_loader.dataset)} 样本")
    print(f"批次大小: {config['data']['batch_size']['train']}")
    
    # 验证多标签格式
    verify_multilabel_format(train_loader, num_samples=2)
    
    return train_loader, val_loader


def test_model_forward(config, train_loader):
    """测试模型前向传播"""
    print("\n=== 测试模型前向传播 ===")
    
    # 创建模型
    model_config = config['model']
    model = SegmentationModel(model_config)
    
    # 设置为评估模式避免批归一化问题
    model.eval()
    
    # 获取一个批次
    batch = next(iter(train_loader))
    images = batch['image']
    masks = batch['mask']
    
    print(f"输入图像形状: {images.shape}")
    print(f"目标掩码形状: {masks.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = model(images)
    
    print(f"模型输出形状: {outputs.shape}")
    print(f"输出范围: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
    
    # 应用sigmoid
    predictions = torch.sigmoid(outputs)
    print(f"Sigmoid后范围: [{predictions.min().item():.4f}, {predictions.max().item():.4f}]")
    
    return model, predictions, masks


def test_loss_computation(config, outputs, targets):
    """测试损失计算"""
    print("\n=== 测试损失计算 ===")
    
    # 创建损失函数
    loss_config = config['loss']
    loss_fn = MultilabelCombinedLoss(
        jaccard_weight=loss_config.get('jaccard_weight', 0.5),
        focal_weight=loss_config.get('focal_weight', 0.5),
        alpha=loss_config.get('alpha', [0.1, 0.3, 0.6]),
        gamma=loss_config.get('gamma', 2.0)
    )
    
    # 计算损失
    # 模拟logits（未经过sigmoid）
    logits = torch.randn_like(outputs)
    loss = loss_fn(logits, targets)
    
    print(f"损失值: {loss.item():.4f}")
    print(f"✓ 损失计算成功")
    
    return loss_fn


def test_metrics_computation(predictions, targets):
    """测试指标计算"""
    print("\n=== 测试指标计算 ===")
    
    # 计算整体指标
    overall_metrics = compute_metrics(predictions, targets, threshold=0.5)
    print("整体指标:")
    for key, value in overall_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 计算每个类别的指标
    per_class_metrics = compute_metrics_per_class(predictions, targets, threshold=0.5)
    print("\n每个类别的指标:")
    for class_name, metrics in per_class_metrics.items():
        print(f"  {class_name}:")
        for key, value in metrics.items():
            print(f"    {key}: {value:.4f}")


def test_visualization(images, masks, predictions):
    """测试可视化"""
    print("\n=== 测试可视化 ===")
    
    # 只可视化2个样本
    num_samples = min(2, images.shape[0])
    
    # 可视化预测结果
    visualize_predictions(
        images[:num_samples],
        masks[:num_samples],
        predictions[:num_samples],
        num_samples=num_samples,
        threshold=0.5
    )
    
    print("✓ 可视化完成")


def test_training_step(model, loss_fn, train_loader, device):
    """测试一个训练步骤"""
    print("\n=== 测试训练步骤 ===")
    
    model.to(device)
    model.train()
    
    # 创建优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # 获取一个批次
    batch = next(iter(train_loader))
    images = batch['image'].to(device)
    masks = batch['mask'].to(device)
    
    # 前向传播
    outputs = model(images)
    loss = loss_fn(outputs, masks)
    
    # 反向传播
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    print(f"训练损失: {loss.item():.4f}")
    print("✓ 训练步骤完成")
    
    return loss.item()


def analyze_overlap_regions(train_loader, num_batches=5):
    """分析重叠区域"""
    print("\n=== 分析重叠区域 ===")
    
    total_pixels = 0
    overlap_pixels = 0
    
    for i, batch in enumerate(train_loader):
        if i >= num_batches:
            break
        
        masks = batch['mask']
        
        # 计算重叠区域
        main_track = masks[:, 1, :, :] > 0.5
        fork_track = masks[:, 2, :, :] > 0.5
        overlap = torch.logical_and(main_track, fork_track)
        
        batch_pixels = masks.shape[0] * masks.shape[2] * masks.shape[3]
        batch_overlap = overlap.sum().item()
        
        total_pixels += batch_pixels
        overlap_pixels += batch_overlap
    
    overlap_ratio = overlap_pixels / total_pixels * 100 if total_pixels > 0 else 0
    
    print(f"分析了 {num_batches} 个批次")
    print(f"总像素数: {total_pixels:,}")
    print(f"重叠像素数: {overlap_pixels:,}")
    print(f"重叠比例: {overlap_ratio:.2f}%")
    
    if overlap_pixels > 0:
        print("✓ 发现重叠区域，多标签掩码生成正常")
    else:
        print("⚠ 未发现重叠区域，请检查掩码生成逻辑")


def main():
    """主测试函数"""
    print("=== 多标签Pipeline测试 ===")
    
    # 加载配置
    config_path = project_root / 'configs' / 'railway_track_config.yaml'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"配置文件: {config_path}")
    print(f"使用多标签: {config['data'].get('use_multilabel', False)}")
    print(f"损失函数: {config['loss']['type']}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"设备: {device}")
    
    # 运行测试
    try:
        # 1. 测试数据加载
        train_loader, val_loader = test_data_loading(config)
        
        # 2. 测试模型前向传播
        model, predictions, masks = test_model_forward(config, train_loader)
        
        # 3. 测试损失计算
        loss_fn = test_loss_computation(config, predictions, masks)
        
        # 4. 测试指标计算
        test_metrics_computation(predictions, masks)
        
        # 5. 测试可视化
        batch = next(iter(train_loader))
        test_visualization(batch['image'], batch['mask'], predictions)
        
        # 6. 测试训练步骤
        test_training_step(model, loss_fn, train_loader, device)
        
        # 7. 分析重叠区域
        analyze_overlap_regions(train_loader)
        
        print("\n=== 所有测试通过 ===")
        print("✓ 多标签Pipeline已完全打通")
        print("✓ 可以开始端到端训练")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()