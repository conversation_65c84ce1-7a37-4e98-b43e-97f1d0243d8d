#!/usr/bin/env python3
"""
自适应集成预测脚本
针对不同类型的图像自动调整预处理策略
"""

import cv2
import numpy as np
import torch
import albumentations as A
from albumentations.pytorch import ToTensorV2
from pathlib import Path
import sys
import argparse
import yaml

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from src.models.ensemble import EnsemblePredictor

class AdaptivePreprocessor:
    """自适应预处理器"""
    
    def __init__(self):
        # 基础变换
        self.base_transforms = A.Compose([
            <PERSON>.Resize(height=544, width=960),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        # 暗光增强变换
        self.dark_enhanced_transforms = A.Compose([
            <PERSON>.<PERSON>size(height=544, width=960),
            A.<PERSON>(clip_limit=2.0, tile_grid_size=(8, 8), p=0.8),  # 对比度增强
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        # 亮光调整变换  
        self.bright_adjusted_transforms = A.Compose([
            A.Resize(height=544, width=960),
            A.RandomBrightnessContrast(brightness_limit=(-0.3, -0.1), contrast_limit=0.1, p=1.0),
            A.CLAHE(clip_limit=1.5, tile_grid_size=(8, 8), p=0.5),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def analyze_image_characteristics(self, image_rgb):
        """分析图像特征，决定使用哪种预处理策略"""
        gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
        
        # 基础统计
        mean_brightness = gray.mean()
        std_brightness = gray.std()
        
        # 对比度分析
        contrast = std_brightness / mean_brightness if mean_brightness > 0 else 0
        
        # 暗像素和亮像素比例
        dark_ratio = (gray < 50).sum() / gray.size
        bright_ratio = (gray > 200).sum() / gray.size
        
        characteristics = {
            'mean_brightness': mean_brightness,
            'std_brightness': std_brightness,
            'contrast': contrast,
            'dark_ratio': dark_ratio,
            'bright_ratio': bright_ratio
        }
        
        return characteristics
    
    def choose_preprocessing_strategy(self, characteristics):
        """根据图像特征选择预处理策略"""
        mean_bright = characteristics['mean_brightness']
        contrast = characteristics['contrast']
        dark_ratio = characteristics['dark_ratio']
        
        if mean_bright < 60:  # 暗图像
            if contrast < 0.3:  # 低对比度
                strategy = 'dark_enhanced'
            else:
                strategy = 'base'
        elif mean_bright > 100:  # 亮图像
            strategy = 'bright_adjusted'
        else:  # 中等亮度
            if dark_ratio > 0.3:
                strategy = 'dark_enhanced'
            else:
                strategy = 'base'
        
        return strategy
    
    def preprocess(self, image_rgb, strategy='auto'):
        """预处理图像"""
        if strategy == 'auto':
            characteristics = self.analyze_image_characteristics(image_rgb)
            strategy = self.choose_preprocessing_strategy(characteristics)
        
        if strategy == 'dark_enhanced':
            transforms = self.dark_enhanced_transforms
        elif strategy == 'bright_adjusted':
            transforms = self.bright_adjusted_transforms
        else:
            transforms = self.base_transforms
        
        result = transforms(image=image_rgb)
        return result['image'], strategy

class AdaptiveEnsemblePredictor:
    """自适应集成预测器"""
    
    def __init__(self, weights_dir, config_path, device='cuda'):
        self.weights_dir = Path(weights_dir)
        self.device = torch.device(device)
        self.preprocessor = AdaptivePreprocessor()
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 加载集成模型
        self.ensemble_predictor = self._load_ensemble_models()
        
        # 类别颜色
        self.class_colors = {
            0: [0, 0, 0],      # 背景 - 黑色
            1: [255, 0, 0],    # 主轨道 - 红色  
            2: [0, 255, 0],    # 分叉轨道 - 绿色
        }
    
    def _load_ensemble_models(self):
        """加载集成模型"""
        import segmentation_models_pytorch as smp
        
        # 模型配置
        models_config = [
            {'name': 'efficientnet_b4', 'actual_name': 'efficientnetb4'},
            {'name': 'eca_nfnet_l2', 'actual_name': 'eca_nfnet_l2'},
            {'name': 'seresnet152d', 'actual_name': 'seresnet152d'}
        ]
        
        # 加载模型
        models = []
        for model_config in models_config:
            weights_path = self.weights_dir / f"{model_config['actual_name']}.pth.tar"
            if weights_path.exists():
                model = torch.load(weights_path, map_location=self.device, weights_only=False)
                model.eval()
                models.append(model)
        
        # 创建权重
        num_classes = 3
        fusion_weights = torch.ones(num_classes, len(models)) / len(models)
        
        return EnsemblePredictor(models, fusion_weights, self.device)
    
    def predict_with_multiple_strategies(self, image_path, threshold=0.3):
        """使用多种策略预测，选择最佳结果"""
        # 读取图像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        original_size = image_rgb.shape[:2]
        
        strategies = ['base', 'dark_enhanced', 'bright_adjusted']
        results = {}
        
        print(f"🔍 测试图像: {Path(image_path).name}")
        print(f"原始尺寸: {image_rgb.shape[1]}x{image_rgb.shape[0]}")
        
        # 分析图像特征
        characteristics = self.preprocessor.analyze_image_characteristics(image_rgb)
        recommended_strategy = self.preprocessor.choose_preprocessing_strategy(characteristics)
        
        print(f"\n📊 图像特征分析:")
        print(f"   亮度均值: {characteristics['mean_brightness']:.1f}")
        print(f"   对比度: {characteristics['contrast']:.3f}")
        print(f"   暗像素比例: {characteristics['dark_ratio']:.1%}")
        print(f"   推荐策略: {recommended_strategy}")
        
        print(f"\n🧪 测试不同预处理策略:")
        print("-" * 60)
        
        best_strategy = None
        best_score = 0
        best_prediction = None
        
        for strategy in strategies:
            # 预处理
            input_tensor, used_strategy = self.preprocessor.preprocess(image_rgb, strategy)
            input_tensor = input_tensor.unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                prediction = self.ensemble_predictor.predict(input_tensor)
                prediction_np = prediction.squeeze(0).cpu().numpy()
            
            # 评估预测质量
            main_track_mean = prediction_np[1].mean()
            main_track_max = prediction_np[1].max()
            fork_track_mean = prediction_np[2].mean()
            
            # 综合评分（主要看主轨道检测）
            score = main_track_mean * 10 + min(main_track_max, 0.5) * 2
            
            results[strategy] = {
                'prediction': prediction_np,
                'main_track_mean': main_track_mean,
                'main_track_max': main_track_max,
                'fork_track_mean': fork_track_mean,
                'score': score
            }
            
            print(f"{strategy:15} | 主轨道: 均值={main_track_mean:.4f} 最大={main_track_max:.4f} | 评分={score:.4f}")
            
            if score > best_score:
                best_score = score
                best_strategy = strategy
                best_prediction = prediction_np
        
        # 后处理最佳预测结果
        if best_prediction is not None:
            final_mask = self._post_process_prediction(best_prediction, threshold, original_size)
        else:
            final_mask = np.zeros(original_size, dtype=np.uint8)
        
        print(f"\n🎯 最佳策略: {best_strategy} (评分: {best_score:.4f})")
        
        return final_mask, best_strategy, results
    
    def _post_process_prediction(self, prediction, threshold, original_size):
        """后处理预测结果"""
        # 调整尺寸
        prediction_resized = np.zeros((3, original_size[0], original_size[1]))
        for i in range(3):
            prediction_resized[i] = cv2.resize(
                prediction[i], 
                (original_size[1], original_size[0]), 
                interpolation=cv2.INTER_LINEAR
            )
        
        # 二值化
        prediction_binary = (prediction_resized > threshold).astype(np.uint8)
        
        # 生成最终掩码
        final_mask = np.zeros((original_size[0], original_size[1]), dtype=np.uint8)
        final_mask[prediction_binary[1] > 0] = 1  # 主轨道
        final_mask[prediction_binary[2] > 0] = 2  # 分叉轨道（优先级更高）
        
        return final_mask
    
    def save_results(self, image_path, mask, best_strategy, output_dir):
        """保存预测结果"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        image_name = Path(image_path).stem
        
        # 保存彩色掩码
        colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        for class_id, color in self.class_colors.items():
            colored_mask[mask == class_id] = color
        
        mask_path = output_dir / f"{image_name}_adaptive_{best_strategy}.png"
        cv2.imwrite(str(mask_path), colored_mask)
        
        # 保存叠加图像
        original = cv2.imread(image_path)
        original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
        
        overlay = original_rgb.copy()
        for class_id in [1, 2]:  # 只叠加轨道类别
            if (mask == class_id).any():
                overlay[mask == class_id] = (overlay[mask == class_id] * 0.6 + 
                                           np.array(self.class_colors[class_id]) * 0.4).astype(np.uint8)
        
        overlay_path = output_dir / f"{image_name}_adaptive_{best_strategy}_overlay.png"
        cv2.imwrite(str(overlay_path), cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))
        
        print(f"✅ 结果已保存:")
        print(f"   掩码: {mask_path}")
        print(f"   叠加: {overlay_path}")
        
        return mask_path, overlay_path

def main():
    parser = argparse.ArgumentParser(description='自适应集成预测')
    parser.add_argument('--weights-dir', type=str, default='finetune', help='权重目录')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml', help='配置文件')
    parser.add_argument('--input', type=str, required=True, help='输入图像路径')
    parser.add_argument('--output', type=str, default='adaptive_predictions', help='输出目录')
    parser.add_argument('--threshold', type=float, default=0.3, help='分割阈值')
    parser.add_argument('--device', type=str, choices=['cuda', 'cpu'], default='cuda', help='计算设备')
    
    args = parser.parse_args()
    
    print("="*80)
    print("🔬 自适应集成预测")
    print("="*80)
    
    # 创建预测器
    predictor = AdaptiveEnsemblePredictor(args.weights_dir, args.config, args.device)
    
    # 预测
    mask, best_strategy, results = predictor.predict_with_multiple_strategies(args.input, args.threshold)
    
    # 保存结果
    mask_path, overlay_path = predictor.save_results(args.input, mask, best_strategy, args.output)
    
    # 统计结果
    total_pixels = mask.size
    track_pixels = (mask > 0).sum()
    track_ratio = track_pixels / total_pixels
    
    print(f"\n📊 预测统计:")
    print(f"   总像素: {total_pixels:,}")
    print(f"   轨道像素: {track_pixels:,}")
    print(f"   轨道比例: {track_ratio:.1%}")
    
    if track_ratio < 0.01:
        print(f"\n⚠️  警告: 检测到的轨道过少，可能存在以下问题:")
        print(f"   1. 图像与训练数据差异过大")
        print(f"   2. 阈值设置不当（当前：{args.threshold}）")
        print(f"   3. 图像中轨道不明显或被遮挡")
        print(f"   建议: 尝试更低的阈值（如0.1-0.2）或检查图像质量")

if __name__ == "__main__":
    main() 