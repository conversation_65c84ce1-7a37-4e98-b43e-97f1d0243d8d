#!/usr/bin/env python3
"""测试集成训练配置"""

import yaml
import sys
from pathlib import Path

def test_config(config_path):
    """测试配置文件"""
    print(f"加载配置文件: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 检查关键配置
    print("\n=== 配置检查 ===")
    
    # 检查集成模型配置
    if 'ensemble' in config and 'models' in config['ensemble']:
        models = config['ensemble']['models']
        print(f"✓ 找到集成模型配置，共 {len(models)} 个模型:")
        for i, model in enumerate(models):
            print(f"  {i+1}. {model['name']} - {model['architecture']} + {model['encoder']}")
    else:
        print("✗ 未找到集成模型配置 (ensemble.models)")
    
    # 检查数据配置
    if 'data' in config:
        print(f"\n✓ 数据配置:")
        print(f"  - 类别数: {config['data'].get('num_classes', '未设置')}")
        print(f"  - 类别: {config['data'].get('classes', '未设置')}")
        print(f"  - 批大小: train={config['data']['batch_size']['train']}, val={config['data']['batch_size']['val']}")
    
    # 检查损失函数配置
    if 'loss' in config:
        print(f"\n✓ 损失函数配置:")
        print(f"  - 类型: {config['loss'].get('type', '未设置')}")
        print(f"  - 权重: alpha={config['loss'].get('alpha', '未设置')}")
    
    print("\n配置文件检查完成！")

if __name__ == '__main__':
    # 测试两个配置文件
    configs = [
        'configs/railway_track_config.yaml',
        'configs/ensemble_training_config.yaml'
    ]
    
    for config_path in configs:
        if Path(config_path).exists():
            print(f"\n{'='*50}")
            test_config(config_path)
        else:
            print(f"\n配置文件不存在: {config_path}")