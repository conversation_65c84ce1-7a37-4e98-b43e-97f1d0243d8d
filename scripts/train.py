#!/usr/bin/env python
"""
训练脚本
用于训练铁路分割模型
支持断点重启和自动恢复
"""

import argparse
import os
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from src.core.config import load_config
from src.core.registry import MODEL_REGISTRY, LOSS_REGISTRY
from src.data import create_dataloaders
from src.training import Trainer
from src.utils import setup_logger, set_random_seed


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练铁路轨道分割模型')
    
    parser.add_argument(
        '--config',
        type=str,
        required=True,
        help='配置文件路径'
    )
    parser.add_argument(
        '--resume',
        type=str,
        default=None,
        help='恢复训练的检查点路径'
    )
    parser.add_argument(
        '--auto-resume',
        action='store_true',
        default=False,
        help='自动寻找最新检查点并恢复训练'
    )
    parser.add_argument(
        '--no-auto-resume',
        action='store_true',
        default=False,
        help='禁用自动恢复功能'
    )
    parser.add_argument(
        '--gpu',
        type=str,
        default='0',
        help='使用的GPU编号'
    )
    parser.add_argument(
        '--num-workers',
        type=int,
        default=None,
        help='数据加载的工作进程数'
    )
    parser.add_argument(
        '--experiment-name',
        type=str,
        default=None,
        help='实验名称'
    )
    parser.add_argument(
        '--fold',
        type=int,
        default=None,
        help='K折交叉验证的折数'
    )
    
    # 相机类型选项
    parser.add_argument(
        '--camera-type',
        type=str,
        choices=['6mm', '25mm', 'all'],
        default='all',
        help='训练的相机类型 (6mm, 25mm, all)'
    )
    
    # 数据集类型
    parser.add_argument(
        '--dataset-type',
        type=str,
        default=None,
        help='数据集类型 (railway_segmentation, railway_track_dataset)'
    )
    
    # JSON标注文件目录
    parser.add_argument(
        '--json-dir',
        type=str,
        default=None,
        help='JSON标注文件目录'
    )
    
    # 覆盖配置的参数
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch-size', type=int, help='批次大小')
    parser.add_argument('--lr', type=float, help='学习率')
    
    # 检查点管理选项
    parser.add_argument(
        '--save-interval',
        type=int,
        default=None,
        help='检查点保存间隔(epoch)'
    )
    parser.add_argument(
        '--keep-checkpoints',
        type=int,
        default=None,
        help='保留检查点数量'
    )
    parser.add_argument(
        '--save-every-n-steps',
        type=int,
        default=500,
        help='每N步保存一次检查点'
    )
    parser.add_argument(
        '--disable-step-saving',
        action='store_true',
        default=False,
        help='禁用基于步数的保存'
    )
    
    return parser.parse_args()


def find_latest_checkpoint(output_dir: Path) -> Path:
    """查找最新的检查点文件"""
    # 优先查找last_checkpoint.pth
    last_checkpoint = output_dir / 'last_checkpoint.pth'
    if last_checkpoint.exists():
        return last_checkpoint
    
    # 否则查找最新的epoch检查点
    checkpoint_pattern = "checkpoint_epoch_*.pth"
    checkpoints = list(output_dir.glob(checkpoint_pattern))
    
    if not checkpoints:
        return None
    
    # 按epoch号排序，返回最新的
    checkpoints.sort(key=lambda x: int(x.stem.split('_')[-1]))
    return checkpoints[-1]


def show_checkpoint_info(checkpoint_path: Path, logger):
    """显示检查点信息"""
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        info = {
            'epoch': checkpoint.get('epoch', 0),
            'global_step': checkpoint.get('global_step', 0),
            'best_metric': checkpoint.get('best_metric', 0),
            'best_metric_name': checkpoint.get('best_metric_name', 'unknown'),
            'history_length': len(checkpoint.get('history', [])),
        }
        
        logger.info("检查点信息:")
        logger.info(f"  文件: {checkpoint_path}")
        logger.info(f"  Epoch: {info['epoch']}")
        logger.info(f"  全局步数: {info['global_step']}")
        logger.info(f"  最佳指标: {info['best_metric_name']}={info['best_metric']:.4f}")
        logger.info(f"  训练历史: {info['history_length']} 个epoch")
        
        return info
        
    except Exception as e:
        logger.error(f"无法读取检查点信息: {e}")
        return None


def create_experiment_summary(output_dir: Path, config, args):
    """创建实验摘要文件"""
    summary = {
        'experiment_name': args.experiment_name,
        'config_file': args.config,
        'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'gpu': args.gpu,
        'camera_type': args.camera_type if args.camera_type != 'all' else None,
        'fold': args.fold,
        'model': config.model.to_dict(),
        'training': config.training.to_dict(),
        'data': {
            'dataset_type': config.data.dataset_type,
            'batch_size': config.data.batch_size.to_dict(),
            'num_classes': config.data.num_classes,
        },
        'checkpointing': config.checkpointing.to_dict(),
        'resumed_from': args.resume,
        'auto_resume': args.auto_resume,
    }
    
    summary_file = output_dir / 'experiment_summary.json'
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    return summary_file


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖配置参数
    if args.num_workers is not None:
        config.set('project.num_workers', args.num_workers)
    if args.epochs is not None:
        config.set('training.epochs', args.epochs)
    if args.batch_size is not None:
        config.set('data.batch_size.train', args.batch_size)
    if args.lr is not None:
        config.set('training.optimizer.lr', args.lr)
    if args.dataset_type is not None:
        config.set('data.dataset_type', args.dataset_type)
    if args.json_dir is not None:
        config.set('data.json_dir', args.json_dir)
    if args.save_interval is not None:
        config.set('training.save_interval', args.save_interval)
    if args.keep_checkpoints is not None:
        config.set('checkpointing.save_top_k', args.keep_checkpoints)
    
    # 处理步数保存参数
    if args.save_every_n_steps is not None:
        config.set('training.save_every_n_steps', args.save_every_n_steps)
    if args.disable_step_saving:
        config.set('training.enable_step_saving', False)
    
    # 处理自动恢复设置
    if args.no_auto_resume:
        config.set('checkpointing.auto_resume', False)
    elif args.auto_resume:
        config.set('checkpointing.auto_resume', True)
    
    # 设置实验名称
    if args.experiment_name is None:
        args.experiment_name = Path(args.config).stem
    
    # 处理相机类型
    camera_type = None
    if args.camera_type != 'all':
        camera_type = args.camera_type
        args.experiment_name += f"_{args.camera_type}"
    
    # 设置输出目录
    output_dir = Path(config.checkpointing.save_dir) / args.experiment_name
    if args.fold is not None:
        output_dir = output_dir / f'fold_{args.fold}'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logger = setup_logger(
        'train',
        output_dir / 'train.log',
        console_level='INFO'
    )
    
    logger.info("=" * 60)
    logger.info("🚀 铁路分割模型训练")
    logger.info("=" * 60)
    logger.info(f"配置文件: {args.config}")
    logger.info(f"实验名称: {args.experiment_name}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"GPU设备: {args.gpu}")
    if camera_type:
        logger.info(f"相机类型: {camera_type}")
    if args.fold is not None:
        logger.info(f"交叉验证折数: {args.fold}")
    
    # 创建实验摘要
    summary_file = create_experiment_summary(output_dir, config, args)
    logger.info(f"实验摘要已保存: {summary_file}")
    
    # 检查是否需要恢复训练
    resume_checkpoint = None
    if args.resume:
        # 显式指定的检查点
        resume_checkpoint = Path(args.resume)
        if not resume_checkpoint.exists():
            logger.error(f"指定的检查点文件不存在: {resume_checkpoint}")
            return
        logger.info(f"将从指定检查点恢复训练: {resume_checkpoint}")
        show_checkpoint_info(resume_checkpoint, logger)
        
    elif config.checkpointing.auto_resume:
        # 自动查找最新检查点
        latest_checkpoint = find_latest_checkpoint(output_dir)
        if latest_checkpoint:
            resume_checkpoint = latest_checkpoint
            logger.info(f"发现最新检查点，将自动恢复训练: {resume_checkpoint}")
            show_checkpoint_info(resume_checkpoint, logger)
        else:
            logger.info("未发现检查点文件，将从头开始训练")
    
    # 设置随机种子
    set_random_seed(config.project.seed)
    logger.info(f"随机种子: {config.project.seed}")
    
    # 保存配置
    config.save(output_dir / 'config.yaml')
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    dataloaders = create_dataloaders(
        config.to_dict(),
        fold=args.fold,
        camera_type=camera_type
    )
    
    train_loader = dataloaders['train']
    val_loader = dataloaders['val']
    
    logger.info(f"训练集大小: {len(train_loader.dataset)}")
    logger.info(f"验证集大小: {len(val_loader.dataset)}")
    
    # 显示数据集信息
    if hasattr(train_loader.dataset, 'get_statistics'):
        stats = train_loader.dataset.get_statistics()
        logger.info(f"数据集统计: {stats}")
    
    # 创建模型
    logger.info("创建模型...")
    model_config = config.model.to_dict()
    
    # 更新类别数（如果数据集定义了）
    if hasattr(train_loader.dataset, 'CLASSES'):
        num_classes = len(train_loader.dataset.CLASSES)
        model_config['classes'] = num_classes
        logger.info(f"从数据集更新类别数: {num_classes}")
    
    model = MODEL_REGISTRY.build(model_config)
    
    logger.info(f"模型架构: {model_config['architecture']}")
    logger.info(f"编码器: {model_config['encoder']}")
    logger.info(f"参数量: {model.count_parameters():,}")
    
    # 创建损失函数
    loss_config = config.loss.to_dict()
    
    if loss_config['type'] == 'dice_bce':
        # 处理简单的dice_bce损失
        loss_fn = LOSS_REGISTRY.get('bce_dice_loss')(
            bce_weight=loss_config.get('bce_weight', 0.5),
            dice_weight=loss_config.get('dice_weight', 0.5)
        )
    elif loss_config['type'] == 'combined_loss':
        # 处理复合损失函数
        # 只传递CombinedLoss需要的参数: losses 和 weights
        combined_config = {
            'losses': loss_config['losses'],
            'weights': loss_config.get('weights', None)
        }
        loss_fn = LOSS_REGISTRY.build({'type': 'combined_loss', **combined_config})
    else:
        # 处理其他损失函数
        loss_fn = LOSS_REGISTRY.build(loss_config)
    
    logger.info(f"损失函数创建成功: {loss_config['type']}")
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        config=config.to_dict(),
        train_loader=train_loader,
        val_loader=val_loader,
        loss_fn=loss_fn,
        output_dir=output_dir,
        logger=logger
    )
    
    # 恢复训练
    if resume_checkpoint:
        logger.info(f"从检查点恢复训练: {resume_checkpoint}")
        trainer.load_checkpoint(str(resume_checkpoint))
        
        # 显示恢复后的训练状态
        checkpoint_info = trainer.get_checkpoint_info()
        logger.info(f"训练进度: {checkpoint_info['training_progress']}")
        logger.info(f"当前最佳指标: {checkpoint_info['best_metric_name']}={checkpoint_info['best_metric']:.4f}")
    
    # 开始训练
    logger.info("开始训练...")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    try:
        history = trainer.train()
        
        training_time = time.time() - start_time
        logger.info("=" * 60)
        logger.info("✅ 训练完成！")
        logger.info(f"总训练时间: {training_time:.2f}秒 ({training_time/3600:.2f}小时)")
        
        # 保存训练历史
        history_file = output_dir / 'history.json'
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
        logger.info(f"训练历史已保存: {history_file}")
        
        # 找出最佳模型
        if history:
            best_epoch = max(history, key=lambda x: x.get('val_iou', 0))
            logger.info(
                f"最佳模型 - Epoch: {best_epoch['epoch']}, "
                f"Val IoU: {best_epoch.get('val_iou', 0):.4f}, "
                f"Val Loss: {best_epoch.get('val_loss', 0):.4f}"
            )
        
        # 更新实验摘要
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        summary.update({
            'end_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'training_time_seconds': training_time,
            'training_completed': True,
            'total_epochs_trained': len(history),
            'best_metric': trainer.best_metric,
            'best_metric_name': trainer.best_metric_name,
        })
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info("实验摘要已更新")
        
    except KeyboardInterrupt:
        logger.warning("训练被用户中断")
        logger.info("检查点已保存，可以使用 --auto-resume 或 --resume 恢复训练")
        
        # 更新实验摘要
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        summary.update({
            'end_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'training_interrupted': True,
            'interruption_reason': 'user_interrupt'
        })
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {str(e)}", exc_info=True)
        
        # 更新实验摘要
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        summary.update({
            'end_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'training_failed': True,
            'error_message': str(e)
        })
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        raise
    
    finally:
        # 显示检查点信息
        final_checkpoint_info = trainer.get_checkpoint_info()
        logger.info("最终训练状态:")
        logger.info(f"  当前Epoch: {final_checkpoint_info['current_epoch']}")
        logger.info(f"  全局步数: {final_checkpoint_info['global_step']}")
        logger.info(f"  最佳指标: {final_checkpoint_info['best_metric_name']}={final_checkpoint_info['best_metric']:.4f}")
        logger.info(f"  保存的检查点: {len(final_checkpoint_info['saved_checkpoints'])}个")
        
        logger.info("=" * 60)


if __name__ == '__main__':
    main() 