#!/usr/bin/env python3
"""
测试PNG格式在整个代码库中的一致性
确保数据加载、训练、评估、预测和可视化都正确使用PNG格式
"""

import sys
import numpy as np
import cv2
import torch
from pathlib import Path
import tempfile
import shutil
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset
from src.data.preprocessing import RailwayAnnotationParser
from src.utils.visualization import load_mask, save_mask_visualization
from scripts.visualize_npy_masks import MaskVisualizer


def create_test_data() -> Tuple[Path, Dict]:
    """创建测试数据"""
    print("\n=== 创建测试数据 ===")
    
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    train_dir = temp_dir / 'train'
    train_images_dir = train_dir / 'images'
    train_masks_dir = train_dir / 'masks'
    train_images_dir.mkdir(parents=True)
    train_masks_dir.mkdir(parents=True)
    
    # 创建测试图像和掩码
    test_size = (544, 960)
    num_samples = 3
    
    for i in range(num_samples):
        # 创建随机图像
        image = np.random.randint(0, 255, (*test_size, 3), dtype=np.uint8)
        image_path = train_images_dir / f'test_image_{i}.png'
        cv2.imwrite(str(image_path), image)
        
        # 创建多标签掩码
        mask = np.zeros((*test_size, 3), dtype=np.float32)
        # 背景
        mask[:, :, 0] = 1.0
        # 主轨道
        y1, y2 = test_size[0] // 3, 2 * test_size[0] // 3
        x1, x2 = test_size[1] // 4, 3 * test_size[1] // 4
        mask[y1:y2, x1:x2, 0] = 0.0
        mask[y1:y2, x1:x2, 1] = 1.0
        # 分叉轨道（部分重叠）
        y3, y4 = test_size[0] // 2, test_size[0] - test_size[0] // 6
        x3, x4 = test_size[1] // 2, test_size[1] - test_size[1] // 4
        mask[y3:y4, x3:x4, 0] = 0.0
        mask[y3:y4, x3:x4, 2] = 1.0
        
        # 保存为PNG格式
        bgr_mask = np.zeros((*test_size, 3), dtype=np.uint8)
        bgr_mask[:, :, 0] = (mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
        bgr_mask[:, :, 1] = (mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
        bgr_mask[:, :, 2] = (mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
        mask_path = train_masks_dir / f'test_image_{i}.png'
        cv2.imwrite(str(mask_path), bgr_mask)
    
    # 创建配置
    config = {
        'data': {
            'processed_data_path': str(temp_dir),
            'use_multilabel': True,
            'mask_format': 'png',
            'classes': ['background', 'main_track', 'fork_track'],
            'image_size': {'height': test_size[0], 'width': test_size[1]}
        },
        'project': {
            'device': 'cpu'
        }
    }
    
    print(f"创建了 {num_samples} 个测试样本在: {temp_dir}")
    return temp_dir, config


def test_data_loading(temp_dir: Path, config: Dict) -> bool:
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        # 创建数据集
        dataset = RailwayTrackDataset(
            data_root=temp_dir,
            split='train',
            config=config,
            transform=None
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 加载样本
        sample = dataset[0]
        image = sample['image']
        mask = sample['mask']
        
        print(f"图像形状: {image.shape}")
        print(f"掩码形状: {mask.shape}")
        print(f"掩码数据类型: {mask.dtype}")
        print(f"掩码值范围: [{mask.min():.2f}, {mask.max():.2f}]")
        
        # 验证掩码格式
        assert mask.shape[2] == 3, f"期望3通道掩码，但得到 {mask.shape[2]} 通道"
        assert mask.dtype == np.float32, f"期望float32类型，但得到 {mask.dtype}"
        assert 0 <= mask.min() <= mask.max() <= 1, "掩码值应在[0, 1]范围内"
        
        # 检查各通道
        background_pixels = np.sum(mask[:, :, 0] > 0.5)
        main_track_pixels = np.sum(mask[:, :, 1] > 0.5)
        fork_track_pixels = np.sum(mask[:, :, 2] > 0.5)
        
        print(f"\n像素统计:")
        print(f"背景: {background_pixels}")
        print(f"主轨道: {main_track_pixels}")
        print(f"分叉轨道: {fork_track_pixels}")
        
        assert background_pixels > 0, "背景像素数应大于0"
        assert main_track_pixels > 0, "主轨道像素数应大于0"
        assert fork_track_pixels > 0, "分叉轨道像素数应大于0"
        
        print("✓ 数据加载测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False


def test_preprocessing(temp_dir: Path) -> bool:
    """测试预处理模块"""
    print("\n=== 测试预处理模块 ===")
    
    try:
        parser = RailwayAnnotationParser()
        
        # 创建测试掩码
        test_mask = np.zeros((100, 100, 3), dtype=np.float32)
        test_mask[30:70, 20:80, 1] = 1.0  # 主轨道
        test_mask[40:80, 40:90, 2] = 1.0  # 分叉轨道
        test_mask[:, :, 0] = 1 - np.maximum(test_mask[:, :, 1], test_mask[:, :, 2])  # 背景
        
        # 保存掩码
        save_path = temp_dir / 'test_mask.png'
        success = parser.save_mask(test_mask, save_path, use_multilabel=True)
        assert success, "保存掩码失败"
        assert save_path.exists(), f"掩码文件未创建: {save_path}"
        
        # 验证保存的格式
        saved_img = cv2.imread(str(save_path), cv2.IMREAD_COLOR)
        assert saved_img is not None, "无法读取保存的掩码"
        assert saved_img.shape == (100, 100, 3), f"保存的掩码形状错误: {saved_img.shape}"
        
        # 检查通道值
        unique_values = np.unique(saved_img)
        assert set(unique_values).issubset({0, 255}), f"掩码应只包含0和255值，但发现: {unique_values}"
        
        print("✓ 预处理模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 预处理模块测试失败: {e}")
        return False


def test_visualization(temp_dir: Path) -> bool:
    """测试可视化工具"""
    print("\n=== 测试可视化工具 ===")
    
    try:
        # 1. 测试load_mask函数
        mask_path = list((temp_dir / 'train' / 'masks').glob('*.png'))[0]
        loaded_mask = load_mask(mask_path)
        
        print(f"加载的掩码形状: {loaded_mask.shape}")
        print(f"加载的掩码类型: {loaded_mask.dtype}")
        
        # 2. 测试save_mask_visualization函数
        if len(loaded_mask.shape) == 2:
            # 转换为多标签格式进行测试
            h, w = loaded_mask.shape
            multi_mask = np.zeros((h, w, 3), dtype=np.float32)
            multi_mask[:, :, 0] = (loaded_mask == 0).astype(np.float32)
            multi_mask[:, :, 1] = (loaded_mask == 1).astype(np.float32)
            multi_mask[:, :, 2] = (loaded_mask == 2).astype(np.float32)
            loaded_mask = multi_mask
        
        vis_path = temp_dir / 'test_visualization.png'
        save_mask_visualization(loaded_mask, vis_path)
        assert vis_path.exists(), "可视化文件未创建"
        
        # 3. 测试MaskVisualizer类
        visualizer = MaskVisualizer()
        mask = visualizer.load_mask(mask_path)
        colored_mask = visualizer.create_color_mask(mask, style='solid')
        
        assert colored_mask.shape[2] == 3, "彩色掩码应为3通道"
        assert colored_mask.dtype == np.uint8, "彩色掩码应为uint8类型"
        
        # 获取统计信息
        stats = visualizer.get_statistics(mask)
        print(f"\n掩码统计:")
        print(f"主轨道占比: {stats['main_track_ratio']:.1%}")
        print(f"分叉轨道占比: {stats['fork_track_ratio']:.1%}")
        print(f"重叠区域占比: {stats['overlap_ratio']:.1%}")
        
        print("✓ 可视化工具测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 可视化工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_prediction_save(temp_dir: Path, config: Dict) -> bool:
    """测试预测结果保存"""
    print("\n=== 测试预测结果保存 ===")
    
    try:
        # 模拟预测结果
        pred_shape = (config['data']['image_size']['height'], 
                     config['data']['image_size']['width'], 3)
        
        # 创建多标签预测
        prediction = np.zeros(pred_shape, dtype=np.float32)
        prediction[:, :, 0] = 1.0  # 背景
        prediction[100:300, 200:700, 0] = 0.0
        prediction[100:300, 200:700, 1] = 1.0  # 主轨道
        prediction[200:400, 400:800, 0] = 0.0
        prediction[200:400, 400:800, 2] = 1.0  # 分叉轨道
        
        # 保存预测结果（模拟predict_inference.py的逻辑）
        pred_path = temp_dir / 'prediction.png'
        bgr_mask = np.zeros((pred_shape[0], pred_shape[1], 3), dtype=np.uint8)
        bgr_mask[:, :, 0] = (prediction[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
        bgr_mask[:, :, 1] = (prediction[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
        bgr_mask[:, :, 2] = (prediction[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
        cv2.imwrite(str(pred_path), bgr_mask)
        
        assert pred_path.exists(), "预测结果文件未创建"
        
        # 验证保存的预测
        saved_pred = cv2.imread(str(pred_path), cv2.IMREAD_COLOR)
        assert saved_pred is not None, "无法读取预测结果"
        assert saved_pred.shape == (pred_shape[0], pred_shape[1], 3), "预测结果形状错误"
        
        print("✓ 预测结果保存测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 预测结果保存测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("PNG格式一致性测试")
    print("=" * 60)
    
    # 创建测试数据
    temp_dir, config = create_test_data()
    
    try:
        # 运行各项测试
        tests = [
            ("数据加载", lambda: test_data_loading(temp_dir, config)),
            ("预处理模块", lambda: test_preprocessing(temp_dir)),
            ("可视化工具", lambda: test_visualization(temp_dir)),
            ("预测保存", lambda: test_prediction_save(temp_dir, config))
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n{'='*40}")
            result = test_func()
            results.append((test_name, result))
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过！PNG格式在整个代码库中保持一致。")
        else:
            print("\n⚠️ 部分测试失败，请检查相关代码。")
        
    finally:
        # 清理临时目录
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"\n已清理临时目录: {temp_dir}")


if __name__ == '__main__':
    run_all_tests()