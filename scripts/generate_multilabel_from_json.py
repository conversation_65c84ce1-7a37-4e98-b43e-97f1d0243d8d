#!/usr/bin/env python3
"""
从JSON标注文件生成多标签掩码
使用data pipeline中的preprocessing模块
"""

import sys
import json
import numpy as np
from pathlib import Path
from tqdm import tqdm
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def process_single_json(args):
    """处理单个JSON文件生成多标签掩码"""
    json_path, output_dir, image_shape, parser = args
    
    try:
        # 解析JSON文件（使用解析器的parse_json_file方法）
        parsed_data = parser.parse_json_file(json_path)

        # 生成多标签掩码
        multilabel_mask = parser.create_multilabel_segmentation_mask(parsed_data, image_shape)
        
        # 保存掩码文件 - 使用PNG格式以节省空间
        mask_filename = json_path.stem + '.png'
        mask_path = output_dir / mask_filename
        
        # 将多标签掩码转换为BGR PNG格式 (OpenCV格式)
        bgr_mask = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        # B通道: 背景
        bgr_mask[:, :, 0] = (multilabel_mask[:, :, 0] > 0.5).astype(np.uint8) * 255
        # G通道: 主轨道
        bgr_mask[:, :, 1] = (multilabel_mask[:, :, 1] > 0.5).astype(np.uint8) * 255
        # R通道: 分叉轨道
        bgr_mask[:, :, 2] = (multilabel_mask[:, :, 2] > 0.5).astype(np.uint8) * 255
        
        # 保存PNG
        cv2.imwrite(str(mask_path), bgr_mask)
        
        # 计算统计信息
        main_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        overlap_pixels = np.sum(np.logical_and(multilabel_mask[:, :, 1] > 0, 
                                               multilabel_mask[:, :, 2] > 0))
        
        return {
            'file': json_path.name,
            'main_pixels': main_pixels,
            'fork_pixels': fork_pixels,
            'overlap_pixels': overlap_pixels,
            'status': 'success'
        }
        
    except Exception as e:
        return {
            'file': json_path.name,
            'status': 'error',
            'error': str(e)
        }


def generate_masks(json_dir: Path, output_base_dir: Path, image_shape: tuple = (1080, 1920), 
                   num_workers: int = None, split_ratio: dict = None):
    """生成所有掩码文件"""
    
    # 设置工作进程数
    if num_workers is None:
        num_workers = min(multiprocessing.cpu_count() - 1, 8)
    
    print(f"使用 {num_workers} 个工作进程")
    
    # 默认数据集划分比例
    if split_ratio is None:
        split_ratio = {'train': 0.8, 'val': 0.1, 'test': 0.1}
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 获取所有JSON文件
    json_files = sorted(list(json_dir.glob('*.json')))
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 计算数据集划分
    total = len(json_files)
    train_end = int(total * split_ratio['train'])
    val_end = train_end + int(total * split_ratio['val'])
    
    # 创建输出目录
    splits = {
        'train': json_files[:train_end],
        'val': json_files[train_end:val_end],
        'test': json_files[val_end:]
    }
    
    print(f"数据集划分: train={len(splits['train'])}, val={len(splits['val'])}, test={len(splits['test'])}")
    
    # 统计信息
    total_main = 0
    total_fork = 0
    total_overlap = 0
    success_count = 0
    error_count = 0
    files_with_fork = 0
    files_with_overlap = 0
    
    # 处理每个split
    for split_name, split_files in splits.items():
        # 创建输出目录
        output_dir = output_base_dir / split_name / 'masks'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n处理 {split_name} 数据集...")
        
        # 准备参数
        args_list = [(json_file, output_dir, image_shape, parser) for json_file in split_files]
        
        # 并行处理
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(process_single_json, args): args[0] 
                              for args in args_list}
            
            # 处理完成的任务
            with tqdm(total=len(split_files), desc=f"生成{split_name}掩码") as pbar:
                for future in as_completed(future_to_file):
                    result = future.result()
                    
                    if isinstance(result, dict) and result.get('status') == 'success':
                        success_count += 1
                        total_main += result['main_pixels']
                        total_fork += result['fork_pixels']
                        total_overlap += result['overlap_pixels']
                        
                        if result['fork_pixels'] > 0:
                            files_with_fork += 1
                        if result['overlap_pixels'] > 0:
                            files_with_overlap += 1
                    else:
                        error_count += 1
                        if isinstance(result, dict):
                            print(f"\n错误: {result.get('file', 'unknown')} - {result.get('error', 'unknown error')}")
                    
                    pbar.update(1)
    
    # 打印统计信息
    print("\n=== 生成完成 ===")
    print(f"成功: {success_count} 个文件")
    print(f"错误: {error_count} 个文件")
    
    if success_count > 0:
        print(f"包含分叉轨道的文件: {files_with_fork} ({files_with_fork/success_count*100:.1f}%)")
        print(f"包含重叠区域的文件: {files_with_overlap} ({files_with_overlap/success_count*100:.1f}%)")
    
    if success_count > 0:
        avg_overlap = total_overlap / success_count
        print(f"\n平均每个文件:")
        print(f"  主轨道像素: {total_main/success_count:.0f}")
        print(f"  分叉轨道像素: {total_fork/success_count:.0f}")
        print(f"  重叠像素: {avg_overlap:.0f}")
    
    return success_count, error_count


def verify_masks(output_dir: Path, num_samples: int = 5):
    """验证生成的掩码"""
    print("\n=== 验证掩码 ===")
    
    mask_files = []
    for split in ['train', 'val', 'test']:
        split_mask_dir = output_dir / split / 'masks'
        if split_mask_dir.exists():
            mask_files.extend(list(split_mask_dir.glob('*.npy')))
    
    if not mask_files:
        print("未找到任何.npy掩码文件")
        return
    
    # 随机采样
    import random
    sample_files = random.sample(mask_files, min(num_samples, len(mask_files)))
    
    for mask_file in sample_files:
        try:
            mask = np.load(mask_file)
            print(f"\n{mask_file.relative_to(output_dir)}:")
            print(f"  形状: {mask.shape}")
            
            if len(mask.shape) == 3 and mask.shape[2] == 3:
                # 多标签掩码
                bg_pixels = np.sum(mask[:, :, 0] > 0)
                main_pixels = np.sum(mask[:, :, 1] > 0)
                fork_pixels = np.sum(mask[:, :, 2] > 0)
                overlap_pixels = np.sum(np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0))
                
                total_pixels = mask.shape[0] * mask.shape[1]
                print(f"  背景: {bg_pixels} ({bg_pixels/total_pixels*100:.1f}%)")
                print(f"  主轨道: {main_pixels} ({main_pixels/total_pixels*100:.1f}%)")
                print(f"  分叉轨道: {fork_pixels} ({fork_pixels/total_pixels*100:.1f}%)")
                print(f"  重叠区域: {overlap_pixels} ({overlap_pixels/total_pixels*100:.1f}%)")
                print(f"  ✓ 多标签格式正确")
                
                # 保存可视化
                if fork_pixels > 0 or overlap_pixels > 0:
                    # 创建可视化
                    vis_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
                    vis_mask[:, :, 1] = (mask[:, :, 1] > 0).astype(np.uint8) * 255  # 主轨道为绿色
                    vis_mask[:, :, 0] = (mask[:, :, 2] > 0).astype(np.uint8) * 255  # 分叉轨道为红色
                    
                    # 重叠区域为黄色
                    overlap = np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
                    vis_mask[overlap] = [255, 255, 0]
                    
                    # 保存可视化
                    vis_filename = f'multilabel_example_{mask_file.stem}.png'
                    cv2.imwrite(vis_filename, vis_mask)
                    print(f"  已保存可视化到 {vis_filename}")
            else:
                print(f"  ✗ 不是多标签格式")
        except Exception as e:
            print(f"\n{mask_file.relative_to(output_dir)}:")
            print(f"  ✗ 读取失败: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description='从JSON标注生成多标签掩码')
    parser.add_argument('--json-dir', type=str, default='data/railway_annotation_6mm',
                        help='JSON标注文件目录')
    parser.add_argument('--output-dir', type=str, default='/home/<USER>/data/railway_track_dataset',
                        help='输出目录路径')
    parser.add_argument('--image-height', type=int, default=1080,
                        help='图像高度')
    parser.add_argument('--image-width', type=int, default=1920,
                        help='图像宽度')
    parser.add_argument('--num-workers', type=int, default=None,
                        help='并行工作进程数')
    parser.add_argument('--verify', action='store_true',
                        help='生成后验证掩码')
    parser.add_argument('--verify-only', action='store_true',
                        help='只验证，不生成')
    parser.add_argument('--sample', type=int, default=None,
                        help='只处理指定数量的样本文件（用于测试）')
    
    args = parser.parse_args()
    
    json_dir = Path(args.json_dir)
    if not json_dir.exists():
        print(f"错误：JSON目录不存在: {json_dir}")
        return
    
    output_dir = Path(args.output_dir)
    image_shape = (args.image_height, args.image_width)
    
    if args.verify_only:
        # 只验证
        verify_masks(output_dir)
    else:
        # 生成掩码
        print("=== 开始从JSON标注生成多标签掩码 ===")
        print(f"JSON目录: {json_dir}")
        print(f"输出目录: {output_dir}")
        print(f"图像尺寸: {image_shape}")
        print("注意：这将生成新的PNG文件！")
        
        # 如果指定了sample参数，只处理部分文件
        if args.sample:
            # 获取样本文件
            all_json_files = sorted(list(json_dir.glob('*.json')))
            import random
            sample_files = random.sample(all_json_files, min(args.sample, len(all_json_files)))
            
            # 创建临时目录
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_json_dir = Path(temp_dir) / 'json_samples'
                temp_json_dir.mkdir()
                
                # 复制样本文件
                import shutil
                for f in sample_files:
                    shutil.copy2(f, temp_json_dir / f.name)
                
                # 生成掩码
                success_count, error_count = generate_masks(
                    temp_json_dir, 
                    output_dir,
                    image_shape,
                    args.num_workers
                )
        else:
            # 生成所有掩码
            success_count, error_count = generate_masks(
                json_dir, 
                output_dir,
                image_shape,
                args.num_workers
            )
        
        if args.verify and success_count > 0:
            verify_masks(output_dir)
        
        print("\n完成！")
        print(f"成功处理 {success_count} 个文件")
        if error_count > 0:
            print(f"失败 {error_count} 个文件")


if __name__ == '__main__':
    main()