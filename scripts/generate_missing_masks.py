#!/usr/bin/env python3
"""
自动生成缺失掩码的脚本
用于修复数据集中图像文件存在但掩码文件缺失的问题
"""

import os
import sys
import cv2
import numpy as np
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from tqdm import tqdm
import torch
import torch.nn as nn
from PIL import Image

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class MissingMaskGenerator:
    """缺失掩码生成器"""
    
    def __init__(self, dataset_root: str, strategy: str = 'background'):
        """
        初始化生成器
        
        Args:
            dataset_root: 数据集根目录
            strategy: 生成策略 ('background', 'inference', 'interpolation')
        """
        self.dataset_root = Path(dataset_root)
        self.strategy = strategy
        
        # 支持的分割
        self.splits = ['train', 'val', 'test']
        
        print(f"初始化缺失掩码生成器")
        print(f"数据集根目录: {self.dataset_root}")
        print(f"生成策略: {strategy}")
    
    def find_missing_masks(self) -> Dict[str, List[str]]:
        """
        查找所有缺失掩码的图像文件
        
        Returns:
            每个分割中缺失掩码的文件列表
        """
        missing_files = {}
        
        for split in self.splits:
            images_dir = self.dataset_root / split / 'images'
            masks_dir = self.dataset_root / split / 'masks'
            
            if not images_dir.exists():
                print(f"跳过不存在的分割: {split}")
                continue
            
            # 获取所有图像文件
            image_files = set(f.name for f in images_dir.glob('*.png'))
            
            # 获取所有掩码文件
            if masks_dir.exists():
                mask_files = set(f.name for f in masks_dir.glob('*.png'))
            else:
                mask_files = set()
                masks_dir.mkdir(parents=True, exist_ok=True)
            
            # 找出缺失的掩码
            missing = list(image_files - mask_files)
            missing.sort()
            
            missing_files[split] = missing
            
            print(f"{split} 分割:")
            print(f"  图像文件: {len(image_files)}")
            print(f"  掩码文件: {len(mask_files)}")
            print(f"  缺失掩码: {len(missing)}")
        
        return missing_files
    
    def generate_background_mask(self, image_path: Path, output_path: Path) -> bool:
        """
        生成全背景掩码
        
        Args:
            image_path: 图像文件路径
            output_path: 输出掩码路径
            
        Returns:
            是否生成成功
        """
        try:
            # 读取图像获取尺寸
            image = cv2.imread(str(image_path))
            if image is None:
                print(f"无法读取图像: {image_path}")
                return False
            
            h, w = image.shape[:2]
            
            # 创建多标签掩码 (H, W, 3) - BGR格式
            mask = np.zeros((h, w, 3), dtype=np.uint8)
            # 背景通道设为255（蓝色通道）
            mask[:, :, 0] = 255  # B通道 = 背景
            # 主轨道和分叉轨道通道保持为0
            mask[:, :, 1] = 0    # G通道 = 主轨道
            mask[:, :, 2] = 0    # R通道 = 分叉轨道
            
            # 保存掩码
            cv2.imwrite(str(output_path), mask)
            return True
            
        except Exception as e:
            print(f"生成背景掩码失败 {image_path}: {e}")
            return False
    
    def generate_inference_mask(self, image_path: Path, output_path: Path, model: Optional[nn.Module] = None) -> bool:
        """
        使用模型推理生成掩码
        
        Args:
            image_path: 图像文件路径
            output_path: 输出掩码路径
            model: 预训练模型
            
        Returns:
            是否生成成功
        """
        if model is None:
            print("推理模式需要提供预训练模型，回退到背景掩码")
            return self.generate_background_mask(image_path, output_path)
        
        try:
            # 加载和预处理图像
            image = cv2.imread(str(image_path))
            if image is None:
                return False
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            h, w = image_rgb.shape[:2]
            
            # 预处理：调整大小和归一化
            input_size = (544, 960)  # 从配置文件读取
            image_resized = cv2.resize(image_rgb, (input_size[1], input_size[0]))
            image_tensor = torch.from_numpy(image_resized).float() / 255.0
            image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0)  # (1, 3, H, W)
            
            # 标准化
            mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
            image_tensor = (image_tensor - mean) / std
            
            # 模型推理
            device = next(model.parameters()).device
            image_tensor = image_tensor.to(device)
            
            with torch.no_grad():
                model.eval()
                outputs = model(image_tensor)
                predictions = torch.sigmoid(outputs)  # (1, 3, H, W)
            
            # 后处理
            predictions = predictions.squeeze(0).cpu().numpy()  # (3, H, W)
            predictions = np.transpose(predictions, (1, 2, 0))  # (H, W, 3)
            
            # 调整回原始尺寸
            mask_resized = cv2.resize(predictions, (w, h))
            
            # 转换为BGR格式的掩码
            mask_bgr = np.zeros((h, w, 3), dtype=np.uint8)
            mask_bgr[:, :, 0] = (mask_resized[:, :, 0] * 255).astype(np.uint8)  # 背景 -> B通道
            mask_bgr[:, :, 1] = (mask_resized[:, :, 1] * 255).astype(np.uint8)  # 主轨道 -> G通道
            mask_bgr[:, :, 2] = (mask_resized[:, :, 2] * 255).astype(np.uint8)  # 分叉轨道 -> R通道
            
            # 保存掩码
            cv2.imwrite(str(output_path), mask_bgr)
            return True
            
        except Exception as e:
            print(f"推理生成掩码失败 {image_path}: {e}")
            return self.generate_background_mask(image_path, output_path)
    
    def generate_interpolation_mask(self, image_path: Path, output_path: Path) -> bool:
        """
        使用相邻帧插值生成掩码（适用于视频序列）
        
        Args:
            image_path: 图像文件路径
            output_path: 输出掩码路径
            
        Returns:
            是否生成成功
        """
        try:
            # 解析文件名，查找相邻帧
            filename = image_path.stem
            masks_dir = output_path.parent
            
            # 从文件名中提取帧号
            if '_frame_' in filename:
                base_name, frame_part = filename.rsplit('_frame_', 1)
                try:
                    frame_num = int(frame_part)
                except ValueError:
                    # 如果无法解析帧号，回退到背景掩码
                    return self.generate_background_mask(image_path, output_path)
            else:
                return self.generate_background_mask(image_path, output_path)
            
            # 查找相邻的有效掩码
            neighbor_masks = []
            search_range = 50  # 搜索前后50帧
            
            for offset in range(1, search_range + 1):
                # 查找前后相邻帧
                for delta in [-offset, offset]:
                    neighbor_frame = frame_num + delta
                    neighbor_filename = f"{base_name}_frame_{neighbor_frame}.png"
                    neighbor_mask_path = masks_dir / neighbor_filename
                    
                    if neighbor_mask_path.exists():
                        neighbor_masks.append(neighbor_mask_path)
                        if len(neighbor_masks) >= 2:  # 找到足够的相邻掩码
                            break
                
                if len(neighbor_masks) >= 2:
                    break
            
            if len(neighbor_masks) == 0:
                # 没有找到相邻掩码，生成背景掩码
                return self.generate_background_mask(image_path, output_path)
            
            # 加载相邻掩码并取平均
            masks = []
            for mask_path in neighbor_masks[:2]:  # 最多使用2个相邻掩码
                mask = cv2.imread(str(mask_path))
                if mask is not None:
                    masks.append(mask.astype(np.float32))
            
            if len(masks) == 0:
                return self.generate_background_mask(image_path, output_path)
            
            # 计算平均掩码
            avg_mask = np.mean(masks, axis=0)
            avg_mask = np.clip(avg_mask, 0, 255).astype(np.uint8)
            
            # 读取目标图像尺寸
            image = cv2.imread(str(image_path))
            if image is None:
                return False
            
            h, w = image.shape[:2]
            
            # 调整掩码尺寸
            if avg_mask.shape[:2] != (h, w):
                avg_mask = cv2.resize(avg_mask, (w, h))
            
            # 保存插值掩码
            cv2.imwrite(str(output_path), avg_mask)
            return True
            
        except Exception as e:
            print(f"插值生成掩码失败 {image_path}: {e}")
            return self.generate_background_mask(image_path, output_path)
    
    def load_pretrained_model(self, model_path: str) -> Optional[nn.Module]:
        """
        加载预训练模型用于推理
        
        Args:
            model_path: 模型权重路径
            
        Returns:
            加载的模型
        """
        try:
            from src.models.segmentation_model import create_model
            
            # 创建模型
            model = create_model(
                architecture='pan',
                backbone='efficientnet-b4',
                num_classes=3,
                pretrained=False
            )
            
            # 加载权重
            checkpoint = torch.load(model_path, map_location='cpu')
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            # 移至GPU（如果可用）
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = model.to(device)
            model.eval()
            
            print(f"成功加载预训练模型: {model_path}")
            return model
            
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            return None
    
    def generate_missing_masks(self, model_path: Optional[str] = None, dry_run: bool = False):
        """
        生成所有缺失的掩码
        
        Args:
            model_path: 预训练模型路径（用于推理策略）
            dry_run: 是否只是预览，不实际生成
        """
        print("\n=== 开始生成缺失掩码 ===")
        
        # 查找缺失文件
        missing_files = self.find_missing_masks()
        
        total_missing = sum(len(files) for files in missing_files.values())
        if total_missing == 0:
            print("没有发现缺失的掩码文件！")
            return
        
        print(f"\n总计需要生成 {total_missing} 个掩码文件")
        
        if dry_run:
            print("预览模式，不会实际生成文件")
            return
        
        # 加载模型（如果使用推理策略）
        model = None
        if self.strategy == 'inference' and model_path:
            model = self.load_pretrained_model(model_path)
            if model is None:
                print("无法加载模型，回退到背景掩码策略")
                self.strategy = 'background'
        
        # 生成掩码
        total_generated = 0
        total_failed = 0
        
        for split, files in missing_files.items():
            if not files:
                continue
            
            print(f"\n处理 {split} 分割的 {len(files)} 个缺失掩码...")
            
            images_dir = self.dataset_root / split / 'images'
            masks_dir = self.dataset_root / split / 'masks'
            masks_dir.mkdir(parents=True, exist_ok=True)
            
            with tqdm(files, desc=f"生成{split}掩码") as pbar:
                for filename in pbar:
                    image_path = images_dir / filename
                    mask_path = masks_dir / filename
                    
                    # 跳过已存在的掩码
                    if mask_path.exists():
                        continue
                    
                    # 根据策略生成掩码
                    success = False
                    if self.strategy == 'background':
                        success = self.generate_background_mask(image_path, mask_path)
                    elif self.strategy == 'inference':
                        success = self.generate_inference_mask(image_path, mask_path, model)
                    elif self.strategy == 'interpolation':
                        success = self.generate_interpolation_mask(image_path, mask_path)
                    
                    if success:
                        total_generated += 1
                    else:
                        total_failed += 1
                    
                    pbar.set_postfix({
                        'generated': total_generated,
                        'failed': total_failed
                    })
        
        print(f"\n=== 掩码生成完成 ===")
        print(f"成功生成: {total_generated}")
        print(f"生成失败: {total_failed}")
        
        # 验证结果
        print("\n验证生成结果:")
        self.verify_completeness()
    
    def verify_completeness(self):
        """验证数据集完整性"""
        for split in self.splits:
            images_dir = self.dataset_root / split / 'images'
            masks_dir = self.dataset_root / split / 'masks'
            
            if not images_dir.exists():
                continue
            
            image_count = len(list(images_dir.glob('*.png')))
            mask_count = len(list(masks_dir.glob('*.png'))) if masks_dir.exists() else 0
            
            print(f"{split}: 图像={image_count}, 掩码={mask_count}, 匹配={'✓' if image_count == mask_count else '✗'}")


def main():
    parser = argparse.ArgumentParser(description='生成缺失的掩码文件')
    parser.add_argument('--dataset-root', type=str, required=True,
                       help='数据集根目录')
    parser.add_argument('--strategy', type=str, choices=['background', 'inference', 'interpolation'],
                       default='background', help='生成策略')
    parser.add_argument('--model-path', type=str, default=None,
                       help='预训练模型路径（用于inference策略）')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，不实际生成文件')
    
    args = parser.parse_args()
    
    # 创建生成器
    generator = MissingMaskGenerator(args.dataset_root, args.strategy)
    
    # 生成掩码
    generator.generate_missing_masks(args.model_path, args.dry_run)


if __name__ == '__main__':
    main() 