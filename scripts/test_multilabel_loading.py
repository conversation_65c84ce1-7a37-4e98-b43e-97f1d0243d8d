#!/usr/bin/env python3
"""
测试多标签掩码加载
"""

import sys
import numpy as np
from pathlib import Path
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset


def test_loading():
    """测试数据集加载"""
    
    # 配置
    data_dir = "/home/<USER>/data/railway_track_dataset"
    
    # 创建数据集
    dataset = RailwayTrackDataset(
        data_root=data_dir,
        split='train',
        transform=None,
        config={'use_multilabel': True}
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试加载几个样本
    for i in range(min(5, len(dataset))):
        try:
            sample = dataset[i]
            image = sample['image']
            mask = sample['mask']
            
            print(f"\n样本 {i}:")
            print(f"  图像形状: {image.shape}")
            print(f"  掩码形状: {mask.shape}")
            
            if len(mask.shape) == 3:
                # 多标签掩码
                bg_pixels = np.sum(mask[:, :, 0] > 0)
                main_pixels = np.sum(mask[:, :, 1] > 0)
                fork_pixels = np.sum(mask[:, :, 2] > 0)
                overlap_pixels = np.sum(np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0))
                
                total_pixels = mask.shape[0] * mask.shape[1]
                print(f"  背景: {bg_pixels} ({bg_pixels/total_pixels*100:.1f}%)")
                print(f"  主轨道: {main_pixels} ({main_pixels/total_pixels*100:.1f}%)")
                print(f"  分叉轨道: {fork_pixels} ({fork_pixels/total_pixels*100:.1f}%)")
                print(f"  重叠区域: {overlap_pixels} ({overlap_pixels/total_pixels*100:.1f}%)")
                
                # 保存一个可视化示例
                if i == 0:
                    # 创建可视化
                    vis_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
                    vis_mask[:, :, 1] = (mask[:, :, 1] > 0).astype(np.uint8) * 255  # 主轨道为绿色
                    vis_mask[:, :, 0] = (mask[:, :, 2] > 0).astype(np.uint8) * 255  # 分叉轨道为红色
                    
                    # 保存可视化
                    cv2.imwrite('multilabel_mask_example.png', vis_mask)
                    print("  已保存可视化到 multilabel_mask_example.png")
            else:
                print(f"  单通道掩码，值: {np.unique(mask)}")
                
        except Exception as e:
            print(f"\n样本 {i} 加载失败: {str(e)}")
    
    print("\n测试完成！")


if __name__ == '__main__':
    test_loading()