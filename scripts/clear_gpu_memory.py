#!/usr/bin/env python
"""
GPU内存清理脚本
用于训练开始前清理GPU内存，解决内存碎片和残留占用问题
"""

import gc
import os
import torch
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def clear_gpu_memory(logger=None):
    """清理GPU内存"""
    if logger is None:
        logger = setup_logging()
        
    logger.info("开始清理GPU内存...")
    
    # 检查CUDA是否可用
    if not torch.cuda.is_available():
        logger.warning("CUDA不可用，跳过GPU内存清理")
        return
        
    # 获取GPU信息
    device_count = torch.cuda.device_count()
    logger.info(f"发现 {device_count} 个GPU设备")
    
    for device_id in range(device_count):
        logger.info(f"清理GPU {device_id} 内存...")
        
        # 设置当前设备
        torch.cuda.set_device(device_id)
        
        # 记录清理前的内存使用情况
        if torch.cuda.is_available():
            allocated_before = torch.cuda.memory_allocated(device_id) / 1024**3
            reserved_before = torch.cuda.memory_reserved(device_id) / 1024**3
            logger.info(f"GPU {device_id} 清理前 - 已分配: {allocated_before:.2f}GB, 已保留: {reserved_before:.2f}GB")
        
        # 清理缓存
        torch.cuda.empty_cache()
        
        # 强制垃圾回收
        gc.collect()
        
        # 重置内存统计
        torch.cuda.reset_peak_memory_stats(device_id)
        torch.cuda.reset_accumulated_memory_stats(device_id)
        
        # 记录清理后的内存使用情况
        if torch.cuda.is_available():
            allocated_after = torch.cuda.memory_allocated(device_id) / 1024**3
            reserved_after = torch.cuda.memory_reserved(device_id) / 1024**3
            logger.info(f"GPU {device_id} 清理后 - 已分配: {allocated_after:.2f}GB, 已保留: {reserved_after:.2f}GB")
            
            # 计算释放的内存
            freed_allocated = allocated_before - allocated_after
            freed_reserved = reserved_before - reserved_after
            logger.info(f"GPU {device_id} 释放内存 - 已分配: {freed_allocated:.2f}GB, 已保留: {freed_reserved:.2f}GB")

def set_memory_fraction(fraction=0.9, logger=None):
    """设置GPU内存使用上限"""
    if logger is None:
        logger = setup_logging()
        
    if not torch.cuda.is_available():
        logger.warning("CUDA不可用，跳过内存分数设置")
        return
        
    logger.info(f"设置GPU内存使用上限为 {fraction*100:.1f}%")
    
    for device_id in range(torch.cuda.device_count()):
        torch.cuda.set_per_process_memory_fraction(fraction, device_id)
        logger.info(f"GPU {device_id} 内存分数已设置为 {fraction}")

def optimize_pytorch_settings(logger=None):
    """优化PyTorch设置以减少内存使用"""
    if logger is None:
        logger = setup_logging()
        
    logger.info("优化PyTorch内存设置...")
    
    # 设置CUDA内存分配器配置
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    logger.info("设置CUDA内存分配器最大分片大小: 128MB")
    
    # 禁用cudnn基准测试（可能导致内存碎片）
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True
    logger.info("禁用cudnn基准测试，启用确定性模式")
    
    # 设置更小的工作进程数
    torch.set_num_threads(2)
    logger.info("设置PyTorch线程数为2")

def get_memory_info(device_id=0):
    """获取详细的GPU内存信息"""
    if not torch.cuda.is_available():
        return None
        
    # 获取总内存
    total_memory = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
    
    # 获取当前内存使用情况
    allocated = torch.cuda.memory_allocated(device_id) / 1024**3
    reserved = torch.cuda.memory_reserved(device_id) / 1024**3
    free = total_memory - reserved
    
    return {
        'total_memory_gb': total_memory,
        'allocated_gb': allocated,
        'reserved_gb': reserved,
        'free_gb': free,
        'utilization_percent': (reserved / total_memory) * 100
    }

def monitor_memory_usage(device_id=0, logger=None):
    """监控内存使用情况"""
    if logger is None:
        logger = setup_logging()
        
    if not torch.cuda.is_available():
        logger.warning("CUDA不可用，无法监控GPU内存")
        return
        
    info = get_memory_info(device_id)
    if info:
        logger.info(f"GPU {device_id} 内存状态:")
        logger.info(f"  总内存: {info['total_memory_gb']:.2f}GB")
        logger.info(f"  已分配: {info['allocated_gb']:.2f}GB")
        logger.info(f"  已保留: {info['reserved_gb']:.2f}GB")
        logger.info(f"  可用内存: {info['free_gb']:.2f}GB")
        logger.info(f"  利用率: {info['utilization_percent']:.1f}%")

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("GPU内存清理和优化脚本")
    logger.info("=" * 60)
    
    # 监控初始内存状态
    logger.info("初始内存状态:")
    monitor_memory_usage(0, logger)
    
    # 清理GPU内存
    clear_gpu_memory(logger)
    
    # 设置内存使用上限
    set_memory_fraction(0.85, logger)  # 设置为85%以留出缓冲
    
    # 优化PyTorch设置
    optimize_pytorch_settings(logger)
    
    # 监控优化后的内存状态
    logger.info("优化后内存状态:")
    monitor_memory_usage(0, logger)
    
    logger.info("=" * 60)
    logger.info("GPU内存清理和优化完成！")
    logger.info("建议重新启动训练脚本以获得最佳效果")
    logger.info("=" * 60)

if __name__ == "__main__":
    main() 