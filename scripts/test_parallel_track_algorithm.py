#!/usr/bin/env python
"""
测试平行轨道线算法
验证从左右两条线生成轨道区域mask的新算法
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import json

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_parallel_track_algorithm():
    """测试平行轨道线算法"""
    print("=== 测试平行轨道线算法 ===")
    
    # 设置测试参数
    json_dir = Path('data/railway_annotation_6mm')
    output_dir = Path('outputs/parallel_track_test')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 获取一个测试文件
    json_files = list(json_dir.glob('*.json'))
    if not json_files:
        print("❌ 没有找到JSON文件")
        return
    
    test_file = json_files[0]
    print(f"测试文件: {test_file.name}")
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(test_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 分析轨道数据
    main_left = []
    main_right = []
    fork_left = []
    fork_right = []
    
    for track in annotation_data['tracks']:
        print(f"  轨道: {track['label']}, {len(track['points'])} 个关键点")
        
        if track['label'] == 'Main_Left':
            main_left = track['points']
        elif track['label'] == 'Main_Right':
            main_right = track['points']
        elif track['label'] == 'Fork_Left':
            fork_left = track['points']
        elif track['label'] == 'Fork_Right':
            fork_right = track['points']
    
    # 图像尺寸
    image_shape = (1080, 1920)
    
    print(f"\n轨道配对分析:")
    print(f"  主轨道: Main_Left={len(main_left)}点, Main_Right={len(main_right)}点")
    print(f"  分叉轨道: Fork_Left={len(fork_left)}点, Fork_Right={len(fork_right)}点")
    
    # 测试主轨道多边形生成
    if main_left and main_right:
        print(f"\n=== 主轨道多边形生成 ===")
        track_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
        print(f"生成的多边形顶点数: {len(track_polygon)}")
        print(f"原始左线点数: {len(main_left)}")
        print(f"原始右线点数: {len(main_right)}")
        print(f"多边形顶点数验证: {len(track_polygon)} == {len(main_left) + len(main_right)} ? {len(track_polygon) == len(main_left) + len(main_right)}")
        
        # 生成掩码
        track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
        main_track_pixels = np.sum(track_mask > 0)
        print(f"主轨道像素数: {main_track_pixels}")
    
    # 生成完整掩码
    print(f"\n=== 完整掩码生成 ===")
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    print(f"掩码统计:")
    unique_values = np.unique(mask)
    print(f"  唯一值: {unique_values}")
    print(f"  背景像素(0): {np.sum(mask == 0)}")
    print(f"  主轨道像素(1): {np.sum(mask == 1)}")
    print(f"  分叉轨道像素(2): {np.sum(mask == 2)}")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 显示原始轨道线
    axes[0, 0].set_xlim(0, 1920)
    axes[0, 0].set_ylim(1080, 0)  # 翻转Y轴
    axes[0, 0].set_title('原始轨道线')
    
    if main_left:
        main_left_x, main_left_y = zip(*main_left)
        axes[0, 0].plot(main_left_x, main_left_y, 'g-o', label=f'Main_Left ({len(main_left)}点)', markersize=3)
    
    if main_right:
        main_right_x, main_right_y = zip(*main_right)
        axes[0, 0].plot(main_right_x, main_right_y, 'b-o', label=f'Main_Right ({len(main_right)}点)', markersize=3)
    
    if fork_left:
        fork_left_x, fork_left_y = zip(*fork_left)
        axes[0, 0].plot(fork_left_x, fork_left_y, 'r-o', label=f'Fork_Left ({len(fork_left)}点)', markersize=3)
    
    if fork_right:
        fork_right_x, fork_right_y = zip(*fork_right)
        axes[0, 0].plot(fork_right_x, fork_right_y, 'm-o', label=f'Fork_Right ({len(fork_right)}点)', markersize=3)
    
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 显示主轨道多边形
    if main_left and main_right:
        axes[0, 1].set_xlim(0, 1920)
        axes[0, 1].set_ylim(1080, 0)
        axes[0, 1].set_title('主轨道多边形构造')
        
        # 显示多边形顶点
        track_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
        if track_polygon:
            polygon_x, polygon_y = zip(*track_polygon)
            axes[0, 1].plot(polygon_x, polygon_y, 'k-', linewidth=2, label='轨道多边形边界')
            axes[0, 1].scatter(polygon_x, polygon_y, c='red', s=20, label=f'多边形顶点({len(track_polygon)}个)')
        
        # 显示原始线
        axes[0, 1].plot(main_left_x, main_left_y, 'g--', alpha=0.7, label='Main_Left')
        axes[0, 1].plot(main_right_x, main_right_y, 'b--', alpha=0.7, label='Main_Right')
        
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    
    # 显示生成的掩码
    axes[1, 0].imshow(mask, cmap='viridis', vmin=0, vmax=2)
    axes[1, 0].set_title('生成的掩码')
    axes[1, 0].axis('off')
    
    # 显示掩码统计
    axes[1, 1].bar(['背景', '主轨道', '分叉轨道'], 
                   [np.sum(mask == 0), np.sum(mask == 1), np.sum(mask == 2)],
                   color=['black', 'green', 'red'])
    axes[1, 1].set_title('像素统计')
    axes[1, 1].set_ylabel('像素数量')
    
    # 添加数值标签
    for i, v in enumerate([np.sum(mask == 0), np.sum(mask == 1), np.sum(mask == 2)]):
        axes[1, 1].text(i, v + 10000, str(v), ha='center', va='bottom')
    
    plt.suptitle(f'平行轨道线算法测试: {test_file.name}', fontsize=16)
    plt.tight_layout()
    
    # 保存测试结果
    output_path = output_dir / f'parallel_track_test_{test_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果保存到: {output_path}")
    
    # 保存生成的掩码
    visual_mask = np.zeros_like(mask, dtype=np.uint8)
    visual_mask[mask == 1] = 85   # 主轨道
    visual_mask[mask == 2] = 170  # 分叉轨道
    
    mask_path = output_dir / f'parallel_track_mask_{test_file.stem}.png'
    cv2.imwrite(str(mask_path), visual_mask)
    print(f"生成的掩码保存到: {mask_path}")
    
    return mask, annotation_data


def test_multiple_samples():
    """测试多个样本"""
    print("\n=== 测试多个样本 ===")
    
    json_dir = Path('data/railway_annotation_6mm')
    output_dir = Path('outputs/parallel_track_test')
    
    parser = RailwayAnnotationParser()
    json_files = list(json_dir.glob('*.json'))[:5]  # 测试前5个文件
    
    results = []
    
    for json_file in json_files:
        try:
            annotation_data = parser.parse_json_file(json_file)
            
            # 统计轨道配对
            main_left_count = 0
            main_right_count = 0
            fork_left_count = 0
            fork_right_count = 0
            
            for track in annotation_data['tracks']:
                if track['label'] == 'Main_Left':
                    main_left_count = len(track['points'])
                elif track['label'] == 'Main_Right':
                    main_right_count = len(track['points'])
                elif track['label'] == 'Fork_Left':
                    fork_left_count = len(track['points'])
                elif track['label'] == 'Fork_Right':
                    fork_right_count = len(track['points'])
            
            # 生成掩码
            mask = parser.create_segmentation_mask(annotation_data, (1080, 1920))
            
            result = {
                'filename': json_file.name,
                'main_left_points': main_left_count,
                'main_right_points': main_right_count,
                'fork_left_points': fork_left_count,
                'fork_right_points': fork_right_count,
                'has_main_pair': main_left_count > 0 and main_right_count > 0,
                'has_fork_pair': fork_left_count > 0 and fork_right_count > 0,
                'main_track_pixels': np.sum(mask == 1),
                'fork_track_pixels': np.sum(mask == 2),
            }
            
            results.append(result)
            
        except Exception as e:
            print(f"处理 {json_file.name} 时出错: {e}")
    
    # 输出统计结果
    print(f"\n样本统计 ({len(results)} 个文件):")
    print(f"{'文件名':<40} {'主轨道配对':<10} {'分叉配对':<10} {'主轨道像素':<12} {'分叉像素':<10}")
    print("-" * 85)
    
    for result in results:
        print(f"{result['filename']:<40} "
              f"{'✓' if result['has_main_pair'] else '✗':<10} "
              f"{'✓' if result['has_fork_pair'] else '✗':<10} "
              f"{result['main_track_pixels']:<12} "
              f"{result['fork_track_pixels']:<10}")
    
    # 汇总统计
    total_with_main = sum(1 for r in results if r['has_main_pair'])
    total_with_fork = sum(1 for r in results if r['has_fork_pair'])
    
    print(f"\n汇总:")
    print(f"  有主轨道配对的文件: {total_with_main}/{len(results)} ({total_with_main/len(results)*100:.1f}%)")
    print(f"  有分叉轨道配对的文件: {total_with_fork}/{len(results)} ({total_with_fork/len(results)*100:.1f}%)")
    
    return results


if __name__ == '__main__':
    # 测试单个样本
    test_parallel_track_algorithm()
    
    # 测试多个样本
    test_multiple_samples()
    
    print("\n✅ 平行轨道线算法测试完成！") 