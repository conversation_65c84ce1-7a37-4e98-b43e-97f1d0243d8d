#!/usr/bin/env python3
"""
备份现有的掩码文件
"""

import shutil
from pathlib import Path
from datetime import datetime
import argparse
from tqdm import tqdm


def backup_masks(data_dir: Path, mask_subdir: str = 'masks'):
    """备份掩码目录"""
    
    mask_dir = data_dir / mask_subdir
    if not mask_dir.exists():
        print(f"错误：掩码目录不存在: {mask_dir}")
        return False
    
    # 创建备份目录名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = data_dir / f"{mask_subdir}_backup_{timestamp}"
    
    print(f"备份掩码目录: {mask_dir}")
    print(f"备份到: {backup_dir}")
    
    try:
        # 复制整个目录
        shutil.copytree(mask_dir, backup_dir)
        
        # 统计文件数
        mask_files = list(backup_dir.glob('*.png'))
        print(f"成功备份 {len(mask_files)} 个掩码文件")
        
        # 计算总大小
        total_size = sum(f.stat().st_size for f in mask_files)
        print(f"总大小: {total_size / (1024**2):.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"备份失败: {e}")
        return False


def restore_masks(data_dir: Path, backup_dir: str, mask_subdir: str = 'masks'):
    """从备份恢复掩码"""
    
    backup_path = data_dir / backup_dir
    if not backup_path.exists():
        print(f"错误：备份目录不存在: {backup_path}")
        return False
    
    mask_dir = data_dir / mask_subdir
    
    print(f"从备份恢复: {backup_path}")
    print(f"恢复到: {mask_dir}")
    
    response = input("这将覆盖当前的掩码文件！确定要继续吗？(y/n): ")
    if response.lower() != 'y':
        print("已取消")
        return False
    
    try:
        # 删除当前掩码目录
        if mask_dir.exists():
            shutil.rmtree(mask_dir)
        
        # 复制备份目录
        shutil.copytree(backup_path, mask_dir)
        
        # 统计文件数
        mask_files = list(mask_dir.glob('*.png'))
        print(f"成功恢复 {len(mask_files)} 个掩码文件")
        
        return True
        
    except Exception as e:
        print(f"恢复失败: {e}")
        return False


def list_backups(data_dir: Path, mask_subdir: str = 'masks'):
    """列出所有备份"""
    
    backup_pattern = f"{mask_subdir}_backup_*"
    backups = sorted(data_dir.glob(backup_pattern))
    
    if not backups:
        print("没有找到备份")
        return
    
    print(f"找到 {len(backups)} 个备份:")
    for backup in backups:
        # 获取备份信息
        mask_files = list(backup.glob('*.png'))
        total_size = sum(f.stat().st_size for f in mask_files) / (1024**2)
        
        print(f"\n{backup.name}:")
        print(f"  文件数: {len(mask_files)}")
        print(f"  总大小: {total_size:.1f} MB")


def main():
    parser = argparse.ArgumentParser(description='备份和恢复掩码文件')
    parser.add_argument('--data-dir', type=str, default='data/railway_annotation_6mm',
                        help='数据目录路径')
    parser.add_argument('--mask-subdir', type=str, default='masks',
                        help='掩码子目录名称')
    parser.add_argument('--action', type=str, choices=['backup', 'restore', 'list'],
                        default='backup', help='执行的操作')
    parser.add_argument('--backup-dir', type=str, help='恢复时使用的备份目录名')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"错误：数据目录不存在: {data_dir}")
        return
    
    if args.action == 'backup':
        backup_masks(data_dir, args.mask_subdir)
    elif args.action == 'list':
        list_backups(data_dir, args.mask_subdir)
    elif args.action == 'restore':
        if not args.backup_dir:
            print("错误：恢复操作需要指定 --backup-dir")
            list_backups(data_dir, args.mask_subdir)
        else:
            restore_masks(data_dir, args.backup_dir, args.mask_subdir)


if __name__ == '__main__':
    main()