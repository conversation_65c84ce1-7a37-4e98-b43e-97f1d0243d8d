#!/usr/bin/env python
"""
测试所有关键点边界脚本
验证边界严格连接每一个关键点，不使用凸包算法
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_all_points_boundary(json_file: Path, output_dir: Path):
    """测试所有关键点形成边界的功能"""
    print(f"\n=== 测试所有关键点边界生成 ===")
    print(f"JSON文件: {json_file.name}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 收集主轨道点
    main_points = []
    for track in annotation_data['tracks']:
        if track['label'] in ['Main_Left', 'Main_Right']:
            main_points.extend(track['points'])
            print(f"    {track['label']}: {len(track['points'])} 个关键点")
    
    print(f"\n主轨道总关键点: {len(main_points)}")
    
    if len(main_points) < 3:
        print("关键点不足，无法生成多边形")
        return
    
    # 设置图像尺寸
    image_shape = (1080, 1920)
    
    # 将点转换为整数坐标
    points_int = np.array([(int(x), int(y)) for x, y in main_points], dtype=np.int32)
    
    # 方法1：凸包算法（原来的方法）
    hull = cv2.convexHull(points_int)
    convex_mask = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(convex_mask, [hull], 255)
    
    # 方法2：所有点形成多边形（新方法）
    full_mask = np.zeros(image_shape, dtype=np.uint8)
    cv2.fillPoly(full_mask, [points_int], 255)
    
    print(f"\n算法对比:")
    print(f"  凸包顶点数: {len(hull)}")
    print(f"  原始关键点数: {len(points_int)}")
    print(f"  凸包面积: {np.count_nonzero(convex_mask)} 像素")
    print(f"  完整多边形面积: {np.count_nonzero(full_mask)} 像素")
    
    # 计算差异
    diff_mask = np.abs(full_mask.astype(int) - convex_mask.astype(int))
    diff_pixels = np.count_nonzero(diff_mask)
    print(f"  面积差异: {diff_pixels} 像素")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 显示所有关键点
    vis_img = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    for i, point in enumerate(points_int):
        color = (0, 255, 0) if i < len(points_int) // 2 else (255, 0, 0)  # 绿色/红色区分
        cv2.circle(vis_img, tuple(point), 8, color, -1)
        cv2.putText(vis_img, str(i), tuple(point + 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    axes[0, 0].imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title(f'所有关键点 ({len(points_int)}个)\n绿色=前半部分, 红色=后半部分')
    axes[0, 0].axis('off')
    
    # 2. 显示凸包边界
    convex_vis = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    
    # 绘制原始点
    for point in points_int:
        cv2.circle(convex_vis, tuple(point), 6, (0, 255, 0), -1)  # 绿色点
    
    # 绘制凸包顶点（特殊标记）
    for point in hull:
        cv2.circle(convex_vis, tuple(point.flatten()), 10, (255, 255, 0), 3)  # 黄色圈
    
    # 绘制凸包边界
    cv2.polylines(convex_vis, [hull], True, (255, 0, 0), 3)  # 红色边界
    
    axes[0, 1].imshow(cv2.cvtColor(convex_vis, cv2.COLOR_BGR2RGB))
    axes[0, 1].set_title(f'凸包边界\n{len(points_int)}点 → {len(hull)}顶点\n绿点=所有点, 黄圈=凸包顶点')
    axes[0, 1].axis('off')
    
    # 3. 显示完整多边形边界
    full_vis = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    
    # 绘制原始点
    for point in points_int:
        cv2.circle(full_vis, tuple(point), 6, (0, 255, 0), -1)  # 绿色点
    
    # 绘制完整多边形边界（连接所有点）
    cv2.polylines(full_vis, [points_int], True, (0, 0, 255), 3)  # 蓝色边界
    
    axes[0, 2].imshow(cv2.cvtColor(full_vis, cv2.COLOR_BGR2RGB))
    axes[0, 2].set_title(f'完整多边形边界\n使用所有{len(points_int)}个点\n绿点=关键点, 蓝线=边界')
    axes[0, 2].axis('off')
    
    # 4. 显示凸包填充
    axes[1, 0].imshow(convex_mask, cmap='Reds')
    axes[1, 0].set_title(f'凸包填充\n面积: {np.count_nonzero(convex_mask)} 像素')
    axes[1, 0].axis('off')
    
    # 5. 显示完整多边形填充
    axes[1, 1].imshow(full_mask, cmap='Blues')
    axes[1, 1].set_title(f'完整多边形填充\n面积: {np.count_nonzero(full_mask)} 像素')
    axes[1, 1].axis('off')
    
    # 6. 显示差异
    axes[1, 2].imshow(diff_mask, cmap='hot')
    axes[1, 2].set_title(f'面积差异\n差异像素: {diff_pixels}')
    axes[1, 2].axis('off')
    
    plt.suptitle(f'关键点边界对比: 凸包 vs 完整多边形\n{json_file.name}', fontsize=16)
    plt.tight_layout()
    
    # 保存结果
    output_path = output_dir / f'all_points_boundary_{json_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果保存到: {output_path}")
    
    # 验证算法是否真的使用了所有点
    if diff_pixels > 0:
        print("✅ 确认：完整多边形与凸包不同，使用了所有关键点")
        area_change = (np.count_nonzero(full_mask) / max(np.count_nonzero(convex_mask), 1) - 1) * 100
        print(f"   面积变化: {area_change:.1f}%")
    else:
        print("⚠️  警告：完整多边形与凸包相同，可能所有点都在凸包上")
    
    return full_mask, convex_mask, diff_mask


def test_with_preprocessing_class(json_file: Path, output_dir: Path):
    """使用修正后的预处理类测试"""
    print(f"\n=== 使用修正后的预处理类测试 ===")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    # 设置图像尺寸
    image_shape = (1080, 1920)
    
    # 生成分割掩码
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    print(f"修正后的掩码生成:")
    print(f"  掩码形状: {mask.shape}")
    
    # 转换为单通道
    merged_mask = np.argmax(mask, axis=2)
    main_pixels = np.sum(merged_mask == 1)
    
    print(f"  主轨道像素: {main_pixels}")
    
    # 验证是否使用了新算法
    # 收集主轨道点进行对比
    main_points = []
    for track in annotation_data['tracks']:
        if track['label'] in ['Main_Left', 'Main_Right']:
            main_points.extend(track['points'])
    
    if len(main_points) >= 3:
        points_int = np.array([(int(x), int(y)) for x, y in main_points], dtype=np.int32)
        
        # 生成凸包掩码用于对比
        hull = cv2.convexHull(points_int)
        convex_mask = np.zeros(image_shape, dtype=np.uint8)
        cv2.fillPoly(convex_mask, [hull], 1)
        convex_pixels = np.count_nonzero(convex_mask)
        
        print(f"  对比凸包像素: {convex_pixels}")
        
        if main_pixels != convex_pixels:
            print("✅ 确认：预处理类使用了完整多边形算法")
            change = (main_pixels / max(convex_pixels, 1) - 1) * 100
            print(f"   相对凸包面积变化: {change:.1f}%")
        else:
            print("⚠️  预处理类可能仍在使用凸包算法")
    
    return mask


def main():
    parser = argparse.ArgumentParser(description='测试所有关键点边界生成')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='JSON标注目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/all_points_boundary_test',
                       help='输出目录')
    parser.add_argument('--test-single', action='store_true',
                       help='只测试单个文件')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    json_dir = Path(args.json_dir)
    
    print("所有关键点边界测试开始...")
    print(f"JSON目录: {json_dir}")
    print(f"输出目录: {output_dir}")
    
    json_files = list(json_dir.glob('*.json'))
    if len(json_files) == 0:
        print("错误：没有找到JSON文件")
        return
    
    if args.test_single:
        # 测试单个文件
        json_file = json_files[0]
        test_all_points_boundary(json_file, output_dir)
        test_with_preprocessing_class(json_file, output_dir)
    else:
        # 测试前3个文件
        for json_file in json_files[:3]:
            print(f"\n处理文件: {json_file.name}")
            try:
                test_all_points_boundary(json_file, output_dir)
                test_with_preprocessing_class(json_file, output_dir)
            except Exception as e:
                print(f"处理 {json_file.name} 时出错: {e}")
    
    print(f"\n测试完成！结果保存在: {output_dir}")


if __name__ == '__main__':
    main() 