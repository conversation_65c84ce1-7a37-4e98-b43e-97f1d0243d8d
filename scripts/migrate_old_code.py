#!/usr/bin/env python
"""
代码迁移脚本
将原始的src目录下的代码迁移到新的模块化结构
"""

import os
import shutil
from pathlib import Path
import argparse


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='迁移旧代码到新结构')
    parser.add_argument(
        '--backup',
        action='store_true',
        help='是否备份原始文件'
    )
    return parser.parse_args()


def backup_files(src_dir: Path):
    """备份原始文件"""
    backup_dir = src_dir.parent / 'src_backup'
    if backup_dir.exists():
        print(f"备份目录 {backup_dir} 已存在，跳过备份")
        return
    
    print(f"备份原始文件到 {backup_dir}")
    shutil.copytree(src_dir, backup_dir)


def migrate_data_module():
    """迁移数据处理相关代码"""
    print("迁移数据处理模块...")
    
    # 原始 data.py 已经被拆分成多个文件
    old_data_file = Path('src/data.py')
    if old_data_file.exists():
        print(f"  - {old_data_file} -> 已拆分为 dataset.py, dataloader.py, transforms.py")
        # 可以选择删除或重命名原文件
        old_data_file.rename('src/data_old.py')


def migrate_utils_module():
    """迁移工具函数"""
    print("迁移工具模块...")
    
    old_utils_file = Path('src/utils.py')
    new_utils_dir = Path('src/utils')
    
    if old_utils_file.exists() and new_utils_dir.exists():
        # 读取原始utils.py的内容
        with open(old_utils_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 创建新的工具文件
        # 这里需要根据实际内容进行拆分
        print(f"  - {old_utils_file} -> {new_utils_dir}/")
        
        # 示例：将RunningAverage和iou_coef移到metrics.py
        metrics_content = '''"""
评估指标相关工具函数
从原始utils.py迁移
"""

import torch
import numpy as np


class RunningAverage:
    """保持运行平均值"""
    
    def __init__(self):
        self.steps = 0
        self.total = 0
    
    def update(self, val):
        self.total += val
        self.steps += 1
    
    def __call__(self):
        return self.total / float(self.steps)


def iou_coef(y_true, y_pred, smooth=1e-6):
    """计算IoU系数"""
    intersection = (y_true * y_pred).sum(dim=(2, 3))
    union = (y_true + y_pred - y_true * y_pred).sum(dim=(2, 3))
    iou = (intersection + smooth) / (union + smooth)
    return iou.mean(dim=1)
'''
        
        with open(new_utils_dir / 'metrics_utils.py', 'w', encoding='utf-8') as f:
            f.write(metrics_content)
        
        # 重命名原文件
        old_utils_file.rename('src/utils_old.py')


def migrate_train_module():
    """迁移训练相关代码"""
    print("迁移训练模块...")
    
    old_train_file = Path('src/train.py')
    if old_train_file.exists():
        print(f"  - {old_train_file} -> src/training/trainer.py")
        # 原始的train函数需要重构为Trainer类
        old_train_file.rename('src/train_old.py')


def migrate_inference_module():
    """迁移推理相关代码"""
    print("迁移推理模块...")
    
    old_inference_file = Path('src/inference.py')
    if old_inference_file.exists():
        print(f"  - {old_inference_file} -> src/inference/predictor.py")
        old_inference_file.rename('src/inference_old.py')


def migrate_visualization_module():
    """迁移可视化相关代码"""
    print("迁移可视化模块...")
    
    old_vis_file = Path('src/visualizations.py')
    if old_vis_file.exists():
        print(f"  - {old_vis_file} -> src/utils/visualization.py")
        old_vis_file.rename('src/visualizations_old.py')


def create_init_files():
    """创建必要的__init__.py文件"""
    print("创建__init__.py文件...")
    
    dirs_need_init = [
        'src',
        'src/training',
        'src/evaluation', 
        'src/inference',
        'src/utils'
    ]
    
    for dir_path in dirs_need_init:
        init_file = Path(dir_path) / '__init__.py'
        if not init_file.exists():
            init_file.touch()
            print(f"  - 创建 {init_file}")


def create_migration_guide():
    """创建迁移指南"""
    guide_content = '''# 代码迁移指南

## 主要变化

### 1. 目录结构变化
- `src/data.py` -> 拆分为:
  - `src/data/dataset.py`: 数据集类
  - `src/data/dataloader.py`: 数据加载器
  - `src/data/transforms.py`: 数据增强
  
- `src/utils.py` -> 拆分为:
  - `src/utils/metrics_utils.py`: 评估指标工具
  - `src/utils/io_utils.py`: IO工具
  - `src/utils/visualization.py`: 可视化工具
  
- `src/train.py` -> `src/training/trainer.py`: 重构为Trainer类

### 2. 导入路径变化

旧代码:
```python
from src.data import SegmentationDataset, get_loaders
from src.utils import RunningAverage, iou_coef
from src.train import train
```

新代码:
```python
from src.data import RailwaySegmentationDataset, create_dataloaders
from src.utils.metrics_utils import RunningAverage, iou_coef
from src.training import Trainer
```

### 3. 配置系统

新项目使用YAML配置文件，不再硬编码参数：

```python
# 旧代码
batch_size = 32
learning_rate = 0.001

# 新代码
from src.core import load_config
config = load_config('configs/base_config.yaml')
batch_size = config.data.batch_size.train
learning_rate = config.training.optimizer.lr
```

### 4. 模型创建

使用注册器系统：

```python
# 旧代码
import segmentation_models_pytorch as smp
model = smp.PAN(encoder_name='efficientnet-b4', classes=3)

# 新代码
from src.core.registry import MODEL_REGISTRY
model = MODEL_REGISTRY.build({
    'type': 'segmentation_model',
    'architecture': 'pan',
    'encoder': 'efficientnet-b4',
    'classes': 3
})
```

### 5. 训练流程

使用Trainer类管理训练：

```python
# 旧代码
history = train(model, optimizer, criterion, train_loader, val_loader, epochs)

# 新代码
trainer = Trainer(
    model=model,
    config=config,
    train_loader=train_loader,
    val_loader=val_loader,
    loss_fn=loss_fn,
    output_dir='outputs'
)
history = trainer.train()
```

## 迁移步骤

1. 备份原始代码
2. 运行迁移脚本: `python scripts/migrate_old_code.py --backup`
3. 更新导入路径
4. 创建配置文件
5. 更新训练脚本
6. 测试新代码

## 注意事项

- 原始文件已重命名为 `*_old.py`，可在迁移完成后删除
- 部分代码需要手动调整以适应新的架构
- 建议逐步迁移，每次迁移一个模块并测试
'''
    
    with open('MIGRATION_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("\n迁移指南已创建: MIGRATION_GUIDE.md")


def main():
    """主函数"""
    args = parse_args()
    
    print("开始迁移代码到新的模块化结构...")
    
    # 检查src目录
    src_dir = Path('src')
    if not src_dir.exists():
        print("错误: src目录不存在")
        return
    
    # 备份
    if args.backup:
        backup_files(src_dir)
    
    # 执行迁移
    migrate_data_module()
    migrate_utils_module()
    migrate_train_module()
    migrate_inference_module()
    migrate_visualization_module()
    
    # 创建必要文件
    create_init_files()
    
    # 创建迁移指南
    create_migration_guide()
    
    print("\n迁移完成！")
    print("请查看 MIGRATION_GUIDE.md 了解详细的代码变化")
    print("原始文件已重命名为 *_old.py，可在确认迁移成功后删除")


if __name__ == '__main__':
    main() 