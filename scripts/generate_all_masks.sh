#!/bin/bash
# Script to generate all masks from JSON annotations

echo "Starting full mask generation from JSON annotations..."
echo "This will generate PNG masks for all 21,436 JSON files"
echo "Expected time: ~7-10 minutes"

python scripts/generate_multilabel_from_json.py \
    --json-dir /home/<USER>/coderepo/railway-infrastructure-segmentation-refactor/data/railway_annotation_6mm \
    --output-dir /media/cidi/0B33112D0B33112D/data/railway_track_dataset_6mm \
    --image-height 1080 \
    --image-width 1920

echo "Mask generation complete!"
echo ""
echo "Verifying results..."
echo "Train masks: $(ls /home/<USER>/data/railway_track_dataset/train/masks/*.png 2>/dev/null | wc -l)"
echo "Val masks: $(ls /home/<USER>/data/railway_track_dataset/val/masks/*.png 2>/dev/null | wc -l)"
echo "Test masks: $(ls /home/<USER>/data/railway_track_dataset/test/masks/*.png 2>/dev/null | wc -l)"