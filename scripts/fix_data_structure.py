#!/usr/bin/env python
"""
数据结构修复脚本
修复铁路轨道数据集的结构和路径问题
"""

import argparse
import sys
import shutil
import json
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import cv2
import numpy as np
from tqdm import tqdm
from src.data.preprocessing import RailwayAnnotationParser


def clean_corrupted_files(images_dir: Path) -> List[Path]:
    """清理损坏的图像文件"""
    print("清理损坏的图像文件...")
    corrupted_files = []
    valid_files = []
    
    for image_file in images_dir.glob('*.png'):
        if image_file.stat().st_size == 0:
            print(f"删除损坏文件: {image_file.name}")
            image_file.unlink()
            corrupted_files.append(image_file)
        else:
            valid_files.append(image_file)
    
    print(f"删除了 {len(corrupted_files)} 个损坏文件")
    print(f"保留 {len(valid_files)} 个有效文件")
    
    return valid_files


def generate_masks_from_json(json_dir: Path, images_dir: Path, output_masks_dir: Path):
    """从JSON标注生成掩码文件"""
    print("从JSON标注生成掩码文件...")
    
    parser = RailwayAnnotationParser(line_thickness=30)
    output_masks_dir.mkdir(parents=True, exist_ok=True)
    
    json_files = list(json_dir.glob('*.json'))
    generated_masks = []
    
    for json_file in tqdm(json_files, desc="生成掩码"):
        try:
            # 解析JSON文件
            annotation_data = parser.parse_json_file(json_file)
            
            # 构建对应的图像文件名
            image_filename = annotation_data['filename']
            image_path = images_dir / image_filename
            
            # 检查图像文件是否存在
            if not image_path.exists():
                print(f"警告: 图像文件不存在 {image_filename}")
                continue
            
            # 读取图像以获取尺寸
            image = cv2.imread(str(image_path))
            if image is None:
                print(f"警告: 无法读取图像 {image_filename}")
                continue
            
            image_shape = image.shape[:2]  # (height, width)
            
            # 生成掩码
            mask = parser.create_segmentation_mask(annotation_data, image_shape)
            
            # 保存掩码
            mask_filename = image_filename
            mask_path = output_masks_dir / mask_filename
            cv2.imwrite(str(mask_path), mask)
            
            generated_masks.append(mask_path)
            
        except Exception as e:
            print(f"处理 {json_file.name} 时出错: {e}")
            continue
    
    print(f"成功生成 {len(generated_masks)} 个掩码文件")
    return generated_masks


def create_dataset_splits(images_dir: Path, masks_dir: Path, output_dir: Path, 
                         split_ratios: Dict[str, float] = None, random_seed: int = 42):
    """创建数据集划分"""
    print("创建数据集划分...")
    
    if split_ratios is None:
        split_ratios = {'train': 0.7, 'val': 0.15, 'test': 0.15}
    
    # 获取所有有效的图像-掩码对
    valid_pairs = []
    
    for image_file in images_dir.glob('*.png'):
        mask_file = masks_dir / image_file.name
        if mask_file.exists():
            valid_pairs.append((image_file, mask_file))
    
    print(f"找到 {len(valid_pairs)} 对有效的图像-掩码数据")
    
    # 随机打乱数据
    import random
    random.seed(random_seed)
    random.shuffle(valid_pairs)
    
    # 计算分割点
    total = len(valid_pairs)
    train_end = int(total * split_ratios['train'])
    val_end = train_end + int(total * split_ratios['val'])
    
    splits = {
        'train': valid_pairs[:train_end],
        'val': valid_pairs[train_end:val_end],
        'test': valid_pairs[val_end:]
    }
    
    # 创建目录结构并复制文件
    for split_name, split_data in splits.items():
        split_images_dir = output_dir / 'images' / split_name
        split_masks_dir = output_dir / 'masks' / split_name
        
        split_images_dir.mkdir(parents=True, exist_ok=True)
        split_masks_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"处理 {split_name} 集: {len(split_data)} 个样本")
        
        for image_path, mask_path in tqdm(split_data, desc=f"复制{split_name}"):
            # 复制图像
            dst_image = split_images_dir / image_path.name
            shutil.copy2(image_path, dst_image)
            
            # 复制掩码
            dst_mask = split_masks_dir / mask_path.name
            shutil.copy2(mask_path, dst_mask)
    
    # 保存划分信息
    splits_info = {}
    for split_name, split_data in splits.items():
        splits_info[split_name] = [
            [str(img), str(mask)] for img, mask in split_data
        ]
    
    splits_file = output_dir / 'splits.json'
    with open(splits_file, 'w', encoding='utf-8') as f:
        json.dump(splits_info, f, indent=2, ensure_ascii=False)
    
    print(f"数据划分信息已保存到 {splits_file}")
    
    return splits


def update_config_paths(config_file: Path, data_root: str):
    """更新配置文件中的数据路径"""
    print(f"更新配置文件 {config_file}")
    
    import yaml
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径
    if 'data' in config:
        config['data']['processed_data_path'] = data_root
        config['data']['json_dir'] = "data/railway_annotation_6mm"
    
    # 保存更新后的配置
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.safe_dump(config, f, indent=2)
    
    print("配置文件已更新")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='修复铁路轨道数据集结构')
    
    parser.add_argument(
        '--json-dir',
        type=str,
        default='data/railway_annotation_6mm',
        help='JSON标注文件目录'
    )
    parser.add_argument(
        '--images-dir',
        type=str,
        default='data/processed/images',
        help='图像文件目录'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default='/home/<USER>/data/Download/railway_track_dataset',
        help='输出目录'
    )
    parser.add_argument(
        '--config-file',
        type=str,
        default='configs/railway_track_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--clean-only',
        action='store_true',
        help='只清理损坏文件，不进行其他处理'
    )
    
    args = parser.parse_args()
    
    # 路径转换
    json_dir = Path(args.json_dir)
    images_dir = Path(args.images_dir)
    output_dir = Path(args.output_dir)
    config_file = Path(args.config_file)
    
    print(f"JSON标注目录: {json_dir}")
    print(f"图像目录: {images_dir}")
    print(f"输出目录: {output_dir}")
    
    # 检查输入目录
    if not json_dir.exists():
        print(f"错误: JSON目录不存在: {json_dir}")
        return
    
    if not images_dir.exists():
        print(f"错误: 图像目录不存在: {images_dir}")
        return
    
    # # 步骤1: 清理损坏的图像文件
    # valid_images = clean_corrupted_files(images_dir)
    
    # if args.clean_only:
    #     print("只清理模式，完成！")
    #     return
    
    # # 步骤2: 生成掩码文件
    # temp_masks_dir = output_dir / 'temp_masks'
    # generate_masks_from_json(json_dir, images_dir, temp_masks_dir)
    
    # # 步骤3: 创建数据集划分
    # create_dataset_splits(images_dir, temp_masks_dir, output_dir)
    
    # # 步骤4: 清理临时文件
    # print("清理临时文件...")
    # if temp_masks_dir.exists():
    #     shutil.rmtree(temp_masks_dir)
    
    # 步骤5: 更新配置文件
    if config_file.exists():
        update_config_paths(config_file, str(output_dir))
    
    print("\n数据结构修复完成！")
    print(f"数据已保存到: {output_dir}")
    print("目录结构:")
    print("├── images/")
    print("│   ├── train/")
    print("│   ├── val/")
    print("│   └── test/")
    print("├── masks/")
    print("│   ├── train/")
    print("│   ├── val/")
    print("│   └── test/")
    print("└── splits.json")


if __name__ == '__main__':
    main() 