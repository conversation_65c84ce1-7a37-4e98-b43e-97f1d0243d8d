#!/usr/bin/env python
"""
训练性能测试脚本
用于测试训练过程的GPU利用率和性能
"""

import sys
import time
import argparse
import torch
import json
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import load_config
from src.models import create_segmentation_model
from src.utils import setup_logger
from src.core.registry import LOSS_REGISTRY
from src.data import create_dataloaders
from src.training.trainer import Trainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练性能测试')
    
    parser.add_argument(
        '--config',
        type=str,
        default='configs/railway_track_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--epochs',
        type=int,
        default=2,
        help='训练的轮数'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default='outputs/benchmark',
        help='输出目录'
    )
    parser.add_argument(
        '--prefetch',
        action='store_true',
        default=True,
        help='是否使用数据预取'
    )
    parser.add_argument(
        '--gpu-optimization',
        action='store_true',
        default=True,
        help='是否使用GPU优化'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志记录器
    logger = setup_logger(
        'benchmark',
        output_dir / 'benchmark.log'
    )
    
    logger.info("=" * 60)
    logger.info("🚀 训练性能测试")
    logger.info("=" * 60)
    
    # 加载配置
    config = load_config(args.config)
    
    # 修改配置以适应测试
    config_dict = config.to_dict()
    
    # 设置训练轮数
    config_dict['training']['epochs'] = args.epochs
    
    # 调整batch size，避免内存溢出
    config_dict['data']['batch_size']['train'] = 4
    config_dict['data']['batch_size']['val'] = 2
    
    # 设置GPU优化选项
    if 'performance_optimization' in config_dict['training']:
        config_dict['training']['performance_optimization']['use_data_prefetcher'] = args.prefetch
    else:
        config_dict['training']['performance_optimization'] = {
            'use_data_prefetcher': args.prefetch,
            'cudnn_benchmark': args.gpu_optimization,
            'use_channels_last': args.gpu_optimization
        }
    
    # 保存测试配置
    with open(output_dir / 'test_config.json', 'w') as f:
        json.dump(config_dict, f, indent=2)
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    dataloaders = create_dataloaders(config_dict)
    train_loader = dataloaders['train']
    val_loader = dataloaders['val']
    
    # 显示数据集信息
    logger.info(f"训练集大小: {len(train_loader.dataset)}")
    logger.info(f"验证集大小: {len(val_loader.dataset)}")
    logger.info(f"批次大小: {config_dict['data']['batch_size']['train']}")
    logger.info(f"工作线程数: {config_dict['project']['num_workers']}")
    logger.info(f"使用数据预取: {args.prefetch}")
    logger.info(f"使用GPU优化: {args.gpu_optimization}")
    
    # 创建模型
    logger.info("创建模型...")
    model_config = config_dict['model']
    model = create_segmentation_model(model_config)
    
    # 打印模型信息
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 获取模型参数量
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"模型架构: {model_config['architecture']}")
    logger.info(f"编码器: {model_config['encoder']}")
    logger.info(f"参数数量: {count_parameters(model):,}")
    logger.info(f"设备: {device}")
    
    # 创建损失函数
    logger.info("创建损失函数...")
    loss_config = config_dict['loss']
    
    if loss_config['type'] == 'dice_bce':
        # 处理简单的dice_bce损失
        loss_fn = LOSS_REGISTRY.get('bce_dice_loss')(
            bce_weight=loss_config.get('bce_weight', 0.5),
            dice_weight=loss_config.get('dice_weight', 0.5)
        )
    elif loss_config['type'] == 'combined_loss':
        # 处理复合损失函数
        # 只传递CombinedLoss需要的参数: losses 和 weights
        combined_config = {
            'losses': loss_config['losses'],
            'weights': loss_config.get('weights', None)
        }
        loss_fn = LOSS_REGISTRY.get('combined_loss')(**combined_config)
    else:
        # 处理其他损失函数
        type_name = loss_config.pop('type')
        loss_fn = LOSS_REGISTRY.get(type_name)(**loss_config)
    
    # 创建训练器
    logger.info("创建训练器...")
    trainer = Trainer(
        model=model,
        config=config_dict,
        train_loader=train_loader,
        val_loader=val_loader,
        loss_fn=loss_fn,
        output_dir=output_dir,
        logger=logger
    )
    
    # 开始测试性能
    logger.info("开始性能测试...")
    start_time = time.time()
    
    # 记录GPU初始状态
    if torch.cuda.is_available():
        initial_gpu_memory = torch.cuda.memory_allocated()
        logger.info(f"初始GPU内存使用: {initial_gpu_memory / 1024**2:.2f}MB")
    
    # 运行训练
    history = trainer.train()
    
    # 记录GPU最终状态
    if torch.cuda.is_available():
        final_gpu_memory = torch.cuda.memory_allocated()
        logger.info(f"最终GPU内存使用: {final_gpu_memory / 1024**2:.2f}MB")
        logger.info(f"GPU内存增长: {(final_gpu_memory - initial_gpu_memory) / 1024**2:.2f}MB")
    
    # 计算运行时间
    end_time = time.time()
    total_time = end_time - start_time
    
    # 保存历史记录
    with open(output_dir / 'history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    # 输出结果
    logger.info("=" * 60)
    logger.info("📊 性能测试结果:")
    logger.info(f"总训练时间: {total_time:.2f}秒")
    logger.info(f"每轮平均时间: {total_time / args.epochs:.2f}秒")
    if history:
        # 计算平均每批次时间
        batch_times = [h.get('time', 0) for h in history]
        avg_batch_time = sum(batch_times) / len(batch_times) if batch_times else 0
        logger.info(f"平均每批次处理时间: {avg_batch_time:.4f}秒")
        # 显示内存使用
        if hasattr(trainer.memory_monitor, 'get_memory_summary'):
            memory_summary = trainer.memory_monitor.get_memory_summary()
            logger.info(f"平均GPU内存使用: {memory_summary['current_gpu_memory_mb']:.2f}MB")
            logger.info(f"峰值GPU内存使用: {memory_summary['peak_gpu_memory_mb']:.2f}MB")
    
    logger.info("=" * 60)
    logger.info("性能测试完成")
    
    return 0


if __name__ == '__main__':
    sys.exit(main()) 