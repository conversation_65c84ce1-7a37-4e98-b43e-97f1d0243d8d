#!/usr/bin/env python3
"""
测试基于离底部远近的方向检测和排序逻辑
验证轨道点的拼接是否正确
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_bottom_distance_ordering():
    """测试基于离底部远近的排序逻辑"""
    print("=== 基于离底部远近的方向检测测试 ===")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 测试用例1：正常顺序的轨道点
    print("\n1. 正常顺序的轨道点测试")
    left_points = [(100, 100), (200, 150), (300, 200), (400, 250)]  # 从左到右，从上到下
    right_points = [(150, 100), (250, 150), (350, 200), (450, 250)]  # 从左到右，从上到下
    
    print(f"原始左边线: {left_points}")
    print(f"原始右边线: {right_points}")
    
    # 测试排序逻辑
    adjusted_left = parser._adjust_points_by_bottom_distance(left_points)
    adjusted_right = parser._adjust_points_by_bottom_distance(right_points)
    
    print(f"调整后左边线: {adjusted_left}")
    print(f"调整后右边线: {adjusted_right}")
    
    # 测试用例2：反向顺序的轨道点
    print("\n2. 反向顺序的轨道点测试")
    left_points_reverse = [(400, 250), (300, 200), (200, 150), (100, 100)]  # 反向
    right_points_reverse = [(450, 250), (350, 200), (250, 150), (150, 100)]  # 反向
    
    print(f"原始左边线(反向): {left_points_reverse}")
    print(f"原始右边线(反向): {right_points_reverse}")
    
    adjusted_left_reverse = parser._adjust_points_by_bottom_distance(left_points_reverse)
    adjusted_right_reverse = parser._adjust_points_by_bottom_distance(right_points_reverse)
    
    print(f"调整后左边线(反向): {adjusted_left_reverse}")
    print(f"调整后右边线(反向): {adjusted_right_reverse}")
    
    # 测试用例3：混合顺序的轨道点
    print("\n3. 混合顺序的轨道点测试")
    left_points_mixed = [(200, 150), (100, 100), (400, 250), (300, 200)]  # 混合顺序
    right_points_mixed = [(250, 150), (150, 100), (450, 250), (350, 200)]  # 混合顺序
    
    print(f"原始左边线(混合): {left_points_mixed}")
    print(f"原始右边线(混合): {right_points_mixed}")
    
    adjusted_left_mixed = parser._adjust_points_by_bottom_distance(left_points_mixed)
    adjusted_right_mixed = parser._adjust_points_by_bottom_distance(right_points_mixed)
    
    print(f"调整后左边线(混合): {adjusted_left_mixed}")
    print(f"调整后右边线(混合): {adjusted_right_mixed}")
    
    return (left_points, right_points, adjusted_left, adjusted_right,
            left_points_reverse, right_points_reverse, adjusted_left_reverse, adjusted_right_reverse,
            left_points_mixed, right_points_mixed, adjusted_left_mixed, adjusted_right_mixed)


def test_polygon_generation():
    """测试多边形生成"""
    print("\n=== 多边形生成测试 ===")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 测试用例：不同顺序的轨道点
    test_cases = [
        ("正常顺序", 
         [(100, 100), (200, 150), (300, 200), (400, 250)],
         [(150, 100), (250, 150), (350, 200), (450, 250)]),
        
        ("反向顺序",
         [(400, 250), (300, 200), (200, 150), (100, 100)],
         [(450, 250), (350, 200), (250, 150), (150, 100)]),
        
        ("混合顺序",
         [(200, 150), (100, 100), (400, 250), (300, 200)],
         [(250, 150), (150, 100), (450, 250), (350, 200)])
    ]
    
    for case_name, left_points, right_points in test_cases:
        print(f"\n{case_name}:")
        print(f"  左边线: {left_points}")
        print(f"  右边线: {right_points}")
        
        # 生成多边形
        polygon = parser.create_track_polygon_from_parallel_lines(left_points, right_points)
        print(f"  生成的多边形点数: {len(polygon)}")
        print(f"  多边形前5个点: {polygon[:5]}")
        print(f"  多边形后5个点: {polygon[-5:]}")
        
        # 验证多边形是否闭合
        if len(polygon) > 0:
            first_point = polygon[0]
            last_point = polygon[-1]
            is_closed = (first_point == last_point)
            print(f"  多边形是否闭合: {is_closed}")
        
        # 生成掩码测试
        image_shape = (300, 500)
        mask = parser.points_to_mask(polygon, image_shape, 1)
        pixel_count = np.sum(mask > 0)
        print(f"  掩码像素数: {pixel_count}")


def visualize_bottom_distance_ordering():
    """可视化基于底部距离的排序效果"""
    print("\n=== 可视化排序效果 ===")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 测试数据
    left_points = [(100, 100), (200, 150), (300, 200), (400, 250)]
    right_points = [(150, 100), (250, 150), (350, 200), (450, 250)]
    
    # 调整顺序
    adjusted_left = parser._adjust_points_by_bottom_distance(left_points)
    adjusted_right = parser._adjust_points_by_bottom_distance(right_points)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 原始轨道线
    axes[0, 0].set_title('原始轨道线')
    axes[0, 0].set_xlim(0, 500)
    axes[0, 0].set_ylim(300, 0)  # 翻转Y轴，使底部在下方
    
    # 绘制左边线
    left_x, left_y = zip(*left_points)
    axes[0, 0].plot(left_x, left_y, 'g-o', label='Left Track', markersize=8, linewidth=2)
    
    # 绘制右边线
    right_x, right_y = zip(*right_points)
    axes[0, 0].plot(right_x, right_y, 'b-o', label='Right Track', markersize=8, linewidth=2)
    
    # 标注点的顺序
    for i, (x, y) in enumerate(left_points):
        axes[0, 0].annotate(f'L{i}', (x-10, y-10), fontsize=10, color='green')
    
    for i, (x, y) in enumerate(right_points):
        axes[0, 0].annotate(f'R{i}', (x+10, y-10), fontsize=10, color='blue')
    
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 2. 调整后的轨道线
    axes[0, 1].set_title('调整后的轨道线（按离底部远近排序）')
    axes[0, 1].set_xlim(0, 500)
    axes[0, 1].set_ylim(300, 0)
    
    # 绘制调整后的左边线
    adj_left_x, adj_left_y = zip(*adjusted_left)
    axes[0, 1].plot(adj_left_x, adj_left_y, 'g-o', label='Adjusted Left Track', markersize=8, linewidth=2)
    
    # 绘制调整后的右边线
    adj_right_x, adj_right_y = zip(*adjusted_right)
    axes[0, 1].plot(adj_right_x, adj_right_y, 'b-o', label='Adjusted Right Track', markersize=8, linewidth=2)
    
    # 标注调整后的顺序
    for i, (x, y) in enumerate(adjusted_left):
        axes[0, 1].annotate(f'L{i}', (x-10, y-10), fontsize=10, color='green')
    
    for i, (x, y) in enumerate(adjusted_right):
        axes[0, 1].annotate(f'R{i}', (x+10, y-10), fontsize=10, color='blue')
    
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 3. 生成的多边形
    axes[1, 0].set_title('生成的多边形')
    axes[1, 0].set_xlim(0, 500)
    axes[1, 0].set_ylim(300, 0)
    
    polygon = parser.create_track_polygon_from_parallel_lines(left_points, right_points)
    if polygon:
        polygon_x, polygon_y = zip(*polygon)
        axes[1, 0].plot(polygon_x, polygon_y, 'r-', linewidth=3, label='Track Polygon')
        axes[1, 0].scatter(polygon_x, polygon_y, c='red', s=50, zorder=5)
        
        # 标注多边形的关键点
        for i, (x, y) in enumerate(polygon):
            if i < len(polygon) - 1:  # 不标注最后一个点（与第一个点相同）
                axes[1, 0].annotate(str(i), (x+5, y+5), fontsize=8, color='red')
    
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # 4. 生成的掩码
    axes[1, 1].set_title('生成的掩码')
    axes[1, 1].set_xlim(0, 500)
    axes[1, 1].set_ylim(300, 0)
    
    # 生成掩码
    image_shape = (300, 500)
    mask = parser.points_to_mask(polygon, image_shape, 1)
    
    # 显示掩码
    axes[1, 1].imshow(mask, cmap='viridis', extent=[0, 500, 300, 0])
    axes[1, 1].set_title(f'Mask ({np.sum(mask > 0)} pixels)')
    
    plt.tight_layout()
    plt.savefig('outputs/bottom_distance_ordering_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"可视化结果已保存到: outputs/bottom_distance_ordering_test.png")


def test_real_data():
    """测试真实数据"""
    print("\n=== 真实数据测试 ===")
    
    # 查找测试JSON文件
    json_dir = Path('data/railway_annotation_6mm')
    if not json_dir.exists():
        print(f"❌ 数据目录不存在: {json_dir}")
        return
    
    json_files = list(json_dir.glob('*.json'))
    if not json_files:
        print(f"❌ 没有找到JSON文件在: {json_dir}")
        return
    
    # 选择第一个文件进行测试
    test_file = json_files[0]
    print(f"测试文件: {test_file.name}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    try:
        # 解析JSON文件
        annotation_data = parser.parse_json_file(test_file)
        
        # 提取轨道数据
        main_left = []
        main_right = []
        
        for track in annotation_data['tracks']:
            if track['label'] == 'Main_Left':
                main_left = track['points']
            elif track['label'] == 'Main_Right':
                main_right = track['points']
        
        if not main_left or not main_right:
            print("❌ 没有找到主轨道配对")
            return
        
        print(f"轨道数据:")
        print(f"  Main_Left: {len(main_left)} 个关键点")
        print(f"  Main_Right: {len(main_right)} 个关键点")
        
        # 测试排序逻辑
        print(f"\n排序前:")
        print(f"  左边线前3个点: {main_left[:3]}")
        print(f"  右边线前3个点: {main_right[:3]}")
        
        adjusted_left = parser._adjust_points_by_bottom_distance(main_left)
        adjusted_right = parser._adjust_points_by_bottom_distance(main_right)
        
        print(f"\n排序后:")
        print(f"  左边线前3个点: {adjusted_left[:3]}")
        print(f"  右边线前3个点: {adjusted_right[:3]}")
        
        # 生成多边形
        track_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
        print(f"\n生成的多边形顶点数: {len(track_polygon)}")
        
        # 生成掩码
        image_shape = (1080, 1920)
        track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
        main_track_pixels = np.sum(track_mask > 0)
        print(f"主轨道掩码像素数: {main_track_pixels}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 创建输出目录
    output_dir = Path('outputs')
    output_dir.mkdir(exist_ok=True)
    
    # 运行测试
    test_bottom_distance_ordering()
    test_polygon_generation()
    visualize_bottom_distance_ordering()
    test_real_data()
    
    print("\n✅ 基于底部距离的方向检测测试完成！") 