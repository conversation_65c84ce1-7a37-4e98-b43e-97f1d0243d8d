#!/usr/bin/env python3
"""
测试整个代码库对.npy格式的支持
"""

import sys
import numpy as np
from pathlib import Path
import torch
from torch.utils.data import DataLoader

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset
from src.utils.visualization import load_mask, save_mask_visualization
from src.data.preprocessing import RailwayAnnotationParser
import yaml


def test_data_loading():
    """测试数据加载模块"""
    print("=== 测试数据加载 ===")
    
    # 加载配置
    with open('configs/railway_track_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 创建数据集
    dataset = RailwayTrackDataset(
        data_root=config['data']['processed_data_path'],
        split='train',
        config=config
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试加载第一个样本
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"图像形状: {sample['image'].shape}")
        print(f"掩码形状: {sample['mask'].shape}")
        
        # 检查掩码格式
        mask = sample['mask']
        if len(mask.shape) == 3:
            print("✓ 多标签掩码格式正确")
            if isinstance(mask, np.ndarray):
                print(f"  背景通道: {np.sum(mask[:, :, 0] > 0)} 像素")
                print(f"  主轨道通道: {np.sum(mask[:, :, 1] > 0)} 像素")
                print(f"  分叉轨道通道: {np.sum(mask[:, :, 2] > 0)} 像素")
            else:
                print(f"  背景通道: {torch.sum(mask[0] > 0).item()} 像素")
                print(f"  主轨道通道: {torch.sum(mask[1] > 0).item()} 像素")
                print(f"  分叉轨道通道: {torch.sum(mask[2] > 0).item()} 像素")
        else:
            print("✗ 掩码格式不正确")
    
    # 测试DataLoader
    dataloader = DataLoader(dataset, batch_size=2, shuffle=False)
    batch = next(iter(dataloader))
    print(f"\n批次图像形状: {batch['image'].shape}")
    print(f"批次掩码形状: {batch['mask'].shape}")
    
    return True


def test_preprocessing():
    """测试预处理模块"""
    print("\n=== 测试预处理模块 ===")
    
    parser = RailwayAnnotationParser()
    
    # 测试save_mask方法
    test_mask = np.zeros((100, 100, 3), dtype=np.float32)
    test_mask[:50, :50, 1] = 1.0  # 主轨道
    test_mask[50:, 50:, 2] = 1.0  # 分叉轨道
    test_mask[:, :, 0] = 1.0 - np.maximum(test_mask[:, :, 1], test_mask[:, :, 2])  # 背景
    
    # 保存为npy格式
    test_path = Path('test_mask')
    success = parser.save_mask(test_mask, test_path, format='npy', use_multilabel=True)
    if success and test_path.with_suffix('.npy').exists():
        print("✓ 保存.npy格式成功")
        
        # 加载并验证
        loaded_mask = np.load(str(test_path.with_suffix('.npy')))
        if np.allclose(loaded_mask, test_mask):
            print("✓ 加载.npy格式成功，数据一致")
        else:
            print("✗ 加载的数据与原始数据不一致")
        
        # 清理测试文件
        test_path.with_suffix('.npy').unlink()
    else:
        print("✗ 保存.npy格式失败")
    
    return True


def test_visualization():
    """测试可视化模块"""
    print("\n=== 测试可视化模块 ===")
    
    # 创建测试掩码
    test_mask_npy = np.zeros((100, 100, 3), dtype=np.float32)
    test_mask_npy[:50, :50, 1] = 1.0  # 主轨道
    test_mask_npy[50:, 50:, 2] = 1.0  # 分叉轨道
    
    # 保存为.npy文件
    test_path = Path('test_vis_mask.npy')
    np.save(str(test_path), test_mask_npy)
    
    # 测试加载
    try:
        loaded_mask = load_mask(test_path)
        print("✓ 加载.npy掩码成功")
        print(f"  加载掩码形状: {loaded_mask.shape}")
    except Exception as e:
        print(f"✗ 加载.npy掩码失败: {e}")
    
    # 测试保存可视化
    try:
        save_mask_visualization(test_mask_npy, Path('test_vis'), format='npy')
        if Path('test_vis.npy').exists():
            print("✓ 保存可视化为.npy格式成功")
            Path('test_vis.npy').unlink()
        else:
            print("✗ 保存可视化为.npy格式失败")
    except Exception as e:
        print(f"✗ 保存可视化失败: {e}")
    
    # 清理测试文件
    if test_path.exists():
        test_path.unlink()
    
    return True


def test_config():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    with open('configs/railway_track_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 检查新增配置项
    if 'mask_format' in config['data']:
        print(f"✓ 找到mask_format配置: {config['data']['mask_format']}")
    else:
        print("✗ 未找到mask_format配置")
    
    if config['data'].get('use_multilabel', False):
        print("✓ 多标签模式已启用")
    else:
        print("✗ 多标签模式未启用")
    
    return True


def main():
    """运行所有测试"""
    print("开始测试.npy格式支持...\n")
    
    tests = [
        ("配置文件", test_config),
        ("预处理模块", test_preprocessing),
        ("可视化模块", test_visualization),
        ("数据加载", test_data_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\n{test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n=== 测试总结 ===")
    all_passed = True
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n所有测试通过！代码库已完全支持.npy格式。")
    else:
        print("\n部分测试失败，请检查相关模块。")


if __name__ == '__main__':
    main()