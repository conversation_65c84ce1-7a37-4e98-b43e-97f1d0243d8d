#\!/usr/bin/env python3
"""Test overlap regions in files with fork tracks"""

import sys
import json
import numpy as np
from pathlib import Path
import random

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser

def test_fork_overlap():
    """Test multi-label generation on files with fork tracks"""
    
    data_dir = Path("data/railway_annotation_6mm")
    parser = RailwayAnnotationParser()
    
    # Find files with fork tracks
    fork_files = []
    for json_file in data_dir.glob("*.json"):
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Check if this file has both main and fork tracks
        labels = [label['label'] for label in data.get('labels', [])]
        has_main = any('Main' in label for label in labels)
        has_fork = any('Fork' in label for label in labels)
        
        if has_main and has_fork:
            fork_files.append(json_file)
    
    print(f"Found {len(fork_files)} files with both main and fork tracks")
    
    # Test a few files
    test_files = random.sample(fork_files, min(5, len(fork_files)))
    
    total_overlap = 0
    for json_file in test_files:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Get corresponding image to determine shape
        img_path = json_file.with_suffix('.png')
        if img_path.exists():
            from PIL import Image
            img = Image.open(img_path)
            image_shape = (img.height, img.width)
        else:
            image_shape = (1080, 1920)  # Default shape
        
        # Generate multi-label mask
        annotation_data = parser.parse_annotation(data)
        mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
        
        # Check overlap
        overlap = np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0)
        overlap_pixels = np.sum(overlap)
        overlap_percent = overlap_pixels / (image_shape[0] * image_shape[1]) * 100
        
        print(f"\n{json_file.name}:")
        print(f"  Main track pixels: {np.sum(mask[:, :, 1] > 0)}")
        print(f"  Fork track pixels: {np.sum(mask[:, :, 2] > 0)}")
        print(f"  Overlap pixels: {overlap_pixels} ({overlap_percent:.2f}%)")
        
        total_overlap += overlap_pixels
    
    print(f"\nTotal overlap pixels across {len(test_files)} files: {total_overlap}")
    
    return total_overlap > 0

if __name__ == '__main__':
    has_overlap = test_fork_overlap()
    if has_overlap:
        print("\n✓ Multi-label masks with overlap regions successfully generated")
    else:
        print("\n⚠ No overlap regions found - check mask generation logic")
