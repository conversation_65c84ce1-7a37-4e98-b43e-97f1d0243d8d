#!/usr/bin/env python
"""
模型评估脚本
用于评估训练好的铁路分割模型
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from src.core import load_config
from src.core.registry import MODEL_REGISTRY
from src.data import create_dataloaders
from src.evaluation import Evaluator
from src.utils import setup_logger


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='评估铁路分割模型')
    
    parser.add_argument(
        '--config',
        type=str,
        required=True,
        help='配置文件路径'
    )
    parser.add_argument(
        '--checkpoint',
        type=str,
        required=True,
        help='模型检查点路径'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default='outputs/evaluation',
        help='评估结果输出目录'
    )
    parser.add_argument(
        '--gpu',
        type=str,
        default='0',
        help='使用的GPU编号'
    )
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.5,
        help='二值化阈值'
    )
    parser.add_argument(
        '--save-predictions',
        action='store_true',
        help='是否保存预测结果'
    )
    parser.add_argument(
        '--benchmark',
        action='store_true',
        help='是否进行推理速度基准测试'
    )
    parser.add_argument(
        '--camera-type',
        type=str,
        choices=['6mm', '25mm', 'all'],
        default='all',
        help='评估的相机类型'
    )
    parser.add_argument(
        '--dataset-type',
        type=str,
        default=None,
        help='数据集类型'
    )
    
    return parser.parse_args()


def load_model(config, checkpoint_path: str, device: str):
    """
    加载训练好的模型
    
    Args:
        config: 配置对象
        checkpoint_path: 检查点路径
        device: 设备
        
    Returns:
        加载的模型
    """
    # 创建模型
    model_config = config.model.to_dict()
    model = MODEL_REGISTRY.build(model_config)
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 根据检查点类型加载权重
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        # 假设整个检查点就是模型权重
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    return model


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logger = setup_logger(
        'evaluate',
        output_dir / 'evaluation.log',
        console_level='INFO'
    )
    
    logger.info(f"配置文件: {args.config}")
    logger.info(f"检查点: {args.checkpoint}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"设备: {device}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖配置参数
    if args.dataset_type is not None:
        config.set('data.dataset_type', args.dataset_type)
    
    # 处理相机类型
    camera_type = None
    if args.camera_type != 'all':
        camera_type = args.camera_type
    
    # 创建数据加载器（仅测试集）
    logger.info("创建测试数据加载器...")
    try:
        dataloaders = create_dataloaders(
            config.to_dict(),
            camera_type=camera_type
        )
    except ValueError as e:
        logger.error(f"创建数据加载器失败: {e}")
        logger.error("可能的原因:")
        logger.error("1. 数据路径配置不正确")
        logger.error("2. 指定的数据集分割不存在")
        logger.error("3. 数据文件损坏或不匹配")
        logger.error("请检查配置文件中的数据路径设置")
        return
    
    test_loader = dataloaders['test']
    logger.info(f"测试集大小: {len(test_loader.dataset)}")
    
    # 检查是否为虚拟数据集
    if hasattr(test_loader.dataset, '__class__') and 'DummyDataset' in str(test_loader.dataset.__class__):
        logger.warning("测试集为空，使用虚拟数据集。评估结果可能不准确。")
        logger.warning("请检查数据路径和配置，确保测试数据存在。")
    
    # 如果测试集为空且不是虚拟数据集，退出
    if len(test_loader.dataset) == 0:
        logger.error("测试集为空，无法进行评估")
        return
    
    # 加载模型
    logger.info("加载模型...")
    model = load_model(config, args.checkpoint, device)
    
    # 显示模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 创建评估器
    evaluator = Evaluator(
        model=model,
        test_loader=test_loader,
        device=device,
        logger=logger
    )
    
    # 开始评估
    logger.info("开始模型评估...")
    
    try:
        # 主要评估
        metrics = evaluator.evaluate(
            save_predictions=args.save_predictions,
            save_dir=output_dir if args.save_predictions else None,
            threshold=args.threshold
        )
        
        # 打印主要指标
        logger.info("评估结果:")
        logger.info(f"  总体IoU: {metrics['iou']:.4f}")
        logger.info(f"  总体Dice: {metrics['dice']:.4f}")
        logger.info(f"  精确率: {metrics['precision']:.4f}")
        logger.info(f"  召回率: {metrics['recall']:.4f}")
        logger.info(f"  F1分数: {metrics['f1']:.4f}")
        logger.info(f"  推理速度: {metrics['fps']:.2f} FPS")
        
        # 类别级别的指标
        if 'per_class' in metrics:
            logger.info("类别级别指标:")
            for class_name, class_metrics in metrics['per_class'].items():
                logger.info(f"  {class_name}:")
                logger.info(f"    IoU: {class_metrics['iou']:.4f}")
                logger.info(f"    Dice: {class_metrics['dice']:.4f}")
                logger.info(f"    F1: {class_metrics['f1']:.4f}")
        
        # 推理速度基准测试
        if args.benchmark:
            logger.info("进行推理速度基准测试...")
            benchmark_results = evaluator.benchmark_inference_speed()
            
            logger.info("基准测试结果:")
            for batch_config, results in benchmark_results.items():
                logger.info(f"  {batch_config}:")
                logger.info(f"    FPS: {results['fps']:.2f}")
                logger.info(f"    每张图片用时: {results['time_per_image']*1000:.2f}ms")
        
        logger.info("评估完成！")
        
    except Exception as e:
        logger.error(f"评估过程中出现错误: {str(e)}", exc_info=True)
        raise


if __name__ == '__main__':
    main() 