#!/usr/bin/env python3
"""
单独模型可视化脚本
为每个训练好的模型生成可视化预测结果
"""

import sys
import os
import torch
import torch.nn as nn
import numpy as np
import cv2
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse
from tqdm import tqdm
import segmentation_models_pytorch as smp
import albumentations as A
from albumentations.pytorch import ToTensorV2
import matplotlib.pyplot as plt
from PIL import Image
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入自定义模块
from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.data.augmentations import get_val_transform
from src.utils.metrics import iou_coef

# 设置matplotlib样式
plt.style.use('ggplot')

def load_model_with_fallback(weights_path: str, model_config: Dict[str, Any], device: str) -> nn.Module:
    """
    加载模型，支持多种格式的回退机制
    
    Args:
        weights_path: 权重文件路径
        model_config: 模型配置
        device: 设备
        
    Returns:
        加载的模型
    """
    print(f"正在加载模型: {model_config['name']} from {weights_path}")
    
    # 方法1: 尝试直接加载完整模型对象
    try:
        model = torch.load(weights_path, map_location=device, weights_only=False)
        if hasattr(model, 'eval'):
            model.eval()
            model.to(device)
            print("✅ 完整模型加载成功")
            return model
    except Exception as e:
        print(f"完整模型加载失败: {e}")
    
    # 方法2: 尝试作为状态字典加载
    try:
        checkpoint = torch.load(weights_path, map_location=device, weights_only=False)
        
        # 创建模型架构
        model = smp.PAN(
            encoder_name=model_config['encoder'],
            encoder_weights=None,  # 不使用预训练权重
            in_channels=3,
            classes=3
        )
        
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ 状态字典加载成功 (model_state_dict)")
        elif isinstance(checkpoint, dict):
            model.load_state_dict(checkpoint)
            print("✅ 状态字典加载成功 (直接)")
        else:
            raise ValueError("无法识别的模型格式")
        
        model.to(device)
        model.eval()
        return model
        
    except Exception as e:
        print(f"状态字典加载也失败: {e}")
        
        # 方法3: 尝试使用自定义反序列化器（处理模块路径问题）
        try:
            print("🔧 尝试使用自定义反序列化器...")
            
            # 定义自定义反序列化器
            class CustomUnpickler(torch.serialization.pickle.Unpickler):
                def find_class(self, module, name):
                    # 处理旧版本的 PAN 模块路径映射
                    if module == 'segmentation_models_pytorch.pan':
                        module = 'segmentation_models_pytorch.decoders.pan'
                    elif module == 'segmentation_models_pytorch.pan.model':
                        module = 'segmentation_models_pytorch.decoders.pan.model'
                    elif module.startswith('segmentation_models_pytorch.pan.'):
                        module = module.replace('segmentation_models_pytorch.pan.', 
                                               'segmentation_models_pytorch.decoders.pan.')
                    
                    # 处理 timm 库的路径变更
                    elif module == 'timm.models.layers.conv2d_same':
                        module = 'timm.layers.conv2d_same'
                    elif module == 'timm.models.layers':
                        module = 'timm.layers'
                    elif module.startswith('timm.models.layers.'):
                        module = module.replace('timm.models.layers.', 'timm.layers.')
                    elif module == 'timm.models.efficientnet_blocks':
                        module = 'timm.models._efficientnet_blocks'
                    elif module == 'timm.models.efficientnet_builder':
                        module = 'timm.models._efficientnet_builder'
                    
                    return super().find_class(module, name)
            
            # 尝试使用自定义反序列化器
            import io
            with open(weights_path, 'rb') as f:
                buffer = io.BytesIO(f.read())
            
            unpickler = CustomUnpickler(buffer)
            
            # 处理持久化存储
            def persistent_load(saved_id):
                if isinstance(saved_id, tuple):
                    if len(saved_id) == 2:
                        return torch._utils._rebuild_tensor_v2(*saved_id)
                    else:
                        return torch.storage._load_from_bytes(*saved_id)
                return saved_id
            
            unpickler.persistent_load = persistent_load
            model = unpickler.load()
            
            if hasattr(model, 'to'):
                model = model.to(device)
                model.eval()
            
            print("✅ 自定义反序列化器加载成功")
            return model
            
        except Exception as e2:
            print(f"自定义反序列化器也失败: {e2}")
            
            # 方法4: 尝试使用 latin-1 编码
            try:
                print("尝试使用latin-1编码...")
                
                import pickle
                with open(weights_path, 'rb') as f:
                    model = pickle.load(f, encoding='latin-1')
                
                if hasattr(model, 'to'):
                    model = model.to(device)
                    model.eval()
                
                print("✅ latin-1编码加载成功")
                return model
                
            except Exception as e3:
                print(f"所有方法都失败: {e3}")
                raise e3

class IndividualModelVisualizer:
    """单独模型可视化器"""
    
    def __init__(self, 
                 weights_dir: str,
                 config_path: str,
                 device: Optional[str] = None):
        """
        初始化可视化器
        
        Args:
            weights_dir: 权重文件目录
            config_path: 配置文件路径
            device: 设备（'cuda' 或 'cpu'）
        """
        self.weights_dir = Path(weights_dir)
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        if device is None:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.device = torch.device(device)
        
        # 模型配置（与训练脚本保持一致）
        self.models_config = {
            'efficientnetb4': {
                'name': 'efficientnetb4',
                'architecture': 'PAN',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': 'noisy-student',
                'description': 'EfficientNet-B4 with Noisy Student weights'
            },
            'eca_nfnet_l2': {
                'name': 'eca_nfnet_l2',
                'architecture': 'PAN',
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': 'imagenet',
                'description': 'ECA-NFNet-L2 with ImageNet weights'
            },
            'seresnet152d': {
                'name': 'seresnet152d',
                'architecture': 'PAN',
                'encoder': 'tu-seresnet152d',
                'encoder_weights': 'imagenet',
                'description': 'SE-ResNet-152D with ImageNet weights'
            }
        }
        
        self.num_classes = 3  # 背景、主轨道、分叉轨道
        
        # 类别颜色映射（铁路轨道分割）
        self.class_colors = {
            0: [0, 0, 0],      # 背景 - 黑色
            1: [255, 0, 0],    # 主轨道 - 红色  
            2: [0, 255, 0],    # 分叉轨道 - 绿色
        }
        
        # 类别名称
        self.class_names = ['背景', '主轨道', '分叉轨道']
        
        # 预处理变换
        self.transforms = A.Compose([
            A.Resize(height=544, width=960),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        # 加载所有可用模型
        self.models = self._load_available_models() 

    def _create_single_model(self, model_config: Dict[str, Any]) -> nn.Module:
        """创建单个模型"""
        model = smp.PAN(
            encoder_name=model_config['encoder'],
            encoder_weights=None,  # 不使用预训练权重，我们会加载自己的
            in_channels=3,
            classes=3
        )
        return model
    
    def _load_available_models(self) -> Dict[str, nn.Module]:
        """加载所有可用的模型"""
        models = {}
        
        print("🔍 扫描可用模型...")
        for model_key, model_config in self.models_config.items():
            weights_path = self.weights_dir / f"{model_key}.pth.tar"
            
            if weights_path.exists():
                print(f"  ✅ 找到模型: {model_key}")
                try:
                    # 尝试加载模型
                    model = load_model_with_fallback(str(weights_path), model_config, self.device)
                    models[model_key] = model
                    print(f"    ✓ 成功加载: {model_key}")
                    
                except Exception as e:
                    print(f"    ✗ 加载失败: {model_key} - {e}")
            else:
                print(f"  ⏭️  跳过模型: {model_key} (权重文件不存在)")
        
        print(f"\n📊 成功加载 {len(models)} 个模型")
        return models
    
    def predict_single_image(self, 
                           model: nn.Module,
                           image_path: str,
                           threshold: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用单个模型预测单张图像
        
        Args:
            model: 模型
            image_path: 图像路径
            threshold: 预测阈值
            
        Returns:
            prediction: 预测结果 [H, W, C]
            original_image: 原始图像 [H, W, C]
        """
        # 加载图像
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        original_size = image.shape[:2]
        
        # 预处理
        transformed = self.transforms(image=image)
        input_tensor = transformed['image'].unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            output = model(input_tensor)
            prediction = torch.sigmoid(output).cpu().numpy()[0]  # [C, H, W]
        
        # 后处理
        prediction = prediction.transpose(1, 2, 0)  # [H, W, C]
        prediction = cv2.resize(prediction, (original_size[1], original_size[0]))
        
        # 二值化
        binary_prediction = (prediction > threshold).astype(np.float32)
        
        return binary_prediction, image 

    def visualize_single_model_prediction(self,
                                        model_name: str,
                                        model: nn.Module,
                                        image_path: str,
                                        output_dir: Path,
                                        threshold: float = 0.5,
                                        save_individual: bool = True) -> Dict[str, Any]:
        """
        可视化单个模型的预测结果 - 简化版：只保留mask投影到原图
        
        Args:
            model_name: 模型名称
            model: 模型
            image_path: 图像路径
            output_dir: 输出目录
            threshold: 预测阈值
            save_individual: 是否保存单独的类别预测图（已废弃，保留兼容性）
            
        Returns:
            预测统计信息
        """
        # 预测
        prediction, original_image = self.predict_single_image(model, image_path, threshold)
        
        # 创建输出目录
        model_output_dir = output_dir / model_name
        model_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取文件名
        image_name = Path(image_path).stem
        
        # 创建彩色掩码
        colored_mask = np.zeros_like(original_image, dtype=np.float32)
        for i in range(1, 3):  # 只显示轨道类别
            color = np.array(self.class_colors[i], dtype=np.float32) / 255.0
            mask = prediction[:, :, i].astype(np.float32)
            for c in range(3):
                colored_mask[:, :, c] += mask * color[c]
        
        colored_mask = np.clip(colored_mask, 0, 1)
        
        # 创建叠加显示
        original_float = original_image.astype(np.float32) / 255.0
        overlay = original_float * 0.7 + colored_mask * 0.3
        overlay = np.clip(overlay, 0, 1)
        
        # 创建可视化图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 原始图像
        axes[0].imshow(original_image)
        axes[0].set_title('原始图像', fontsize=12, fontweight='bold')
        axes[0].axis('off')
        
        # 预测掩码
        axes[1].imshow(colored_mask)
        axes[1].set_title('预测掩码', fontsize=12, fontweight='bold')
        axes[1].axis('off')
        
        # 叠加显示
        axes[2].imshow(overlay)
        axes[2].set_title('叠加显示', fontsize=12, fontweight='bold')
        axes[2].axis('off')
        
        # 设置总标题
        plt.suptitle(f'{model_name} - {image_name}', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        # 保存可视化结果
        vis_path = model_output_dir / f'{image_name}_visualization.png'
        plt.savefig(vis_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        # 保存预测掩码
        mask_path = model_output_dir / f'{image_name}_prediction.npy'
        np.save(mask_path, prediction)
        
        # 保存彩色掩码图像
        colored_mask_img = (colored_mask * 255).astype(np.uint8)
        colored_mask_path = model_output_dir / f'{image_name}_colored_mask.png'
        cv2.imwrite(str(colored_mask_path), cv2.cvtColor(colored_mask_img, cv2.COLOR_RGB2BGR))
        
        # 保存叠加图像 - 修复数据类型问题
        overlay_img = (overlay * 255).astype(np.uint8)
        overlay_path = model_output_dir / f'{image_name}_overlay.png'
        cv2.imwrite(str(overlay_path), cv2.cvtColor(overlay_img, cv2.COLOR_RGB2BGR))
        
        # 统计信息
        stats = {}
        for i in range(3):
            class_pixels = np.sum(prediction[:, :, i])
            total_pixels = prediction.shape[0] * prediction.shape[1]
            percentage = (class_pixels / total_pixels) * 100
            stats[self.class_names[i]] = {
                'pixels': int(class_pixels),
                'percentage': round(percentage, 2)
            }
        
        return {
            'model_name': model_name,
            'image_name': image_name,
            'prediction_shape': prediction.shape,
            'stats': stats,
            'vis_path': str(vis_path),
            'mask_path': str(mask_path),
            'colored_mask_path': str(colored_mask_path),
            'overlay_path': str(overlay_path)
        }
    
    def visualize_all_models(self,
                           image_path: str,
                           output_dir: str,
                           threshold: float = 0.5,
                           save_individual: bool = True) -> Dict[str, Any]:
        """
        为所有模型生成可视化
        
        Args:
            image_path: 图像路径
            output_dir: 输出目录
            threshold: 预测阈值
            save_individual: 是否保存单独的类别预测图
            
        Returns:
            所有模型的预测结果
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        image_name = Path(image_path).stem
        results = {}
        
        print(f"\n🎨 为图像 '{image_name}' 生成所有模型的可视化...")
        
        for model_name, model in self.models.items():
            print(f"\n  📊 处理模型: {model_name}")
            
            try:
                result = self.visualize_single_model_prediction(
                    model_name=model_name,
                    model=model,
                    image_path=image_path,
                    output_dir=output_dir,
                    threshold=threshold,
                    save_individual=save_individual
                )
                
                results[model_name] = result
                print(f"    ✅ 完成: {model_name}")
                
            except Exception as e:
                print(f"    ✗ 失败: {model_name} - {e}")
                results[model_name] = {'error': str(e)}
        
        # 生成对比图
        self._create_comparison_visualization(image_path, results, output_dir, threshold)
        
        # 保存结果摘要
        self._save_results_summary(results, output_dir, image_name)
        
        return results 

    def _create_comparison_visualization(self,
                                       image_path: str,
                                       results: Dict[str, Any],
                                       output_dir: Path,
                                       threshold: float):
        """创建所有模型的对比可视化 - 简化版：只显示叠加结果"""
        # 加载原始图像
        original_image = cv2.imread(image_path)
        original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        
        # 创建对比图
        num_models = len([r for r in results.values() if 'error' not in r])
        if num_models == 0:
            return
        
        fig, axes = plt.subplots(1, num_models + 1, figsize=(5 * (num_models + 1), 5))
        
        # 原始图像
        axes[0].imshow(original_image)
        axes[0].set_title('原始图像', fontsize=12, fontweight='bold')
        axes[0].axis('off')
        
        # 每个模型的叠加预测
        col_idx = 1
        for model_name, result in results.items():
            if 'error' in result:
                continue
                
            # 加载预测结果
            prediction = np.load(result['mask_path'])
            
            # 创建彩色掩码
            colored_mask = np.zeros_like(original_image, dtype=np.float32)
            for i in range(1, 3):  # 只显示轨道类别
                color = np.array(self.class_colors[i], dtype=np.float32) / 255.0
                mask = prediction[:, :, i].astype(np.float32)
                for c in range(3):
                    colored_mask[:, :, c] += mask * color[c]
            
            colored_mask = np.clip(colored_mask, 0, 1)
            
            # 创建叠加显示
            original_float = original_image.astype(np.float32) / 255.0
            overlay = original_float * 0.7 + colored_mask * 0.3
            overlay = np.clip(overlay, 0, 1)
            
            # 显示叠加图
            axes[col_idx].imshow(overlay)
            axes[col_idx].set_title(f'{model_name}\n叠加显示', fontsize=10, fontweight='bold')
            axes[col_idx].axis('off')
            
            col_idx += 1
        
        # 隐藏多余的子图
        for i in range(col_idx, len(axes)):
            axes[i].axis('off')
        
        plt.suptitle(f'模型预测对比 - {Path(image_path).stem}', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        # 保存对比图
        comparison_path = output_dir / f'{Path(image_path).stem}_comparison.png'
        plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"  📊 对比图已保存: {comparison_path}")
    
    def _save_results_summary(self,
                             results: Dict[str, Any],
                             output_dir: Path,
                             image_name: str):
        """保存结果摘要"""
        summary = {
            'image_name': image_name,
            'timestamp': datetime.now().isoformat(),
            'threshold': 0.5,
            'models': {}
        }
        
        for model_name, result in results.items():
            if 'error' not in result:
                summary['models'][model_name] = {
                    'stats': result['stats'],
                    'vis_path': result['vis_path'],
                    'mask_path': result['mask_path'],
                    'colored_mask_path': result['colored_mask_path'],
                    'overlay_path': result['overlay_path']
                }
            else:
                summary['models'][model_name] = {
                    'error': result['error']
                }
        
        # 保存JSON摘要
        summary_path = output_dir / f'{image_name}_summary.json'
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"  📝 结果摘要已保存: {summary_path}")
    
    def batch_visualize(self,
                       image_dir: str,
                       output_dir: str,
                       threshold: float = 0.5,
                       save_individual: bool = True,
                       max_images: Optional[int] = None) -> Dict[str, Any]:
        """
        批量可视化多张图像
        
        Args:
            image_dir: 图像目录
            output_dir: 输出目录
            threshold: 预测阈值
            save_individual: 是否保存单独的类别预测图
            max_images: 最大处理图像数量
            
        Returns:
            批量处理结果
        """
        image_dir = Path(image_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的图像格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = [f for f in image_dir.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        if max_images:
            image_files = image_files[:max_images]
        
        print(f"\n🎨 批量可视化 {len(image_files)} 张图像...")
        
        all_results = {}
        
        for i, image_file in enumerate(tqdm(image_files, desc="处理图像")):
            print(f"\n📸 处理图像 {i+1}/{len(image_files)}: {image_file.name}")
            
            try:
                results = self.visualize_all_models(
                    image_path=str(image_file),
                    output_dir=output_dir,
                    threshold=threshold,
                    save_individual=save_individual
                )
                
                all_results[image_file.name] = results
                
            except Exception as e:
                print(f"  ✗ 处理失败: {e}")
                all_results[image_file.name] = {'error': str(e)}
        
        # 保存批量处理摘要
        batch_summary = {
            'total_images': len(image_files),
            'processed_images': len([r for r in all_results.values() if 'error' not in r]),
            'failed_images': len([r for r in all_results.values() if 'error' in r]),
            'timestamp': datetime.now().isoformat(),
            'results': all_results
        }
        
        batch_summary_path = output_dir / 'batch_summary.json'
        with open(batch_summary_path, 'w', encoding='utf-8') as f:
            json.dump(batch_summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 批量处理完成!")
        print(f"  总图像数: {batch_summary['total_images']}")
        print(f"  成功处理: {batch_summary['processed_images']}")
        print(f"  处理失败: {batch_summary['failed_images']}")
        print(f"  摘要文件: {batch_summary_path}")
        
        return all_results 

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单独模型可视化脚本')
    parser.add_argument('--weights-dir', required=True, type=str,
                       help='训练好的权重目录路径')
    parser.add_argument('--config', required=True, type=str,
                       help='配置文件路径')
    parser.add_argument('--input', required=True, type=str,
                       help='输入图像路径或目录')
    parser.add_argument('--output-dir', default='./individual_visualizations', type=str,
                       help='输出目录路径')
    parser.add_argument('--threshold', default=0.5, type=float,
                       help='预测阈值')
    parser.add_argument('--device', default=None, type=str,
                       help='计算设备 (cuda/cpu)')
    parser.add_argument('--batch', action='store_true',
                       help='批量处理模式（输入为目录）')
    parser.add_argument('--max-images', type=int, default=None,
                       help='批量处理时的最大图像数量')
    parser.add_argument('--no-individual', action='store_true',
                       help='不保存单独的类别预测图')
    
    args = parser.parse_args()
    
    print("🎨 启动单独模型可视化...")
    print(f"  权重目录: {args.weights_dir}")
    print(f"  配置文件: {args.config}")
    print(f"  输入: {args.input}")
    print(f"  输出目录: {args.output_dir}")
    print(f"  预测阈值: {args.threshold}")
    print(f"  批量模式: {args.batch}")
    
    # 创建可视化器
    visualizer = IndividualModelVisualizer(
        weights_dir=args.weights_dir,
        config_path=args.config,
        device=args.device
    )
    
    if not visualizer.models:
        print("❌ 没有找到可用的模型!")
        return
    
    print(f"\n✅ 找到 {len(visualizer.models)} 个可用模型:")
    for model_name in visualizer.models.keys():
        print(f"  - {model_name}")
    
    # 执行可视化
    if args.batch:
        # 批量处理模式
        if not Path(args.input).is_dir():
            print("❌ 批量模式需要输入目录!")
            return
        
        visualizer.batch_visualize(
            image_dir=args.input,
            output_dir=args.output_dir,
            threshold=args.threshold,
            save_individual=not args.no_individual,
            max_images=args.max_images
        )
    else:
        # 单张图像模式
        if not Path(args.input).is_file():
            print("❌ 单张图像模式需要输入文件!")
            return
        
        visualizer.visualize_all_models(
            image_path=args.input,
            output_dir=args.output_dir,
            threshold=args.threshold,
            save_individual=not args.no_individual
        )
    
    print(f"\n🎉 可视化完成!")
    print(f"📂 结果保存在: {args.output_dir}")

if __name__ == '__main__':
    main() 