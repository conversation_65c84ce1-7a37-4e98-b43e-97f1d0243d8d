#!/usr/bin/env python3
"""
交互式NPY掩码查看器
支持实时切换样式、缩放、统计信息显示
"""

import sys
import numpy as np
import cv2
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from PIL import Image, ImageTk
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Rectangle
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.visualize_npy_masks import MaskVisualizer


class InteractiveMaskViewer:
    """交互式掩码查看器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Railway Track Mask Viewer - 交互式掩码查看器")
        self.root.geometry("1400x900")
        
        # 初始化可视化器
        self.visualizer = MaskVisualizer()
        
        # 当前状态
        self.current_mask = None
        self.current_image = None
        self.current_colored_mask = None
        self.zoom_level = 1.0
        self.style = 'solid'
        self.show_overlay = True
        self.alpha = 0.4
        
        # 设置界面
        self.setup_ui()
        
        # 绑定键盘快捷键
        self.setup_shortcuts()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 1. 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 文件选择
        ttk.Button(control_frame, text="选择NPY掩码", 
                  command=self.load_mask).grid(row=0, column=0, padx=5)
        ttk.Button(control_frame, text="选择原始图像", 
                  command=self.load_image).grid(row=0, column=1, padx=5)
        
        # 样式选择
        ttk.Label(control_frame, text="显示样式:").grid(row=0, column=2, padx=(20, 5))
        self.style_var = tk.StringVar(value='solid')
        style_combo = ttk.Combobox(control_frame, textvariable=self.style_var, 
                                  values=['solid', 'blend', 'gradient'], 
                                  width=10, state='readonly')
        style_combo.grid(row=0, column=3, padx=5)
        style_combo.bind('<<ComboboxSelected>>', self.update_visualization)
        
        # 透明度滑块
        ttk.Label(control_frame, text="叠加透明度:").grid(row=0, column=4, padx=(20, 5))
        self.alpha_var = tk.DoubleVar(value=0.4)
        alpha_scale = ttk.Scale(control_frame, from_=0, to=1, 
                               variable=self.alpha_var, orient=tk.HORIZONTAL,
                               length=150, command=self.update_overlay)
        alpha_scale.grid(row=0, column=5, padx=5)
        
        # 显示选项
        self.show_overlay_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="显示叠加", 
                       variable=self.show_overlay_var,
                       command=self.update_visualization).grid(row=0, column=6, padx=20)
        
        # 2. 主显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建画布
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, display_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 3. 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="10")
        info_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        # 统计信息文本
        self.info_text = tk.Text(info_frame, width=30, height=20, 
                                font=('Consolas', 10))
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # 4. 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 缩放控制
        ttk.Label(status_frame, text="缩放:").pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(status_frame, text="-", width=3,
                  command=lambda: self.zoom(-0.1)).pack(side=tk.RIGHT)
        self.zoom_label = ttk.Label(status_frame, text="100%")
        self.zoom_label.pack(side=tk.RIGHT, padx=5)
        ttk.Button(status_frame, text="+", width=3,
                  command=lambda: self.zoom(0.1)).pack(side=tk.RIGHT)
        
        # 添加颜色图例
        self.add_color_legend()
    
    def setup_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-o>', lambda e: self.load_mask())
        self.root.bind('<Control-i>', lambda e: self.load_image())
        self.root.bind('<Control-s>', lambda e: self.save_current())
        self.root.bind('<Control-plus>', lambda e: self.zoom(0.1))
        self.root.bind('<Control-minus>', lambda e: self.zoom(-0.1))
        self.root.bind('<space>', lambda e: self.toggle_overlay())
        self.root.bind('1', lambda e: self.set_style('solid'))
        self.root.bind('2', lambda e: self.set_style('blend'))
        self.root.bind('3', lambda e: self.set_style('gradient'))
    
    def add_color_legend(self):
        """添加颜色图例到信息面板"""
        legend_text = """
=== 颜色说明 ===
🟠 主轨道 (Main Track)
   RGB: (255, 140, 0)
   
🔵 分叉轨道 (Fork Track)
   RGB: (0, 191, 255)
   
🔴 重叠区域 (Overlap)
   RGB: (255, 20, 147)
   
⬛ 背景 (Background)
   RGB: (30, 30, 30)

=== 快捷键 ===
Ctrl+O: 打开掩码
Ctrl+I: 打开图像
Ctrl+S: 保存当前视图
Ctrl++: 放大
Ctrl+-: 缩小
Space: 切换叠加
1/2/3: 切换样式
"""
        self.info_text.insert('1.0', legend_text)
        self.info_text.config(state='disabled')
    
    def load_mask(self):
        """加载NPY掩码文件"""
        filename = filedialog.askopenfilename(
            title="选择NPY掩码文件",
            filetypes=[("NPY files", "*.npy"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                self.current_mask = self.visualizer.load_npy_mask(filename)
                self.update_visualization()
                self.update_statistics()
                self.status_label.config(text=f"已加载: {Path(filename).name}")
            except Exception as e:
                messagebox.showerror("错误", f"加载掩码失败: {str(e)}")
    
    def load_image(self):
        """加载原始图像"""
        filename = filedialog.askopenfilename(
            title="选择原始图像",
            filetypes=[("Image files", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                self.current_image = cv2.imread(filename)
                self.current_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
                self.update_visualization()
                self.status_label.config(text=f"已加载图像: {Path(filename).name}")
            except Exception as e:
                messagebox.showerror("错误", f"加载图像失败: {str(e)}")
    
    def update_visualization(self, event=None):
        """更新可视化显示"""
        if self.current_mask is None:
            return
        
        # 清除当前图像
        self.ax.clear()
        
        # 获取当前样式
        style = self.style_var.get()
        
        # 创建彩色掩码
        self.current_colored_mask = self.visualizer.create_color_mask(
            self.current_mask, style
        )
        
        # 决定显示什么
        if self.show_overlay_var.get() and self.current_image is not None:
            # 显示叠加图像
            overlay = self.visualizer.create_overlay(
                self.current_image, 
                self.current_colored_mask,
                self.alpha_var.get()
            )
            display_image = cv2.cvtColor(overlay, cv2.COLOR_BGR2RGB)
        else:
            # 只显示掩码
            display_image = cv2.cvtColor(self.current_colored_mask, cv2.COLOR_BGR2RGB)
        
        # 应用缩放
        if self.zoom_level != 1.0:
            h, w = display_image.shape[:2]
            new_h, new_w = int(h * self.zoom_level), int(w * self.zoom_level)
            display_image = cv2.resize(display_image, (new_w, new_h), 
                                     interpolation=cv2.INTER_LINEAR)
        
        # 显示图像
        self.ax.imshow(display_image)
        self.ax.set_title(f'轨道掩码可视化 ({style.title()}风格)', 
                         fontsize=14, fontweight='bold')
        self.ax.axis('off')
        
        # 添加图例
        self.visualizer.add_legend(self.ax)
        
        # 刷新画布
        self.canvas.draw()
    
    def update_overlay(self, value=None):
        """更新叠加透明度"""
        if self.show_overlay_var.get():
            self.update_visualization()
    
    def update_statistics(self):
        """更新统计信息"""
        if self.current_mask is None:
            return
        
        stats = self.visualizer.get_statistics(self.current_mask)
        
        # 清除旧信息
        self.info_text.config(state='normal')
        self.info_text.delete('1.0', tk.END)
        
        # 构建统计文本
        stats_text = f"""=== 掩码统计 ===
总像素数: {stats['total_pixels']:,}

背景:
  像素数: {stats['background']:,}
  占比: {stats['background_ratio']:.1%}

主轨道:
  像素数: {stats['main_track']:,}
  占比: {stats['main_track_ratio']:.1%}

分叉轨道:
  像素数: {stats['fork_track']:,}
  占比: {stats['fork_track_ratio']:.1%}

重叠区域:
  像素数: {stats['overlap']:,}
  占比: {stats['overlap_ratio']:.1%}

=== 图像信息 ===
尺寸: {self.current_mask.shape[1]} × {self.current_mask.shape[0]}
通道数: {self.current_mask.shape[2]}
"""
        
        self.info_text.insert('1.0', stats_text)
        
        # 重新添加颜色图例和快捷键
        self.add_color_legend()
    
    def zoom(self, delta):
        """缩放图像"""
        self.zoom_level = max(0.1, min(3.0, self.zoom_level + delta))
        self.zoom_label.config(text=f"{int(self.zoom_level * 100)}%")
        self.update_visualization()
    
    def toggle_overlay(self):
        """切换叠加显示"""
        self.show_overlay_var.set(not self.show_overlay_var.get())
        self.update_visualization()
    
    def set_style(self, style):
        """设置显示样式"""
        self.style_var.set(style)
        self.update_visualization()
    
    def save_current(self):
        """保存当前视图"""
        if self.current_colored_mask is None:
            messagebox.showwarning("警告", "没有可保存的图像")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存可视化结果",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                # 保存当前显示的内容
                self.fig.savefig(filename, dpi=150, bbox_inches='tight')
                messagebox.showinfo("成功", f"图像已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = InteractiveMaskViewer(root)
    root.mainloop()


if __name__ == '__main__':
    main()