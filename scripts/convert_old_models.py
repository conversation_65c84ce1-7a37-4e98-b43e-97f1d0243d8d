#!/usr/bin/env python3
"""
转换旧版本模型到新格式
仅保存模型的state_dict而不是整个模型
"""

import torch
import sys
from pathlib import Path
import argparse

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import segmentation_models_pytorch as smp


def convert_model(old_model_path, output_path=None, model_type='efficientnetb4'):
    """
    转换旧模型格式到新格式
    
    Args:
        old_model_path: 旧模型路径
        output_path: 输出路径（如果为None，会自动生成）
        model_type: 模型类型
    """
    old_model_path = Path(old_model_path)
    if not old_model_path.exists():
        print(f"错误：模型文件不存在: {old_model_path}")
        return False
    
    if output_path is None:
        output_path = old_model_path.parent / f"{old_model_path.stem}_converted.pth"
    
    print(f"转换模型: {old_model_path}")
    print(f"输出路径: {output_path}")
    
    # 根据模型类型创建新模型
    model_configs = {
        'efficientnetb4': {
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'encoder_weights': None,  # 不使用预训练权重，稍后加载
        },
        'eca_nfnet_l2': {
            'encoder': 'tu-eca_nfnet_l2',
            'encoder_weights': None,
        },
        'seresnet152d': {
            'encoder': 'tu-seresnet152d',
            'encoder_weights': None,
        }
    }
    
    if model_type not in model_configs:
        print(f"错误：不支持的模型类型: {model_type}")
        print(f"支持的类型: {list(model_configs.keys())}")
        return False
    
    config = model_configs[model_type]
    
    # 创建新模型架构
    print(f"创建 {model_type} 模型架构...")
    new_model = smp.PAN(
        encoder_name=config['encoder'],
        encoder_weights=config['encoder_weights'],
        in_channels=3,
        classes=3
    )
    
    # 尝试加载旧模型的state_dict
    print("加载旧模型state_dict...")
    try:
        # 尝试直接加载整个checkpoint
        # PyTorch 2.6 兼容性：设置 weights_only=False
        checkpoint = torch.load(old_model_path, map_location='cpu', weights_only=False)
        
        if isinstance(checkpoint, dict):
            # 如果是字典，尝试提取state_dict
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                # 假设整个字典就是state_dict
                state_dict = checkpoint
        elif hasattr(checkpoint, 'state_dict'):
            # 如果是模型对象，提取state_dict
            state_dict = checkpoint.state_dict()
        else:
            print("错误：无法识别的checkpoint格式")
            return False
            
        # 加载state_dict到新模型
        print("加载权重到新模型...")
        new_model.load_state_dict(state_dict, strict=True)
        print("✅ 权重加载成功!")
        
        # 保存转换后的模型
        print("保存转换后的模型...")
        torch.save({
            'model_state_dict': new_model.state_dict(),
            'model_type': model_type,
            'encoder': config['encoder'],
            'architecture': 'PAN',
            'classes': 3
        }, output_path)
        
        print(f"✅ 模型转换成功！保存到: {output_path}")
        
        # 验证保存的模型
        print("\n验证保存的模型...")
        test_checkpoint = torch.load(output_path)
        test_model = smp.PAN(
            encoder_name=config['encoder'],
            encoder_weights=None,
            in_channels=3,
            classes=3
        )
        test_model.load_state_dict(test_checkpoint['model_state_dict'])
        print("✅ 验证成功！转换后的模型可以正常加载")
        
        return True
        
    except Exception as e:
        print(f"错误：转换失败 - {e}")
        # 如果标准方法失败，尝试使用自定义加载器
        print("尝试使用高级兼容模式...")
        
        try:
            # 导入高级兼容加载器
            sys.path.insert(0, str(Path(__file__).parent))
            from ensemble_training_notebook_exact import load_pretrained_model
            
            # 使用高级加载器
            old_model = load_pretrained_model(str(old_model_path), device='cpu')
            
            if hasattr(old_model, 'state_dict'):
                state_dict = old_model.state_dict()
                
                # 加载到新模型
                new_model.load_state_dict(state_dict, strict=False)
                print("✅ 高级兼容模式成功!")
                
                # 保存转换后的模型
                torch.save({
                    'model_state_dict': new_model.state_dict(),
                    'model_type': model_type,
                    'encoder': config['encoder'],
                    'architecture': 'PAN',
                    'classes': 3
                }, output_path)
                
                print(f"✅ 模型转换成功！保存到: {output_path}")
                return True
            else:
                print("错误：加载的对象不是有效的模型")
                return False
                
        except Exception as e2:
            print(f"高级兼容模式也失败: {e2}")
            print("\n建议:")
            print("1. 模型文件可能已损坏")
            print("2. 模型格式与当前环境不兼容")
            print("3. 建议从头训练新模型")
            return False


def main():
    parser = argparse.ArgumentParser(description='转换旧版本模型到新格式')
    parser.add_argument('model_path', help='旧模型文件路径')
    parser.add_argument('--type', choices=['efficientnetb4', 'eca_nfnet_l2', 'seresnet152d'],
                       required=True, help='模型类型')
    parser.add_argument('--output', help='输出文件路径（可选）')
    
    args = parser.parse_args()
    
    success = convert_model(args.model_path, args.output, args.type)
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()