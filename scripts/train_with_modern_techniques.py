#!/usr/bin/env python3
"""
Example script demonstrating modern training techniques for railway segmentation.
This script shows how to use cosine learning rate, EMA, SWA, and other advanced features.
"""

import os
import sys
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.train_v2 import main as train_main


def run_modern_training():
    """
    Run training with modern techniques using the enhanced configuration.
    """
    
    print("=" * 80)
    print("Railway Track Segmentation - Modern Training")
    print("=" * 80)
    print("\nThis training includes:")
    print("✓ Cosine learning rate scheduling with warmup")
    print("✓ Exponential Moving Average (EMA)")
    print("✓ Stochastic Weight Averaging (SWA)")
    print("✓ Label smoothing")
    print("✓ Mixed precision training")
    print("✓ Gradient accumulation and clipping")
    print("✓ Advanced data augmentation")
    print("✓ TensorBoard integration")
    print("\n" + "=" * 80 + "\n")
    
    # Set up arguments for the training script
    sys.argv = [
        'train_v2.py',
        '--config', 'configs/railway_track_config_modern.yaml',
        '--experiment', 'modern_techniques_demo'
    ]
    
    # Run training
    train_main()


def compare_training_methods():
    """
    Compare traditional vs modern training techniques.
    """
    
    print("\n" + "=" * 80)
    print("Comparison: Traditional vs Modern Training")
    print("=" * 80)
    
    print("\nTraditional Training:")
    print("- Fixed or step learning rate schedule")
    print("- No warmup period")
    print("- Standard model weights")
    print("- Basic augmentation")
    print("- Single precision training")
    
    print("\nModern Training:")
    print("- Cosine annealing with warm restarts")
    print("- Linear warmup for stable start")
    print("- EMA for smoother predictions")
    print("- SWA for better generalization")
    print("- Label smoothing for robustness")
    print("- Mixed precision for 2x speedup")
    print("- Advanced augmentation scheduling")
    
    print("\nExpected Improvements:")
    print("- 5-10% better validation IoU")
    print("- 2x faster training with mixed precision")
    print("- More stable training curves")
    print("- Better generalization to test data")
    print("- Reduced overfitting")
    print("\n" + "=" * 80 + "\n")


def create_custom_config():
    """
    Create a custom configuration for specific use cases.
    """
    
    custom_configs = {
        "small_dataset": {
            "description": "Configuration for small datasets (< 1000 images)",
            "changes": {
                "training.batch_size": 4,
                "training.gradient_accumulation_steps": 4,
                "augmentation.train": "heavy",  # More augmentation
                "training.use_swa": True,
                "training.swa_start_epoch": 50,
                "loss.label_smoothing": 0.2  # More smoothing
            }
        },
        "large_dataset": {
            "description": "Configuration for large datasets (> 10000 images)",
            "changes": {
                "training.batch_size": 32,
                "training.num_epochs": 200,
                "scheduler.type": "onecycle",
                "training.mixed_precision": True,
                "distributed.enable": True
            }
        },
        "fast_experimentation": {
            "description": "Quick training for experimentation",
            "changes": {
                "training.num_epochs": 20,
                "training.use_ema": False,
                "training.use_swa": False,
                "data.image_size": [540, 960],  # Smaller images
                "training.val_frequency": 5
            }
        },
        "production": {
            "description": "Production-ready training",
            "changes": {
                "training.num_epochs": 150,
                "training.early_stopping_patience": 30,
                "training.use_ema": True,
                "training.use_swa": True,
                "test_time_augmentation.enable": True,
                "inference.use_ema_model": True
            }
        }
    }
    
    print("\n" + "=" * 80)
    print("Custom Configuration Examples")
    print("=" * 80)
    
    for name, config in custom_configs.items():
        print(f"\n{name.upper()}: {config['description']}")
        print("Key changes:")
        for key, value in config['changes'].items():
            print(f"  - {key}: {value}")


def training_tips():
    """
    Display training tips and best practices.
    """
    
    print("\n" + "=" * 80)
    print("Training Tips and Best Practices")
    print("=" * 80)
    
    tips = [
        ("Start with pretrained weights", 
         "Always use pretrained=true for faster convergence"),
        
        ("Monitor validation metrics", 
         "Check val_iou and val_dice, not just loss"),
        
        ("Use gradient accumulation for large models", 
         "If GPU memory is limited, increase gradient_accumulation_steps"),
        
        ("Enable mixed precision training", 
         "Get 2x speedup with minimal accuracy loss"),
        
        ("Tune learning rate first", 
         "LR is the most important hyperparameter"),
        
        ("Use cosine annealing", 
         "Better than step scheduler for most cases"),
        
        ("Enable EMA for stable predictions", 
         "EMA models often perform better at inference"),
        
        ("Save checkpoints frequently", 
         "You can always resume training later"),
        
        ("Visualize predictions regularly", 
         "Check TensorBoard to catch issues early"),
        
        ("Use appropriate augmentation", 
         "Match augmentation to your data characteristics")
    ]
    
    for i, (tip, detail) in enumerate(tips, 1):
        print(f"\n{i}. {tip}")
        print(f"   → {detail}")
    
    print("\n" + "=" * 80 + "\n")


def main():
    parser = argparse.ArgumentParser(
        description='Modern training techniques for railway segmentation'
    )
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'compare', 'config', 'tips'],
                       help='Mode to run: train, compare, config, or tips')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        run_modern_training()
    elif args.mode == 'compare':
        compare_training_methods()
    elif args.mode == 'config':
        create_custom_config()
    elif args.mode == 'tips':
        training_tips()
    
    # Show all information if no specific mode
    if args.mode != 'train':
        print("\nTo start training with modern techniques, run:")
        print("python scripts/train_with_modern_techniques.py --mode train")


if __name__ == '__main__':
    main()