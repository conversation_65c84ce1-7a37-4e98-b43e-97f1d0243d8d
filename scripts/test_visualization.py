#!/usr/bin/env python3
"""
测试NPY掩码可视化效果
"""

import sys
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.visualize_npy_masks import MaskVisualizer


def test_single_visualization():
    """测试单个掩码的可视化"""
    print("=== 测试单个掩码可视化 ===")
    
    # 查找一个包含分叉轨道的掩码
    data_dir = Path('/home/<USER>/data/railway_track_dataset')
    mask_file = None
    
    for split in ['train', 'val', 'test']:
        mask_dir = data_dir / split / 'masks'
        if mask_dir.exists():
            for f in mask_dir.glob('*.npy'):
                # 加载掩码检查是否包含分叉轨道
                mask = np.load(str(f))
                if mask.shape[-1] >= 3 and np.sum(mask[:, :, 2] > 0) > 0:
                    mask_file = f
                    break
            if mask_file:
                break
    
    if not mask_file:
        print("未找到包含分叉轨道的掩码文件")
        return
    
    print(f"使用掩码文件: {mask_file}")
    
    # 查找对应的图像
    image_file = mask_file.parent.parent / 'images' / (mask_file.stem.replace('.npy', '') + '.png')
    if not image_file.exists():
        image_file = mask_file.parent.parent / 'images' / mask_file.stem.replace('_mask', '') + '.png'
    
    # 创建可视化器
    visualizer = MaskVisualizer()
    
    # 测试不同风格
    styles = ['solid', 'blend', 'gradient']
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for idx, style in enumerate(styles):
        colored_mask = visualizer.create_color_mask(
            np.load(str(mask_file)), style
        )
        
        # 转换为RGB显示
        import cv2
        rgb_mask = cv2.cvtColor(colored_mask, cv2.COLOR_BGR2RGB)
        
        axes[idx].imshow(rgb_mask)
        axes[idx].set_title(f'{style.title()}风格', fontsize=14, fontweight='bold')
        axes[idx].axis('off')
        
        if idx == 0:
            visualizer.add_legend(axes[idx])
    
    plt.suptitle('不同可视化风格对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('visualization_styles_comparison.png', dpi=150, bbox_inches='tight')
    print("已保存风格对比图: visualization_styles_comparison.png")
    plt.show()
    
    # 显示统计信息
    mask = np.load(str(mask_file))
    stats = visualizer.get_statistics(mask)
    print(f"\n掩码统计信息:")
    print(f"  主轨道占比: {stats['main_track_ratio']:.1%}")
    print(f"  分叉轨道占比: {stats['fork_track_ratio']:.1%}")
    print(f"  重叠区域占比: {stats['overlap_ratio']:.1%}")


def test_batch_visualization():
    """测试批量可视化"""
    print("\n=== 测试批量可视化 ===")
    
    data_dir = Path('/home/<USER>/data/railway_track_dataset/train/masks')
    if not data_dir.exists():
        print("数据目录不存在")
        return
    
    visualizer = MaskVisualizer()
    
    # 创建保存目录
    save_dir = Path('visualization_outputs')
    save_dir.mkdir(exist_ok=True)
    
    # 批量可视化
    visualizer.visualize_batch(data_dir, num_samples=9, 
                              style='solid', save_dir=save_dir)
    
    print(f"批量可视化已保存到: {save_dir}")


def test_tensorboard_visualization():
    """测试TensorBoard可视化功能"""
    print("\n=== 测试TensorBoard可视化 ===")
    
    import torch
    from torch.utils.tensorboard import SummaryWriter
    sys.path.append(str(project_root / 'src'))
    from utils.visualization import create_multilabel_visualization_for_tensorboard
    
    # 创建测试数据
    batch_size = 4
    height, width = 256, 256
    
    # 模拟图像和掩码
    images = torch.rand(batch_size, 3, height, width)
    masks = torch.zeros(batch_size, 3, height, width)
    
    # 创建一些模拟的轨道掩码
    for i in range(batch_size):
        # 主轨道
        masks[i, 1, height//3:2*height//3, width//4:3*width//4] = 1
        
        # 分叉轨道
        if i % 2 == 0:
            masks[i, 2, height//2:3*height//4, width//2:width] = 1
        
        # 背景
        masks[i, 0] = 1 - torch.max(masks[i, 1:], dim=0)[0]
    
    # 创建预测（稍微不同于真实掩码）
    predictions = masks.clone()
    noise = torch.randn_like(predictions) * 0.1
    predictions = torch.sigmoid(predictions + noise)
    
    # 创建可视化
    viz = create_multilabel_visualization_for_tensorboard(
        images, masks, predictions, num_samples=4
    )
    
    # 保存到TensorBoard
    writer = SummaryWriter('runs/test_multilabel_viz')
    writer.add_images('Test/MultiLabel', viz, 0)
    writer.close()
    
    print("TensorBoard可视化已保存到: runs/test_multilabel_viz")
    print("使用以下命令查看: tensorboard --logdir=runs")


def create_demo_mask():
    """创建演示用的掩码"""
    print("\n=== 创建演示掩码 ===")
    
    # 创建一个包含所有特征的演示掩码
    height, width = 512, 768
    mask = np.zeros((height, width, 3), dtype=np.float32)
    
    # 背景
    mask[:, :, 0] = 1.0
    
    # 主轨道 - S形曲线
    for y in range(height):
        x_center = width // 2 + int(100 * np.sin(y * 0.02))
        x_start = max(0, x_center - 40)
        x_end = min(width, x_center + 40)
        mask[y, x_start:x_end, 1] = 1.0
        mask[y, x_start:x_end, 0] = 0.0
    
    # 分叉轨道 - 从中间分出
    fork_start_y = height // 3
    for y in range(fork_start_y, height):
        progress = (y - fork_start_y) / (height - fork_start_y)
        x_offset = int(150 * progress)
        x_center = width // 2 + x_offset
        x_start = max(0, x_center - 30)
        x_end = min(width, x_center + 30)
        if x_end < width:
            mask[y, x_start:x_end, 2] = 1.0
            # 不清除背景，创建重叠
            if progress < 0.3:  # 前30%有重叠
                pass
            else:
                mask[y, x_start:x_end, 0] = 0.0
    
    # 保存演示掩码
    demo_path = Path('demo_mask.npy')
    np.save(str(demo_path), mask)
    print(f"演示掩码已保存到: {demo_path}")
    
    # 可视化演示掩码
    visualizer = MaskVisualizer()
    for style in ['solid', 'blend', 'gradient']:
        visualizer.visualize_single(
            demo_path,
            style=style,
            save_path=f'demo_mask_{style}.png',
            show=False
        )
        print(f"已保存演示图: demo_mask_{style}.png")
    
    # 显示统计
    stats = visualizer.get_statistics(mask)
    print(f"\n演示掩码统计:")
    print(f"  主轨道: {stats['main_track_ratio']:.1%}")
    print(f"  分叉轨道: {stats['fork_track_ratio']:.1%}")
    print(f"  重叠区域: {stats['overlap_ratio']:.1%}")


def main():
    """运行所有测试"""
    print("开始测试NPY掩码可视化...\n")
    
    # 创建演示掩码
    create_demo_mask()
    
    # 测试单个可视化
    test_single_visualization()
    
    # 测试批量可视化
    # test_batch_visualization()
    
    # 测试TensorBoard
    try:
        test_tensorboard_visualization()
    except ImportError:
        print("TensorBoard未安装，跳过测试")
    
    print("\n所有测试完成！")
    print("\n使用方法:")
    print("1. 查看单个掩码: python scripts/visualize_npy_masks.py <mask.npy>")
    print("2. 批量查看: python scripts/visualize_npy_masks.py <mask_dir> --batch")
    print("3. 交互式查看: python scripts/interactive_mask_viewer.py")


if __name__ == '__main__':
    main()