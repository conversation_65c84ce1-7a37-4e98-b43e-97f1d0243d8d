#!/usr/bin/env python3
"""
为训练组织图像数据
根据已分割的掩码文件，创建对应的图像目录结构
"""

import os
import sys
from pathlib import Path
import shutil
import argparse

def find_original_image(mask_filename, original_images_dir):
    """
    根据掩码文件名找到对应的原始图像
    
    Args:
        mask_filename: 掩码文件名 (如: 20250118164602376.near.avi_frame_1135.png)
        original_images_dir: 原始图像目录
    
    Returns:
        原始图像路径或None
    """
    # 移除.png扩展名
    base_name = mask_filename.replace('.png', '')
    
    # 可能的图像扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    for ext in image_extensions:
        image_path = Path(original_images_dir) / f"{base_name}{ext}"
        if image_path.exists():
            return image_path
    
    return None

def organize_images_by_masks(dataset_dir, original_images_dir, mode='symlink'):
    """
    根据掩码文件组织图像目录结构
    
    Args:
        dataset_dir: 数据集目录 (包含train/val/test/masks/)
        original_images_dir: 原始图像目录
        mode: 'symlink' (软链接) 或 'copy' (复制)
    """
    dataset_path = Path(dataset_dir)
    original_path = Path(original_images_dir)
    
    if not original_path.exists():
        print(f"❌ 原始图像目录不存在: {original_path}")
        return False
    
    splits = ['train', 'val', 'test']
    total_processed = 0
    total_missing = 0
    
    for split in splits:
        masks_dir = dataset_path / split / 'masks'
        images_dir = dataset_path / split / 'images'
        
        if not masks_dir.exists():
            print(f"⚠️  跳过 {split}: 掩码目录不存在")
            continue
        
        # 创建图像目录
        images_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有掩码文件
        mask_files = list(masks_dir.glob('*.png'))
        print(f"\n📂 处理 {split} 集: {len(mask_files)} 个掩码文件")
        
        processed = 0
        missing = 0
        
        for mask_file in mask_files:
            # 查找对应的原始图像
            original_image = find_original_image(mask_file.name, original_path)
            
            if original_image:
                target_image = images_dir / mask_file.name
                
                try:
                    if mode == 'symlink':
                        # 创建软链接
                        if target_image.exists():
                            target_image.unlink()  # 删除已存在的链接
                        target_image.symlink_to(original_image.absolute())
                    else:
                        # 复制文件
                        shutil.copy2(original_image, target_image)
                    
                    processed += 1
                    
                except Exception as e:
                    print(f"❌ 处理 {mask_file.name} 失败: {e}")
                    missing += 1
            else:
                print(f"⚠️  未找到对应图像: {mask_file.name}")
                missing += 1
        
        print(f"✅ {split}: 成功处理 {processed} 个, 缺失 {missing} 个")
        total_processed += processed
        total_missing += missing
    
    print(f"\n🎯 总结:")
    print(f"  成功处理: {total_processed} 个图像")
    print(f"  缺失图像: {total_missing} 个")
    
    return total_missing == 0

def main():
    parser = argparse.ArgumentParser(description='为训练组织图像数据')
    parser.add_argument('--dataset-dir', type=str, required=True,
                       help='数据集目录 (包含train/val/test/masks/)')
    parser.add_argument('--original-images-dir', type=str, required=True,
                       help='原始图像目录')
    parser.add_argument('--mode', choices=['symlink', 'copy'], default='symlink',
                       help='组织模式: symlink(软链接) 或 copy(复制)')
    parser.add_argument('--dry-run', action='store_true',
                       help='只检查不执行')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("图像数据组织工具")
    print("=" * 60)
    print(f"数据集目录: {args.dataset_dir}")
    print(f"原始图像目录: {args.original_images_dir}")
    print(f"组织模式: {args.mode}")
    print(f"预览模式: {args.dry_run}")
    print()
    
    if args.dry_run:
        print("🔍 预览模式 - 只检查不执行")
        # TODO: 实现预览功能
        return
    
    success = organize_images_by_masks(
        args.dataset_dir,
        args.original_images_dir,
        args.mode
    )
    
    if success:
        print("\n🎉 图像组织完成!")
        print("\n📋 现在可以运行训练:")
        print(f"python scripts/ensemble_training_notebook_exact_with_fusion.py \\")
        print(f"    --data-dir {args.dataset_dir}")
    else:
        print("\n⚠️  部分图像缺失，但仍可以进行训练")
        print("系统会自动跳过缺失的图像文件")

if __name__ == '__main__':
    main()
