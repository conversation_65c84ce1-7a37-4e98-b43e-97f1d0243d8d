#!/usr/bin/env python3
"""
将PNG格式的掩码转换为多标签格式
从单通道PNG掩码（0=背景, 85=主轨道, 170=分叉轨道）转换为3通道多标签掩码
"""

import sys
import numpy as np
from pathlib import Path
from tqdm import tqdm
import cv2
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def process_single_mask(args):
    """处理单个掩码文件的转换"""
    mask_path, output_dir = args
    
    try:
        # 读取PNG掩码
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if mask is None:
            return f"跳过 {mask_path.name} - 无法读取文件"
        
        # 创建多标签掩码 (H, W, 3)
        height, width = mask.shape
        multilabel_mask = np.zeros((height, width, 3), dtype=np.float32)
        
        # 转换掩码
        # 背景通道 (channel 0)
        multilabel_mask[:, :, 0] = (mask == 0).astype(np.float32)
        
        # 主轨道通道 (channel 1)
        multilabel_mask[:, :, 1] = (mask == 85).astype(np.float32)
        
        # 分叉轨道通道 (channel 2)
        multilabel_mask[:, :, 2] = (mask == 170).astype(np.float32)
        
        # 保存为.npy文件
        output_filename = mask_path.stem + '.npy'
        output_path = output_dir / output_filename
        np.save(output_path, multilabel_mask)
        
        # 计算统计信息
        main_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        overlap_pixels = np.sum(np.logical_and(multilabel_mask[:, :, 1] > 0, 
                                               multilabel_mask[:, :, 2] > 0))
        
        return {
            'file': mask_path.name,
            'main_pixels': main_pixels,
            'fork_pixels': fork_pixels,
            'overlap_pixels': overlap_pixels,
            'status': 'success'
        }
        
    except Exception as e:
        return {
            'file': mask_path.name,
            'status': 'error',
            'error': str(e)
        }


def convert_masks(data_dir: Path, output_dir: Path = None, num_workers: int = None, sample_size: int = None):
    """转换所有掩码文件"""
    
    # 设置工作进程数
    if num_workers is None:
        num_workers = min(multiprocessing.cpu_count() - 1, 8)
    
    print(f"使用 {num_workers} 个工作进程")
    
    # 如果没有指定输出目录，使用相同目录
    if output_dir is None:
        output_dir = data_dir
    else:
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找所有掩码文件
    mask_files = []
    for split in ['train', 'val', 'test']:
        split_mask_dir = data_dir / split / 'masks'
        if split_mask_dir.exists():
            split_output_dir = output_dir / split / 'masks'
            split_output_dir.mkdir(parents=True, exist_ok=True)
            
            for mask_file in split_mask_dir.glob('*.png'):
                mask_files.append((mask_file, split_output_dir))
    
    print(f"找到 {len(mask_files)} 个PNG掩码文件")
    
    # 如果指定了样本大小，则随机采样
    if sample_size is not None and sample_size < len(mask_files):
        import random
        mask_files = random.sample(mask_files, sample_size)
        print(f"随机采样 {sample_size} 个文件进行处理")
    
    # 准备参数
    args_list = mask_files
    
    # 统计信息
    total_main = 0
    total_fork = 0
    total_overlap = 0
    success_count = 0
    error_count = 0
    files_with_fork = 0
    files_with_overlap = 0
    
    # 并行处理
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        # 提交所有任务
        future_to_file = {executor.submit(process_single_mask, args): args[0] 
                          for args in args_list}
        
        # 处理完成的任务
        with tqdm(total=len(mask_files), desc="转换掩码") as pbar:
            for future in as_completed(future_to_file):
                result = future.result()
                
                if isinstance(result, dict) and result.get('status') == 'success':
                    success_count += 1
                    total_main += result['main_pixels']
                    total_fork += result['fork_pixels']
                    total_overlap += result['overlap_pixels']
                    
                    if result['fork_pixels'] > 0:
                        files_with_fork += 1
                    if result['overlap_pixels'] > 0:
                        files_with_overlap += 1
                else:
                    error_count += 1
                    if isinstance(result, dict):
                        print(f"\n错误: {result.get('file', 'unknown')} - {result.get('error', 'unknown error')}")
                
                pbar.update(1)
    
    # 打印统计信息
    print("\n=== 转换完成 ===")
    print(f"成功: {success_count} 个文件")
    print(f"错误: {error_count} 个文件")
    print(f"包含分叉轨道的文件: {files_with_fork} ({files_with_fork/success_count*100:.1f}%)")
    print(f"包含重叠区域的文件: {files_with_overlap} ({files_with_overlap/success_count*100:.1f}%)")
    
    if success_count > 0:
        avg_overlap = total_overlap / success_count
        print(f"\n平均每个文件:")
        print(f"  主轨道像素: {total_main/success_count:.0f}")
        print(f"  分叉轨道像素: {total_fork/success_count:.0f}")
        print(f"  重叠像素: {avg_overlap:.0f}")
    
    return success_count, error_count


def verify_masks(data_dir: Path, num_samples: int = 5):
    """验证生成的掩码"""
    print("\n=== 验证掩码 ===")
    
    mask_files = []
    for split in ['train', 'val', 'test']:
        split_mask_dir = data_dir / split / 'masks'
        if split_mask_dir.exists():
            mask_files.extend(list(split_mask_dir.glob('*.npy')))
    
    if not mask_files:
        print("未找到任何.npy掩码文件")
        return
    
    # 随机采样
    import random
    sample_files = random.sample(mask_files, min(num_samples, len(mask_files)))
    
    for mask_file in sample_files:
        try:
            mask = np.load(mask_file)
            print(f"\n{mask_file.relative_to(data_dir)}:")
            print(f"  形状: {mask.shape}")
            
            if len(mask.shape) == 3 and mask.shape[2] == 3:
                # 多标签掩码
                bg_pixels = np.sum(mask[:, :, 0] > 0)
                main_pixels = np.sum(mask[:, :, 1] > 0)
                fork_pixels = np.sum(mask[:, :, 2] > 0)
                overlap_pixels = np.sum(np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0))
                
                total_pixels = mask.shape[0] * mask.shape[1]
                print(f"  背景: {bg_pixels} ({bg_pixels/total_pixels*100:.1f}%)")
                print(f"  主轨道: {main_pixels} ({main_pixels/total_pixels*100:.1f}%)")
                print(f"  分叉轨道: {fork_pixels} ({fork_pixels/total_pixels*100:.1f}%)")
                print(f"  重叠区域: {overlap_pixels} ({overlap_pixels/total_pixels*100:.1f}%)")
                print(f"  ✓ 多标签格式正确")
            else:
                print(f"  ✗ 不是多标签格式")
        except Exception as e:
            print(f"\n{mask_file.relative_to(data_dir)}:")
            print(f"  ✗ 读取失败: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description='将PNG掩码转换为多标签格式')
    parser.add_argument('--data-dir', type=str, default='/home/<USER>/data/railway_track_dataset',
                        help='数据目录路径')
    parser.add_argument('--output-dir', type=str, default=None,
                        help='输出目录路径（默认覆盖原文件）')
    parser.add_argument('--num-workers', type=int, default=None,
                        help='并行工作进程数')
    parser.add_argument('--verify', action='store_true',
                        help='生成后验证掩码')
    parser.add_argument('--verify-only', action='store_true',
                        help='只验证，不生成')
    parser.add_argument('--sample', type=int, default=None,
                        help='只处理指定数量的样本文件')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"错误：数据目录不存在: {data_dir}")
        return
    
    output_dir = Path(args.output_dir) if args.output_dir else None
    
    if args.verify_only:
        # 只验证
        verify_masks(data_dir if output_dir is None else output_dir)
    else:
        # 转换掩码
        print("=== 开始转换PNG掩码为多标签格式 ===")
        print(f"数据目录: {data_dir}")
        if output_dir:
            print(f"输出目录: {output_dir}")
        else:
            print("输出目录: 覆盖原目录")
        print("注意：这将生成新的.npy文件！")
        
        response = input("\n确定要继续吗？(y/n): ")
        if response.lower() != 'y':
            print("已取消")
            return
        
        success_count, error_count = convert_masks(
            data_dir, 
            output_dir,
            args.num_workers,
            args.sample
        )
        
        if args.verify and success_count > 0:
            verify_masks(data_dir if output_dir is None else output_dir)
        
        print("\n完成！")
        print(f"成功处理 {success_count} 个文件")
        if error_count > 0:
            print(f"失败 {error_count} 个文件")


if __name__ == '__main__':
    main()