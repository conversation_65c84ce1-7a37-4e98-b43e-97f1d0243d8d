#!/usr/bin/env python3
"""
集成预测脚本
使用训练好的多个模型进行集成预测
"""

import sys
import os
import torch
import torch.nn as nn
import numpy as np
import cv2
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
from tqdm import tqdm
import segmentation_models_pytorch as smp
import albumentations as A
from albumentations.pytorch import ToTensorV2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.ensemble import EnsemblePredictor


class RailwayEnsemblePredictor:
    """铁路轨道集成预测器"""
    
    def __init__(self, 
                 weights_dir: str,
                 config_path: str,
                 device: Optional[str] = None):
        """
        初始化集成预测器
        
        Args:
            weights_dir: 权重文件目录
            config_path: 配置文件路径
            device: 设备（'cuda' 或 'cpu'）
        """
        self.weights_dir = Path(weights_dir)
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        if device is None:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.device = torch.device(device)
        
        # 模型配置（与训练脚本保持一致）
        self.models_config = [
            {
                'name': 'efficientnet_b4',
                'architecture': 'pan',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': 'noisy-student'
            },
            {
                'name': 'eca_nfnet_l2',
                'architecture': 'pan', 
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': 'imagenet'
            },
            {
                'name': 'seresnet152d',
                'architecture': 'pan',
                'encoder': 'tu-seresnet152d', 
                'encoder_weights': 'imagenet'
            }
        ]
        
        self.num_classes = self.config['model']['classes']
        
        # 类别颜色映射（铁路轨道分割）
        self.class_colors = {
            0: [0, 0, 0],      # 背景 - 黑色
            1: [255, 0, 0],    # 主轨道 - 红色  
            2: [0, 255, 0],    # 分叉轨道 - 绿色
        }
        
        # 预处理变换
        self.transforms = A.Compose([
            A.Resize(height=544, width=960),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        # 加载模型和权重
        self.ensemble_predictor = self._load_ensemble_models()
        
        # 多标签预测结果
        self.last_multilabel_prediction = None
        
    def _create_single_model(self, model_config: Dict[str, Any]) -> nn.Module:
        """
        创建单个模型
        
        Args:
            model_config: 模型配置
            
        Returns:
            模型实例
        """
        # 通用参数
        params = {
            'encoder_name': model_config['encoder'],
            'encoder_weights': None,  # 不使用预训练权重，我们会加载自己的权重
            'in_channels': 3,
            'classes': self.num_classes,
            'activation': None  # 不使用激活函数
        }
        
        # 根据架构创建模型
        if model_config['architecture'].lower() == 'pan':
            model = smp.PAN(**params)
        elif model_config['architecture'].lower() == 'unet':
            model = smp.Unet(**params)
        elif model_config['architecture'].lower() == 'fpn':
            model = smp.FPN(**params)
        else:
            raise ValueError(f"不支持的架构: {model_config['architecture']}")
        
        return model
    
    def _load_ensemble_models(self) -> EnsemblePredictor:
        """
        加载集成模型
        
        Returns:
            集成预测器
        """
        print("加载集成模型...")
        
        # 加载各个模型
        models = []
        for model_config in self.models_config:
            model = self._create_single_model(model_config)
            
            # 🔧 修复文件路径构建逻辑 - 处理实际文件名
            model_name = model_config['name']
            
            # 处理模型名称映射
            name_mapping = {
                'efficientnet_b4': 'efficientnetb4',  # 移除下划线
                'eca_nfnet_l2': 'eca_nfnet_l2',      # 保持原样
                'seresnet152d': 'seresnet152d'        # 保持原样
            }
            
            actual_model_name = name_mapping.get(model_name, model_name)
            
            # 尝试多种可能的文件格式
            possible_paths = [
                self.weights_dir / f"{actual_model_name}.pth.tar",  # 实际格式
                self.weights_dir / f"{model_name}.pth.tar",         # 原始名称+.pth.tar
                self.weights_dir / f"{actual_model_name}.pth",      # 无.tar
                self.weights_dir / f"{model_name}.pth"              # 原始格式
            ]
            
            weights_path = None
            for path in possible_paths:
                if path.exists():
                    weights_path = path
                    break
            
            if weights_path is None:
                print(f"❌ 找不到模型权重文件: {model_name}")
                print("🔍 查找了以下路径:")
                for path in possible_paths:
                    print(f"   - {path}")
                
                # 列出目录中的实际文件
                if self.weights_dir.exists():
                    print(f"\n📁 {self.weights_dir} 目录中的实际文件:")
                    for file in self.weights_dir.glob("*.pth*"):
                        print(f"   - {file.name}")
                
                raise FileNotFoundError(f"权重文件不存在: {model_name}")
            
            print(f"✅ 加载权重: {weights_path}")
            
            # 🔧 处理不同的模型文件格式
            try:
                # 先尝试直接加载完整模型（.pth.tar格式）
                checkpoint = torch.load(weights_path, map_location=self.device, weights_only=False)
                
                if hasattr(checkpoint, 'eval'):
                    # 这是完整的模型对象
                    model = checkpoint
                    print(f"   📦 加载完整模型对象")
                else:
                    # 这是状态字典
                    state_dict = checkpoint
                    
                    # 处理可能的键名不匹配
                    if isinstance(state_dict, dict):
                        if 'model' in state_dict:
                            state_dict = state_dict['model']
                        elif 'state_dict' in state_dict:
                            state_dict = state_dict['state_dict']
                        elif 'model_state_dict' in state_dict:
                            state_dict = state_dict['model_state_dict']
                    
                    model.load_state_dict(state_dict, strict=False)
                    print(f"   📦 加载状态字典")
                    
            except Exception as e:
                print(f"❌ 加载模型失败: {e}")
                raise
            model.to(self.device)
            model.eval()
            models.append(model)
        
        # 加载集成权重
        ensemble_weights_path = self.weights_dir / 'ensemble_weights.yaml'
        if ensemble_weights_path.exists():
            print(f"加载集成权重: {ensemble_weights_path}")
            with open(ensemble_weights_path, 'r', encoding='utf-8') as f:
                weights_data = yaml.safe_load(f)
            
            # 构建权重张量
            fusion_weights = torch.zeros(self.num_classes, len(models))
            for class_idx, weight_info in weights_data.items():
                if isinstance(class_idx, str):
                    class_idx = int(class_idx)
                fusion_weights[class_idx] = torch.tensor(weight_info['weights'])
        else:
            print("未找到集成权重文件，使用均等权重")
            fusion_weights = torch.ones(self.num_classes, len(models)) / len(models)
        
        # 创建集成预测器
        ensemble_predictor = EnsemblePredictor(models, fusion_weights, self.device)
        
        print(f"集成模型加载完成，包含 {len(models)} 个子模型")
        return ensemble_predictor
    
    def predict_image(self, 
                     image_path: str,
                     output_path: Optional[str] = None,
                     threshold: float = 0.5,
                     save_overlay: bool = True) -> np.ndarray:
        """
        对单张图像进行预测
        
        Args:
            image_path: 输入图像路径
            output_path: 输出路径（可选）
            threshold: 分割阈值
            save_overlay: 是否保存叠加图像
            
        Returns:
            预测掩码
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        original_height, original_width = image_rgb.shape[:2]
        
        # 预处理
        transformed = self.transforms(image=image_rgb)
        input_tensor = transformed['image'].unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            prediction = self.ensemble_predictor.predict(input_tensor)
            prediction = prediction.squeeze(0).cpu().numpy()  # (C, H, W)
        
        # 后处理
        prediction_mask = self._post_process_prediction(
            prediction, threshold, (original_height, original_width)
        )
        
        # 保存结果
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存分割掩码
            mask_colored = self._colorize_mask(prediction_mask)
            cv2.imwrite(str(output_path), mask_colored)
            
            # 保存叠加图像
            if save_overlay:
                overlay_path = output_path.parent / f"{output_path.stem}_overlay{output_path.suffix}"
                overlay = self._create_overlay(image_rgb, prediction_mask, alpha=0.6)
                overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(overlay_path), overlay_bgr)
                
                # 保存多标签预测结果（每个类别单独保存）
                if self.last_multilabel_prediction is not None:
                    for class_idx in range(self.num_classes):
                        class_name = ['background', 'main_track', 'fork_track'][class_idx]
                        multilabel_path = output_path.parent / f"{output_path.stem}_{class_name}{output_path.suffix}"
                        class_mask = self.last_multilabel_prediction[class_idx] * 255
                        cv2.imwrite(str(multilabel_path), class_mask)
        
        return prediction_mask
    
    def predict_batch(self,
                     image_dir: str,
                     output_dir: str,
                     threshold: float = 0.5,
                     save_overlay: bool = True) -> Dict[str, Dict[str, float]]:
        """
        批量预测
        
        Args:
            image_dir: 输入图像目录
            output_dir: 输出目录
            threshold: 分割阈值
            save_overlay: 是否保存叠加图像
            
        Returns:
            预测统计信息
        """
        image_dir = Path(image_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = [f for f in image_dir.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        if not image_files:
            print(f"在目录 {image_dir} 中未找到图像文件")
            return {}
        
        print(f"找到 {len(image_files)} 张图像，开始批量预测...")
        
        # 预测统计
        predictions_stats = {}
        
        for image_file in tqdm(image_files, desc="预测进度"):
            try:
                # 预测
                output_path = output_dir / f"{image_file.stem}_pred.png"
                prediction_mask = self.predict_image(
                    str(image_file), str(output_path), threshold, save_overlay
                )
                
                # 统计预测结果
                stats = self._calculate_prediction_stats(prediction_mask)
                predictions_stats[image_file.name] = stats
                
            except Exception as e:
                print(f"预测图像 {image_file} 时出错: {e}")
                continue
        
        # 保存统计信息
        stats_path = output_dir / 'prediction_stats.yaml'
        with open(stats_path, 'w', encoding='utf-8') as f:
            yaml.dump(predictions_stats, f, default_flow_style=False, allow_unicode=True)
        
        print(f"批量预测完成，结果保存在: {output_dir}")
        return predictions_stats
    
    def _post_process_prediction(self, 
                               prediction: np.ndarray,
                               threshold: float,
                               original_size: tuple) -> np.ndarray:
        """
        后处理预测结果 - 多标签方式
        
        Args:
            prediction: 原始预测 (C, H, W)
            threshold: 阈值
            original_size: 原始尺寸 (H, W)
            
        Returns:
            后处理后的掩码 (H, W) 或 (C, H, W) for multi-label
        """
        # 调整尺寸
        prediction_resized = np.zeros((self.num_classes, original_size[0], original_size[1]))
        for i in range(self.num_classes):
            prediction_resized[i] = cv2.resize(
                prediction[i], 
                (original_size[1], original_size[0]), 
                interpolation=cv2.INTER_LINEAR
            )
        
        # 多标签处理：每个类别独立判断
        prediction_binary = (prediction_resized > threshold).astype(np.uint8)
        
        # 为了兼容现有的可视化代码，我们需要生成一个单通道掩码
        # 优先级：分叉轨道 > 主轨道 > 背景
        final_mask = np.zeros((original_size[0], original_size[1]), dtype=np.uint8)
        
        # 背景（类别0）
        final_mask[prediction_binary[0] > 0] = 0
        
        # 主轨道（类别1）
        final_mask[prediction_binary[1] > 0] = 1
        
        # 分叉轨道（类别2） - 最高优先级
        final_mask[prediction_binary[2] > 0] = 2
        
        # 保存多标签结果以供后续使用
        self.last_multilabel_prediction = prediction_binary
        
        return final_mask
    
    def _colorize_mask(self, mask: np.ndarray) -> np.ndarray:
        """
        给掩码上色
        
        Args:
            mask: 分割掩码 (H, W)
            
        Returns:
            彩色掩码 (H, W, 3)
        """
        colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        
        for class_id, color in self.class_colors.items():
            colored_mask[mask == class_id] = color
        
        return colored_mask
    
    def _create_overlay(self, 
                       image: np.ndarray, 
                       mask: np.ndarray, 
                       alpha: float = 0.6) -> np.ndarray:
        """
        创建叠加图像
        
        Args:
            image: 原始图像 (H, W, 3)
            mask: 分割掩码 (H, W)
            alpha: 透明度
            
        Returns:
            叠加图像 (H, W, 3)
        """
        colored_mask = self._colorize_mask(mask)
        
        # 只在非背景区域叠加
        overlay = image.copy()
        non_background = mask > 0
        overlay[non_background] = (
            alpha * colored_mask[non_background] + 
            (1 - alpha) * image[non_background]
        ).astype(np.uint8)
        
        return overlay
    
    def _calculate_prediction_stats(self, mask: np.ndarray) -> Dict[str, float]:
        """
        计算预测统计信息
        
        Args:
            mask: 预测掩码
            
        Returns:
            统计信息字典
        """
        total_pixels = mask.shape[0] * mask.shape[1]
        stats = {'total_pixels': total_pixels}
        
        for class_id in range(self.num_classes):
            class_pixels = np.sum(mask == class_id)
            class_ratio = class_pixels / total_pixels
            
            class_name = ['background', 'main_track', 'fork_track'][class_id]
            stats[f'{class_name}_pixels'] = int(class_pixels)
            stats[f'{class_name}_ratio'] = float(class_ratio)
        
        return stats
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'num_models': len(self.models_config),
            'model_names': [cfg['name'] for cfg in self.models_config],
            'num_classes': self.num_classes,
            'device': str(self.device),
            'weights_dir': str(self.weights_dir)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='集成预测脚本')
    parser.add_argument('--weights-dir', type=str, required=True,
                       help='权重文件目录')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--input', type=str, required=True,
                       help='输入图像文件或目录')
    parser.add_argument('--output', type=str, required=True,
                       help='输出目录')
    parser.add_argument('--threshold', type=float, default=0.5,
                       help='分割阈值')
    parser.add_argument('--device', type=str, choices=['cuda', 'cpu'],
                       help='计算设备')
    parser.add_argument('--no-overlay', action='store_true',
                       help='不保存叠加图像')
    
    args = parser.parse_args()
    
    # 创建预测器
    print("初始化集成预测器...")
    predictor = RailwayEnsemblePredictor(
        weights_dir=args.weights_dir,
        config_path=args.config,
        device=args.device
    )
    
    # 打印模型信息
    model_info = predictor.get_model_info()
    print("\n=== 模型信息 ===")
    for key, value in model_info.items():
        print(f"{key}: {value}")
    
    # 执行预测
    input_path = Path(args.input)
    
    if input_path.is_file():
        # 单张图像预测
        print(f"\n对单张图像进行预测: {input_path}")
        output_path = Path(args.output) / f"{input_path.stem}_pred.png"
        
        prediction_mask = predictor.predict_image(
            str(input_path), 
            str(output_path), 
            args.threshold,
            not args.no_overlay
        )
        
        # 打印统计信息
        stats = predictor._calculate_prediction_stats(prediction_mask)
        print("\n=== 预测统计 ===")
        for key, value in stats.items():
            print(f"{key}: {value}")
        
        print(f"预测结果已保存到: {output_path}")
        
    elif input_path.is_dir():
        # 批量预测
        print(f"\n对目录进行批量预测: {input_path}")
        
        predictions_stats = predictor.predict_batch(
            str(input_path),
            args.output,
            args.threshold,
            not args.no_overlay
        )
        
        # 打印总体统计
        if predictions_stats:
            print("\n=== 批量预测统计 ===")
            print(f"成功预测图像数量: {len(predictions_stats)}")
            
            # 计算平均统计
            avg_stats = {}
            for stats in predictions_stats.values():
                for key, value in stats.items():
                    if isinstance(value, (int, float)):
                        if key not in avg_stats:
                            avg_stats[key] = []
                        avg_stats[key].append(value)
            
            print("\n平均统计:")
            for key, values in avg_stats.items():
                if 'ratio' in key:
                    print(f"{key}: {np.mean(values):.4f}")
        
        print(f"批量预测完成，结果保存在: {args.output}")
        
    else:
        print(f"错误：输入路径不存在或不是文件/目录: {input_path}")


if __name__ == '__main__':
    main() 