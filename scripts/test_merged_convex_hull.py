#!/usr/bin/env python
"""
测试合并轨道凸包生成脚本
验证左右轨道关键点合并生成统一凸多边形的功能
"""

import argparse
import numpy as np
import cv2
from pathlib import Path
import json
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import sys

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def visualize_merged_convex_hull(json_file: Path, output_dir: Path):
    """可视化合并轨道凸包生成过程"""
    print(f"\n=== 测试合并轨道凸包生成 ===")
    print(f"JSON文件: {json_file.name}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  相机类型: {annotation_data['camera_type']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 分别收集主轨道和分叉轨道的点
    main_points = []
    fork_points = []
    
    for track in annotation_data['tracks']:
        points_count = len(track['points'])
        print(f"    {track['label']}: {points_count} 个关键点")
        
        if track['label'] in ['Main_Left', 'Main_Right']:
            main_points.extend(track['points'])
        elif track['label'] in ['Fork_Left', 'Fork_Right']:
            fork_points.extend(track['points'])
    
    print(f"\n合并后:")
    print(f"  主轨道总关键点: {len(main_points)}")
    print(f"  分叉轨道总关键点: {len(fork_points)}")
    
    # 设置图像尺寸
    image_shape = (1080, 1920)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 1. 显示分离的轨道点
    vis_img = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
    
    # 绘制主轨道点（红色）
    if main_points:
        main_points_int = np.array([(int(x), int(y)) for x, y in main_points], dtype=np.int32)
        for point in main_points_int:
            cv2.circle(vis_img, tuple(point), 6, (0, 0, 255), -1)  # 红色
    
    # 绘制分叉轨道点（蓝色）
    if fork_points:
        fork_points_int = np.array([(int(x), int(y)) for x, y in fork_points], dtype=np.int32)
        for point in fork_points_int:
            cv2.circle(vis_img, tuple(point), 6, (255, 0, 0), -1)  # 蓝色
    
    axes[0, 0].imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title(f'原始关键点\n红色=主轨道({len(main_points)}点)\n蓝色=分叉轨道({len(fork_points)}点)')
    axes[0, 0].axis('off')
    
    # 2. 显示主轨道凸包
    if len(main_points) >= 3:
        main_hull = cv2.convexHull(main_points_int)
        print(f"  主轨道凸包顶点: {len(main_hull)}")
        
        main_vis = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        
        # 绘制原始点
        for point in main_points_int:
            cv2.circle(main_vis, tuple(point), 6, (0, 255, 0), -1)  # 绿色点
        
        # 绘制凸包边界
        cv2.polylines(main_vis, [main_hull], True, (255, 255, 0), 3)  # 黄色边界
        
        # 填充凸包
        main_filled = main_vis.copy()
        cv2.fillPoly(main_filled, [main_hull], (255, 0, 0))  # 红色填充
        
        axes[0, 1].imshow(cv2.cvtColor(main_vis, cv2.COLOR_BGR2RGB))
        axes[0, 1].set_title(f'主轨道凸包边界\n{len(main_points)}点 → {len(main_hull)}顶点')
        axes[0, 1].axis('off')
        
        axes[1, 0].imshow(cv2.cvtColor(main_filled, cv2.COLOR_BGR2RGB))
        axes[1, 0].set_title('主轨道填充凸包')
        axes[1, 0].axis('off')
    else:
        axes[0, 1].text(0.5, 0.5, '主轨道点不足\n无法生成凸包', 
                       ha='center', va='center', transform=axes[0, 1].transAxes)
        axes[0, 1].axis('off')
        
        axes[1, 0].text(0.5, 0.5, '主轨道点不足\n无法生成凸包', 
                       ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 0].axis('off')
    
    # 3. 显示分叉轨道凸包（如果存在）
    if len(fork_points) >= 3:
        fork_hull = cv2.convexHull(fork_points_int)
        print(f"  分叉轨道凸包顶点: {len(fork_hull)}")
        
        fork_vis = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        
        # 绘制原始点
        for point in fork_points_int:
            cv2.circle(fork_vis, tuple(point), 6, (0, 255, 0), -1)  # 绿色点
        
        # 绘制凸包边界
        cv2.polylines(fork_vis, [fork_hull], True, (255, 255, 0), 3)  # 黄色边界
        
        # 填充凸包
        fork_filled = fork_vis.copy()
        cv2.fillPoly(fork_filled, [fork_hull], (0, 0, 255))  # 蓝色填充
        
        axes[0, 2].imshow(cv2.cvtColor(fork_vis, cv2.COLOR_BGR2RGB))
        axes[0, 2].set_title(f'分叉轨道凸包边界\n{len(fork_points)}点 → {len(fork_hull)}顶点')
        axes[0, 2].axis('off')
        
        axes[1, 1].imshow(cv2.cvtColor(fork_filled, cv2.COLOR_BGR2RGB))
        axes[1, 1].set_title('分叉轨道填充凸包')
        axes[1, 1].axis('off')
    else:
        axes[0, 2].text(0.5, 0.5, '无分叉轨道', 
                       ha='center', va='center', transform=axes[0, 2].transAxes)
        axes[0, 2].axis('off')
        
        axes[1, 1].text(0.5, 0.5, '无分叉轨道', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].axis('off')
    
    # 4. 生成完整的分割掩码
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    merged_mask = np.argmax(mask, axis=2)
    
    # 自定义颜色映射
    colors = ['black', 'green', 'red']
    cmap = ListedColormap(colors[:len(np.unique(merged_mask))])
    
    axes[1, 2].imshow(merged_mask, cmap=cmap, vmin=0, vmax=2)
    axes[1, 2].set_title('最终合并掩码\n(绿色=主轨道凸包, 红色=分叉轨道凸包)')
    axes[1, 2].axis('off')
    
    plt.suptitle(f'合并轨道凸包生成: {json_file.name}', fontsize=16)
    plt.tight_layout()
    
    # 保存结果
    output_path = output_dir / f'merged_convex_hull_{json_file.stem}.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果保存到: {output_path}")
    
    # 统计最终掩码
    unique_vals, counts = np.unique(merged_mask, return_counts=True)
    print(f"\n最终掩码统计:")
    for val, count in zip(unique_vals, counts):
        if val == 0:
            print(f"  背景: {count} 像素")
        elif val == 1:
            print(f"  主轨道: {count} 像素")
        elif val == 2:
            print(f"  分叉轨道: {count} 像素")
    
    return merged_mask, annotation_data


def compare_separate_vs_merged(json_file: Path, output_dir: Path):
    """对比分离轨道vs合并轨道的效果"""
    print(f"\n=== 对比分离vs合并轨道 ===")
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    image_shape = (1080, 1920)
    parser = RailwayAnnotationParser()
    
    # 方法1：分离处理（旧方法模拟）
    separate_mask = np.zeros(image_shape, dtype=np.uint8)
    
    for label_data in data['labels']:
        if label_data['label'] in ['Main_Left', 'Main_Right']:
            points = [(int(label_data['points'][i]), int(label_data['points'][i+1])) 
                     for i in range(0, len(label_data['points']), 2)]
            
            if len(points) >= 3:
                points_array = np.array(points, dtype=np.int32)
                hull = cv2.convexHull(points_array)
                cv2.fillPoly(separate_mask, [hull], 1)
    
    # 方法2：合并处理（新方法）
    annotation_data = parser.parse_json_file(json_file)
    merged_mask_full = parser.create_segmentation_mask(annotation_data, image_shape)
    merged_mask = np.argmax(merged_mask_full, axis=2)
    
    # 创建对比可视化
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 分离方法
    axes[0].imshow(separate_mask, cmap='gray')
    axes[0].set_title(f'分离轨道凸包\n非零像素: {np.count_nonzero(separate_mask)}')
    axes[0].axis('off')
    
    # 合并方法
    colors = ['black', 'green', 'red']
    cmap = ListedColormap(colors)
    axes[1].imshow(merged_mask, cmap=cmap, vmin=0, vmax=2)
    axes[1].set_title(f'合并轨道凸包\n非零像素: {np.count_nonzero(merged_mask)}')
    axes[1].axis('off')
    
    # 差异
    merged_binary = (merged_mask > 0).astype(np.uint8)
    diff = np.abs(separate_mask.astype(int) - merged_binary.astype(int))
    axes[2].imshow(diff, cmap='hot')
    axes[2].set_title(f'差异\n不同像素: {np.count_nonzero(diff)}')
    axes[2].axis('off')
    
    plt.suptitle('对比：分离轨道凸包 vs 合并轨道凸包', fontsize=16)
    plt.tight_layout()
    
    comparison_path = output_dir / f'separate_vs_merged_{json_file.stem}.png'
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"对比结果保存到: {comparison_path}")
    
    # 输出统计
    print(f"\n对比统计:")
    print(f"  分离方法非零像素: {np.count_nonzero(separate_mask)}")
    print(f"  合并方法非零像素: {np.count_nonzero(merged_mask)}")
    coverage_change = (np.count_nonzero(merged_mask) / max(np.count_nonzero(separate_mask), 1) - 1) * 100
    print(f"  覆盖面积变化: {coverage_change:.1f}%")


def main():
    parser = argparse.ArgumentParser(description='测试合并轨道凸包生成')
    parser.add_argument('--json-dir', type=str,
                       default='data/railway_annotation_6mm',
                       help='JSON标注目录')
    parser.add_argument('--output-dir', type=str,
                       default='outputs/merged_convex_test',
                       help='输出目录')
    parser.add_argument('--test-single', action='store_true',
                       help='只测试单个文件')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    json_dir = Path(args.json_dir)
    
    print("合并轨道凸包测试开始...")
    print(f"JSON目录: {json_dir}")
    print(f"输出目录: {output_dir}")
    
    json_files = list(json_dir.glob('*.json'))
    if len(json_files) == 0:
        print("错误：没有找到JSON文件")
        return
    
    if args.test_single:
        # 测试单个文件
        json_file = json_files[0]
        visualize_merged_convex_hull(json_file, output_dir)
        compare_separate_vs_merged(json_file, output_dir)
    else:
        # 测试前3个文件
        for json_file in json_files[:3]:
            print(f"\n处理文件: {json_file.name}")
            try:
                visualize_merged_convex_hull(json_file, output_dir)
            except Exception as e:
                print(f"处理 {json_file.name} 时出错: {e}")
    
    print(f"\n测试完成！结果保存在: {output_dir}")


if __name__ == '__main__':
    main() 