#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分叉轨道掩码生成脚本
"""

import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_fork_mask_generation(json_file_path: str):
    """测试分叉轨道掩码生成"""
    print(f"测试文件: {json_file_path}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 解析JSON文件
    annotation_data = parser.parse_json_file(json_file_path)
    
    print(f"解析结果:")
    print(f"  文件名: {annotation_data['filename']}")
    print(f"  轨道数量: {len(annotation_data['tracks'])}")
    
    # 分析轨道类型
    main_tracks = []
    fork_tracks = []
    
    for track in annotation_data['tracks']:
        print(f"  轨道: {track['label']}, 点数: {len(track['points'])}")
        if track['label'] in ['Main_Left', 'Main_Right']:
            main_tracks.append(track)
        elif track['label'] in ['Fork_Left', 'Fork_Right']:
            fork_tracks.append(track)
    
    print(f"\n轨道统计:")
    print(f"  主轨道: {len(main_tracks)} 条")
    print(f"  分叉轨道: {len(fork_tracks)} 条")
    
    # 生成掩码
    image_shape = (1080, 1920)
    mask = parser.create_segmentation_mask(annotation_data, image_shape)
    
    # 分析掩码
    unique_labels = np.unique(mask)
    print(f"\n掩码分析:")
    print(f"  掩码形状: {mask.shape}")
    print(f"  唯一标签: {unique_labels}")
    
    for label in unique_labels:
        count = np.sum(mask == label)
        percentage = count / mask.size * 100
        if label == 0:
            label_name = "背景"
        elif label == 1:
            label_name = "主轨道"
        elif label == 2:
            label_name = "分叉轨道"
        else:
            label_name = f"标签{label}"
        print(f"  {label_name}(标签{label}): {count} 像素 ({percentage:.2f}%)")
    
    # 可视化
    plt.figure(figsize=(15, 5))
    
    # 1. 原始掩码
    plt.subplot(1, 3, 1)
    plt.imshow(mask, cmap='viridis', vmin=0, vmax=2)
    plt.title('原始掩码')
    plt.colorbar()
    
    # 2. 主轨道
    plt.subplot(1, 3, 2)
    main_mask = (mask == 1).astype(np.uint8) * 255
    plt.imshow(main_mask, cmap='Greens')
    plt.title(f'主轨道 ({np.sum(mask == 1)} 像素)')
    
    # 3. 分叉轨道
    plt.subplot(1, 3, 3)
    fork_mask = (mask == 2).astype(np.uint8) * 255
    plt.imshow(fork_mask, cmap='Reds')
    plt.title(f'分叉轨道 ({np.sum(mask == 2)} 像素)')
    
    plt.tight_layout()
    
    # 保存结果
    output_path = f'outputs/fork_test_{Path(json_file_path).stem}.png'
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n可视化结果保存到: {output_path}")
    
    # 检查是否有分叉轨道
    if 2 in unique_labels:
        print("✅ 成功生成分叉轨道掩码")
    else:
        print("❌ 未生成分叉轨道掩码")
        if len(fork_tracks) > 0:
            print("⚠️  存在分叉轨道标注但未生成掩码，可能是算法问题")


if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("用法: python test_fork_mask.py <json_file_path>")
        sys.exit(1)
    
    json_file_path = sys.argv[1]
    test_fork_mask_generation(json_file_path) 