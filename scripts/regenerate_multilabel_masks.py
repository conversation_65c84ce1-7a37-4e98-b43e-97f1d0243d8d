#!/usr/bin/env python3
"""
重新生成多标签掩码
不改变数据集划分，不重新读写图像，只更新标签文件
"""

import sys
import json
import numpy as np
from pathlib import Path
from tqdm import tqdm
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def process_single_file(args):
    """处理单个文件的标签生成"""
    json_path, mask_dir, parser = args
    
    try:
        # 读取JSON标注
        with open(json_path, 'r', encoding='utf-8') as f:
            annotation_data = json.load(f)
        
        # 获取对应的掩码文件路径
        mask_filename = json_path.stem + '_mask.png'
        mask_path = mask_dir / mask_filename
        
        # 检查是否存在旧的掩码文件
        if not mask_path.exists():
            return f"跳过 {json_path.name} - 掩码文件不存在"
        
        # 读取旧掩码获取图像尺寸
        old_mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if old_mask is None:
            return f"跳过 {json_path.name} - 无法读取掩码文件"
        if len(old_mask.shape) == 2:
            # 单通道掩码
            image_shape = old_mask.shape
        else:
            # 多通道掩码
            image_shape = old_mask.shape[:2]
        
        # 生成新的多标签掩码
        multilabel_mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
        
        # 保存新掩码（覆盖旧文件） - 使用PNG格式
        bgr_mask = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        bgr_mask[:, :, 0] = (multilabel_mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
        bgr_mask[:, :, 1] = (multilabel_mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
        bgr_mask[:, :, 2] = (multilabel_mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
        cv2.imwrite(str(mask_path), bgr_mask)
        
        # 计算统计信息
        main_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        overlap_pixels = np.sum(np.logical_and(multilabel_mask[:, :, 1] > 0, 
                                               multilabel_mask[:, :, 2] > 0))
        
        return {
            'file': json_path.name,
            'main_pixels': main_pixels,
            'fork_pixels': fork_pixels,
            'overlap_pixels': overlap_pixels,
            'status': 'success'
        }
        
    except Exception as e:
        return {
            'file': json_path.name,
            'status': 'error',
            'error': str(e)
        }


def regenerate_masks(data_dir: Path, mask_subdir: str = 'masks', num_workers: int = None):
    """重新生成所有掩码文件"""
    
    # 设置工作进程数
    if num_workers is None:
        num_workers = min(multiprocessing.cpu_count() - 1, 8)
    
    print(f"使用 {num_workers} 个工作进程")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 获取所有JSON文件
    json_files = list(data_dir.glob('*.json'))
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 掩码目录
    mask_dir = data_dir / mask_subdir
    if not mask_dir.exists():
        print(f"错误：掩码目录不存在: {mask_dir}")
        return
    
    # 准备参数
    args_list = [(json_file, mask_dir, parser) for json_file in json_files]
    
    # 统计信息
    total_main = 0
    total_fork = 0
    total_overlap = 0
    success_count = 0
    error_count = 0
    files_with_fork = 0
    files_with_overlap = 0
    
    # 并行处理
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        # 提交所有任务
        future_to_file = {executor.submit(process_single_file, args): args[0] 
                          for args in args_list}
        
        # 处理完成的任务
        with tqdm(total=len(json_files), desc="重新生成掩码") as pbar:
            for future in as_completed(future_to_file):
                result = future.result()
                
                if isinstance(result, dict) and result.get('status') == 'success':
                    success_count += 1
                    total_main += result['main_pixels']
                    total_fork += result['fork_pixels']
                    total_overlap += result['overlap_pixels']
                    
                    if result['fork_pixels'] > 0:
                        files_with_fork += 1
                    if result['overlap_pixels'] > 0:
                        files_with_overlap += 1
                else:
                    error_count += 1
                    if isinstance(result, dict):
                        print(f"\n错误: {result.get('file', 'unknown')} - {result.get('error', 'unknown error')}")
                
                pbar.update(1)
    
    # 打印统计信息
    print("\n=== 重新生成完成 ===")
    print(f"成功: {success_count} 个文件")
    print(f"错误: {error_count} 个文件")
    print(f"包含分叉轨道的文件: {files_with_fork} ({files_with_fork/success_count*100:.1f}%)")
    print(f"包含重叠区域的文件: {files_with_overlap} ({files_with_overlap/success_count*100:.1f}%)")
    
    if success_count > 0:
        avg_overlap = total_overlap / success_count
        print(f"\n平均每个文件:")
        print(f"  主轨道像素: {total_main/success_count:.0f}")
        print(f"  分叉轨道像素: {total_fork/success_count:.0f}")
        print(f"  重叠像素: {avg_overlap:.0f}")
    
    return success_count, error_count


def verify_masks(data_dir: Path, mask_subdir: str = 'masks', num_samples: int = 5):
    """验证生成的掩码"""
    print("\n=== 验证掩码 ===")
    
    mask_dir = data_dir / mask_subdir
    mask_files = list(mask_dir.glob('*_mask.npy'))
    
    # 随机采样
    import random
    sample_files = random.sample(mask_files, min(num_samples, len(mask_files)))
    
    for mask_file in sample_files:
        mask = np.load(mask_file)
        print(f"\n{mask_file.name}:")
        print(f"  形状: {mask.shape}")
        
        if len(mask.shape) == 3 and mask.shape[2] == 3:
            # 多标签掩码
            bg_pixels = np.sum(mask[:, :, 0] > 0)
            main_pixels = np.sum(mask[:, :, 1] > 0)
            fork_pixels = np.sum(mask[:, :, 2] > 0)
            overlap_pixels = np.sum(np.logical_and(mask[:, :, 1] > 0, mask[:, :, 2] > 0))
            
            total_pixels = mask.shape[0] * mask.shape[1]
            print(f"  背景: {bg_pixels} ({bg_pixels/total_pixels*100:.1f}%)")
            print(f"  主轨道: {main_pixels} ({main_pixels/total_pixels*100:.1f}%)")
            print(f"  分叉轨道: {fork_pixels} ({fork_pixels/total_pixels*100:.1f}%)")
            print(f"  重叠区域: {overlap_pixels} ({overlap_pixels/total_pixels*100:.1f}%)")
            print(f"  ✓ 多标签格式正确")
        else:
            print(f"  ✗ 不是多标签格式")


def main():
    parser = argparse.ArgumentParser(description='重新生成多标签掩码')
    parser.add_argument('--data-dir', type=str, default='data/railway_annotation_6mm',
                        help='数据目录路径')
    parser.add_argument('--mask-subdir', type=str, default='masks',
                        help='掩码子目录名称')
    parser.add_argument('--num-workers', type=int, default=None,
                        help='并行工作进程数')
    parser.add_argument('--verify', action='store_true',
                        help='生成后验证掩码')
    parser.add_argument('--verify-only', action='store_true',
                        help='只验证，不生成')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"错误：数据目录不存在: {data_dir}")
        return
    
    if args.verify_only:
        # 只验证
        verify_masks(data_dir, args.mask_subdir)
    else:
        # 重新生成掩码
        print("=== 开始重新生成多标签掩码 ===")
        print(f"数据目录: {data_dir}")
        print(f"掩码目录: {data_dir / args.mask_subdir}")
        print("注意：这将覆盖现有的掩码文件！")
        
        response = input("\n确定要继续吗？(y/n): ")
        if response.lower() != 'y':
            print("已取消")
            return
        
        success_count, error_count = regenerate_masks(
            data_dir, 
            args.mask_subdir,
            args.num_workers
        )
        
        if args.verify and success_count > 0:
            verify_masks(data_dir, args.mask_subdir)
        
        print("\n完成！")
        print(f"成功处理 {success_count} 个文件")
        if error_count > 0:
            print(f"失败 {error_count} 个文件")


if __name__ == '__main__':
    main()