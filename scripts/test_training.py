#!/usr/bin/env python
"""
训练流程测试脚本
快速验证训练环境和代码是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import tempfile
import shutil
from src.core import load_config
from src.core.registry import MODEL_REGISTRY, LOSS_REGISTRY
from src.data import create_dataloaders
from src.training import Trainer
from src.utils import setup_logger, set_random_seed


def test_training_pipeline():
    """测试完整的训练流水线"""
    print("=" * 60)
    print("训练流程测试")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    print(f"临时目录: {temp_dir}")
    
    try:
        # 1. 测试配置加载
        print("\n1. 测试配置加载...")
        config_path = project_root / 'configs' / 'base_config.yaml'
        if not config_path.exists():
            config_path = project_root / 'configs' / 'railway_track_config.yaml'
        
        config = load_config(config_path)
        print(f"✓ 配置加载成功: {config_path}")
        
        # 修改配置用于快速测试
        config.set('training.epochs', 2)
        config.set('data.batch_size.train', 2)
        config.set('data.batch_size.val', 2)
        config.set('training.save_interval', 1)
        
        # 2. 测试模型创建
        print("\n2. 测试模型创建...")
        model_config = config.model.to_dict()
        model_config['classes'] = 3  # 确保有正确的类别数
        
        model = MODEL_REGISTRY.build(model_config)
        print(f"✓ 模型创建成功: {model_config['architecture']}")
        print(f"  参数量: {model.count_parameters():,}")
        
        # 3. 测试损失函数创建
        print("\n3. 测试损失函数创建...")
        loss_config = config.loss.to_dict()
        
        if loss_config['type'] == 'dice_bce':
            loss_fn = LOSS_REGISTRY.get('bce_dice_loss')(
                bce_weight=loss_config.get('bce_weight', 0.5),
                dice_weight=loss_config.get('dice_weight', 0.5)
            )
        else:
            loss_fn = LOSS_REGISTRY.build(loss_config)
        
        print(f"✓ 损失函数创建成功: {loss_config['type']}")
        
        # 4. 测试数据加载器创建
        print("\n4. 测试数据加载器创建...")
        try:
            dataloaders = create_dataloaders(config.to_dict())
            train_loader = dataloaders['train']
            val_loader = dataloaders['val']
            
            print(f"✓ 数据加载器创建成功")
            print(f"  训练集大小: {len(train_loader.dataset)}")
            print(f"  验证集大小: {len(val_loader.dataset)}")
        except Exception as e:
            print(f"✗ 数据加载器创建失败: {e}")
            print("  创建虚拟数据加载器用于测试...")
            
            # 创建虚拟数据集
            from torch.utils.data import Dataset, DataLoader
            
            class DummyDataset(Dataset):
                def __init__(self, size=10):
                    self.size = size
                
                def __len__(self):
                    return self.size
                
                def __getitem__(self, idx):
                    return {
                        'image': torch.randn(3, 544, 960),
                        'mask': torch.randint(0, 2, (3, 544, 960)).float(),
                        'filename': f'dummy_{idx}.jpg'
                    }
            
            train_loader = DataLoader(DummyDataset(20), batch_size=2, shuffle=True)
            val_loader = DataLoader(DummyDataset(10), batch_size=2, shuffle=False)
            print("✓ 虚拟数据加载器创建成功")
        
        # 5. 测试训练器创建
        print("\n5. 测试训练器创建...")
        set_random_seed(config.project.seed)
        
        logger = setup_logger(
            'test_train',
            temp_dir / 'test_train.log',
            console_level='INFO'
        )
        
        trainer = Trainer(
            model=model,
            config=config.to_dict(),
            train_loader=train_loader,
            val_loader=val_loader,
            loss_fn=loss_fn,
            output_dir=temp_dir,
            logger=logger
        )
        print("✓ 训练器创建成功")
        
        # 6. 测试训练一个epoch
        print("\n6. 测试训练一个epoch...")
        trainer.current_epoch = 0
        train_metrics = trainer.train_epoch()
        print(f"✓ 训练epoch成功")
        print(f"  训练损失: {train_metrics['train_loss']:.4f}")
        print(f"  学习率: {train_metrics['lr']:.6f}")
        
        # 7. 测试验证
        print("\n7. 测试验证...")
        val_metrics = trainer.validate()
        print(f"✓ 验证成功")
        print(f"  验证损失: {val_metrics['val_loss']:.4f}")
        print(f"  IoU: {val_metrics['val_iou']:.4f}")
        
        # 8. 测试保存检查点
        print("\n8. 测试保存检查点...")
        trainer._save_checkpoint('test_checkpoint.pth')
        checkpoint_path = temp_dir / 'test_checkpoint.pth'
        assert checkpoint_path.exists(), "检查点文件未创建"
        print("✓ 检查点保存成功")
        
        # 9. 测试加载检查点
        print("\n9. 测试加载检查点...")
        trainer.load_checkpoint(str(checkpoint_path))
        print("✓ 检查点加载成功")
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！训练流程正常工作。")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            print(f"\n清理临时目录: {temp_dir}")
        except:
            pass


def test_device_compatibility():
    """测试设备兼容性"""
    print("\n设备兼容性测试:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"  CUDA版本: {torch.version.cuda}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"    GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"      显存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    else:
        print("  将使用CPU进行训练")


if __name__ == '__main__':
    print("开始训练流程测试...")
    
    # 测试设备
    test_device_compatibility()
    
    # 测试训练流程
    success = test_training_pipeline()
    
    if success:
        print("\n✅ 训练环境配置正确，可以开始正式训练！")
        print("\n建议使用以下命令开始训练:")
        print("python scripts/train.py --config configs/railway_track_config.yaml")
        sys.exit(0)
    else:
        print("\n❌ 训练环境存在问题，请检查配置和依赖！")
        sys.exit(1) 