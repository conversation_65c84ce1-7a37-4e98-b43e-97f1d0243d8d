#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
铁路轨道语义分割预测脚本
用于对新图像进行语义分割预测，支持单张和批量预测
"""

import torch
import cv2
import numpy as np
import argparse
import yaml
import os
from pathlib import Path
from typing import Tuple, Dict, List, Optional, Union
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader
import logging
import torch.nn as nn
import torchvision.transforms as T

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class InferenceDataset(Dataset):
    """推理数据集类"""
    
    def __init__(self, image_paths: List[str], transform=None, simple_transform=None):
        self.image_paths = image_paths
        self.transform = transform
        self.simple_transform = simple_transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        result = {'image': image, 'path': image_path}
        
        if self.transform is not None:
            # 使用albumentations
            transformed = self.transform(image=image)
            result['image'] = transformed['image']
        elif self.simple_transform is not None:
            # 使用torchvision transforms
            result['image'] = self.simple_transform(image)
        
        return result


class RailwaySegmentationPredictor:
    """铁路轨道语义分割预测器"""
    
    def __init__(self, config_path: str, checkpoint_path: str):
        """
        初始化预测器
        
        Args:
            config_path: 配置文件路径
            checkpoint_path: 模型权重文件路径
        """
        self.config = self.load_config(config_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        
        # 加载模型
        self.model = self._load_model(checkpoint_path)
        
        # 设置预处理管道
        data_config = self.config.get('data', {})
        image_size_config = data_config.get('image_size', {})
        
        # 从配置文件读取正确的图像尺寸
        if isinstance(image_size_config, dict):
            image_height = image_size_config.get('height', 544)
            image_width = image_size_config.get('width', 960)
        else:
            # 如果是单个数值，使用正方形
            image_height = image_width = int(image_size_config) if image_size_config else 544
        
        logger.info(f"使用训练时的图像尺寸: {image_height}x{image_width}")
        
        try:
            self.transform = A.Compose([
                A.Resize(height=image_height, width=image_width),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
        except Exception as e:
            logger.warning(f"使用albumentations创建transforms失败: {e}")
            logger.info("切换到简单的transforms实现")
            # 如果albumentations有问题，使用简单的transforms
            self.simple_transform = T.Compose([
                T.ToPILImage(),
                T.Resize((image_height, image_width)),
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            self.transform = None
        
        # 类别颜色映射（与配置文件保持一致）
        self.color_map = {
            0: [0, 0, 0],        # 背景 - 黑色
            1: [255, 0, 0],      # 主轨道 - 红色
            2: [0, 255, 0],      # 分叉轨道 - 绿色
        }
        
        # 类别名称映射
        self.class_names = {
            0: '背景',
            1: '主轨道',
            2: '分叉轨道'
        }
    
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            return {
                'data': {'image_size': 512},
                'model': {'num_classes': 3}  # 改为3类：背景、主轨道、分叉轨道
            }
    
    def _load_model(self, checkpoint_path: str):
        """加载模型"""
        try:
            # 这里需要根据实际的模型定义来加载
            # 假设使用segmentation_models_pytorch
            import segmentation_models_pytorch as smp
            
            model_config = self.config.get('model', {})
            architecture = model_config.get('architecture', 'PAN')
            encoder = model_config.get('encoder', 'efficientnet-b4')
            num_classes = model_config.get('num_classes', 3)
            
            # 创建模型时指定encoder_weights=None来避免pydantic验证问题
            if architecture.lower() == 'pan':
                model = smp.PAN(
                    encoder_name=encoder,
                    encoder_weights=None,  # 避免预训练权重的验证问题
                    classes=num_classes,
                    activation=None
                )
            elif architecture.lower() == 'unet':
                model = smp.Unet(
                    encoder_name=encoder,
                    encoder_weights=None,  # 避免预训练权重的验证问题
                    classes=num_classes,
                    activation=None
                )
            elif architecture.lower() == 'fpn':
                model = smp.FPN(
                    encoder_name=encoder,
                    encoder_weights=None,  # 避免预训练权重的验证问题
                    classes=num_classes,
                    activation=None
                )
            else:
                model = smp.PAN(
                    encoder_name=encoder,
                    encoder_weights=None,  # 避免预训练权重的验证问题
                    classes=num_classes,
                    activation=None
                )
            
            # 加载权重
            if os.path.exists(checkpoint_path):
                checkpoint = torch.load(checkpoint_path, map_location=self.device)
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                    
                    # 检查键名是否需要添加'model.'前缀
                    model_keys = set(model.state_dict().keys())
                    checkpoint_keys = set(state_dict.keys())
                    
                    # 检查是否需要移除'model.'前缀（原有逻辑保留）
                    if all(key.startswith('model.') for key in state_dict.keys()):
                        state_dict = {key[6:]: value for key, value in state_dict.items()}
                        logger.info("检测到模型键名包含'model.'前缀，已自动移除")
                    
                    # 检查是否需要添加'model.'前缀（新增逻辑）
                    elif not any(key.startswith('model.') for key in state_dict.keys()) and \
                         any(key.startswith('model.') for key in model_keys):
                        state_dict = {f'model.{key}': value for key, value in state_dict.items()}
                        logger.info("检测到需要添加'model.'前缀，已自动添加")
                    
                    # 检查键名匹配情况
                    updated_checkpoint_keys = set(state_dict.keys())
                    missing_keys = model_keys - updated_checkpoint_keys
                    unexpected_keys = updated_checkpoint_keys - model_keys
                    
                    if missing_keys:
                        logger.warning(f"模型中缺失的键: {len(missing_keys)} 个")
                        if len(missing_keys) <= 5:
                            for key in list(missing_keys)[:5]:
                                logger.warning(f"  缺失: {key}")
                    
                    if unexpected_keys:
                        logger.warning(f"检查点中多余的键: {len(unexpected_keys)} 个")
                        if len(unexpected_keys) <= 5:
                            for key in list(unexpected_keys)[:5]:
                                logger.warning(f"  多余: {key}")
                    
                    # 尝试加载权重
                    try:
                        model.load_state_dict(state_dict, strict=False)
                        logger.info("成功加载模型权重（允许部分不匹配）")
                    except Exception as e:
                        logger.error(f"加载权重失败: {e}")
                        logger.info("将使用随机初始化的权重")
                else:
                    model.load_state_dict(checkpoint)
                
                # 显示加载的模型信息
                if 'epoch' in checkpoint:
                    logger.info(f"模型来自第 {checkpoint['epoch']} 个epoch")
                if 'best_metric' in checkpoint and 'best_metric_name' in checkpoint:
                    logger.info(f"最佳指标: {checkpoint['best_metric_name']}={checkpoint['best_metric']:.4f}")
            else:
                logger.warning(f"模型权重文件不存在: {checkpoint_path}，使用随机初始化权重")
            
            model.to(self.device)
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            # 如果模型加载失败，创建一个简单的替代模型用于演示
            try:
                logger.info("尝试创建简单的替代模型...")
                
                # 创建一个简单的卷积模型作为替代
                class SimpleSegmentationModel(nn.Module):
                    def __init__(self, num_classes=3):  # 改为3类默认值
                        super().__init__()
                        self.conv1 = nn.Conv2d(3, 64, 3, padding=1)
                        self.conv2 = nn.Conv2d(64, 128, 3, padding=1)
                        self.conv3 = nn.Conv2d(128, 64, 3, padding=1)
                        self.conv4 = nn.Conv2d(64, num_classes, 1)
                        self.upsample = nn.Upsample(scale_factor=1, mode='bilinear', align_corners=False)
                        
                        # 初始化权重，使模型能产生一些有意义的输出
                        for m in self.modules():
                            if isinstance(m, nn.Conv2d):
                                nn.init.xavier_uniform_(m.weight)
                                if m.bias is not None:
                                    nn.init.constant_(m.bias, 0)
                        
                    def forward(self, x):
                        x = torch.relu(self.conv1(x))
                        x = torch.relu(self.conv2(x))
                        x = torch.relu(self.conv3(x))
                        x = self.conv4(x)
                        return x
                
                model = SimpleSegmentationModel(num_classes=3)
                model.to(self.device)
                model.eval()
                logger.info("成功创建简单替代模型")
                return model
                
            except Exception as e2:
                logger.error(f"创建替代模型也失败: {e2}")
                raise Exception(f"无法创建任何可用的模型: {e}")
    
    def predict_single(self, image_path: str, threshold: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测单张图片
        
        Args:
            image_path: 图片路径
            threshold: 预测置信度阈值
            
        Returns:
            prediction_mask: 预测的分割掩码
            prediction_probs: 预测的概率分布
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        original_size = image.shape[:2]  # (height, width)
        
        # 预处理
        if self.transform is not None:
            # 使用albumentations
            transformed = self.transform(image=image)
            input_tensor = transformed['image'].unsqueeze(0).to(self.device)
        else:
            # 使用简单的torchvision transforms
            input_tensor = self.simple_transform(image).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            prediction = self.model(input_tensor)
            prediction = torch.softmax(prediction, dim=1)
            prediction = prediction.cpu().numpy()[0]
        
        # 调试信息：显示原始预测统计
        logger.info("=== 模型原始输出分析 ===")
        for i in range(prediction.shape[0]):
            class_name = self.class_names.get(i, f'类别_{i}')
            class_probs = prediction[i]
            logger.info(f"{class_name}: 平均概率={class_probs.mean():.4f}, 最大概率={class_probs.max():.4f}, "
                       f"高于阈值的像素={np.sum(class_probs > threshold)}")
        
        # 后处理：调整到原始尺寸
        prediction = np.transpose(prediction, (1, 2, 0))
        # 确保尺寸参数是整数类型，cv2.resize需要(width, height)格式
        target_size = (int(original_size[1]), int(original_size[0]))  # (width, height)
        prediction = cv2.resize(prediction, target_size)
        
        # 使用新的后处理方法
        prediction_mask = self.post_process_prediction(prediction, threshold=threshold)
        
        return prediction_mask, prediction
    
    def predict_batch(self, image_paths: List[str], batch_size: int = 4, threshold: float = 0.5) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        批量预测多张图片
        
        Args:
            image_paths: 图片路径列表
            batch_size: 批次大小
            threshold: 预测置信度阈值
            
        Returns:
            预测结果列表
        """
        simple_transform = getattr(self, 'simple_transform', None)
        dataset = InferenceDataset(image_paths, self.transform, simple_transform)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False, num_workers=2)
        
        results = []
        
        for batch in dataloader:
            images = batch['image'].to(self.device)
            paths = batch['path']
            
            with torch.no_grad():
                predictions = self.model(images)
                predictions = torch.softmax(predictions, dim=1)
                predictions = predictions.cpu().numpy()
            
            for i, (pred, path) in enumerate(zip(predictions, paths)):
                # 读取原图获取原始尺寸
                original_image = cv2.imread(path)
                if original_image is None:
                    logger.warning(f"无法读取图片: {path}，跳过该图片")
                    continue
                original_size = original_image.shape[:2]  # (height, width)
                
                # 后处理
                pred = np.transpose(pred, (1, 2, 0))
                # 确保尺寸参数是整数类型，cv2.resize需要(width, height)格式
                target_size = (int(original_size[1]), int(original_size[0]))  # (width, height)
                pred = cv2.resize(pred, target_size)
                
                # 使用新的后处理方法
                pred_mask = self.post_process_prediction(pred, threshold=threshold)
                
                results.append((pred_mask, pred))
        
        return results
    
    def visualize_prediction(self, image_path: str, save_path: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测并生成可视化结果 - 在原图上直接叠加轨道预测
        
        Args:
            image_path: 输入图片路径
            save_path: 保存路径（可选）
            
        Returns:
            overlay: 叠加后的可视化结果
            prediction_mask: 预测掩码
        """
        # 读取原图
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 进行预测
        prediction_mask, _ = self.predict_single(image_path)
        
        # 创建叠加图像，从原图开始
        overlay = image_rgb.copy().astype(np.float32)
        
        # 只在主轨道和分叉轨道区域进行叠加，背景保持原图
        # 主轨道区域：红色叠加，增强显示效果
        main_track_mask = (prediction_mask == 1)
        if np.any(main_track_mask):
            overlay[main_track_mask, 0] = overlay[main_track_mask, 0] * 0.3 + 255 * 0.7  # 红色通道
            overlay[main_track_mask, 1] = overlay[main_track_mask, 1] * 0.3              # 绿色通道
            overlay[main_track_mask, 2] = overlay[main_track_mask, 2] * 0.3              # 蓝色通道
        
        # 分叉轨道区域：绿色叠加，增强显示效果
        fork_track_mask = (prediction_mask == 2)
        if np.any(fork_track_mask):
            overlay[fork_track_mask, 0] = overlay[fork_track_mask, 0] * 0.3              # 红色通道
            overlay[fork_track_mask, 1] = overlay[fork_track_mask, 1] * 0.3 + 255 * 0.7  # 绿色通道
            overlay[fork_track_mask, 2] = overlay[fork_track_mask, 2] * 0.3              # 蓝色通道
        
        # 转换回uint8
        overlay = np.clip(overlay, 0, 255).astype(np.uint8)
        
        # 保存结果
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            cv2.imwrite(save_path, cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))
            logger.info(f"可视化结果已保存到: {save_path}")
        
        return overlay, prediction_mask
    
    def save_prediction(self, prediction_mask: np.ndarray, save_path: str, save_colored: bool = True):
        """
        保存预测结果
        
        Args:
            prediction_mask: 预测掩码
            save_path: 保存路径
            save_colored: 是否保存彩色版本
            mask_format: 掩码格式 ('png' 或 'npy')，如果为None则从配置读取
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 统一使用PNG格式
        base_path = os.path.splitext(save_path)[0]
        png_path = base_path + '.png'
        
        if self.config.get('data', {}).get('use_multilabel', True):
            # 多标签格式 - 保存为BGR PNG
            if len(prediction_mask.shape) == 3:
                # 已经是多通道格式
                bgr_mask = np.zeros((prediction_mask.shape[0], prediction_mask.shape[1], 3), dtype=np.uint8)
                bgr_mask[:, :, 0] = (prediction_mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
                bgr_mask[:, :, 1] = (prediction_mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
                bgr_mask[:, :, 2] = (prediction_mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
                cv2.imwrite(png_path, bgr_mask)
            else:
                # 单通道需要转换
                bgr_mask = np.zeros((prediction_mask.shape[0], prediction_mask.shape[1], 3), dtype=np.uint8)
                bgr_mask[:, :, 0] = (prediction_mask == 0).astype(np.uint8) * 255  # B: 背景
                bgr_mask[:, :, 1] = (prediction_mask == 1).astype(np.uint8) * 255  # G: 主轨道
                bgr_mask[:, :, 2] = (prediction_mask == 2).astype(np.uint8) * 255  # R: 分叉轨道
                cv2.imwrite(png_path, bgr_mask)
        else:
            # 单标签格式
            cv2.imwrite(png_path, prediction_mask.astype(np.uint8))
        
        logger.info(f"预测结果已保存到: {png_path}")
        
        if save_colored:
            # 保存彩色版本（始终为PNG）
            colored_mask = np.zeros((prediction_mask.shape[0], prediction_mask.shape[1], 3), dtype=np.uint8)
            for class_id, color in self.color_map.items():
                colored_mask[prediction_mask == class_id] = color
            
            colored_path = base_path + '_colored.png'
            cv2.imwrite(colored_path, cv2.cvtColor(colored_mask, cv2.COLOR_RGB2BGR))
            logger.info(f"彩色预测结果已保存到: {colored_path}")
    
    def get_prediction_stats(self, prediction_mask: np.ndarray) -> Dict[str, float]:
        """
        获取预测统计信息
        
        Args:
            prediction_mask: 预测掩码
            
        Returns:
            统计信息字典
        """
        unique_classes, counts = np.unique(prediction_mask, return_counts=True)
        total_pixels = prediction_mask.size
        
        stats = {}
        for class_id, count in zip(unique_classes, counts):
            class_name = self.class_names.get(class_id, f'类别_{class_id}')
            percentage = (count / total_pixels) * 100
            stats[class_name] = {
                'pixel_count': int(count),
                'percentage': round(percentage, 2)
            }
        
        return stats
    
    def post_process_prediction(self, prediction_probs: np.ndarray, threshold: float = 0.5) -> np.ndarray:
        """
        后处理预测结果，参考evaluation模块的处理方式
        
        Args:
            prediction_probs: 预测概率 (H, W, C)
            threshold: 二值化阈值
            
        Returns:
            后处理后的预测掩码
        """
        # 对于多类分割，使用argmax获取最大概率的类别
        prediction_mask = np.argmax(prediction_probs, axis=2)
        
        # 可选：对预测结果进行置信度过滤
        # 如果最大概率低于阈值，则认为是背景
        max_probs = np.max(prediction_probs, axis=2)
        prediction_mask[max_probs < threshold] = 0
        
        return prediction_mask.astype(np.uint8)
    
    def get_prediction_confidence(self, prediction_probs: np.ndarray) -> Dict[str, float]:
        """
        获取预测的置信度统计
        
        Args:
            prediction_probs: 预测概率 (H, W, C)
            
        Returns:
            置信度统计字典
        """
        max_probs = np.max(prediction_probs, axis=2)
        pred_classes = np.argmax(prediction_probs, axis=2)
        
        confidence_stats = {}
        for class_id, class_name in self.class_names.items():
            class_mask = pred_classes == class_id
            if np.any(class_mask):
                class_confidences = max_probs[class_mask]
                confidence_stats[class_name] = {
                    'mean_confidence': float(np.mean(class_confidences)),
                    'min_confidence': float(np.min(class_confidences)),
                    'max_confidence': float(np.max(class_confidences)),
                    'pixel_count': int(np.sum(class_mask))
                }
            else:
                confidence_stats[class_name] = {
                    'mean_confidence': 0.0,
                    'min_confidence': 0.0,
                    'max_confidence': 0.0,
                    'pixel_count': 0
                }
        
        return confidence_stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='铁路轨道语义分割预测')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml', help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, default='models/checkpoints/railway_track_config/best_model.pth', help='模型权重路径')
    parser.add_argument('--input', type=str, required=True, help='输入图片路径或目录')
    parser.add_argument('--output', type=str, default='outputs/predictions', help='输出目录')
    parser.add_argument('--batch-size', type=int, default=4, help='批次大小')
    parser.add_argument('--visualize', action='store_true', help='生成可视化结果')
    parser.add_argument('--stats', action='store_true', help='显示预测统计信息')
    parser.add_argument('--threshold', type=float, default=0.3, help='预测置信度阈值')
    parser.add_argument('--confidence', action='store_true', help='显示预测置信度信息')
    
    args = parser.parse_args()
    
    # 创建预测器
    try:
        predictor = RailwaySegmentationPredictor(args.config, args.checkpoint)
    except Exception as e:
        logger.error(f"创建预测器失败: {e}")
        return
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 获取输入图片路径
    if os.path.isfile(args.input):
        image_paths = [args.input]
    elif os.path.isdir(args.input):
        extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(Path(args.input).glob(f'*{ext}'))
            image_paths.extend(Path(args.input).glob(f'*{ext.upper()}'))
        image_paths = [str(p) for p in image_paths]
    else:
        logger.error(f"输入路径不存在: {args.input}")
        return
    
    if not image_paths:
        logger.error("未找到有效的图片文件")
        return
    
    logger.info(f"找到 {len(image_paths)} 张图片")
    
    # 预测
    if len(image_paths) == 1:
        # 单张图片预测
        image_path = image_paths[0]
        image_name = Path(image_path).stem
        
        try:
            prediction_mask, prediction_probs = predictor.predict_single(image_path, args.threshold)
            
            # 保存预测结果
            output_path = os.path.join(args.output, f'{image_name}_prediction.png')
            predictor.save_prediction(prediction_mask, output_path)
            
            # 生成可视化结果
            if args.visualize:
                viz_path = os.path.join(args.output, f'{image_name}_visualization.png')
                predictor.visualize_prediction(image_path, viz_path)
            
            # 显示统计信息
            if args.stats:
                stats = predictor.get_prediction_stats(prediction_mask)
                logger.info("预测统计信息:")
                for class_name, info in stats.items():
                    logger.info(f"  {class_name}: {info['pixel_count']} 像素 ({info['percentage']}%)")
            
            # 显示置信度信息
            if args.confidence:
                confidence_stats = predictor.get_prediction_confidence(prediction_probs)
                logger.info("预测置信度信息:")
                for class_name, info in confidence_stats.items():
                    if info['pixel_count'] > 0:
                        logger.info(f"  {class_name}: 平均置信度={info['mean_confidence']:.3f}, "
                                  f"像素数={info['pixel_count']}")
            
            logger.info(f"预测完成，结果已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
    
    else:
        # 批量预测
        try:
            results = predictor.predict_batch(image_paths, args.batch_size, args.threshold)
            
            for i, (image_path, (prediction_mask, prediction_probs)) in enumerate(zip(image_paths, results)):
                image_name = Path(image_path).stem
                
                # 保存预测结果
                output_path = os.path.join(args.output, f'{image_name}_prediction.png')
                predictor.save_prediction(prediction_mask, output_path)
                
                # 生成可视化结果
                if args.visualize:
                    viz_path = os.path.join(args.output, f'{image_name}_visualization.png')
                    predictor.visualize_prediction(image_path, viz_path)
                
                # 显示统计信息
                if args.stats:
                    stats = predictor.get_prediction_stats(prediction_mask)
                    logger.info(f"图片 {image_name} 预测统计信息:")
                    for class_name, info in stats.items():
                        logger.info(f"  {class_name}: {info['pixel_count']} 像素 ({info['percentage']}%)")
                
                # 显示置信度信息
                if args.confidence:
                    confidence_stats = predictor.get_prediction_confidence(prediction_probs)
                    logger.info(f"图片 {image_name} 预测置信度信息:")
                    for class_name, info in confidence_stats.items():
                        if info['pixel_count'] > 0:
                            logger.info(f"  {class_name}: 平均置信度={info['mean_confidence']:.3f}, "
                                      f"像素数={info['pixel_count']}")
                
                logger.info(f"完成 {i+1}/{len(image_paths)}: {image_name}")
            
            logger.info(f"批量预测完成，所有结果已保存到: {args.output}")
            
        except Exception as e:
            logger.error(f"批量预测失败: {e}")


if __name__ == "__main__":
    # 使用示例
    if len(os.sys.argv) == 1:
        # 如果没有命令行参数，运行示例代码
        print("运行示例代码...")
        
        # 示例配置
        config_path = 'configs/railway_track_config.yaml'
        checkpoint_path = 'models/checkpoints/railway_track_config/best_model.pth'
        
        try:
            predictor = RailwaySegmentationPredictor(config_path, checkpoint_path)
            
            # 预测示例（需要实际的图片路径）
            image_path = 'data/test_images/sample.jpg'
            if os.path.exists(image_path):
                mask, probabilities = predictor.predict_single(image_path)
                
                # 生成可视化结果
                overlay, mask = predictor.visualize_prediction(
                    image_path, 
                    save_path='outputs/predictions/example_result.jpg'
                )
                
                # 获取统计信息
                stats = predictor.get_prediction_stats(mask)
                
                print("预测完成！")
                print(f"检测到的类别: {list(np.unique(mask))}")
                print("类别统计:")
                for class_name, info in stats.items():
                    print(f"  {class_name}: {info['percentage']}%")
            else:
                print(f"示例图片不存在: {image_path}")
                print("请使用命令行参数运行预测:")
                print("python predict_inference.py --input path/to/image.jpg --output outputs/predictions")
                
        except Exception as e:
            print(f"示例运行失败: {e}")
            print("请检查配置文件和模型权重文件是否存在")
    else:
        # 运行命令行模式
        main() 
