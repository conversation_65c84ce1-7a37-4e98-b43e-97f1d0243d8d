#!/usr/bin/env python3
"""
TensorBoard功能测试脚本
演示TensorBoard集成功能
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

# 添加项目路径
import sys
sys.path.append('.')

from src.utils.tensorboard_logger import TensorBoardLogger


def create_sample_data():
    """创建示例数据"""
    # 创建示例图像和掩码
    batch_size, channels, height, width = 4, 3, 256, 256
    
    images = torch.randn(batch_size, channels, height, width)
    masks = torch.randint(0, 3, (batch_size, 3, height, width)).float()  # 3类分割
    predictions = torch.sigmoid(torch.randn(batch_size, 3, height, width))
    
    return images, masks, predictions


def test_tensorboard_logging():
    """测试TensorBoard日志功能"""
    print("开始TensorBoard功能测试...")
    
    # 创建TensorBoard记录器
    log_dir = Path('outputs/tensorboard_test')
    tb_logger = TensorBoardLogger(
        log_dir=log_dir,
        comment="_test_experiment"
    )
    
    print(f"TensorBoard日志保存到: {log_dir}")
    
    # 模拟训练过程
    epochs = 5
    steps_per_epoch = 10
    
    for epoch in range(epochs):
        print(f"模拟Epoch {epoch + 1}/{epochs}")
        
        # 模拟训练指标
        for step in range(steps_per_epoch):
            global_step = epoch * steps_per_epoch + step
            
            # 模拟损失下降
            train_loss = 1.0 * np.exp(-global_step * 0.02) + 0.1 + np.random.normal(0, 0.05)
            val_loss = 0.8 * np.exp(-global_step * 0.015) + 0.15 + np.random.normal(0, 0.03)
            
            # 模拟学习率变化
            lr = 0.001 * (0.95 ** epoch)
            
            # 记录训练指标
            tb_logger.log_scalar('Train/Loss', train_loss, global_step)
            tb_logger.log_scalar('Learning_Rate', lr, global_step)
            
            # 每几步记录一次详细信息
            if step % 5 == 0:
                # 记录验证指标
                tb_logger.log_scalar('Validation/Loss', val_loss, global_step)
                
                # 模拟其他指标
                iou = 0.3 + 0.6 * (1 - np.exp(-global_step * 0.03)) + np.random.normal(0, 0.02)
                dice = 0.2 + 0.7 * (1 - np.exp(-global_step * 0.025)) + np.random.normal(0, 0.02)
                f1 = 0.25 + 0.65 * (1 - np.exp(-global_step * 0.028)) + np.random.normal(0, 0.02)
                
                metrics = {
                    'IoU': max(0, min(1, iou)),
                    'Dice': max(0, min(1, dice)),
                    'F1': max(0, min(1, f1))
                }
                
                tb_logger.log_scalars('Metrics/Validation', metrics, global_step)
                
        # Epoch结束时记录对比指标
        epoch_train_loss = 1.0 * np.exp(-epoch * 2 * 0.02) + 0.1
        epoch_val_loss = 0.8 * np.exp(-epoch * 2 * 0.015) + 0.15
        
        loss_comparison = {
            'Train': epoch_train_loss,
            'Validation': epoch_val_loss
        }
        tb_logger.log_scalars('Loss/Comparison', loss_comparison, epoch)
        
        # 记录示例图像（每个epoch）
        images, masks, predictions = create_sample_data()
        
        if epoch % 2 == 0:  # 每2个epoch记录一次图像
            tb_logger.log_prediction_samples(
                images[:2], masks[:2], predictions[:2], 
                step=epoch, max_samples=2
            )
        
        # 模拟参数直方图
        if epoch == 0:
            # 创建模拟的模型参数
            weights = torch.randn(1000) * 0.1
            gradients = torch.randn(1000) * 0.01
            
            tb_logger.log_histogram('Parameters/conv1.weight', weights, epoch)
            tb_logger.log_histogram('Gradients/conv1.weight', gradients, epoch)
    
    # 创建训练曲线图
    print("生成训练曲线图...")
    x = np.arange(epochs * steps_per_epoch)
    train_losses = [1.0 * np.exp(-i * 0.02) + 0.1 + np.random.normal(0, 0.05) for i in x]
    val_losses = [0.8 * np.exp(-i * 0.015) + 0.15 + np.random.normal(0, 0.03) for i in x[::5]]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失曲线
    ax1.plot(x, train_losses, label='Training Loss', alpha=0.7)
    ax1.plot(x[::5], val_losses, label='Validation Loss', alpha=0.7)
    ax1.set_xlabel('Step')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training Progress')
    ax1.legend()
    ax1.grid(True)
    
    # 指标曲线
    ious = [0.3 + 0.6 * (1 - np.exp(-i * 0.03)) + np.random.normal(0, 0.02) for i in x[::5]]
    dices = [0.2 + 0.7 * (1 - np.exp(-i * 0.025)) + np.random.normal(0, 0.02) for i in x[::5]]
    
    ax2.plot(x[::5], ious, label='IoU', alpha=0.7)
    ax2.plot(x[::5], dices, label='Dice', alpha=0.7)
    ax2.set_xlabel('Step')
    ax2.set_ylabel('Score')
    ax2.set_title('Validation Metrics')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    
    # 记录到TensorBoard
    tb_logger.log_figure('Summary/Training_Curves', fig, step=epochs)
    
    # 测试文本日志
    summary_text = f"""
    # 训练总结
    
    ## 配置信息
    - 总Epochs: {epochs}
    - 每Epoch步数: {steps_per_epoch}
    - 最终训练损失: {train_losses[-1]:.4f}
    - 最终验证损失: {val_losses[-1]:.4f}
    - 最终IoU: {ious[-1]:.4f}
    
    ## 模型性能
    训练已完成，模型收敛良好。
    """
    
    tb_logger.log_text('Summary/Training_Summary', summary_text, epochs)
    
    # 强制刷新并关闭
    tb_logger.flush()
    tb_logger.close()
    
    print("TensorBoard测试完成！")
    print(f"日志保存在: {log_dir}")
    print(f"启动TensorBoard查看结果:")
    print(f"  tensorboard --logdir {log_dir}")
    print(f"  或运行: python scripts/start_tensorboard.py --logdir {log_dir.parent} --experiment {log_dir.name}")


if __name__ == '__main__':
    test_tensorboard_logging() 