#!/usr/bin/env python3
"""
测试预训练模型加载功能
"""

import os
import sys
import torch
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入修复后的加载函数
from scripts.ensemble_training_notebook_exact import load_pretrained_model


def test_model_loading(model_paths):
    """测试模型加载"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    print("=" * 60)
    
    for model_path in model_paths:
        model_path = Path(model_path)
        if not model_path.exists():
            print(f"⚠️  文件不存在: {model_path}")
            continue
            
        print(f"\n测试加载: {model_path.name}")
        print("-" * 60)
        
        try:
            # 尝试加载模型
            model = load_pretrained_model(str(model_path), device=str(device))
            
            # 验证模型结构
            print("\n模型结构检查:")
            print(f"  - 类型: {type(model).__name__}")
            print(f"  - 有encoder属性: {hasattr(model, 'encoder')}")
            print(f"  - 有decoder属性: {hasattr(model, 'decoder')}")
            print(f"  - 有segmentation_head属性: {hasattr(model, 'segmentation_head')}")
            
            if hasattr(model, 'encoder'):
                print(f"  - 编码器名称: {getattr(model.encoder, 'name', 'unknown')}")
            
            if hasattr(model, 'segmentation_head'):
                try:
                    out_channels = model.segmentation_head[0].out_channels
                    print(f"  - 输出通道数: {out_channels}")
                except:
                    print("  - 无法获取输出通道数")
            
            # 测试前向传播
            print("\n测试前向传播:")
            try:
                dummy_input = torch.randn(1, 3, 544, 960).to(device)
                with torch.no_grad():
                    output = model(dummy_input)
                print(f"  - 输入形状: {dummy_input.shape}")
                print(f"  - 输出形状: {output.shape}")
                print("  ✅ 前向传播成功!")
            except Exception as e:
                print(f"  ❌ 前向传播失败: {e}")
            
            print(f"\n✅ {model_path.name} 加载成功!")
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试完成!")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='测试预训练模型加载')
    parser.add_argument('models', nargs='+', help='模型文件路径')
    
    args = parser.parse_args()
    
    test_model_loading(args.models)