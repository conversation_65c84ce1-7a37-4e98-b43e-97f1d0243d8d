#!/usr/bin/env python3
"""
集成学习训练脚本
基于notebook中的方法，训练多个模型并优化融合权重
"""

# 显存优化配置
import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'  # 减少显存碎片

import sys
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
import yaml
from pathlib import Path
from typing import Dict, List, Any
import argparse
from tqdm import tqdm
import segmentation_models_pytorch as smp

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.models.ensemble import EnsembleModel, EnsemblePredictor
from src.utils.metrics import iou_coef
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
from src.models.segmentation_model import create_model


class EnsembleTrainer:
    """集成学习训练器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device(config['project']['device'])
        
        # 模型配置
        self.models_config = [
            {
                'name': 'efficientnet_b4',
                'architecture': 'pan',
                'encoder': 'tu-tf_efficientnet_b4_ns',
                'encoder_weights': 'noisy-student',
                'epochs': 25
            },
            {
                'name': 'eca_nfnet_l2',
                'architecture': 'pan', 
                'encoder': 'tu-eca_nfnet_l2',
                'encoder_weights': 'imagenet',
                'epochs': 25
            },
            {
                'name': 'seresnet152d',
                'architecture': 'pan',
                'encoder': 'tu-seresnet152d', 
                'encoder_weights': 'imagenet',
                'epochs': 30
            }
        ]
        
        # 创建输出目录（兼容不同配置）
        checkpoint_root = config.get('training', {}).get(
            'save_dir',  # 优先使用 training.save_dir
            config.get('checkpointing', {}).get('save_dir', 'outputs')  # 其次 checkpointing.save_dir
        )
        self.weights_dir = Path(checkpoint_root) / 'ensemble_weights'
        self.weights_dir.mkdir(parents=True, exist_ok=True)
        
    def create_model(self, model_config: Dict[str, Any]) -> nn.Module:
        """
        创建单个模型
        
        Args:
            model_config: 模型配置
            
        Returns:
            模型实例
        """
        # 优先使用与train_v2.py相同的create_model函数
        try:
            model = create_model(
                architecture=model_config['architecture'],
                backbone=model_config['encoder'],
                num_classes=self.config['data']['num_classes'],
                pretrained=True  # 集成训练使用预训练权重
            )
            return model.to(self.device)
        except Exception as e:
            print(f"使用create_model失败: {e}")
            print("回退到原始的segmentation_models_pytorch方法")
        
        # 回退方案：使用原始的segmentation_models_pytorch方法（保持notebook一致性）
        # 通用参数
        params = {
            'encoder_name': model_config['encoder'],
            'encoder_weights': model_config['encoder_weights'],
            'in_channels': 3,
            'classes': self.config['model']['classes'],
            'activation': None  # 不使用激活函数
        }
        
        # 根据架构创建模型
        if model_config['architecture'].lower() == 'pan':
            model = smp.PAN(**params)
        elif model_config['architecture'].lower() == 'unet':
            model = smp.Unet(**params)
        elif model_config['architecture'].lower() == 'fpn':
            model = smp.FPN(**params)
        else:
            raise ValueError(f"不支持的架构: {model_config['architecture']}")
        
        return model.to(self.device)
    
    def train_single_model(self, 
                          model: nn.Module,
                          model_config: Dict[str, Any],
                          train_loader: torch.utils.data.DataLoader,
                          val_loader: torch.utils.data.DataLoader) -> str:
        """
        训练单个模型
        
        Args:
            model: 模型实例
            model_config: 模型配置
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            
        Returns:
            保存的权重文件路径
        """
        model_name = model_config['name']
        epochs = model_config['epochs']
        
        print(f"\n开始训练模型: {model_name}")
        print(f"编码器: {model_config['encoder']}")
        print(f"训练轮数: {epochs}")
        
        # 检查是否已存在权重文件
        weights_path = self.weights_dir / f"{model_name}.pth"
        if weights_path.exists():
            print(f"权重文件已存在，跳过训练: {weights_path}")
            model.load_state_dict(torch.load(weights_path, map_location=self.device))
            return str(weights_path)
        
        # 使用与train_v2.py相同的损失函数
        loss_config = self.config.get('loss', {})
        alpha_weights = loss_config.get('alpha', [0.1, 0.3, 0.6])
        
        criterion = MultiLabelSegmentationLoss(
            jaccard_weight=loss_config.get('dice_weight', 0.5),
            focal_weight=loss_config.get('ce_weight', 0.5), 
            alpha=alpha_weights,
            gamma=loss_config.get('focal_gamma', 2.0)
        )
        
        # 优化器
        training_config = self.config.get('training', {})
        optimizer_config = self.config.get('optimizer', {})
        
        lr = training_config.get('learning_rate', 0.0005)
        weight_decay = optimizer_config.get('weight_decay', 0.0001)
        optimizer_type = optimizer_config.get('type', 'adamw').lower()
        
        if optimizer_type == 'adamw':
            betas = optimizer_config.get('betas', [0.9, 0.999])
            optimizer = torch.optim.AdamW(
                model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=betas
            )
        elif optimizer_type == 'sgd':
            momentum = optimizer_config.get('momentum', 0.9)
            optimizer = torch.optim.SGD(
                model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                momentum=momentum
            )
        else:
            # 默认使用AdamW
            optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        
        # 使用与train_v2.py相同的调度器配置
        scheduler_config = self.config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'cosine').lower()
        
        if scheduler_type == 'cosine':
            min_lr = scheduler_config.get('min_lr', 1e-6)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs, eta_min=min_lr, last_epoch=-1
            )
        elif scheduler_type == 'step':
            step_size = scheduler_config.get('step_size', 30)
            gamma = scheduler_config.get('gamma', 0.1)
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer, step_size=step_size, gamma=gamma
            )
        elif scheduler_type == 'reduce_on_plateau':
            factor = scheduler_config.get('factor', 0.5)
            patience = scheduler_config.get('patience', 10)
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='max', factor=factor, patience=patience
            )
        else:
            # 默认使用余弦退火
            min_lr = scheduler_config.get('min_lr', 1e-6)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs, eta_min=min_lr
            )
        
        # 训练历史
        train_losses = []
        val_losses = []
        val_ious = []
        best_iou = 0.0
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_batches = 0
            
            train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} - 训练")
            for batch in train_pbar:
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = model(images)
                loss = criterion(outputs, masks)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
                
                train_pbar.set_postfix({'loss': f'{loss.item():.4f}'})
            
            avg_train_loss = train_loss / train_batches
            train_losses.append(avg_train_loss)
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_iou = 0.0
            val_batches = 0
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} - 验证")
                for batch in val_pbar:
                    images = batch['image'].to(self.device)
                    masks = batch['mask'].to(self.device)
                    
                    outputs = model(images)
                    loss = criterion(outputs, masks)
                    
                    # 计算IoU
                    outputs_prob = torch.sigmoid(outputs)
                    iou = iou_coef(masks, outputs_prob)
                    
                    val_loss += loss.item()
                    val_iou += iou.item()
                    val_batches += 1
                    
                    val_pbar.set_postfix({'loss': f'{loss.item():.4f}', 'iou': f'{iou.item():.4f}'})
            
            avg_val_loss = val_loss / val_batches
            avg_val_iou = val_iou / val_batches
            
            val_losses.append(avg_val_loss)
            val_ious.append(avg_val_iou)
            
            # 更新学习率
            scheduler.step()
            
            # 保存最佳模型
            if avg_val_iou > best_iou:
                best_iou = avg_val_iou
                torch.save(model.state_dict(), weights_path)
                print(f"保存最佳模型，IoU: {best_iou:.4f}")
            
            print(f"Epoch {epoch+1}/{epochs} - "
                  f"训练损失: {avg_train_loss:.4f}, "
                  f"验证损失: {avg_val_loss:.4f}, "
                  f"验证IoU: {avg_val_iou:.4f}")
        
        print(f"模型 {model_name} 训练完成，最佳IoU: {best_iou:.4f}")
        
        return str(weights_path)
    
    def train_all_models(self, 
                        train_loader: torch.utils.data.DataLoader,
                        val_loader: torch.utils.data.DataLoader) -> List[str]:
        """
        训练所有模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            
        Returns:
            权重文件路径列表
        """
        weights_paths = []
        
        for model_config in self.models_config:
            # 创建模型
            model = self.create_model(model_config)
            
            # 训练模型
            weights_path = self.train_single_model(model, model_config, train_loader, val_loader)
            weights_paths.append(weights_path)
            
            # 清理GPU内存
            del model
            torch.cuda.empty_cache()
        
        return weights_paths
    
    def optimize_ensemble_weights(self, 
                                 weights_paths: List[str],
                                 val_loader: torch.utils.data.DataLoader) -> Dict[int, Dict[str, float]]:
        """
        优化集成权重
        
        Args:
            weights_paths: 模型权重文件路径列表
            val_loader: 验证数据加载器
            
        Returns:
            优化后的权重字典
        """
        print("\n开始优化集成权重...")
        
        # 加载所有模型
        models = []
        for i, (model_config, weights_path) in enumerate(zip(self.models_config, weights_paths)):
            model = self.create_model(model_config)
            model.load_state_dict(torch.load(weights_path, map_location=self.device))
            model.eval()
            models.append(model)
            print(f"加载模型 {i+1}/{len(weights_paths)}: {model_config['name']}")
        
        # 收集所有预测结果
        all_predictions = [[] for _ in range(len(models))]
        all_targets = []
        
        print("收集模型预测结果...")
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="预测"):
                images = batch['image'].to(self.device)
                targets = batch['mask'].to(self.device)
                
                # 获取每个模型的预测
                for i, model in enumerate(models):
                    pred = model(images)
                    pred = torch.sigmoid(pred)  # 转换为概率
                    all_predictions[i].append(pred.cpu())
                
                all_targets.append(targets.cpu())
        
        # 合并所有批次
        for i in range(len(models)):
            all_predictions[i] = torch.cat(all_predictions[i], dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        
        # 优化每个类别的权重
        best_weights = {}
        num_classes = self.config['model']['classes']
        
        for class_idx in range(num_classes):
            print(f"\n优化类别 {class_idx} 的权重...")
            
            best_iou = 0.0
            best_class_weights = None
            
            # 网格搜索
            with tqdm(total=9801, desc=f"类别 {class_idx}") as pbar:  # 99^2
                for alpha in np.arange(0.01, 1.00, 0.01):
                    for beta in np.arange(0.01, 1.00, 0.01):
                        gamma = 1.0 - alpha - beta
                        if gamma <= 0:
                            pbar.update(1)
                            continue
                        
                        # 计算加权预测
                        weights = [alpha, beta, gamma]
                        weighted_pred = torch.zeros_like(all_predictions[0][:, class_idx:class_idx+1])
                        
                        for i, weight in enumerate(weights):
                            if i < len(all_predictions):
                                weighted_pred += weight * all_predictions[i][:, class_idx:class_idx+1]
                        
                        # 计算IoU
                        target_class = all_targets[:, class_idx:class_idx+1]
                        iou = iou_coef(target_class, weighted_pred)
                        
                        # 更新最佳权重
                        if iou > best_iou:
                            best_iou = iou
                            best_class_weights = weights.copy()
                        
                        pbar.update(1)
            
            # 保存最佳权重
            best_weights[class_idx] = {
                'weights': best_class_weights,
                'iou': best_iou.item() if torch.is_tensor(best_iou) else best_iou
            }
            
            print(f"类别 {class_idx} 最佳权重: {best_class_weights}")
            print(f"类别 {class_idx} 最佳IoU: {best_iou:.4f}")
        
        # 保存权重配置
        weights_config_path = self.weights_dir / 'ensemble_weights.yaml'
        with open(weights_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(best_weights, f, default_flow_style=False, allow_unicode=True)
        
        print(f"\n集成权重已保存到: {weights_config_path}")
        return best_weights


def print_gpu_memory_usage(prefix=""):
    """打印GPU显存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        print(f"{prefix}显存使用: 已分配 {allocated:.2f} GB, 已保留 {reserved:.2f} GB")


def main():
    """主函数"""
    print("=== 集成学习训练脚本 ===")
    print("已与train_v2.py保持配置一致:")
    print("✅ 数据集处理: RailwayTrackDataset + 相同transforms")
    print("✅ 损失函数: MultilabelCombinedLoss (Dice + Focal)")
    print("✅ 优化器: 从配置文件读取 (AdamW/SGD)")
    print("✅ 学习率调度: 从配置文件读取 (cosine/step/plateau)")
    print("✅ 模型创建: 优先使用create_model函数")
    print("⚠️  Batch_size: 为显存优化自动调整")
    print("⚠️  训练循环: 保持notebook简单实现")
    print()
    
    # 初始显存清理和检查
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        device = torch.cuda.current_device()
        total_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
        print(f"GPU设备: {torch.cuda.get_device_name(device)}")
        print(f"总显存: {total_memory:.2f} GB")
        print(f"初始清理完成")
    
    parser = argparse.ArgumentParser(description='集成学习训练脚本')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据集根目录')
    parser.add_argument('--skip-training', action='store_true',
                       help='跳过模型训练，直接优化权重')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新数据路径（适配RailwayTrackDataset）
    config['data']['train_path'] = args.data_dir
    config['data']['val_path'] = args.data_dir
    
    # 创建训练器
    trainer = EnsembleTrainer(config)
    
    # 创建数据集
    print("创建数据集...")
    
    # 训练集（使用配置文件中的transform配置）
    from src.data.augmentations import get_train_transform, get_val_transform
    
    train_transform = get_train_transform(config)
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    # 验证集
    val_transform = get_val_transform(config)
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val', 
        config=config,
        transform=val_transform
    )
    
    # 为集成训练降低batch_size，避免显存不足
    # 从配置文件读取batch_size，但为了避免显存不足需要调整
    config_train_batch = config['data']['batch_size']['train']
    config_val_batch = config['data']['batch_size']['val']
    
    # 使用配置文件中的batch_size
    ensemble_batch_size = config_train_batch
    val_batch_size = config_val_batch
    
    print(f"使用配置文件中的batch_size: train={ensemble_batch_size}, val={val_batch_size}")
    
    # 创建DataLoader
    train_loader = DataLoader(
        train_dataset,
        batch_size=ensemble_batch_size,
        shuffle=True,
        num_workers=config['project']['num_workers'],
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=val_batch_size,
        shuffle=False,
        num_workers=config['project']['num_workers'],
        pin_memory=True
    )
    
    # 训练模型或加载现有权重
    if not args.skip_training:
        print("开始训练集成模型...")
        weights_paths = trainer.train_all_models(train_loader, val_loader)
    else:
        print("跳过训练，加载现有权重...")
        weights_paths = [
            str(trainer.weights_dir / f"{model_config['name']}.pth")
            for model_config in trainer.models_config
        ]
        
        # 检查权重文件是否存在
        for weights_path in weights_paths:
            if not Path(weights_path).exists():
                raise FileNotFoundError(f"权重文件不存在: {weights_path}")
    
    # 优化集成权重
    print("\n开始优化集成权重...")
    best_weights = trainer.optimize_ensemble_weights(weights_paths, val_loader)
    
    print("\n=== 集成学习训练完成 ===")
    print(f"模型权重保存在: {trainer.weights_dir}")
    
    # 打印最终结果
    print("\n最优集成权重:")
    for class_idx, weight_info in best_weights.items():
        print(f"类别 {class_idx}: {weight_info['weights']} (IoU: {weight_info['iou']:.4f})")


if __name__ == '__main__':
    main() 