#!/usr/bin/env python
"""
测试GPU优化性能
运行前后对比GPU利用率变化
"""

import sys
import time
import json
from pathlib import Path
import argparse
import torch

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import load_config
from src.data import create_dataloaders
from src.utils import setup_logger


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试GPU优化性能')
    
    parser.add_argument(
        '--config',
        type=str,
        default='configs/railway_track_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--iterations',
        type=int,
        default=100,
        help='测试迭代次数'
    )
    parser.add_argument(
        '--gpu',
        type=str,
        default='0',
        help='使用的GPU编号'
    )
    parser.add_argument(
        '--prefetch',
        action='store_true',
        default=False,
        help='是否使用数据预取'
    )
    
    return parser.parse_args()


def gpu_info():
    """获取GPU信息"""
    if not torch.cuda.is_available():
        return {'status': 'unavailable'}
    
    return {
        'name': torch.cuda.get_device_name(0),
        'device_count': torch.cuda.device_count(),
        'current_device': torch.cuda.current_device(),
        'allocated_memory': torch.cuda.memory_allocated() / 1024 ** 2,  # MB
        'reserved_memory': torch.cuda.memory_reserved() / 1024 ** 2,  # MB
        'max_memory': torch.cuda.get_device_properties(0).total_memory / 1024 ** 2  # MB
    }


def test_dataloader_performance(loader, iterations=100, use_prefetch=False):
    """测试数据加载器性能"""
    if use_prefetch:
        from src.data.dataloader import DataPrefetcher
        loader = DataPrefetcher(loader, torch.device('cuda'))
    
    start_time = time.time()
    batch_sizes = []
    
    # 创建一个简单模型测试GPU利用率
    model = torch.nn.Sequential(
        torch.nn.Conv2d(3, 64, kernel_size=3, padding=1),
        torch.nn.ReLU(),
        torch.nn.MaxPool2d(2),
        torch.nn.Conv2d(64, 128, kernel_size=3, padding=1),
        torch.nn.ReLU(),
        torch.nn.MaxPool2d(2)
    ).to('cuda')
    model.train()
    
    # 模拟优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = torch.nn.MSELoss()
    
    # GPU预热
    print("预热GPU...")
    for _ in range(10):
        batch = next(iter(loader))
        if use_prefetch:
            images = batch['image']
        else:
            images = batch['image'].to('cuda')
        outputs = model(images)
        # 使用随机目标进行反向传播
        target = torch.rand_like(outputs)
        loss = criterion(outputs, target)
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
    
    # 测量GPU使用情况
    memory_usage = []
    processing_times = []
    
    print(f"开始测试，循环 {iterations} 次...")
    for i in range(iterations):
        # 获取一个批次
        batch_start = time.time()
        
        try:
            batch = next(iter(loader))
            if use_prefetch:
                images = batch['image']
            else:
                images = batch['image'].to('cuda')
            
            batch_end = time.time()
            
            # 记录批次大小
            if isinstance(batch['image'], torch.Tensor):
                batch_sizes.append(batch['image'].shape[0])
                
            # 前向传播和反向传播
            outputs = model(images)
            target = torch.rand_like(outputs)
            loss = criterion(outputs, target)
            loss.backward()
            optimizer.step()
            optimizer.zero_grad()
            
            # 记录GPU内存
            memory = {
                'allocated': torch.cuda.memory_allocated() / 1024**2,  # MB
                'reserved': torch.cuda.memory_reserved() / 1024**2,    # MB
                'utilization': torch.cuda.utilization()                # %
            }
            memory_usage.append(memory)
            
            # 记录处理时间
            processing_time = time.time() - batch_start
            processing_times.append(processing_time)
            
            # 打印进度
            if (i + 1) % 10 == 0:
                print(f"完成 {i+1}/{iterations} 批次，"
                      f"GPU内存: {memory['allocated']:.1f}MB，"
                      f"GPU利用率: {memory['utilization']}%，"
                      f"批次时间: {processing_time*1000:.2f}ms")
                
            # 强制同步，确保计时准确
            torch.cuda.synchronize()
            
        except StopIteration:
            if i == 0:
                print("错误：数据加载器为空")
                return None
            break
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 收集统计信息
    stats = {
        'iterations': i + 1,
        'total_time_seconds': total_time,
        'avg_batch_size': sum(batch_sizes) / len(batch_sizes) if batch_sizes else 0,
        'batches_per_second': (i + 1) / total_time,
        'avg_batch_time_ms': sum(processing_times) / len(processing_times) * 1000 if processing_times else 0,
        'memory_usage': {
            'min_allocated_mb': min([m['allocated'] for m in memory_usage]) if memory_usage else 0,
            'max_allocated_mb': max([m['allocated'] for m in memory_usage]) if memory_usage else 0,
            'avg_allocated_mb': sum([m['allocated'] for m in memory_usage]) / len(memory_usage) if memory_usage else 0,
            'min_utilization': min([m['utilization'] for m in memory_usage]) if memory_usage else 0,
            'max_utilization': max([m['utilization'] for m in memory_usage]) if memory_usage else 0,
            'avg_utilization': sum([m['utilization'] for m in memory_usage]) / len(memory_usage) if memory_usage else 0
        }
    }
    
    return stats


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置GPU
    import os
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    
    # 检查CUDA可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，请检查GPU设置")
        return 1
    
    # 初始化CUDA
    torch.cuda.init()
    torch.cuda.empty_cache()
    
    # 输出GPU信息
    info = gpu_info()
    print("=" * 60)
    print(f"🔍 GPU信息:")
    print(f"  型号: {info['name']}")
    print(f"  总内存: {info['max_memory']:.1f}MB")
    print(f"  设备数量: {info['device_count']}")
    
    # 设置输出目录
    output_dir = Path("gpu_optimization_tests")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logger = setup_logger(
        'gpu_tests',
        output_dir / 'gpu_tests.log',
        console_level='INFO'
    )
    
    logger.info("=" * 60)
    logger.info("🚀 GPU性能优化测试")
    logger.info("=" * 60)
    logger.info(f"配置文件: {args.config}")
    logger.info(f"GPU设备: {args.gpu}")
    logger.info(f"测试迭代次数: {args.iterations}")
    logger.info(f"使用数据预取: {args.prefetch}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 测试配置
    test_config = config.to_dict()
    
    # 启用/禁用性能优化
    if 'performance_optimization' in test_config['training']:
        test_config['training']['performance_optimization']['use_data_prefetcher'] = args.prefetch
    
    # 缓存当前配置
    with open(output_dir / 'test_config.json', 'w') as f:
        json.dump(test_config, f, indent=2)
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    dataloaders = create_dataloaders(test_config)
    train_loader = dataloaders['train']
    
    # 测试前准备
    torch.backends.cudnn.benchmark = True
    
    # 执行测试
    logger.info("开始性能测试...")
    test_results = test_dataloader_performance(
        train_loader, 
        iterations=args.iterations,
        use_prefetch=args.prefetch
    )
    
    if test_results is None:
        logger.error("测试失败")
        return 1
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info("📊 测试结果:")
    logger.info(f"  总批次数: {test_results['iterations']}")
    logger.info(f"  每秒处理批次: {test_results['batches_per_second']:.2f}")
    logger.info(f"  平均批次时间: {test_results['avg_batch_time_ms']:.2f}ms")
    logger.info(f"  平均批次大小: {test_results['avg_batch_size']:.1f}")
    logger.info(f"  总运行时间: {test_results['total_time_seconds']:.2f}秒")
    logger.info(f"  平均GPU利用率: {test_results['memory_usage']['avg_utilization']:.1f}%")
    logger.info(f"  最大GPU利用率: {test_results['memory_usage']['max_utilization']:.1f}%")
    logger.info(f"  平均GPU内存使用: {test_results['memory_usage']['avg_allocated_mb']:.1f}MB")
    logger.info(f"  最大GPU内存使用: {test_results['memory_usage']['max_allocated_mb']:.1f}MB")
    
    # 保存结果到JSON文件
    result_file = output_dir / f'results_prefetch_{args.prefetch}_{int(time.time())}.json'
    with open(result_file, 'w') as f:
        json.dump(test_results, f, indent=2)
    
    logger.info(f"测试结果已保存到: {result_file}")
    logger.info("=" * 60)
    
    return 0


if __name__ == '__main__':
    sys.exit(main()) 