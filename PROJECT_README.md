# 🚂 Railway Infrastructure Segmentation

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.9+-ee4c2c.svg)](https://pytorch.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

一个专门用于铁路轨道语义分割的深度学习项目，支持多标签分割（主轨道、分叉轨道及重叠区域）。

## ✨ 主要特性

- 🎯 **多标签语义分割**: 同时识别主轨道、分叉轨道和重叠区域
- 💾 **高效存储**: PNG格式相比NPY节省99.9%存储空间
- 🚀 **高性能**: 支持混合精度训练和多GPU并行
- 📊 **丰富的可视化**: 多种可视化风格和TensorBoard集成
- 🔧 **模块化设计**: 易于扩展和定制
- 📱 **多平台部署**: 支持ONNX导出和TensorRT加速

## 📸 效果展示

<table>
<tr>
<td align="center">
<img src="docs/images/sample_input.jpg" width="300px">
<br>
原始图像
</td>
<td align="center">
<img src="docs/images/sample_prediction.png" width="300px">
<br>
预测结果
</td>
<td align="center">
<img src="docs/images/sample_visualization.png" width="300px">
<br>
可视化效果
</td>
</tr>
</table>

## 🚀 快速开始

### 环境准备

```bash
# 克隆仓库
git clone https://github.com/your-username/railway-infrastructure-segmentation.git
cd railway-infrastructure-segmentation-refactor

# 创建虚拟环境
conda create -n railway python=3.8
conda activate railway

# 安装依赖
pip install -r requirements.txt
```

### 15分钟快速体验

```bash
# 1. 准备数据（使用样本数据）
python scripts/generate_multilabel_from_json.py --sample 100

# 2. 开始训练
python src/train.py training.num_epochs=10

# 3. 查看结果
python src/inference.py --checkpoint outputs/checkpoints/latest_model.pth \
                       --image test_image.jpg --output prediction.png

# 4. 可视化
python scripts/visualize_npy_masks.py prediction.png --style gradient
```

详细教程请查看 [快速入门指南](QUICKSTART.md)。

## 📖 文档

- [完整使用指南](docs/README.md) - 详细的使用说明和最佳实践
- [API参考文档](docs/API_REFERENCE.md) - 完整的API接口文档
- [多标签分割指南](docs/multilabel_pipeline_guide.md) - 多标签分割技术详解
- [推理部署指南](docs/inference_usage.md) - 模型部署和优化

## 🏗️ 项目结构

```
railway-infrastructure-segmentation-refactor/
├── configs/                     # 配置文件
├── src/                        # 源代码
│   ├── data/                   # 数据处理
│   ├── models/                 # 模型定义
│   ├── utils/                  # 工具函数
│   ├── train.py               # 训练脚本
│   ├── evaluate.py            # 评估脚本
│   └── inference.py           # 推理脚本
├── scripts/                    # 实用脚本
├── docs/                       # 文档
└── examples/                   # 示例代码
```

## 🎯 主要功能

### 数据处理
- JSON标注解析和多标签掩码生成
- PNG格式多标签编码（BGR通道）
- 灵活的数据增强管道

### 模型训练
- 多种分割架构支持（DeepLabV3+, U-Net, FPN）
- 自定义多标签损失函数
- 混合精度训练和分布式训练

### 评估和推理
- 全面的评估指标（IoU, Dice, 像素准确率）
- 批量预测和视频处理
- 实时推理支持

### 可视化
- 三种可视化风格（实体、混合、渐变）
- TensorBoard集成
- 训练过程和结果可视化

## 📊 性能指标

在测试集上的表现：

| 指标 | 主轨道 | 分叉轨道 | 平均 |
|------|--------|----------|------|
| IoU | 0.92 | 0.85 | 0.89 |
| Dice | 0.96 | 0.91 | 0.94 |
| 像素准确率 | 0.98 | 0.94 | 0.96 |

## 🛠️ 高级功能

### 自定义模型
```python
from src.models.base import BaseSegmentationModel

class CustomModel(BaseSegmentationModel):
    def __init__(self, num_classes=3):
        super().__init__()
        # 实现你的模型
```

### 自定义损失函数
```python
from src.models.multilabel_losses import MultiLabelSegmentationLoss

criterion = MultiLabelSegmentationLoss(
    main_weight=1.0,
    fork_weight=2.0,
    overlap_weight=3.0
)
```

### 模型导出
```bash
# 导出ONNX格式
python scripts/export_onnx.py --checkpoint best_model.pth --output model.onnx

# TensorRT优化
python scripts/optimize_tensorrt.py --onnx model.onnx --output model.trt
```

## 🤝 贡献指南

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与。

## 📝 引用

如果您在研究中使用了本项目，请引用：

```bibtex
@software{railway_segmentation,
  title = {Railway Infrastructure Segmentation},
  author = {Your Name},
  year = {2025},
  url = {https://github.com/your-username/railway-infrastructure-segmentation}
}
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- 感谢所有贡献者
- 基于 PyTorch 和 segmentation_models.pytorch
- 数据标注工具基于 LabelMe

---

<p align="center">
  Made with ❤️ for Railway Safety
</p>