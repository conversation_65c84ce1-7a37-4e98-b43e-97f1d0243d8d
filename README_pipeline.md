# 铁路轨道分割数据处理管线

## 🎯 概述

这是一个完整的铁路分割数据预处理管线，使用**全关键点边界算法**，确保分割掩码严格连接每一个标注关键点。

### 🔧 核心特性

- ✅ **完整关键点利用**: 边界严格连接每一个标注关键点（100%利用率）
- ✅ **标签分离**: 主轨道(标签1)和分叉轨道(标签2)使用不同标签
- ✅ **轨道合并**: Main_Left + Main_Right → 统一主轨道区域
- ✅ **精确边界**: 不使用凸包，避免忽略中间关键点
- ✅ **自动数据划分**: 训练/验证/测试集自动划分
- ✅ **完整验证**: 数据质量自动验证和报告

## 🚀 快速开始

### 1. 基本使用

```bash
# 处理完整数据集
python scripts/railway_data_pipeline.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/processed_data

# 只处理6mm相机数据
python scripts/railway_data_pipeline.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/6mm_data \
  --camera-type 6mm

# 测试模式（只处理10个文件）
python scripts/railway_data_pipeline.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/test_run \
  --max-files 10
```

### 2. 自定义数据划分

```bash
python scripts/railway_data_pipeline.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/custom_split \
  --train-ratio 0.8 \
  --val-ratio 0.1 \
  --test-ratio 0.1
```

### 3. 只生成掩码（不划分数据集）

```bash
python scripts/railway_data_pipeline.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/masks_only \
  --skip-splits
```

## 📊 算法对比

| 特性 | 凸包算法 (旧) | 完整多边形算法 (新) |
|------|---------------|---------------------|
| 关键点利用率 | 38% (8/21点) | 100% (21/21点) ✅ |
| 边界精度 | 粗糙 (只有外围顶点) | 精确 (所有关键点) ✅ |
| 面积准确性 | 过度包围 (+1290%) | 精确贴合 (-91.2%) ✅ |
| 信息丢失 | 忽略中间点 ❌ | 无信息丢失 ✅ |

### 实际效果对比

```
测试样本: 21个主轨道关键点

凸包算法:
- 使用顶点: 8个 (丢失13个关键点)
- 生成面积: 145,223 像素
- 信息利用率: 38%

完整多边形算法:
- 使用顶点: 21个 (全部关键点)
- 生成面积: 12,852 像素  
- 信息利用率: 100% ✅
```

## 📁 输出目录结构

```
output_dir/
├── images/                 # 处理后的图像
├── masks/                  # 生成的掩码文件
├── train/                  # 训练集
│   ├── images/            
│   └── masks/             
├── val/                    # 验证集
│   ├── images/            
│   └── masks/             
├── test/                   # 测试集
│   ├── images/            
│   └── masks/             
├── metadata.json           # 数据元信息
├── split_info.json         # 数据划分信息
├── pipeline_report.md      # 处理报告
└── pipeline_YYYYMMDD_HHMMSS.log  # 详细日志
```

## 🏷️ 标签体系

| 标签值 | 含义 | 来源 | 颜色映射 |
|--------|------|------|----------|
| 0 | 背景 | - | 黑色 |
| 1 | 主轨道 | Main_Left + Main_Right | 绿色 (像素值85) |
| 2 | 分叉轨道 | Fork_Left + Fork_Right | 红色 (像素值170) |

## 🧪 验证和测试

### 单独测试掩码生成

```bash
# 测试单个文件的掩码生成
python scripts/fix_mask_generation.py --test-single

# 测试标签分离功能
python scripts/test_track_labels.py --test-single

# 测试全关键点边界算法
python scripts/test_all_points_boundary.py --test-single
```

### 批量验证

```bash
# 验证生成的掩码质量
python scripts/fix_mask_generation.py \
  --json-dir data/railway_annotation_6mm \
  --images-dir /path/to/images \
  --output-dir outputs/mask_validation
```

## 📋 参数说明

| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--json-dir` | ✅ | - | JSON标注文件目录 |
| `--images-dir` | ❌ | None | 图像文件目录 |
| `--output-dir` | ✅ | - | 输出目录 |
| `--camera-type` | ❌ | None | 相机类型筛选 (6mm/25mm) |
| `--max-files` | ❌ | None | 最大处理文件数 (测试用) |
| `--train-ratio` | ❌ | 0.7 | 训练集比例 |
| `--val-ratio` | ❌ | 0.15 | 验证集比例 |
| `--test-ratio` | ❌ | 0.15 | 测试集比例 |
| `--skip-analysis` | ❌ | False | 跳过数据分析 |
| `--skip-splits` | ❌ | False | 跳过数据集划分 |

## 🔍 数据质量检查

管线会自动执行以下质量检查：

1. **输入验证**: JSON格式、必要字段检查
2. **标注分析**: 轨道类型、关键点统计
3. **掩码验证**: 像素值分布、非零像素检查
4. **数据平衡**: 各数据集的掩码质量验证

## 📈 性能统计

根据实际测试数据：

- **数据集规模**: 21,436个标注文件
- **总轨道数**: 46,479条轨道
- **关键点总数**: 758,962个关键点
- **处理速度**: ~1000文件/分钟
- **关键点利用率**: 100% ✅

## 🚨 注意事项

1. **内存需求**: 大规模数据集处理需要充足内存
2. **存储空间**: 输出目录需要足够空间存储图像和掩码
3. **数据一致性**: 确保JSON标注和图像文件名匹配
4. **算法变更**: 新算法生成的掩码与之前版本不兼容

## 🔧 故障排除

### 常见问题

1. **掩码为空**: 检查JSON标注是否包含有效轨道数据
2. **文件缺失**: 确认图像目录包含对应的图像文件
3. **内存不足**: 减少`--max-files`参数或分批处理
4. **标签错误**: 检查JSON中轨道标签是否为标准格式

### 日志分析

```bash
# 查看详细处理日志
tail -f outputs/processed_data/pipeline_*.log

# 检查错误信息
grep "ERROR" outputs/processed_data/pipeline_*.log
```

## 📖 相关文档

- [`src/data/preprocessing.py`](src/data/preprocessing.py) - 核心预处理类
- [`outputs/track_labels_test/final_all_points_boundary_report.md`](outputs/track_labels_test/final_all_points_boundary_report.md) - 算法修正详细报告
- [`scripts/railway_data_pipeline.py`](scripts/railway_data_pipeline.py) - 完整管线脚本

