{"config": {"augmentation": {"train": [{"p": 0.5, "type": "horizontal_flip"}, {"p": 0.3, "type": "vertical_flip"}, {"limit": 15, "p": 0.5, "type": "rotate"}, {"p": 0.3, "scale": [0.05, 0.1], "type": "perspective"}, {"brightness_limit": 0.2, "contrast_limit": 0.2, "p": 0.5, "type": "brightness_contrast"}, {"blur_limit": 7, "p": 0.3, "type": "motion_blur"}, {"max_height": 128, "max_holes": 8, "max_width": 128, "p": 0.5, "type": "coarse_dropout"}], "val": []}, "camera_configs": {"25mm": {"batch_size": {"train": 4, "val": 6}, "image_size": {"height": 544, "width": 960}}, "6mm": {"batch_size": {"train": 4, "val": 6}, "image_size": {"height": 544, "width": 960}}}, "checkpointing": {"mode": "max", "monitor": "val_iou", "save_dir": "models/checkpoints", "save_last": true, "save_top_k": 5, "auto_resume": true, "cleanup_strategy": "best_k"}, "data": {"batch_size": {"test": 1}, "camera_specific_training": false, "camera_types": ["6mm", "25mm"], "classes": ["background", "main_track", "fork_track"], "dataset_type": "railway_track_dataset", "image_size": {"height": 544, "width": 960}, "json_dir": "data/railway_annotation_6mm", "k_folds": null, "num_classes": 3, "processed_data_path": "/home/<USER>/data/railway_track_dataset", "raw_data_path": "data/raw", "split_ratio": {"test": 0.15, "train": 0.7, "val": 0.15}, "use_multilabel": true, "mask_format": "png", "data_path": "/home/<USER>/data/railway_track_dataset"}, "inference": {"output_dir": "outputs/predictions", "save_overlay": true, "test_time_augmentation": true, "threshold": 0.3, "tta_transforms": ["horizontal_flip", "vertical_flip"]}, "logging": {"log_dir": "outputs/logs", "log_every_n_steps": 10, "log_images": true, "max_images_to_log": 8, "tensorboard": true}, "loss": {"type": "multilabel_combined_loss", "jaccard_weight": 0.5, "focal_weight": 0.5, "alpha": [0.1, 0.3, 0.6], "gamma": 2.0}, "metrics": {"enabled": ["iou", "dice", "precision", "recall", "f1"], "per_class_metrics": true}, "model": {"type": "segmentation_model", "activation": "sigmoid", "architecture": "pan", "classes": 3, "encoder": "efficientnet-b4", "encoder_weights": "imagenet", "ensemble": {"enabled": false, "models": [{"type": "segmentation_model", "encoder": "efficientnet-b4"}, {"type": "segmentation_model", "encoder": "se_resnext50_32x4d"}]}}, "project": {"device": "cuda", "name": "railway-track-segmentation", "num_workers": 8, "seed": 42, "gpu_optimization": {"pin_memory": true, "persistent_workers": true, "prefetch_factor": 2, "use_cuda_graph": true}}, "training": {"epochs": 60, "early_stopping_patience": 25, "gradient_accumulation_steps": 2, "clip_gradient": true, "mixed_precision": true, "eval_interval": 1, "save_interval": 5, "enable_step_saving": true, "save_every_n_steps": 500, "keep_step_checkpoints": 3, "memory_cleanup_interval": 20, "enable_memory_monitoring": true, "memory_leak_threshold": 500.0, "resume_training": true, "save_optimizer_state": true, "save_scheduler_state": true, "save_random_state": true, "validation": {"max_batches": 500, "sample_for_visualization": 2}, "performance_optimization": {"cudnn_benchmark": true, "use_channels_last": true, "use_data_prefetcher": true, "gradient_checkpointing": false, "max_grad_norm": 1.0}, "optimizer": {"betas": [0.9, 0.999], "lr": 0.0003, "type": "adamw", "weight_decay": 0.0001}, "scheduler": {"min_lr": 1e-06, "type": "cosine", "warmup_epochs": 5}}, "visualization": {"colors": {"background": [0, 0, 0], "fork_track": [0, 255, 0], "main_track": [255, 0, 0]}, "overlay_alpha": 0.5, "save_dir": "outputs/visualizations", "save_predictions": true}}, "test_parameters": {"threshold": 0.5, "device": "cuda", "weights_dir": "models/checkpoints/eca_nfnet_stable/weights", "num_samples": 3, "memory_optimized": true}, "metrics": {"overall": {"背景_IoU": 0.9923150487475288, "背景_Dice": 0.9961426483150927, "背景_Accuracy": 0.9923151552287582, "主轨道_IoU": 0.0, "主轨道_Dice": 0.0, "主轨道_Accuracy": 0.9999221303104576, "分叉轨道_IoU": 0.0, "分叉轨道_Dice": 0.0, "分叉轨道_Accuracy": 0.9995110804738562, "Mean_IoU": 0.33077168291584297, "Mean_Dice": 0.33204754943836423, "Mean_Accuracy": 0.9972494553376907}, "per_class_summary": {"class_0": {"mean_iou": 0.9923150487475288, "std_iou": 0.0004641277329576678, "mean_dice": 0.9961426483150927, "std_dice": 0.00023389613171133952, "mean_accuracy": 0.9923151552287582, "std_accuracy": 0.00046397714920460345, "sample_count": 3}, "class_1": {"mean_iou": 0.0, "std_iou": 0.0, "mean_dice": 0.0, "std_dice": 0.0, "mean_accuracy": 0.9999221303104576, "std_accuracy": 2.0995307653453235e-05, "sample_count": 3}, "class_2": {"mean_iou": 0.0, "std_iou": 0.0, "mean_dice": 0.0, "std_dice": 0.0, "mean_accuracy": 0.9995110804738562, "std_accuracy": 0.0006552189859858719, "sample_count": 3}}}, "timestamp": "2025-07-14T15:38:17.202845"}