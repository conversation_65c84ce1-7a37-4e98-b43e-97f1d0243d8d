# Railway Infrastructure Segmentation - 环境安装

## 📋 环境要求

- Python 3.10+
- CUDA 11.8 (推荐)
- GPU内存 >= 8GB (推荐)

## 🚀 快速安装

### 1. 创建conda环境
```bash
conda create -n railway-seg python=3.10
conda activate railway-seg
```

### 2. 安装PyTorch (CUDA 11.8)
```bash
pip install torch==2.0.0+cu118 torchvision==0.15.0+cu118 --index-url https://download.pytorch.org/whl/cu118
```

### 3. 安装其他依赖
```bash
pip install -r requirements.txt
```

### 4. 验证安装
```bash
python verify_environment.py
```

## 📦 主要包版本

- **torch**: 2.0.0+cu118
- **torchvision**: 0.15.0+cu118  
- **segmentation-models-pytorch**: 0.3.4
- **timm**: 0.9.7
- **albumentations**: 1.3.1
- **opencv-contrib-python**: ********

## 🔧 故障排除

### CUDA版本不匹配
如果您的CUDA版本不是11.8，请安装对应版本的PyTorch：

```bash
# CUDA 12.1
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121

# CPU版本
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### 包冲突
如果遇到包版本冲突：

```bash
# 创建新环境
conda create -n railway-seg-clean python=3.10
conda activate railway-seg-clean

# 逐步安装
pip install torch==2.0.0+cu118 torchvision==0.15.0+cu118 --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

## ✅ 验证成功标志

运行 `python verify_environment.py` 应该显示：
```
🎉 环境检查通过！
📊 检查结果: 21/21 包正确
🚀 ✅ CUDA 11.8 可用
```
