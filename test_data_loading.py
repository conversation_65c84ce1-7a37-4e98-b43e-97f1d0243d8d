#!/usr/bin/env python3
"""
Test data loading to ensure everything works correctly.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from train_standalone import SimpleRailwayDataset
import matplotlib.pyplot as plt
import numpy as np


def denormalize_image(image):
    """Denormalize image for visualization."""
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image = image.transpose(1, 2, 0)
    image = image * std + mean
    image = np.clip(image, 0, 1)
    return image


def visualize_sample(dataset, idx=0):
    """Visualize a sample from the dataset."""
    sample = dataset[idx]
    image = sample['image'].numpy()
    mask = sample['mask'].numpy()
    
    # Denormalize image
    image = denormalize_image(image)
    
    # Create visualization
    fig, axes = plt.subplots(1, 4, figsize=(16, 4))
    
    # Original image
    axes[0].imshow(image)
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Background mask
    axes[1].imshow(mask[0], cmap='gray')
    axes[1].set_title('Background')
    axes[1].axis('off')
    
    # Main track mask
    axes[2].imshow(mask[1], cmap='gray')
    axes[2].set_title('Main Track')
    axes[2].axis('off')
    
    # Fork track mask
    axes[3].imshow(mask[2], cmap='gray')
    axes[3].set_title('Fork Track')
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('data_loading_test.png', dpi=150, bbox_inches='tight')
    print(f"Saved visualization to data_loading_test.png")
    plt.close()


def main():
    data_dir = '/home/<USER>/data/railway_track_dataset/train'
    
    print(f"Testing data loading from: {data_dir}")
    
    # Create dataset
    dataset = SimpleRailwayDataset(data_dir, image_size=(544, 960))
    
    if len(dataset) == 0:
        print("No data found!")
        return
    
    # Test loading a few samples
    print(f"\nTesting {min(5, len(dataset))} samples...")
    for i in range(min(5, len(dataset))):
        try:
            sample = dataset[i]
            image = sample['image']
            mask = sample['mask']
            print(f"Sample {i}: Image shape: {image.shape}, Mask shape: {mask.shape}")
            
            # Check mask statistics
            bg_pixels = (mask[0] > 0.5).sum().item()
            main_pixels = (mask[1] > 0.5).sum().item()
            fork_pixels = (mask[2] > 0.5).sum().item()
            
            total_pixels = mask.shape[1] * mask.shape[2]
            print(f"  Background: {bg_pixels}/{total_pixels} ({bg_pixels/total_pixels*100:.1f}%)")
            print(f"  Main track: {main_pixels}/{total_pixels} ({main_pixels/total_pixels*100:.1f}%)")
            print(f"  Fork track: {fork_pixels}/{total_pixels} ({fork_pixels/total_pixels*100:.1f}%)")
            
        except Exception as e:
            print(f"Error loading sample {i}: {str(e)}")
    
    # Visualize first sample
    if len(dataset) > 0:
        print("\nCreating visualization...")
        visualize_sample(dataset, 0)
    
    print("\nData loading test completed!")


if __name__ == '__main__':
    main()