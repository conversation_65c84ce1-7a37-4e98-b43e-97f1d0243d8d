# GPU优化方案与测试

本文档详细说明了铁路轨道分割项目的GPU优化方案及测试方法。

## 1. 问题分析

经过分析，发现项目中GPU利用率低的主要原因包括：

1. **数据加载效率低**：工作线程数不足（num_workers=4），无法充分利用多核CPU进行数据预处理
2. **批次大小设置过小**：batch_size=4/8过小，无法充分利用GPU并行计算能力
3. **内存管理不优化**：频繁的内存清理（每5批次）导致GPU计算被中断
4. **数据预取机制未充分利用**：缺乏高效的数据预取机制，导致GPU等待数据
5. **缺少GPU相关优化配置**：未启用channels_last内存格式、cuDNN基准测试等优化选项

## 2. 优化方案

### 2.1 配置文件优化

对比`configs/railway_track_config.yaml`与`configs/optimized_config.yaml`：

| 参数 | 原始值 | 优化值 | 说明 |
|------|-------|-------|------|
| num_workers | 8 | 12 | 增加工作线程数 |
| batch_size.train | 8 | 12 | 增加训练批次大小 |
| batch_size.val | 8 | 16 | 增加验证批次大小 |
| camera_configs.*.batch_size.train | 4 | 6 | 增加相机特定训练批次大小 |
| camera_configs.*.batch_size.val | 6 | 8 | 增加相机特定验证批次大小 |
| prefetch_factor | 2 | 4 | 增加预取因子 |
| memory_cleanup_interval | 5 | 20 | 减少内存清理频率 |
| memory_leak_threshold | 300 | 500 | 提高内存泄漏阈值 |
| gradient_accumulation_steps | 2 | 1 | 减少梯度累积步数，提高GPU利用率 |
| save_every_n_steps | 500 | 1000 | 减少保存检查点频率 |

同时，添加了以下新的优化配置：

- `non_blocking: true`：启用非阻塞数据传输
- `benchmark_cudnn: true`：启用cuDNN基准测试
- `jit_compile: true`：启用JIT编译
- `compile_mode: "reduce-overhead"`：设置编译模式
- `find_unused_parameters: false`：禁用查找未使用参数
- `deterministic: false`：禁用确定性计算以提高性能
- `optimize_memory_usage: true`：优化内存使用
- `use_tf32: true`：对A100等新GPU启用TF32

### 2.2 增强版数据预取器

创建了`src/data/enhanced_prefetcher.py`，实现了两个高效的数据预取器：

1. **EnhancedDataPrefetcher**：
   - 使用单独的线程进行数据预取
   - 支持非阻塞数据传输
   - 支持channels_last内存格式
   - 提供性能统计信息

2. **MultiDeviceDataPrefetcher**：
   - 支持多GPU并行数据预取
   - 自动将数据分发到多个设备
   - 轮询设备选择机制

### 2.3 增强版内存监控器

创建了`src/utils/enhanced_memory_monitor.py`，提供了更精确的内存监控和管理功能：

- 详细的GPU内存信息收集
- 自动内存监控线程
- 内存泄漏检测和警报
- 内存使用历史记录和可视化
- 代码块内存使用监控

### 2.4 测试脚本

创建了以下测试脚本：

1. **test_gpu_optimization.py**：
   - 测试数据加载性能
   - 比较不同配置下的数据加载效率

2. **benchmark_training.py**：
   - 测试模型训练性能
   - 比较优化前后的训练速度和GPU利用率
   - 生成性能图表

3. **run_tests.sh**：
   - 自动运行所有测试
   - 生成测试报告

## 3. 测试方法

### 3.1 运行测试

```bash
# 确保脚本可执行
chmod +x gpu_optimization_tests/run_tests.sh

# 运行测试
./gpu_optimization_tests/run_tests.sh
```

### 3.2 单独测试

测试数据加载性能：

```bash
python gpu_optimization_tests/test_gpu_optimization.py \
  --config configs/optimized_config.yaml \
  --test-type data \
  --iterations 50
```

测试模型训练性能：

```bash
python gpu_optimization_tests/benchmark_training.py \
  --config configs/railway_track_config.yaml \
  --optimized-config configs/optimized_config.yaml \
  --epochs 3 \
  --iterations 50
```

## 4. 预期效果

通过以上优化，预期可以：

1. **提高GPU利用率**：从不到10%提高到30%以上
2. **减少批次处理时间**：减少20-30%的批次处理时间
3. **提高训练速度**：整体训练速度提升15-25%
4. **减少内存泄漏风险**：通过更精确的内存监控和管理

## 5. 注意事项

1. 批次大小增加可能导致内存不足，需要根据实际GPU内存大小调整
2. 对于高分辨率图像（如25mm相机），可能需要进一步调整批次大小
3. 如果出现内存溢出，可以尝试：
   - 减小批次大小
   - 启用梯度检查点（gradient_checkpointing）
   - 使用混合精度训练（已启用）

## 6. 进一步优化方向

1. 实现自动批次大小查找，根据GPU内存自动选择最优批次大小
2. 探索更高效的数据增强策略，减少CPU处理时间
3. 实现模型量化，减少内存占用
4. 添加分布式训练支持，利用多GPU并行训练 
