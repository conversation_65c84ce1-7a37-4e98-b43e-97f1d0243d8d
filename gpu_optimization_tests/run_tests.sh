#!/bin/bash
# GPU优化测试运行脚本

# 确保脚本在出错时停止
set -e

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.."

# 创建输出目录
mkdir -p gpu_optimization_tests/charts

echo "====================================================================="
echo "🚀 开始GPU优化测试"
echo "====================================================================="

# 安装依赖（如果需要）
echo "检查依赖..."
pip install -q matplotlib tqdm psutil GPUtil

# 1. 测试数据加载性能
echo -e "\n====================================================================="
echo "📊 测试数据加载性能"
echo "====================================================================="

echo "测试原始配置..."
python gpu_optimization_tests/test_gpu_optimization.py \
  --config configs/railway_track_config.yaml \
  --test-type data \
  --iterations 30 \
  --log-file gpu_optimization_tests/data_loading_test.log

echo "测试优化配置..."
python gpu_optimization_tests/test_gpu_optimization.py \
  --config configs/optimized_config.yaml \
  --test-type data \
  --iterations 30 \
  --log-file gpu_optimization_tests/data_loading_test_optimized.log

# 2. 测试模型训练性能
echo -e "\n====================================================================="
echo "📊 测试模型训练性能"
echo "====================================================================="

python gpu_optimization_tests/benchmark_training.py \
  --config configs/railway_track_config.yaml \
  --optimized-config configs/optimized_config.yaml \
  --epochs 2 \
  --iterations 30 \
  --log-file gpu_optimization_tests/benchmark_training.log

# 3. 生成优化报告
echo -e "\n====================================================================="
echo "📝 生成优化报告"
echo "====================================================================="

# 创建报告目录
mkdir -p gpu_optimization_tests/reports

# 生成报告（如果有Python脚本）
# python gpu_optimization_tests/generate_report.py

# 或者简单地汇总结果
echo "汇总测试结果..."
echo "测试完成时间: $(date)" > gpu_optimization_tests/reports/summary.txt
echo "原始配置测试结果:" >> gpu_optimization_tests/reports/summary.txt
grep -A 10 "测试结果:" gpu_optimization_tests/data_loading_test.log | tail -n 10 >> gpu_optimization_tests/reports/summary.txt
echo -e "\n优化配置测试结果:" >> gpu_optimization_tests/reports/summary.txt
grep -A 10 "测试结果:" gpu_optimization_tests/data_loading_test_optimized.log | tail -n 10 >> gpu_optimization_tests/reports/summary.txt
echo -e "\n训练性能比较:" >> gpu_optimization_tests/reports/summary.txt
grep -A 4 "性能比较" gpu_optimization_tests/benchmark_training.log | tail -n 4 >> gpu_optimization_tests/reports/summary.txt

echo -e "\n====================================================================="
echo "🎉 所有测试完成！"
echo "测试报告保存在: gpu_optimization_tests/reports/summary.txt"
echo "性能图表保存在: gpu_optimization_tests/charts/"
echo "=====================================================================" 