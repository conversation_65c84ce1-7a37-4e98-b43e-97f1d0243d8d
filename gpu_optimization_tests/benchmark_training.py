#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
训练性能基准测试脚本
比较优化前后的训练性能
"""

import os
import sys
import time
import json
import logging
import argparse
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler
import yaml
import matplotlib.pyplot as plt
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.dataloader import create_dataloaders
from src.data.enhanced_prefetcher import EnhancedDataPrefetcher
from src.models.segmentation import create_segmentation_model
from src.utils.enhanced_memory_monitor import EnhancedMemoryMonitor, cleanup_cuda_memory
from src.utils.memory_monitor import log_gpu_memory_status


def setup_logger(name, log_file=None, level=logging.INFO):
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s', 
                                  datefmt='%Y-%m-%d %H:%M:%S')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def load_config(config_path):
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def monitor_gpu(device_id=0):
    """监控GPU状态"""
    try:
        if not torch.cuda.is_available():
            return {
                'gpu_util': 0,
                'gpu_memory_used_mb': 0,
                'gpu_memory_total_mb': 0
            }
        
        # 获取GPU使用率
        gpu_util = torch.cuda.utilization(device_id)
        
        # 获取内存使用
        memory_allocated = torch.cuda.memory_allocated(device_id) / (1024 * 1024)  # MB
        memory_reserved = torch.cuda.memory_reserved(device_id) / (1024 * 1024)    # MB
        memory_total = torch.cuda.get_device_properties(device_id).total_memory / (1024 * 1024)  # MB
        
        return {
            'gpu_util': gpu_util,
            'gpu_memory_used_mb': memory_allocated,
            'gpu_memory_reserved_mb': memory_reserved,
            'gpu_memory_total_mb': memory_total
        }
    except Exception as e:
        return {
            'gpu_util': 0,
            'gpu_memory_used_mb': 0,
            'gpu_memory_total_mb': 0,
            'error': str(e)
        }


class BenchmarkTrainer:
    """基准测试训练器"""
    
    def __init__(
        self,
        config_path,
        epochs=3,
        iterations_per_epoch=50,
        use_enhanced_prefetcher=True,
        use_enhanced_memory_monitor=True,
        logger=None
    ):
        """
        初始化基准测试训练器
        
        Args:
            config_path: 配置文件路径
            epochs: 训练轮数
            iterations_per_epoch: 每轮迭代次数
            use_enhanced_prefetcher: 是否使用增强版数据预取器
            use_enhanced_memory_monitor: 是否使用增强版内存监控器
            logger: 日志记录器
        """
        self.config_path = config_path
        self.epochs = epochs
        self.iterations_per_epoch = iterations_per_epoch
        self.use_enhanced_prefetcher = use_enhanced_prefetcher
        self.use_enhanced_memory_monitor = use_enhanced_memory_monitor
        self.logger = logger or logging.getLogger(__name__)
        
        # 加载配置
        self.config = load_config(config_path)
        
        # 设置设备
        self.device = torch.device(self.config['project'].get('device', 'cuda') 
                              if torch.cuda.is_available() else 'cpu')
        
        # 性能指标
        self.metrics = {
            'epoch_times': [],
            'batch_times': [],
            'gpu_utils': [],
            'gpu_memory_usages': [],
            'loss_values': []
        }
        
        # 设置内存监控器
        if use_enhanced_memory_monitor:
            self.memory_monitor = EnhancedMemoryMonitor(
                logger=self.logger,
                check_interval=10.0,
                auto_monitoring=True,
                alert_threshold_mb=1000.0
            )
        else:
            from src.utils.memory_monitor import MemoryMonitor
            self.memory_monitor = MemoryMonitor(self.logger)
    
    def setup(self):
        """设置训练环境"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 设置训练环境")
        self.logger.info("=" * 60)
        
        # 应用GPU优化配置
        if torch.cuda.is_available():
            # 启用cuDNN基准测试
            if self.config['training'].get('performance_optimization', {}).get('cudnn_benchmark', False):
                torch.backends.cudnn.benchmark = True
                self.logger.info("启用cuDNN基准测试")
            
            # 禁用确定性计算以提高性能
            if not self.config['training'].get('performance_optimization', {}).get('deterministic', True):
                torch.backends.cudnn.deterministic = False
                torch.backends.cudnn.enabled = True
                self.logger.info("禁用确定性计算以提高性能")
            
            # 启用TF32（对于Ampere架构）
            if self.config['training'].get('performance_optimization', {}).get('use_tf32', False):
                if hasattr(torch.backends.cuda, 'matmul') and hasattr(torch.backends.cudnn, 'allow_tf32'):
                    torch.backends.cuda.matmul.allow_tf32 = True
                    torch.backends.cudnn.allow_tf32 = True
                    self.logger.info("启用TF32精度")
        
        # 创建数据加载器
        self.logger.info("创建数据加载器...")
        self.dataloaders = create_dataloaders(self.config)
        self.train_loader = self.dataloaders['train']
        
        # 使用增强版数据预取器
        if self.use_enhanced_prefetcher:
            self.logger.info("使用增强版数据预取器")
            self.train_loader = EnhancedDataPrefetcher(
                self.train_loader, 
                self.device,
                queue_size=3,
                non_blocking=True,
                use_channels_last=self.config['training'].get('performance_optimization', {}).get('use_channels_last', False),
                logger=self.logger
            )
        
        # 创建模型
        self.logger.info("创建模型...")
        self.model = create_segmentation_model(self.config['model'])
        self.model = self.model.to(self.device)
        
        # 使用channels_last内存格式
        if self.config['training'].get('performance_optimization', {}).get('use_channels_last', False):
            self.model = self.model.to(memory_format=torch.channels_last)
            self.logger.info("使用channels_last内存格式")
        
        # 使用JIT编译（如果启用）
        if self.config['training'].get('performance_optimization', {}).get('jit_compile', False):
            if hasattr(torch, 'compile'):
                compile_mode = self.config['training'].get('performance_optimization', {}).get('compile_mode', 'reduce-overhead')
                self.model = torch.compile(self.model, mode=compile_mode)
                self.logger.info(f"使用PyTorch 2.0编译模式: {compile_mode}")
        
        # 创建优化器
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=self.config['training']['optimizer'].get('lr', 0.001),
            weight_decay=self.config['training']['optimizer'].get('weight_decay', 0.0001)
        )
        
        # 创建损失函数（简化版，实际应根据配置创建）
        self.loss_fn = nn.BCEWithLogitsLoss()
        
        # 混合精度训练
        self.use_amp = self.config['training'].get('mixed_precision', True)
        if self.use_amp:
            self.scaler = GradScaler()
            self.logger.info("启用混合精度训练")
        
        # 梯度累积
        self.accumulation_steps = self.config['training'].get('gradient_accumulation_steps', 1)
        if self.accumulation_steps > 1:
            self.logger.info(f"使用梯度累积，步数: {self.accumulation_steps}")
        
        # 清理内存
        cleanup_cuda_memory()
        
        # 记录初始内存状态
        log_gpu_memory_status(self.logger)
        
        return self
    
    def run_benchmark(self):
        """运行基准测试"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 开始训练基准测试")
        self.logger.info("=" * 60)
        
        # 记录总开始时间
        total_start_time = time.time()
        
        for epoch in range(self.epochs):
            # 记录每轮开始时间
            epoch_start_time = time.time()
            
            # 训练一轮
            self.train_epoch(epoch)
            
            # 记录每轮结束时间
            epoch_time = time.time() - epoch_start_time
            self.metrics['epoch_times'].append(epoch_time)
            
            self.logger.info(f"Epoch {epoch+1}/{self.epochs} 完成，耗时: {epoch_time:.2f}秒")
            
            # 清理内存
            cleanup_cuda_memory()
        
        # 记录总结束时间
        total_time = time.time() - total_start_time
        
        # 计算统计数据
        avg_epoch_time = np.mean(self.metrics['epoch_times'])
        avg_batch_time = np.mean(self.metrics['batch_times'])
        avg_gpu_util = np.mean(self.metrics['gpu_utils'])
        max_gpu_util = max(self.metrics['gpu_utils'])
        avg_gpu_memory = np.mean(self.metrics['gpu_memory_usages'])
        max_gpu_memory = max(self.metrics['gpu_memory_usages'])
        
        # 输出结果
        self.logger.info("=" * 60)
        self.logger.info("📊 基准测试结果:")
        self.logger.info(f"  总训练时间: {total_time:.2f}秒")
        self.logger.info(f"  平均每轮时间: {avg_epoch_time:.2f}秒")
        self.logger.info(f"  平均批次时间: {avg_batch_time:.2f}ms")
        self.logger.info(f"  平均GPU利用率: {avg_gpu_util:.1f}%")
        self.logger.info(f"  最大GPU利用率: {max_gpu_util:.1f}%")
        self.logger.info(f"  平均GPU内存使用: {avg_gpu_memory:.1f}MB")
        self.logger.info(f"  最大GPU内存使用: {max_gpu_memory:.1f}MB")
        
        # 保存结果
        result = {
            'config_path': self.config_path,
            'epochs': self.epochs,
            'iterations_per_epoch': self.iterations_per_epoch,
            'use_enhanced_prefetcher': self.use_enhanced_prefetcher,
            'use_enhanced_memory_monitor': self.use_enhanced_memory_monitor,
            'total_time_seconds': total_time,
            'avg_epoch_time_seconds': float(avg_epoch_time),
            'avg_batch_time_ms': float(avg_batch_time),
            'memory_usage': {
                'avg_gpu_util': float(avg_gpu_util),
                'max_gpu_util': float(max_gpu_util),
                'avg_gpu_memory_mb': float(avg_gpu_memory),
                'max_gpu_memory_mb': float(max_gpu_memory)
            },
            'detailed_metrics': {
                'epoch_times': [float(t) for t in self.metrics['epoch_times']],
                'batch_times': [float(t) for t in self.metrics['batch_times'][:100]],  # 仅保存前100个样本
                'gpu_utils': [float(u) for u in self.metrics['gpu_utils'][:100]],
                'gpu_memory_usages': [float(m) for m in self.metrics['gpu_memory_usages'][:100]],
                'loss_values': [float(l) for l in self.metrics['loss_values'][:100]]
            }
        }
        
        # 保存结果到JSON文件
        timestamp = int(time.time())
        config_name = os.path.splitext(os.path.basename(self.config_path))[0]
        result_file = f"gpu_optimization_tests/benchmark_{config_name}_prefetcher_{self.use_enhanced_prefetcher}_{timestamp}.json"
        
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        self.logger.info(f"基准测试结果已保存到: {result_file}")
        
        # 绘制性能图表
        self.plot_performance_charts(result_file)
        
        return result
    
    def train_epoch(self, epoch):
        """训练一轮"""
        self.model.train()
        
        # 使用tqdm显示进度
        pbar = tqdm(
            range(self.iterations_per_epoch),
            desc=f'Epoch {epoch+1}/{self.epochs}',
            leave=False
        )
        
        # 重置优化器梯度
        self.optimizer.zero_grad()
        
        # 获取数据迭代器
        train_iter = iter(self.train_loader)
        
        for i in pbar:
            # 记录批次开始时间
            batch_start = time.time()
            
            try:
                # 获取下一批数据
                batch = next(train_iter)
            except StopIteration:
                # 如果迭代器用完，重新创建
                train_iter = iter(self.train_loader)
                batch = next(train_iter)
            
            # 如果不是使用增强版预取器，需要手动移动数据到设备
            if not self.use_enhanced_prefetcher:
                batch = {k: v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
            
            # 获取输入和标签
            images = batch['image']
            masks = batch['mask']
            
            # 转换为channels_last格式（如果启用）
            if self.config['training'].get('performance_optimization', {}).get('use_channels_last', False):
                images = images.to(memory_format=torch.channels_last)
            
            # 前向传播
            if self.use_amp:
                with autocast():
                    outputs = self.model(images)
                    loss = self.loss_fn(outputs, masks)
                    loss = loss / self.accumulation_steps
            else:
                outputs = self.model(images)
                loss = self.loss_fn(outputs, masks)
                loss = loss / self.accumulation_steps
            
            # 反向传播
            if self.use_amp:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # 更新参数（如果达到累积步数）
            if (i + 1) % self.accumulation_steps == 0:
                # 梯度裁剪
                if self.config['training'].get('clip_gradient', False):
                    max_norm = self.config['training'].get('performance_optimization', {}).get('max_grad_norm', 1.0)
                    
                    if self.use_amp:
                        self.scaler.unscale_(self.optimizer)
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm)
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm)
                        self.optimizer.step()
                else:
                    if self.use_amp:
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                    else:
                        self.optimizer.step()
                
                self.optimizer.zero_grad()
            
            # 记录批次结束时间
            batch_end = time.time()
            batch_time = (batch_end - batch_start) * 1000  # 转换为毫秒
            self.metrics['batch_times'].append(batch_time)
            
            # 记录损失值
            self.metrics['loss_values'].append(loss.item() * self.accumulation_steps)
            
            # 监控GPU状态
            gpu_status = monitor_gpu()
            self.metrics['gpu_utils'].append(gpu_status['gpu_util'])
            self.metrics['gpu_memory_usages'].append(gpu_status['gpu_memory_used_mb'])
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f'{loss.item() * self.accumulation_steps:.4f}',
                'time': f'{batch_time:.1f}ms',
                'gpu': f'{gpu_status["gpu_util"]:.0f}%'
            })
            
            # 清理内存
            if (i + 1) % self.config['training'].get('memory_cleanup_interval', 20) == 0:
                cleanup_cuda_memory()
            
            # 显式删除不需要的变量
            del outputs, loss
    
    def plot_performance_charts(self, result_file):
        """绘制性能图表"""
        try:
            # 加载结果
            with open(result_file, 'r') as f:
                result = json.load(f)
            
            # 创建图表目录
            charts_dir = os.path.join(os.path.dirname(result_file), 'charts')
            os.makedirs(charts_dir, exist_ok=True)
            
            # 获取配置名称
            config_name = os.path.splitext(os.path.basename(self.config_path))[0]
            
            # 绘制批次时间
            plt.figure(figsize=(10, 6))
            plt.plot(result['detailed_metrics']['batch_times'])
            plt.title(f'Batch Processing Time ({config_name})')
            plt.xlabel('Batch')
            plt.ylabel('Time (ms)')
            plt.grid(True)
            plt.savefig(os.path.join(charts_dir, f'{config_name}_batch_time.png'))
            
            # 绘制GPU利用率
            plt.figure(figsize=(10, 6))
            plt.plot(result['detailed_metrics']['gpu_utils'])
            plt.title(f'GPU Utilization ({config_name})')
            plt.xlabel('Batch')
            plt.ylabel('Utilization (%)')
            plt.grid(True)
            plt.savefig(os.path.join(charts_dir, f'{config_name}_gpu_util.png'))
            
            # 绘制GPU内存使用
            plt.figure(figsize=(10, 6))
            plt.plot(result['detailed_metrics']['gpu_memory_usages'])
            plt.title(f'GPU Memory Usage ({config_name})')
            plt.xlabel('Batch')
            plt.ylabel('Memory (MB)')
            plt.grid(True)
            plt.savefig(os.path.join(charts_dir, f'{config_name}_gpu_memory.png'))
            
            # 绘制损失值
            plt.figure(figsize=(10, 6))
            plt.plot(result['detailed_metrics']['loss_values'])
            plt.title(f'Loss Values ({config_name})')
            plt.xlabel('Batch')
            plt.ylabel('Loss')
            plt.grid(True)
            plt.savefig(os.path.join(charts_dir, f'{config_name}_loss.png'))
            
            self.logger.info(f"性能图表已保存到: {charts_dir}")
            
        except Exception as e:
            self.logger.error(f"绘制图表失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='训练性能基准测试')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--optimized-config', type=str, default='configs/optimized_config.yaml',
                        help='优化后的配置文件路径')
    parser.add_argument('--epochs', type=int, default=3,
                        help='训练轮数')
    parser.add_argument('--iterations', type=int, default=50,
                        help='每轮迭代次数')
    parser.add_argument('--log-file', type=str, default='gpu_optimization_tests/benchmark_training.log',
                        help='日志文件路径')
    parser.add_argument('--skip-original', action='store_true',
                        help='跳过原始配置测试')
    parser.add_argument('--skip-optimized', action='store_true',
                        help='跳过优化配置测试')
    args = parser.parse_args()
    
    # 确保目录存在
    os.makedirs(os.path.dirname(args.log_file), exist_ok=True)
    
    # 设置日志
    logger = setup_logger('benchmark_training', args.log_file)
    
    logger.info("=" * 80)
    logger.info("🚀 开始训练性能基准测试")
    logger.info("=" * 80)
    
    results = {}
    
    # 测试原始配置
    if not args.skip_original:
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试原始配置")
        logger.info("=" * 60)
        
        # 使用原始配置和基本预取器
        trainer = BenchmarkTrainer(
            config_path=args.config,
            epochs=args.epochs,
            iterations_per_epoch=args.iterations,
            use_enhanced_prefetcher=False,
            use_enhanced_memory_monitor=False,
            logger=logger
        )
        
        trainer.setup()
        results['original'] = trainer.run_benchmark()
    
    # 测试优化配置
    if not args.skip_optimized:
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试优化配置")
        logger.info("=" * 60)
        
        # 使用优化配置和增强预取器
        trainer = BenchmarkTrainer(
            config_path=args.optimized_config,
            epochs=args.epochs,
            iterations_per_epoch=args.iterations,
            use_enhanced_prefetcher=True,
            use_enhanced_memory_monitor=True,
            logger=logger
        )
        
        trainer.setup()
        results['optimized'] = trainer.run_benchmark()
    
    # 如果两种配置都测试了，比较结果
    if 'original' in results and 'optimized' in results:
        logger.info("\n" + "=" * 60)
        logger.info("📊 性能比较")
        logger.info("=" * 60)
        
        orig = results['original']
        opt = results['optimized']
        
        # 计算改进百分比
        time_improvement = (orig['total_time_seconds'] - opt['total_time_seconds']) / orig['total_time_seconds'] * 100
        batch_time_improvement = (orig['avg_batch_time_ms'] - opt['avg_batch_time_ms']) / orig['avg_batch_time_ms'] * 100
        gpu_util_improvement = (opt['memory_usage']['avg_gpu_util'] - orig['memory_usage']['avg_gpu_util'])
        
        logger.info(f"总训练时间: {orig['total_time_seconds']:.2f}秒 -> {opt['total_time_seconds']:.2f}秒 (改进: {time_improvement:.1f}%)")
        logger.info(f"平均批次时间: {orig['avg_batch_time_ms']:.2f}ms -> {opt['avg_batch_time_ms']:.2f}ms (改进: {batch_time_improvement:.1f}%)")
        logger.info(f"平均GPU利用率: {orig['memory_usage']['avg_gpu_util']:.1f}% -> {opt['memory_usage']['avg_gpu_util']:.1f}% (增加: {gpu_util_improvement:.1f}%)")
        logger.info(f"最大GPU利用率: {orig['memory_usage']['max_gpu_util']:.1f}% -> {opt['memory_usage']['max_gpu_util']:.1f}%")
        
        # 绘制比较图表
        try:
            charts_dir = os.path.join(os.path.dirname(args.log_file), 'charts')
            os.makedirs(charts_dir, exist_ok=True)
            
            # 批次时间比较
            plt.figure(figsize=(10, 6))
            plt.plot(orig['detailed_metrics']['batch_times'][:50], label='Original')
            plt.plot(opt['detailed_metrics']['batch_times'][:50], label='Optimized')
            plt.title('Batch Processing Time Comparison')
            plt.xlabel('Batch')
            plt.ylabel('Time (ms)')
            plt.grid(True)
            plt.legend()
            plt.savefig(os.path.join(charts_dir, 'comparison_batch_time.png'))
            
            # GPU利用率比较
            plt.figure(figsize=(10, 6))
            plt.plot(orig['detailed_metrics']['gpu_utils'][:50], label='Original')
            plt.plot(opt['detailed_metrics']['gpu_utils'][:50], label='Optimized')
            plt.title('GPU Utilization Comparison')
            plt.xlabel('Batch')
            plt.ylabel('Utilization (%)')
            plt.grid(True)
            plt.legend()
            plt.savefig(os.path.join(charts_dir, 'comparison_gpu_util.png'))
            
            logger.info(f"比较图表已保存到: {charts_dir}")
            
        except Exception as e:
            logger.error(f"绘制比较图表失败: {e}")
    
    logger.info("\n" + "=" * 80)
    logger.info("🎉 基准测试完成!")
    logger.info("=" * 80)


if __name__ == '__main__':
    main() 