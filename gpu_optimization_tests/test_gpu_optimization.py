#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GPU优化测试脚本
测试不同配置下的数据加载和模型训练性能
"""

import os
import sys
import time
import json
import logging
import argparse
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
from torch.cuda.amp import autocast
import yaml
import psutil
import GPUtil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.dataloader import create_dataloaders
from src.models.segmentation import create_segmentation_model
from src.utils.memory_monitor import MemoryMonitor, cleanup_cuda_memory
from src.data.dataloader import DataPrefetcher


def setup_logger(name, log_file=None, level=logging.INFO):
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s', 
                                  datefmt='%Y-%m-%d %H:%M:%S')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def load_config(config_path):
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def monitor_gpu(device_id=0):
    """监控GPU状态"""
    try:
        gpu = GPUtil.getGPUs()[device_id]
        return {
            'gpu_util': gpu.load * 100,  # 转换为百分比
            'gpu_memory_used_mb': gpu.memoryUsed,
            'gpu_memory_total_mb': gpu.memoryTotal
        }
    except Exception as e:
        return {
            'gpu_util': 0,
            'gpu_memory_used_mb': 0,
            'gpu_memory_total_mb': 0,
            'error': str(e)
        }


def test_data_loading(config, logger, iterations=50, use_prefetcher=True):
    """测试数据加载性能"""
    logger.info("=" * 60)
    logger.info("🚀 数据加载性能测试")
    logger.info("=" * 60)
    
    # 设置设备
    device = torch.device(config['project'].get('device', 'cuda') 
                          if torch.cuda.is_available() else 'cpu')
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    dataloaders = create_dataloaders(config)
    train_loader = dataloaders['train']
    
    # 使用数据预取器（如果启用）
    if use_prefetcher:
        logger.info("使用数据预取器")
        train_loader = DataPrefetcher(train_loader, device)
    
    # 性能测量变量
    batch_times = []
    gpu_utils = []
    gpu_memory_usages = []
    
    # 清理内存
    cleanup_cuda_memory()
    
    logger.info(f"开始测试 ({iterations} 次迭代)...")
    start_time = time.time()
    
    # 模拟数据加载和处理
    for i, batch in enumerate(train_loader):
        if i >= iterations:
            break
            
        # 记录批次开始时间
        batch_start = time.time()
        
        # 如果不使用预取器，需要手动移动数据到设备
        if not use_prefetcher:
            batch = {k: v.to(device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
        
        # 模拟数据处理
        images = batch['image']
        masks = batch['mask']
        
        # 模拟前向传播计算
        with torch.no_grad():
            # 执行一些操作以确保数据被处理
            _ = images.mean()
            _ = masks.sum()
        
        # 记录批次结束时间
        batch_end = time.time()
        batch_time = (batch_end - batch_start) * 1000  # 转换为毫秒
        batch_times.append(batch_time)
        
        # 监控GPU状态
        gpu_status = monitor_gpu()
        gpu_utils.append(gpu_status['gpu_util'])
        gpu_memory_usages.append(gpu_status['gpu_memory_used_mb'])
        
        # 输出进度
        if (i + 1) % 10 == 0:
            logger.info(f"批次 {i+1}/{iterations}, 时间: {batch_time:.2f}ms, "
                       f"GPU: {gpu_status['gpu_util']:.1f}%, "
                       f"内存: {gpu_status['gpu_memory_used_mb']:.1f}MB")
        
        # 清理内存
        if (i + 1) % 20 == 0:
            cleanup_cuda_memory()
    
    total_time = time.time() - start_time
    
    # 计算统计数据
    avg_batch_time = np.mean(batch_times)
    avg_batch_size = train_loader.batch_size if hasattr(train_loader, 'batch_size') else config['data']['batch_size']['train']
    batches_per_second = iterations / total_time
    
    # GPU统计
    min_gpu_util = min(gpu_utils)
    max_gpu_util = max(gpu_utils)
    avg_gpu_util = np.mean(gpu_utils)
    
    min_gpu_memory = min(gpu_memory_usages)
    max_gpu_memory = max(gpu_memory_usages)
    avg_gpu_memory = np.mean(gpu_memory_usages)
    
    # 输出结果
    logger.info("=" * 60)
    logger.info("📊 测试结果:")
    logger.info(f"  总批次数: {iterations}")
    logger.info(f"  每秒处理批次: {batches_per_second:.2f}")
    logger.info(f"  平均批次时间: {avg_batch_time:.2f}ms")
    logger.info(f"  平均批次大小: {avg_batch_size}")
    logger.info(f"  总运行时间: {total_time:.2f}秒")
    logger.info(f"  平均GPU利用率: {avg_gpu_util:.1f}%")
    logger.info(f"  最大GPU利用率: {max_gpu_util:.1f}%")
    logger.info(f"  平均GPU内存使用: {avg_gpu_memory:.1f}MB")
    logger.info(f"  最大GPU内存使用: {max_gpu_memory:.1f}MB")
    
    # 保存结果
    result = {
        'iterations': iterations,
        'total_time_seconds': total_time,
        'avg_batch_size': avg_batch_size,
        'batches_per_second': batches_per_second,
        'avg_batch_time_ms': avg_batch_time,
        'memory_usage': {
            'min_allocated_mb': float(min_gpu_memory),
            'max_allocated_mb': float(max_gpu_memory),
            'avg_allocated_mb': float(avg_gpu_memory),
            'min_utilization': float(min_gpu_util),
            'max_utilization': float(max_gpu_util),
            'avg_utilization': float(avg_gpu_util)
        }
    }
    
    # 保存结果到JSON文件
    timestamp = int(time.time())
    result_file = f"gpu_optimization_tests/results_data_loading_prefetch_{use_prefetcher}_{timestamp}.json"
    with open(result_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    logger.info(f"测试结果已保存到: {result_file}")
    logger.info("=" * 60)
    
    return result


def test_model_training(config, logger, iterations=20):
    """测试模型训练性能"""
    logger.info("=" * 60)
    logger.info("🚀 模型训练性能测试")
    logger.info("=" * 60)
    
    # 设置设备
    device = torch.device(config['project'].get('device', 'cuda') 
                          if torch.cuda.is_available() else 'cpu')
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    dataloaders = create_dataloaders(config)
    train_loader = dataloaders['train']
    
    # 创建模型
    logger.info("创建模型...")
    model = create_segmentation_model(config['model'])
    model = model.to(device)
    
    # 使用channels_last内存格式（如果配置中启用）
    if config['training'].get('performance_optimization', {}).get('use_channels_last', False):
        model = model.to(memory_format=torch.channels_last)
        logger.info("使用channels_last内存格式")
    
    # 启用cuDNN基准测试（如果配置中启用）
    if config['training'].get('performance_optimization', {}).get('cudnn_benchmark', False):
        torch.backends.cudnn.benchmark = True
        logger.info("启用cuDNN基准测试")
    
    # 使用混合精度训练（如果配置中启用）
    use_amp = config['training'].get('mixed_precision', True)
    if use_amp:
        logger.info("启用混合精度训练")
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['training']['optimizer'].get('lr', 0.001),
        weight_decay=config['training']['optimizer'].get('weight_decay', 0.0001)
    )
    
    # 创建损失函数（简化版，实际应根据配置创建）
    loss_fn = nn.BCEWithLogitsLoss()
    
    # 性能测量变量
    batch_times = []
    gpu_utils = []
    gpu_memory_usages = []
    
    # 清理内存
    cleanup_cuda_memory()
    
    # 使用数据预取器（如果配置中启用）
    use_prefetcher = config['training'].get('performance_optimization', {}).get('use_data_prefetcher', False)
    if use_prefetcher:
        logger.info("使用数据预取器")
        train_loader = DataPrefetcher(train_loader, device)
    
    # 梯度累积步数
    accumulation_steps = config['training'].get('gradient_accumulation_steps', 1)
    logger.info(f"使用梯度累积，步数: {accumulation_steps}")
    
    logger.info(f"开始测试 ({iterations} 次迭代)...")
    start_time = time.time()
    
    model.train()
    optimizer.zero_grad()
    
    # 模拟训练过程
    batch_count = 0
    while batch_count < iterations:
        for batch in train_loader:
            if batch_count >= iterations:
                break
                
            # 记录批次开始时间
            batch_start = time.time()
            
            # 如果不使用预取器，需要手动移动数据到设备
            if not use_prefetcher:
                batch = {k: v.to(device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
            
            images = batch['image']
            masks = batch['mask']
            
            # 转换为channels_last格式（如果启用）
            if config['training'].get('performance_optimization', {}).get('use_channels_last', False):
                images = images.to(memory_format=torch.channels_last)
            
            # 前向传播
            if use_amp:
                with autocast():
                    outputs = model(images)
                    loss = loss_fn(outputs, masks)
                    loss = loss / accumulation_steps
            else:
                outputs = model(images)
                loss = loss_fn(outputs, masks)
                loss = loss / accumulation_steps
            
            # 反向传播
            loss.backward()
            
            # 更新参数（如果达到累积步数）
            if (batch_count + 1) % accumulation_steps == 0:
                # 梯度裁剪
                if config['training'].get('clip_gradient', False):
                    max_norm = config['training'].get('performance_optimization', {}).get('max_grad_norm', 1.0)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
                
                optimizer.step()
                optimizer.zero_grad()
            
            # 记录批次结束时间
            batch_end = time.time()
            batch_time = (batch_end - batch_start) * 1000  # 转换为毫秒
            batch_times.append(batch_time)
            
            # 监控GPU状态
            gpu_status = monitor_gpu()
            gpu_utils.append(gpu_status['gpu_util'])
            gpu_memory_usages.append(gpu_status['gpu_memory_used_mb'])
            
            # 输出进度
            if (batch_count + 1) % 5 == 0:
                logger.info(f"批次 {batch_count+1}/{iterations}, 时间: {batch_time:.2f}ms, "
                           f"GPU: {gpu_status['gpu_util']:.1f}%, "
                           f"内存: {gpu_status['gpu_memory_used_mb']:.1f}MB, "
                           f"损失: {loss.item():.4f}")
            
            # 清理内存
            if (batch_count + 1) % config['training'].get('memory_cleanup_interval', 20) == 0:
                cleanup_cuda_memory()
            
            # 显式删除不需要的变量
            del outputs, loss
            
            batch_count += 1
            if batch_count >= iterations:
                break
    
    total_time = time.time() - start_time
    
    # 计算统计数据
    avg_batch_time = np.mean(batch_times)
    avg_batch_size = train_loader.batch_size if hasattr(train_loader, 'batch_size') else config['data']['batch_size']['train']
    batches_per_second = iterations / total_time
    
    # GPU统计
    min_gpu_util = min(gpu_utils)
    max_gpu_util = max(gpu_utils)
    avg_gpu_util = np.mean(gpu_utils)
    
    min_gpu_memory = min(gpu_memory_usages)
    max_gpu_memory = max(gpu_memory_usages)
    avg_gpu_memory = np.mean(gpu_memory_usages)
    
    # 输出结果
    logger.info("=" * 60)
    logger.info("📊 测试结果:")
    logger.info(f"  总批次数: {iterations}")
    logger.info(f"  每秒处理批次: {batches_per_second:.2f}")
    logger.info(f"  平均批次时间: {avg_batch_time:.2f}ms")
    logger.info(f"  平均批次大小: {avg_batch_size}")
    logger.info(f"  总运行时间: {total_time:.2f}秒")
    logger.info(f"  平均GPU利用率: {avg_gpu_util:.1f}%")
    logger.info(f"  最大GPU利用率: {max_gpu_util:.1f}%")
    logger.info(f"  平均GPU内存使用: {avg_gpu_memory:.1f}MB")
    logger.info(f"  最大GPU内存使用: {max_gpu_memory:.1f}MB")
    
    # 保存结果
    result = {
        'iterations': iterations,
        'total_time_seconds': total_time,
        'avg_batch_size': avg_batch_size,
        'batches_per_second': batches_per_second,
        'avg_batch_time_ms': avg_batch_time,
        'memory_usage': {
            'min_allocated_mb': float(min_gpu_memory),
            'max_allocated_mb': float(max_gpu_memory),
            'avg_allocated_mb': float(avg_gpu_memory),
            'min_utilization': float(min_gpu_util),
            'max_utilization': float(max_gpu_util),
            'avg_utilization': float(avg_gpu_util)
        }
    }
    
    # 保存结果到JSON文件
    timestamp = int(time.time())
    result_file = f"gpu_optimization_tests/results_model_training_{timestamp}.json"
    with open(result_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    logger.info(f"测试结果已保存到: {result_file}")
    logger.info("=" * 60)
    
    return result


def main():
    parser = argparse.ArgumentParser(description='GPU优化测试')
    parser.add_argument('--config', type=str, default='configs/railway_track_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--test-type', type=str, default='both',
                        choices=['data', 'model', 'both'],
                        help='测试类型: data=数据加载, model=模型训练, both=两者')
    parser.add_argument('--iterations', type=int, default=50,
                        help='测试迭代次数')
    parser.add_argument('--log-file', type=str, default='gpu_optimization_tests/gpu_optimization_test.log',
                        help='日志文件路径')
    args = parser.parse_args()
    
    # 确保目录存在
    os.makedirs(os.path.dirname(args.log_file), exist_ok=True)
    
    # 设置日志
    logger = setup_logger('gpu_optimization_test', args.log_file)
    
    # 加载配置
    config = load_config(args.config)
    
    # 根据测试类型运行测试
    if args.test_type in ['data', 'both']:
        # 测试不使用预取器的数据加载
        test_data_loading(config, logger, args.iterations, use_prefetcher=False)
        
        # 测试使用预取器的数据加载
        test_data_loading(config, logger, args.iterations, use_prefetcher=True)
    
    if args.test_type in ['model', 'both']:
        # 测试模型训练
        test_model_training(config, logger, args.iterations)


if __name__ == '__main__':
    main() 