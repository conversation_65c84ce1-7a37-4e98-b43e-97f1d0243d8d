{"project": {"name": "railway-track-segmentation", "seed": 42, "device": "cuda", "num_workers": 8, "gpu_optimization": {"pin_memory": true, "persistent_workers": true, "prefetch_factor": 2, "use_cuda_graph": true}}, "data": {"raw_data_path": "data/raw", "processed_data_path": "/home/<USER>/data/Download/railway_track_dataset", "splits_path": "data/splits", "image_size": {"width": 960, "height": 544}, "split_ratio": {"train": 0.7, "val": 0.15, "test": 0.15}, "k_folds": null, "batch_size": {"train": 8, "val": 8, "test": 4}, "camera_specific_training": false, "camera_types": ["6mm", "25mm"], "classes": ["background", "main_track", "fork_track"], "dataset_type": "railway_track_dataset", "json_dir": "data/railway_annotation_6mm", "num_classes": 3}, "training": {"epochs": 150, "early_stopping_patience": 20, "gradient_accumulation_steps": 2, "clip_gradient": true, "mixed_precision": true, "optimizer": {"type": "adamw", "lr": 0.0005, "weight_decay": 0.0001, "betas": [0.9, 0.999]}, "scheduler": {"type": "cosine", "warmup_epochs": 5, "min_lr": 1e-06}, "eval_interval": 1, "save_interval": 5, "enable_step_saving": true, "save_every_n_steps": 500, "keep_step_checkpoints": 3, "memory_cleanup_interval": 20, "enable_memory_monitoring": true, "memory_leak_threshold": 500.0, "resume_training": true, "save_optimizer_state": true, "save_scheduler_state": true, "save_random_state": true, "validation": {"max_batches": 500, "sample_for_visualization": 2}, "performance_optimization": {"cudnn_benchmark": true, "use_channels_last": true, "use_data_prefetcher": true, "gradient_checkpointing": false, "max_grad_norm": 1.0}}, "model": {"type": "segmentation_model", "architecture": "pan", "encoder": "efficientnet-b4", "encoder_weights": "imagenet", "classes": 3, "activation": "sigmoid", "ensemble": {"enabled": false, "models": [{"type": "segmentation_model", "encoder": "efficientnet-b4"}, {"type": "segmentation_model", "encoder": "se_resnext50_32x4d"}]}}, "loss": {"type": "combined_loss", "dice_weight": 0.5, "bce_weight": 0.5, "losses": [{"smooth": 1.0, "type": "dice_loss"}, {"alpha": [0.2, 0.4, 0.4], "gamma": 2.0, "type": "focal_loss"}], "weights": [0.5, 0.5]}, "augmentation": {"train": [{"p": 0.5, "type": "horizontal_flip"}, {"p": 0.3, "type": "vertical_flip"}, {"limit": 15, "p": 0.5, "type": "rotate"}, {"p": 0.3, "scale": [0.05, 0.1], "type": "perspective"}, {"brightness_limit": 0.2, "contrast_limit": 0.2, "p": 0.5, "type": "brightness_contrast"}, {"blur_limit": 7, "p": 0.3, "type": "motion_blur"}, {"max_height": 128, "max_holes": 8, "max_width": 128, "p": 0.5, "type": "coarse_dropout"}], "val": []}, "metrics": {"enabled": ["iou", "dice", "precision", "recall", "f1"], "per_class_metrics": true}, "logging": {"log_dir": "outputs/logs", "tensorboard": true, "log_every_n_steps": 10, "log_images": true, "max_images_to_log": 8}, "checkpointing": {"save_dir": "models/checkpoints", "save_top_k": 5, "monitor": "val_iou", "mode": "max", "save_last": true, "auto_resume": true, "cleanup_strategy": "best_k"}, "inference": {"test_time_augmentation": true, "tta_transforms": ["horizontal_flip", "vertical_flip"], "threshold": 0.5, "output_dir": "outputs/predictions", "save_overlay": true}, "visualization": {"save_dir": "outputs/visualizations", "save_predictions": true, "overlay_alpha": 0.5, "colors": {"railway_track": [255, 0, 0], "rolling_stock": [0, 255, 0], "background": [0, 0, 0], "fork_track": [0, 255, 0], "main_track": [255, 0, 0]}}, "camera_configs": {"25mm": {"batch_size": {"train": 4, "val": 6}, "image_size": {"height": 1088, "width": 1920}}, "6mm": {"batch_size": {"train": 4, "val": 6}, "image_size": {"height": 1088, "width": 1920}}}}