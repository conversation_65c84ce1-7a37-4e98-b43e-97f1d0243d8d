#!/usr/bin/env python3
"""
测试有问题的JSON文件解析和掩码生成
"""

import sys
import json
import numpy as np
from pathlib import Path
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser

def test_problematic_json():
    """测试有问题的JSON文件解析"""
    
    # 目标JSON文件路径
    json_path = Path("/home/<USER>/Downloads/轨道线标注导出/railway_annotation_25mm/20250119132444251.far.avi_frame_830.json")
    
    if not json_path.exists():
        print(f"错误：JSON文件不存在: {json_path}")
        return
    
    print(f"测试JSON文件: {json_path}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    try:
        # 解析JSON文件
        annotation_data = parser.parse_json_file(json_path)
        
        print("\n=== 解析结果 ===")
        print(f"文件名: {annotation_data['filename']}")
        print(f"相机类型: {annotation_data['camera_type']}")
        print(f"轨道数量: {len(annotation_data['tracks'])}")
        
        # 显示轨道信息
        for i, track in enumerate(annotation_data['tracks']):
            print(f"  轨道 {i+1}: {track['label']} - {len(track['points'])} 个点")
            if len(track['points']) > 0:
                print(f"     第一个点: {track['points'][0]}")
                print(f"     最后一个点: {track['points'][-1]}")
        
        # 测试掩码生成
        image_shape = (1080, 1920)
        print(f"\n=== 掩码生成测试 ===")
        print(f"图像尺寸: {image_shape}")
        
        # 生成多标签掩码
        multilabel_mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
        
        print(f"掩码形状: {multilabel_mask.shape}")
        print(f"掩码数据类型: {multilabel_mask.dtype}")
        
        # 统计各通道的像素数
        background_pixels = np.sum(multilabel_mask[:, :, 0] > 0)
        main_track_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_track_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        
        print(f"\n=== 像素统计 ===")
        print(f"背景像素: {background_pixels}")
        print(f"主轨道像素: {main_track_pixels}")
        print(f"分叉轨道像素: {fork_track_pixels}")
        
        # 检查是否有空标签
        if main_track_pixels == 0 and fork_track_pixels == 0:
            print("\n❌ 问题：生成了空标签（没有轨道像素）")
        else:
            print("\n✅ 成功：生成了有效的掩码")
            
            # 保存测试掩码
            test_mask_path = "test_mask_20250119132444251.far.avi_frame_830.png"
            bgr_mask = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
            bgr_mask[:, :, 0] = (multilabel_mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
            bgr_mask[:, :, 1] = (multilabel_mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
            bgr_mask[:, :, 2] = (multilabel_mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
            
            cv2.imwrite(test_mask_path, bgr_mask)
            print(f"测试掩码已保存到: {test_mask_path}")
        
        # 进一步调试
        print("\n=== 详细调试信息 ===")
        print("原始JSON数据:")
        with open(json_path, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        print(f"labels字段数量: {len(raw_data['labels'])}")
        for i, label_data in enumerate(raw_data['labels']):
            label = label_data['label']
            points = label_data['points']
            print(f"  {i+1}. {label}: {len(points)} 个点")
            
            # 解析点数据
            parsed_points = parser._parse_points(points)
            print(f"     解析后: {len(parsed_points)} 个点")
            
            if len(parsed_points) >= 2:
                print(f"     第一个点: {parsed_points[0]}")
                print(f"     最后一个点: {parsed_points[-1]}")
                
                # 检查点是否在图像范围内
                x_coords = [p[0] for p in parsed_points]
                y_coords = [p[1] for p in parsed_points]
                min_x, max_x = min(x_coords), max(x_coords)
                min_y, max_y = min(y_coords), max(y_coords)
                print(f"     X范围: {min_x:.1f} - {max_x:.1f}")
                print(f"     Y范围: {min_y:.1f} - {max_y:.1f}")
                
                # 检查是否有超出图像范围的点
                out_of_bounds = []
                for j, (x, y) in enumerate(parsed_points):
                    if x < 0 or x >= image_shape[1] or y < 0 or y >= image_shape[0]:
                        out_of_bounds.append((j, x, y))
                
                if out_of_bounds:
                    print(f"     ⚠️  发现 {len(out_of_bounds)} 个超出图像范围的点:")
                    for idx, x, y in out_of_bounds[:5]:  # 只显示前5个
                        print(f"       点{idx}: ({x:.1f}, {y:.1f})")
                    if len(out_of_bounds) > 5:
                        print(f"       ... 还有 {len(out_of_bounds) - 5} 个")
        
        # 检查轨道多边形生成
        print("\n=== 轨道多边形生成测试 ===")
        main_left = []
        main_right = []
        fork_left = []
        fork_right = []
        
        for track in annotation_data['tracks']:
            if track['label'] == 'Main_Left':
                main_left = track['points']
            elif track['label'] == 'Main_Right':
                main_right = track['points']
            elif track['label'] == 'Fork_Left':
                fork_left = track['points']
            elif track['label'] == 'Fork_Right':
                fork_right = track['points']
        
        # 测试主轨道多边形
        if main_left and main_right:
            print("测试主轨道多边形生成...")
            track_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
            print(f"  主轨道多边形点数: {len(track_polygon)}")
            if len(track_polygon) >= 3:
                track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
                main_pixels = np.sum(track_mask > 0)
                print(f"  主轨道掩码像素数: {main_pixels}")
            else:
                print("  ❌ 主轨道多边形点数不足")
        
        # 测试分叉轨道多边形
        if fork_left and fork_right:
            print("测试分叉轨道多边形生成...")
            track_polygon = parser.create_track_polygon_from_parallel_lines(fork_left, fork_right)
            print(f"  分叉轨道多边形点数: {len(track_polygon)}")
            if len(track_polygon) >= 3:
                track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
                fork_pixels = np.sum(track_mask > 0)
                print(f"  分叉轨道掩码像素数: {fork_pixels}")
            else:
                print("  ❌ 分叉轨道多边形点数不足")
        
    except Exception as e:
        print(f"❌ 错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_problematic_json() 