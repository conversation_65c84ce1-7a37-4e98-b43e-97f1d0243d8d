#!/usr/bin/env python3
"""
分析问题图像的脚本
帮助理解为什么某些图像预测失败
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import albumentations as A
from albumentations.pytorch import ToTensorV2

def analyze_image(image_path, save_path="problem_image_analysis.png"):
    """分析问题图像"""
    print(f"🔍 分析图像: {image_path}")
    
    # 读取原图
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像: {image_path}")
        return
        
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_height, original_width = image_rgb.shape[:2]
    
    print(f"📏 原始尺寸: {original_width}x{original_height}")
    
    # 预处理变换（与训练时相同）
    transforms = A.Compose([
        <PERSON>.<PERSON>size(height=544, width=960),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])
    
    # 应用变换并反归一化用于可视化
    transformed = transforms(image=image_rgb)
    processed_tensor = transformed['image']
    
    # 反归一化用于可视化
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    
    processed_image = processed_tensor.permute(1, 2, 0).numpy()
    processed_image = (processed_image * std + mean)
    processed_image = np.clip(processed_image, 0, 1)
    
    # 分析图像统计信息
    print(f"\n📊 图像统计信息:")
    print(f"   原图 - 均值: {image_rgb.mean():.2f}, 标准差: {image_rgb.std():.2f}")
    print(f"   原图 - 最小值: {image_rgb.min()}, 最大值: {image_rgb.max()}")
    
    # 分析各个通道
    for i, channel in enumerate(['R', 'G', 'B']):
        channel_data = image_rgb[:, :, i]
        print(f"   {channel}通道 - 均值: {channel_data.mean():.2f}, 标准差: {channel_data.std():.2f}")
    
    # 检查图像亮度分布
    gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
    hist, bins = np.histogram(gray, bins=256, range=(0, 256))
    
    # 计算亮度统计
    dark_pixels = np.sum(hist[:50]) / hist.sum() * 100  # 0-50 暗像素比例
    bright_pixels = np.sum(hist[200:]) / hist.sum() * 100  # 200-255 亮像素比例
    
    print(f"\n💡 亮度分析:")
    print(f"   灰度均值: {gray.mean():.2f}")
    print(f"   暗像素比例 (<50): {dark_pixels:.1f}%")
    print(f"   亮像素比例 (>200): {bright_pixels:.1f}%")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'问题图像分析: {Path(image_path).name}', fontsize=16)
    
    # 原始图像
    axes[0, 0].imshow(image_rgb)
    axes[0, 0].set_title('原始图像')
    axes[0, 0].axis('off')
    
    # 预处理后图像
    axes[0, 1].imshow(processed_image)
    axes[0, 1].set_title('预处理后图像')
    axes[0, 1].axis('off')
    
    # 灰度图像
    axes[0, 2].imshow(gray, cmap='gray')
    axes[0, 2].set_title('灰度图像')
    axes[0, 2].axis('off')
    
    # 直方图
    axes[1, 0].hist(gray.flatten(), bins=50, alpha=0.7, color='blue')
    axes[1, 0].set_title('亮度直方图')
    axes[1, 0].set_xlabel('亮度值')
    axes[1, 0].set_ylabel('像素数量')
    
    # 边缘检测
    edges = cv2.Canny(gray, 50, 150)
    axes[1, 1].imshow(edges, cmap='gray')
    axes[1, 1].set_title('边缘检测')
    axes[1, 1].axis('off')
    
    # RGB通道分布
    colors = ['red', 'green', 'blue']
    for i, color in enumerate(colors):
        channel_hist, _ = np.histogram(image_rgb[:, :, i], bins=50, range=(0, 256))
        axes[1, 2].plot(channel_hist, color=color, alpha=0.7, label=f'{color.upper()}通道')
    axes[1, 2].set_title('RGB通道分布')
    axes[1, 2].set_xlabel('亮度值')
    axes[1, 2].set_ylabel('像素数量')
    axes[1, 2].legend()
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"✅ 分析结果已保存: {save_path}")
    
    # 给出诊断建议
    print(f"\n💡 诊断建议:")
    
    if gray.mean() < 80:
        print("   ⚠️  图像偏暗，可能影响模型识别")
    elif gray.mean() > 180:
        print("   ⚠️  图像偏亮，可能影响模型识别")
    else:
        print("   ✅ 图像亮度正常")
    
    if dark_pixels > 50:
        print("   ⚠️  暗像素过多，可能缺乏细节")
    
    if bright_pixels > 30:
        print("   ⚠️  过曝像素较多，可能丢失细节")
    
    # 建议对比
    print(f"\n🔬 建议对比:")
    print("   1. 查看训练集中亮度相似的图像预测效果")
    print("   2. 尝试不同的预处理参数")
    print("   3. 检查这张图像是否真的包含可见轨道")
    print("   4. 考虑对这类图像进行数据增强")

def compare_with_working_image():
    """对比问题图像和正常工作的图像"""
    problem_image = "/home/<USER>/data/railway_track_dataset/test/images/20250119132744163.near.avi_frame_1291.png"
    working_image = "/home/<USER>/data/railway_track_dataset/images/20250118231400318.near.avi_frame_1461.png"
    
    print("="*80)
    print("🔍 对比分析：问题图像 vs 正常图像")
    print("="*80)
    
    if Path(problem_image).exists():
        print("\n【问题图像分析】")
        analyze_image(problem_image, "problem_image_analysis.png")
    else:
        print("❌ 问题图像不存在")
    
    if Path(working_image).exists():
        print("\n" + "="*50)
        print("【正常图像分析】")
        analyze_image(working_image, "working_image_analysis.png")
    else:
        print("❌ 正常图像不存在")

if __name__ == "__main__":
    compare_with_working_image() 