# 铁路轨道分割 - 快速入门指南

本指南帮助您在15分钟内开始使用铁路轨道分割系统。

## 🚀 快速开始

### 1. 环境准备 (5分钟)

```bash
# 克隆项目
git clone <repository_url>
cd railway-infrastructure-segmentation-refactor

# 创建环境并安装依赖
conda create -n railway python=3.8 -y
conda activate railway
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

### 2. 准备数据 (5分钟)

```bash
# 如果您有JSON标注文件
python scripts/generate_multilabel_from_json.py \
    --json-dir data/railway_annotation_6mm \
    --output-dir /home/<USER>/data/railway_track_dataset \
    --sample 100  # 先用100个样本测试
```

### 3. 开始训练 (5分钟)

```bash
# 使用默认配置快速训练
python train_standalone.py --epochs 10  # 先训练10个epoch测试
```

## 📊 查看结果

### 可视化训练过程
```bash
# 启动TensorBoard
tensorboard --logdir=outputs/runs
# 访问 http://localhost:6006
```

### 可视化预测结果
```bash
# 预测单张图像
python src/inference.py \
    --checkpoint outputs/checkpoints/best_model.pth \
    --image test_image.jpg \
    --output prediction.png

# 可视化掩码
python scripts/visualize_npy_masks.py prediction.png --style gradient
```

## 🎯 常用命令速查

### 数据处理
```bash
# 生成所有掩码
bash scripts/generate_all_masks.sh

# 验证数据
python scripts/check_dataset.py --data-dir /path/to/data
```

### 模型训练
```bash
# 完整训练
python train_standalone.py --epochs 50

# 调整超参数
python train_standalone.py --batch-size 8 --lr 0.001 --epochs 100

# 使用现代训练技术（高级）
python src/train_v2.py --config configs/railway_track_config_modern.yaml
```

### 评估和预测
```bash
# 评估模型
python src/evaluate.py --checkpoint outputs/checkpoints/best_model.pth

# 批量预测
python scripts/predict_inference.py \
    --checkpoint outputs/checkpoints/best_model.pth \
    --input-dir /path/to/images \
    --output-dir /path/to/predictions
```

### 可视化
```bash
# 查看掩码 - 三种风格
python scripts/visualize_npy_masks.py mask.png --style solid    # 实体
python scripts/visualize_npy_masks.py mask.png --style blend    # 混合
python scripts/visualize_npy_masks.py mask.png --style gradient # 渐变

# 批量可视化
python scripts/visualize_npy_masks.py mask.png --batch --num-samples 5
```

## 🔧 常见问题快速解决

### GPU内存不足
```bash
python src/train.py training.batch_size=4  # 减小批次大小
```

### 训练速度慢
```bash
python src/train.py data.num_workers=8  # 增加数据加载线程
```

### 需要更快看到结果
```bash
python src/train.py \
    training.num_epochs=20 \
    training.save_frequency=5 \
    training.val_frequency=5
```

## 📁 重要文件位置

- **配置文件**: `configs/railway_track_config.yaml`
- **训练日志**: `outputs/logs/`
- **模型检查点**: `outputs/checkpoints/`
- **TensorBoard日志**: `outputs/runs/`
- **预测结果**: `outputs/predictions/`

## 💡 下一步

1. 阅读完整文档: `docs/README.md`
2. 查看多标签分割指南: `docs/multilabel_pipeline_guide.md`
3. 了解推理部署: `docs/inference_usage.md`

---

有问题？查看 `docs/README.md` 中的故障排除部分。