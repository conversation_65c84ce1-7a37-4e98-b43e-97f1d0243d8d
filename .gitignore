# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt

# TensorFlow
*.pb
*.h5
*.hdf5

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb_checkpoints

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data files
data/raw/
data/processed/
data/railway_annotation_25mm/
data/railway_annotation_6mm/

# Model files
models/checkpoints/
models/final/
*.pkl
*.joblib

# Output files
outputs/
logs/
runs/
wandb/

# Large notebook files (keep structure but not large outputs)
*.ipynb

# Cache
.cache/
.pytest_cache/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
*.log 