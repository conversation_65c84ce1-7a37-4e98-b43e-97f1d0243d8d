#!/bin/bash
# 内存优化的集成训练运行脚本
# 自动处理内存限制并运行训练+集成学习

echo "=========================================="
echo "内存优化的集成训练脚本"
echo "=========================================="

# 设置默认参数
DATA_DIR="${1:-/home/<USER>/data/railway_track_dataset/}"
OUTPUT_DIR="${2:-ensemble_weights_optimized}"
ENSEMBLE_ITERATIONS="${3:-1000}"

# 检查GPU内存
echo "🔍 检查系统资源..."
if command -v nvidia-smi &> /dev/null; then
    echo "GPU信息:"
    nvidia-smi --query-gpu=memory.total,memory.free --format=csv,noheader,nounits
    
    # 获取GPU内存大小（GB）
    GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
    GPU_MEMORY_GB=$((GPU_MEMORY / 1024))
    
    echo "GPU内存: ${GPU_MEMORY_GB}GB"
    
    # 根据GPU内存自动调整参数
    if [ $GPU_MEMORY_GB -lt 8 ]; then
        echo "⚠️  GPU内存较小，使用节约模式"
        ENSEMBLE_ITERATIONS=500
    elif [ $GPU_MEMORY_GB -lt 12 ]; then
        echo "⚠️  GPU内存中等，使用平衡模式"
        ENSEMBLE_ITERATIONS=800
    else
        echo "✅ GPU内存充足，使用完整模式"
    fi
else
    echo "⚠️  未检测到GPU，将使用CPU模式"
    ENSEMBLE_ITERATIONS=300
fi

echo ""
echo "运行参数:"
echo "  数据目录: $DATA_DIR"
echo "  输出目录: $OUTPUT_DIR"
echo "  集成迭代次数: $ENSEMBLE_ITERATIONS"
echo ""

# 设置内存优化环境变量
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
# 注意：移除 CUDA_LAUNCH_BLOCKING=1 以避免性能问题

# 检查数据目录
if [ ! -d "$DATA_DIR" ]; then
    echo "❌ 错误: 数据目录不存在: $DATA_DIR"
    echo "请提供正确的数据目录路径"
    echo "用法: $0 <数据目录> [输出目录] [迭代次数]"
    exit 1
fi

# 运行训练脚本
echo "🚀 开始内存优化的集成训练..."
python scripts/ensemble_training_notebook_exact_with_fusion.py \
    --data-dir "$DATA_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --ensemble-iterations $ENSEMBLE_ITERATIONS \
    --config configs/railway_track_config.yaml

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "🎉 训练完成!"
    echo "=========================================="
    echo "输出目录: $OUTPUT_DIR"
    echo "查看结果:"
    echo "  - 模型权重: $OUTPUT_DIR/*.pth.tar"
    echo "  - 集成配置: $OUTPUT_DIR/ensemble_config.json"
    echo "  - 训练曲线: $OUTPUT_DIR/*_curve_*.png"
    echo ""
    echo "使用集成模型进行预测:"
    echo "python scripts/ensemble_prediction.py \\"
    echo "    --weights-dir $OUTPUT_DIR \\"
    echo "    --input /path/to/image.jpg \\"
    echo "    --output predictions/"
else
    echo ""
    echo "=========================================="
    echo "❌ 训练失败"
    echo "=========================================="
    echo "可能的解决方案:"
    echo "1. 检查GPU内存是否足够"
    echo "2. 减少集成迭代次数: --ensemble-iterations 300"
    echo "3. 检查数据路径是否正确"
    echo "4. 查看错误日志"
    exit 1
fi 