#!/bin/bash
# 优化的集成训练运行脚本 - 针对显存问题

# 设置默认参数
DATA_DIR="${1:-/home/<USER>/data/railway_track_dataset}"
EXPERIMENT_NAME="${2:-ensemble_$(date +%Y%m%d_%H%M%S)}"

echo "=== 集成训练启动脚本（显存优化版） ==="
echo "数据目录: $DATA_DIR"
echo "实验名称: $EXPERIMENT_NAME"
echo ""

# 检查数据目录
if [ ! -d "$DATA_DIR" ]; then
    echo "错误: 数据目录不存在: $DATA_DIR"
    echo "用法: $0 <数据目录> [实验名称]"
    exit 1
fi

# 设置环境变量优化显存
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:128"
export CUDA_LAUNCH_BLOCKING=1

# 清理显存
echo "清理GPU显存..."
python -c "import torch; torch.cuda.empty_cache() if torch.cuda.is_available() else None"

# 运行训练
echo "开始训练..."
python scripts/ensemble_training_enhanced_fixed.py \
    --config configs/ensemble_training_config.yaml \
    --data-dir "$DATA_DIR" \
    --experiment-name "$EXPERIMENT_NAME"

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "训练完成！"
    echo "查看结果："
    echo "  - 可视化: experiments/$EXPERIMENT_NAME/visualizations/"
    echo "  - 日志: experiments/$EXPERIMENT_NAME/logs/training.log"
    echo "  - TensorBoard: tensorboard --logdir experiments/$EXPERIMENT_NAME/tensorboard"
else
    echo ""
    echo "训练失败，请检查日志"
fi