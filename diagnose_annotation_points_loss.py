#!/usr/bin/env python3
"""
诊断JSON标注点丢失问题的专用脚本
分析为什么标注明明有点，但生成的mask却没有包含的情况
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def analyze_annotation_points_loss(json_path: str, output_dir: str = "outputs"):
    """
    分析JSON标注点丢失问题
    
    Args:
        json_path: JSON标注文件路径
        output_dir: 输出目录
    """
    print(f"🔍 分析JSON标注文件: {json_path}")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    # 1. 解析JSON文件
    try:
        annotation_data = parser.parse_json_file(Path(json_path))
        print(f"✅ JSON解析成功")
        print(f"   文件名: {annotation_data['filename']}")
        print(f"   相机类型: {annotation_data['camera_type']}")
        print(f"   轨道数量: {len(annotation_data['tracks'])}")
    except Exception as e:
        print(f"❌ JSON解析失败: {e}")
        return
    
    # 2. 分析原始JSON数据
    print(f"\n📊 原始JSON数据分析:")
    with open(json_path, 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    
    if 'labels' in raw_data:
        print(f"   labels字段数量: {len(raw_data['labels'])}")
        for i, label_data in enumerate(raw_data['labels']):
            label = label_data['label']
            raw_points = label_data.get('points', [])
            print(f"   {i+1}. {label}: {len(raw_points)} 个原始点")
            
            # 分析点的解析过程
            parsed_points = parser._parse_points(raw_points)
            print(f"      解析后: {len(parsed_points)} 个点")
            
            if len(parsed_points) != len(raw_points):
                print(f"      ⚠️  点数量发生变化: {len(raw_points)} -> {len(parsed_points)}")
                
            # 检查点的坐标范围
            if parsed_points:
                x_coords = [p[0] for p in parsed_points]
                y_coords = [p[1] for p in parsed_points]
                print(f"      X范围: {min(x_coords):.1f} - {max(x_coords):.1f}")
                print(f"      Y范围: {min(y_coords):.1f} - {max(y_coords):.1f}")
                
                # 检查是否有超出图像范围的点
                image_width, image_height = 1920, 1080  # 标准图像尺寸
                out_of_bounds = []
                for j, (x, y) in enumerate(parsed_points):
                    if x < 0 or x >= image_width or y < 0 or y >= image_height:
                        out_of_bounds.append((j, x, y))
                
                if out_of_bounds:
                    print(f"      ⚠️  发现 {len(out_of_bounds)} 个超出图像范围的点:")
                    for j, x, y in out_of_bounds:
                        print(f"        点{j}: ({x:.1f}, {y:.1f})")
    
    # 3. 测试多边形生成
    print(f"\n🔧 多边形生成测试:")
    image_shape = (1080, 1920)
    
    # 分离轨道数据
    main_left = []
    main_right = []
    fork_left = []
    fork_right = []
    
    for track in annotation_data['tracks']:
        label = track['label']
        points = track['points']
        
        if label == 'Main_Left':
            main_left = points
        elif label == 'Main_Right':
            main_right = points
        elif label == 'Fork_Left':
            fork_left = points
        elif label == 'Fork_Right':
            fork_right = points
    
    # 测试主轨道多边形
    if main_left and main_right:
        print("测试主轨道多边形生成...")
        try:
            track_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
            print(f"  主轨道多边形点数: {len(track_polygon)}")
            
            if len(track_polygon) >= 3:
                track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
                main_pixels = np.sum(track_mask > 0)
                print(f"  主轨道掩码像素数: {main_pixels}")
                
                # 可视化主轨道多边形
                visualize_track_polygon(main_left, main_right, track_polygon, track_mask, 
                                       "主轨道", output_path / "main_track_analysis.png")
            else:
                print("  ❌ 主轨道多边形点数不足")
        except Exception as e:
            print(f"  ❌ 主轨道多边形生成失败: {e}")
    else:
        print("❌ 缺少主轨道数据")
    
    # 测试分叉轨道多边形
    if fork_left and fork_right:
        print("测试分叉轨道多边形生成...")
        try:
            track_polygon = parser.create_track_polygon_from_parallel_lines(fork_left, fork_right)
            print(f"  分叉轨道多边形点数: {len(track_polygon)}")
            
            if len(track_polygon) >= 3:
                track_mask = parser.points_to_mask(track_polygon, image_shape, 1)
                fork_pixels = np.sum(track_mask > 0)
                print(f"  分叉轨道掩码像素数: {fork_pixels}")
                
                # 可视化分叉轨道多边形
                visualize_track_polygon(fork_left, fork_right, track_polygon, track_mask, 
                                       "分叉轨道", output_path / "fork_track_analysis.png")
            else:
                print("  ❌ 分叉轨道多边形点数不足")
        except Exception as e:
            print(f"  ❌ 分叉轨道多边形生成失败: {e}")
    else:
        print("❌ 缺少分叉轨道数据")
    
    # 4. 生成完整掩码并分析
    print(f"\n🎯 完整掩码生成测试:")
    try:
        multilabel_mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
        
        # 统计各通道的像素数
        background_pixels = np.sum(multilabel_mask[:, :, 0] > 0)
        main_track_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_track_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        
        total_pixels = image_shape[0] * image_shape[1]
        
        print(f"  掩码形状: {multilabel_mask.shape}")
        print(f"  背景像素: {background_pixels} ({background_pixels/total_pixels*100:.2f}%)")
        print(f"  主轨道像素: {main_track_pixels} ({main_track_pixels/total_pixels*100:.2f}%)")
        print(f"  分叉轨道像素: {fork_track_pixels} ({fork_track_pixels/total_pixels*100:.2f}%)")
        
        # 检查是否有轨道区域
        if main_track_pixels == 0 and fork_track_pixels == 0:
            print("  ❌ 警告: 没有生成任何轨道区域!")
            return analyze_failure_reasons(annotation_data, parser, image_shape)
        elif main_track_pixels == 0:
            print("  ⚠️  警告: 没有生成主轨道区域!")
        elif fork_track_pixels == 0:
            print("  ⚠️  警告: 没有生成分叉轨道区域!")
        else:
            print("  ✅ 成功生成轨道区域")
            
        # 保存可视化结果
        save_mask_visualization(multilabel_mask, output_path / "complete_mask_analysis.png")
        
    except Exception as e:
        print(f"  ❌ 完整掩码生成失败: {e}")
        return analyze_failure_reasons(annotation_data, parser, image_shape)


def visualize_track_polygon(left_points, right_points, polygon_points, mask, track_name, save_path):
    """可视化轨道多边形生成过程"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'{track_name}多边形生成分析', fontsize=16)
    
    # 1. 原始轨道线
    axes[0].set_title('原始轨道线')
    axes[0].set_xlim(0, 1920)
    axes[0].set_ylim(1080, 0)
    
    if left_points:
        left_x, left_y = zip(*left_points)
        axes[0].plot(left_x, left_y, 'g-o', label='Left Track', markersize=4, linewidth=2)
    
    if right_points:
        right_x, right_y = zip(*right_points)
        axes[0].plot(right_x, right_y, 'b-o', label='Right Track', markersize=4, linewidth=2)
    
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. 多边形边界
    axes[1].set_title('多边形边界')
    axes[1].set_xlim(0, 1920)
    axes[1].set_ylim(1080, 0)
    
    if polygon_points:
        polygon_x, polygon_y = zip(*polygon_points)
        axes[1].plot(polygon_x, polygon_y, 'r-', linewidth=2, label='Polygon Boundary')
        axes[1].scatter(polygon_x, polygon_y, c='red', s=20, zorder=5)
        
        # 标注点的顺序
        for i, (x, y) in enumerate(polygon_points):
            axes[1].annotate(str(i), (x+20, y+20), fontsize=8, color='red')
    
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 3. 生成的掩码
    axes[2].set_title('生成的掩码')
    axes[2].imshow(mask, cmap='viridis', aspect='auto')
    axes[2].set_xlabel(f'像素数: {np.sum(mask > 0)}')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"  可视化已保存: {save_path}")


def save_mask_visualization(multilabel_mask, save_path):
    """保存掩码可视化"""
    fig, axes = plt.subplots(1, 4, figsize=(20, 5))
    fig.suptitle('多标签掩码分析', fontsize=16)
    
    # 背景通道
    axes[0].imshow(multilabel_mask[:, :, 0], cmap='gray')
    axes[0].set_title(f'背景 ({np.sum(multilabel_mask[:, :, 0] > 0)} 像素)')
    axes[0].axis('off')
    
    # 主轨道通道
    axes[1].imshow(multilabel_mask[:, :, 1], cmap='Reds')
    axes[1].set_title(f'主轨道 ({np.sum(multilabel_mask[:, :, 1] > 0)} 像素)')
    axes[1].axis('off')
    
    # 分叉轨道通道
    axes[2].imshow(multilabel_mask[:, :, 2], cmap='Greens')
    axes[2].set_title(f'分叉轨道 ({np.sum(multilabel_mask[:, :, 2] > 0)} 像素)')
    axes[2].axis('off')
    
    # 合并可视化
    combined = np.zeros((multilabel_mask.shape[0], multilabel_mask.shape[1], 3))
    combined[:, :, 0] = multilabel_mask[:, :, 1]  # 主轨道 -> 红色
    combined[:, :, 1] = multilabel_mask[:, :, 2]  # 分叉轨道 -> 绿色
    
    axes[3].imshow(combined)
    axes[3].set_title('合并显示 (红=主轨道, 绿=分叉轨道)')
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"  掩码可视化已保存: {save_path}")


def analyze_failure_reasons(annotation_data, parser, image_shape):
    """分析失败原因"""
    print(f"\n🔍 失败原因分析:")
    
    reasons = []
    
    # 检查轨道数据完整性
    track_labels = [track['label'] for track in annotation_data['tracks']]
    
    if 'Main_Left' not in track_labels:
        reasons.append("缺少Main_Left轨道数据")
    if 'Main_Right' not in track_labels:
        reasons.append("缺少Main_Right轨道数据")
    if 'Fork_Left' not in track_labels:
        reasons.append("缺少Fork_Left轨道数据")
    if 'Fork_Right' not in track_labels:
        reasons.append("缺少Fork_Right轨道数据")
    
    # 检查点数量
    for track in annotation_data['tracks']:
        label = track['label']
        points = track['points']
        if len(points) < 2:
            reasons.append(f"{label}轨道点数不足 ({len(points)} < 2)")
    
    # 检查点坐标范围
    for track in annotation_data['tracks']:
        label = track['label']
        points = track['points']
        for i, (x, y) in enumerate(points):
            if x < 0 or x >= 1920 or y < 0 or y >= 1080:
                reasons.append(f"{label}轨道第{i}个点超出图像范围: ({x:.1f}, {y:.1f})")
    
    if reasons:
        print("  发现以下问题:")
        for i, reason in enumerate(reasons, 1):
            print(f"    {i}. {reason}")
    else:
        print("  未发现明显问题，可能是算法实现的bug")
    
    return reasons


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="诊断JSON标注点丢失问题")
    parser.add_argument("--json-path", required=True, help="JSON标注文件路径")
    parser.add_argument("--output-dir", default="outputs", help="输出目录")
    
    args = parser.parse_args()
    
    analyze_annotation_points_loss(args.json_path, args.output_dir)
