#!/usr/bin/env python3
"""
Standalone training script with minimal dependencies.
This script can run independently without complex imports.
"""

import os
import sys
import yaml
import argparse
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import segmentation_models_pytorch as smp
from tqdm import tqdm
import numpy as np
import cv2


class SimpleRailwayDataset(Dataset):
    """Simple dataset class for railway track segmentation."""
    
    def __init__(self, data_dir, image_size=(544, 960)):
        self.data_dir = Path(data_dir)
        self.image_dir = self.data_dir / 'images'
        self.mask_dir = self.data_dir / 'masks'
        self.image_size = image_size
        
        # Get all image files
        self.image_files = sorted(list(self.image_dir.glob('*.jpg')) + 
                                 list(self.image_dir.glob('*.png')))
        
        print(f"Found {len(self.image_files)} images in {self.image_dir}")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # Load image
        image_path = self.image_files[idx]
        image = cv2.imread(str(image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load mask
        mask_path = self.mask_dir / image_path.name.replace('.jpg', '.png')
        if mask_path.exists():
            mask = cv2.imread(str(mask_path), cv2.IMREAD_COLOR)
            # Convert BGR to multi-label format
            multilabel_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.float32)
            multilabel_mask[:, :, 0] = (mask[:, :, 0] > 127).astype(np.float32)  # B: background
            multilabel_mask[:, :, 1] = (mask[:, :, 1] > 127).astype(np.float32)  # G: main track
            multilabel_mask[:, :, 2] = (mask[:, :, 2] > 127).astype(np.float32)  # R: fork track
        else:
            # Create empty mask if not found
            multilabel_mask = np.zeros((image.shape[0], image.shape[1], 3), dtype=np.float32)
            multilabel_mask[:, :, 0] = 1.0  # All background
        
        # Resize
        image = cv2.resize(image, self.image_size)
        multilabel_mask = cv2.resize(multilabel_mask, self.image_size)
        
        # Normalize image
        image = image.astype(np.float32) / 255.0
        image = (image - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
        
        # Convert to tensors
        image = torch.from_numpy(image.transpose(2, 0, 1)).float()
        mask = torch.from_numpy(multilabel_mask.transpose(2, 0, 1)).float()
        
        return {'image': image, 'mask': mask}


class SimpleLoss(nn.Module):
    """Simple multi-label loss combining BCE and Dice."""
    
    def __init__(self):
        super().__init__()
        self.bce = nn.BCEWithLogitsLoss()
    
    def forward(self, pred, target):
        # BCE loss
        bce_loss = self.bce(pred, target)
        
        # Dice loss
        pred_sigmoid = torch.sigmoid(pred)
        intersection = (pred_sigmoid * target).sum(dim=(2, 3))
        union = pred_sigmoid.sum(dim=(2, 3)) + target.sum(dim=(2, 3))
        dice_loss = 1.0 - (2.0 * intersection + 1e-6) / (union + 1e-6)
        dice_loss = dice_loss.mean()
        
        return bce_loss + dice_loss


def calculate_metrics(pred, target, threshold=0.5):
    """Calculate IoU and Dice metrics."""
    pred = (torch.sigmoid(pred) > threshold).float()
    
    # Calculate IoU
    intersection = (pred * target).sum(dim=(2, 3))
    union = (pred + target - pred * target).sum(dim=(2, 3))
    iou = (intersection + 1e-6) / (union + 1e-6)
    
    # Calculate Dice
    dice = (2.0 * intersection + 1e-6) / (pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) + 1e-6)
    
    return iou.mean(), dice.mean()


def train_epoch(model, dataloader, criterion, optimizer, device):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    total_iou = 0
    
    pbar = tqdm(dataloader, desc='Training')
    for batch in pbar:
        images = batch['image'].to(device)
        masks = batch['mask'].to(device)
        
        # Forward pass
        outputs = model(images)
        loss = criterion(outputs, masks)
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Calculate metrics
        with torch.no_grad():
            iou, _ = calculate_metrics(outputs, masks)
        
        total_loss += loss.item()
        total_iou += iou.item()
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}', 'iou': f'{iou.item():.4f}'})
    
    return total_loss / len(dataloader), total_iou / len(dataloader)


def validate(model, dataloader, criterion, device):
    """Validate the model."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc='Validation'):
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            iou, dice = calculate_metrics(outputs, masks)
            
            total_loss += loss.item()
            total_iou += iou.item()
            total_dice += dice.item()
    
    return (total_loss / len(dataloader), 
            total_iou / len(dataloader), 
            total_dice / len(dataloader))


def main():
    parser = argparse.ArgumentParser(description='Standalone training script')
    parser.add_argument('--data-dir', type=str, 
                       default='/home/<USER>/data/railway_track_dataset',
                       help='Path to data directory')
    parser.add_argument('--epochs', type=int, default=10,
                       help='Number of epochs')
    parser.add_argument('--batch-size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    
    args = parser.parse_args()
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create datasets
    print("\nCreating datasets...")
    train_dataset = SimpleRailwayDataset(
        data_dir=os.path.join(args.data_dir, 'train'),
        image_size=(544, 960)
    )
    
    val_dataset = SimpleRailwayDataset(
        data_dir=os.path.join(args.data_dir, 'val'),
        image_size=(544, 960)
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")
    
    # Create model
    print("\nCreating model...")
    model = smp.Unet(
        encoder_name='resnet34',
        encoder_weights='imagenet',
        in_channels=3,
        classes=3,  # Multi-label: background, main_track, fork_track
    )
    model = model.to(device)
    
    # Create loss and optimizer
    criterion = SimpleLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Training loop
    print(f"\nStarting training for {args.epochs} epochs...")
    print("=" * 80)
    
    best_iou = 0
    for epoch in range(1, args.epochs + 1):
        # Train
        train_loss, train_iou = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        
        # Validate
        val_loss, val_iou, val_dice = validate(
            model, val_loader, criterion, device
        )
        
        # Update learning rate
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # Print epoch results
        print(f"\nEpoch {epoch}/{args.epochs} (LR: {current_lr:.6f})")
        print(f"Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}, Dice: {val_dice:.4f}")
        
        # Save best model
        if val_iou > best_iou:
            best_iou = val_iou
            save_path = Path('outputs/checkpoints')
            save_path.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_iou': best_iou,
            }, save_path / 'best_model.pth')
            print(f"Saved best model with IoU: {best_iou:.4f}")
        
        print("-" * 80)
    
    print("\nTraining completed!")
    print(f"Best validation IoU: {best_iou:.4f}")
    
    # Save final model
    torch.save(model.state_dict(), 'outputs/checkpoints/final_model.pth')
    print("Saved final model")


if __name__ == '__main__':
    main()