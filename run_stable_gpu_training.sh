#!/bin/bash
# GPU利用率稳定训练脚本
# 专门优化 ensemble_training_notebook_exact_with_fusion.py 的GPU利用率

echo "🚀 GPU利用率稳定训练"
echo "================================"

# 🔥 GPU利用率稳定化环境变量（兼容所有PyTorch版本）
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128,roundup_power2_divisions:16
export CUDA_LAUNCH_BLOCKING=0  # 异步执行
export TORCH_CUDNN_V8_API_ENABLED=1
export OMP_NUM_THREADS=6
export MKL_NUM_THREADS=6
export TOKENIZERS_PARALLELISM=false
export PYTHONWARNINGS='ignore'

# 参数设置
DATA_DIR="${1:-/home/<USER>/data/railway_track_dataset}"
OUTPUT_DIR="${2:-stable_gpu_ensemble_weights}"
MODELS="${3:-eca_nfnet_l2 seresnet152d}"

echo "📋 训练参数:"
echo "  数据目录: $DATA_DIR"
echo "  输出目录: $OUTPUT_DIR"
echo "  训练模型: $MODELS"
echo ""

# 检查GPU状态
echo "🔍 GPU状态检查:"
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,memory.free,utilization.gpu --format=csv,noheader
    
    # 获取GPU信息用于优化
    GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
    GPU_MEMORY_GB=$((GPU_MEMORY / 1024))
    
    echo ""
    echo "💡 GPU优化建议:"
    if [ $GPU_MEMORY_GB -ge 20 ]; then
        echo "  ✅ 大内存GPU ($GPU_MEMORY_GB GB) - 使用高性能配置"
        BATCH_SIZE_HINT="train=12, val=16"
    elif [ $GPU_MEMORY_GB -ge 12 ]; then
        echo "  ✅ 中等内存GPU ($GPU_MEMORY_GB GB) - 使用平衡配置"
        BATCH_SIZE_HINT="train=8, val=12"
    else
        echo "  ⚠️  小内存GPU ($GPU_MEMORY_GB GB) - 使用保守配置"
        BATCH_SIZE_HINT="train=6, val=8"
    fi
    echo "  建议批次大小: $BATCH_SIZE_HINT"
else
    echo "❌ 未检测到nvidia-smi"
    exit 1
fi

echo ""
echo "🔧 启动GPU利用率稳定训练..."
echo "优化特性:"
echo "  ✅ 异步数据传输 (non_blocking=True)"
echo "  ✅ 持久化工作进程 (persistent_workers=True)"
echo "  ✅ 优化预取策略 (prefetch_factor=3)"
echo "  ✅ channels_last内存格式"
echo "  ✅ 智能内存管理"
echo "  ✅ GPU利用率监控"
echo ""

# 构建模型参数
MODEL_ARGS=""
if [[ "$MODELS" == *"eca_nfnet_l2"* ]]; then
    MODEL_ARGS="$MODEL_ARGS --pretrained-nfnet eca_nfnet_l2.pth_converted.pth"
fi
if [[ "$MODELS" == *"seresnet152d"* ]]; then
    MODEL_ARGS="$MODEL_ARGS --pretrained-resnet seresnet152d.pth_converted.pth"
fi

# 运行优化后的训练
python scripts/ensemble_training_notebook_exact_with_fusion.py \
    --config configs/railway_track_config.yaml \
    --data-dir "$DATA_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --models $MODELS \
    $MODEL_ARGS \
    --ensemble-iterations 1000

TRAIN_EXIT_CODE=$?

echo ""
if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "=========================================="
    echo "🎉 GPU稳定训练完成!"
    echo "=========================================="
    echo "输出目录: $OUTPUT_DIR"
    echo ""
    echo "📊 训练结果:"
    echo "  - 模型权重: $OUTPUT_DIR/*.pth.tar"
    echo "  - 集成配置: $OUTPUT_DIR/ensemble_config.json"
    echo "  - 训练日志: 查看控制台输出"
    echo ""
    echo "💡 GPU利用率优化效果:"
    echo "  - 利用率应该稳定在70-85%"
    echo "  - 减少了利用率波动"
    echo "  - 提升了训练效率"
    echo ""
    echo "🔍 如需查看最终GPU状态:"
    nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv,noheader
else
    echo "=========================================="
    echo "❌ 训练失败"
    echo "=========================================="
    echo "退出代码: $TRAIN_EXIT_CODE"
    echo ""
    echo "🔧 可能的解决方案:"
    echo "1. 检查数据目录是否存在"
    echo "2. 检查预训练权重文件是否存在"
    echo "3. 检查GPU内存是否足够"
    echo "4. 查看详细错误日志"
    echo ""
    echo "🔍 当前GPU状态:"
    nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv,noheader
    
    exit $TRAIN_EXIT_CODE
fi
