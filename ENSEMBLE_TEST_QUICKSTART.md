# 集成学习测试预测 - 快速开始

## 🎯 概述

本文档帮助您快速使用集成学习模型对测试集进行预测和可视化分析。

## 📋 前提条件

### 1. 已训练的集成模型
确保您已经使用 `ensemble_training_enhanced.py` 训练了集成模型，并且有以下文件：

```
outputs/your_experiment/weights/
├── efficientnet_b4.pth        # EfficientNet-B4模型权重
├── eca_nfnet_l2.pth           # ECA-NFNet-L2模型权重  
├── seresnet152d.pth           # SE-ResNet152d模型权重
└── ensemble_weights.yaml      # 集成权重配置(可选)
```

### 2. 测试数据集
准备好测试数据，结构如下：

```
test_data/
└── test/
    ├── images/              # 测试图像(必需)
    │   ├── sample_001.jpg
    │   ├── sample_002.jpg
    │   └── ...
    └── masks/               # 测试标签(可选，用于性能评估)
        ├── sample_001.png
        ├── sample_002.png
        └── ...
```

## 🚀 快速使用

### 基本命令

```bash
python scripts/ensemble_test_prediction.py \
    --weights-dir outputs/your_experiment/weights \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/your/test_data \
    --output-dir ./test_results
```

### 参数说明

- `--weights-dir`: 训练好的模型权重目录
- `--config`: 配置文件路径
- `--data-dir`: 测试数据根目录
- `--output-dir`: 结果输出目录

### 可选参数

```bash
python scripts/ensemble_test_prediction.py \
    --weights-dir outputs/enhanced_experiment/weights \
    --config configs/railway_track_config.yaml \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --output-dir ./detailed_results \
    --threshold 0.6 \        # 预测阈值(默认0.5)
    --num-samples 12 \       # 可视化样本数(默认8)
    --device cuda           # 计算设备(默认auto)
```

## 📊 输出结果

### 目录结构

```
output_dir/
├── test_results.json                 # 完整测试报告
├── summary_results.csv               # 汇总指标
├── detailed_results.csv              # 详细样本数据
└── visualizations/                   # 可视化结果
    ├── basic_predictions/            # 基础预测对比
    ├── metrics_distribution/         # 指标分布分析
    ├── confusion_matrices/           # 混淆矩阵
    ├── error_analysis/               # 错误案例分析
    └── class_performance/            # 类别性能对比
```

### 关键指标

查看 `summary_results.csv` 获取关键性能指标：

- **Mean_IoU**: 平均交并比
- **Mean_Dice**: 平均Dice系数
- **Mean_Accuracy**: 平均像素准确率
- **各类别IoU**: 背景、主轨道、分叉轨道的IoU

## 🎨 可视化结果说明

### 1. 基础预测对比 (`basic_predictions/`)
- 显示原图、各类别概率图、预测结果和叠加图
- 快速评估预测质量

### 2. 指标分布分析 (`metrics_distribution/`)
- IoU分布直方图：了解性能分布
- 箱线图对比：各指标的分布情况

### 3. 混淆矩阵 (`confusion_matrices/`)
- 每个类别的分类准确性
- 识别假阳性和假阴性问题

### 4. 错误案例分析 (`error_analysis/`)
- 最佳/最差样本的详细分析
- 理解模型的失败模式

### 5. 类别性能对比 (`class_performance/`)
- 雷达图：直观的性能对比
- 条形图：具体数值对比

## 🔧 常见问题

### Q1: 权重文件找不到
**错误信息**: `❌ 权重文件不存在`

**解决方案**:
1. 检查权重目录路径是否正确
2. 确保已完成集成模型训练
3. 验证权重文件名是否正确

### Q2: 测试集为空
**错误信息**: `❌ 测试集为空！`

**解决方案**:
1. 检查数据目录结构
2. 确保 `test/images/` 目录存在
3. 验证图像文件格式(.jpg, .png)

### Q3: GPU内存不足
**错误信息**: `RuntimeError: CUDA out of memory`

**解决方案**:
```bash
# 使用CPU
--device cpu

# 或减少可视化样本数
--num-samples 4
```

### Q4: 配置文件错误
**错误信息**: `FileNotFoundError: config.yaml`

**解决方案**:
1. 使用绝对路径指定配置文件
2. 检查配置文件是否存在
3. 使用项目提供的默认配置

## 📖 完整示例

### 示例1: 基础测试
```bash
python scripts/ensemble_test_prediction.py \
    --weights-dir outputs/enhanced_experiment/weights \
    --config configs/railway_track_config.yaml \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --output-dir ./test_results_basic
```

### 示例2: 高精度分析
```bash
python scripts/ensemble_test_prediction.py \
    --weights-dir outputs/enhanced_experiment/weights \
    --config configs/railway_track_config.yaml \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --output-dir ./test_results_detailed \
    --threshold 0.6 \
    --num-samples 16
```

### 示例3: CPU运行
```bash
python scripts/ensemble_test_prediction.py \
    --weights-dir outputs/enhanced_experiment/weights \
    --config configs/railway_track_config.yaml \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --output-dir ./test_results_cpu \
    --device cpu
```

## 🎯 最佳实践

1. **先检查数据**: 确保测试数据格式正确
2. **使用描述性输出目录**: 便于区分不同实验
3. **保存重要结果**: 备份关键的测试结果
4. **对比不同阈值**: 尝试不同阈值找到最佳设置
5. **分析错误案例**: 理解模型的优缺点

## ⚡ 运行示例脚本

我们提供了一个交互式示例脚本：

```bash
python examples/run_ensemble_test_prediction.py
```

这个脚本会：
- 自动检查必要文件
- 提供使用示例
- 交互式运行演示
- 显示详细的结果分析

## 📈 性能预期

- **推理速度**: GPU约1-2秒/张，CPU约5-10秒/张
- **内存需求**: 建议8GB+ RAM，GPU需要6GB+ VRAM
- **存储空间**: 可视化结果约10-50MB/实验

开始您的集成学习测试分析吧！🚄

---

如需更详细的信息，请参考: `docs/ensemble_test_prediction_guide.md` 