#!/usr/bin/env python3
"""
Main training script for railway track segmentation.
This is the entry point for basic training without modern techniques.
"""

import os
import sys
import yaml
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.optim import Adam, SGD
from torch.optim.lr_scheduler import Step<PERSON>, CosineAnnealingLR

from src.data.railway_dataset import RailwayDataset
from src.data.augmentations import get_train_transform, get_val_transform
from src.models.segmentation_model import create_model
from src.models.multilabel_losses import MultiLabelSegmentationLoss
from src.train import train, EpochStats
from src.utils import RunningAverage, iou_coef


def load_config(config_path):
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def override_config(config, overrides):
    """Override config parameters from command line.
    
    Args:
        config: Base configuration dictionary
        overrides: List of strings in format "key.subkey=value"
    """
    for override in overrides:
        if '=' not in override:
            continue
            
        key_path, value = override.split('=', 1)
        keys = key_path.split('.')
        
        # Navigate to the correct nested dictionary
        current = config
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # Set the value (try to infer type)
        try:
            # Try to parse as number
            if '.' in value:
                current[keys[-1]] = float(value)
            else:
                current[keys[-1]] = int(value)
        except ValueError:
            # Parse as boolean or string
            if value.lower() == 'true':
                current[keys[-1]] = True
            elif value.lower() == 'false':
                current[keys[-1]] = False
            else:
                current[keys[-1]] = value
    
    return config


def create_data_loaders(config):
    """Create train and validation data loaders."""
    
    # Get transforms
    train_transform = get_train_transform(config)
    val_transform = get_val_transform(config)
    
    # Create datasets
    train_dataset = RailwayDataset(
        data_dir=config['data'].get('processed_data_path', '/home/<USER>/data/railway_track_dataset') + '/train',
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_dataset = RailwayDataset(
        data_dir=config['data'].get('processed_data_path', '/home/<USER>/data/railway_track_dataset') + '/val',
        split='val',
        config=config,
        transform=val_transform
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training'].get('batch_size', 8),
        shuffle=True,
        num_workers=config['project'].get('num_workers', 4),
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training'].get('batch_size', 8) * 2,
        shuffle=False,
        num_workers=config['project'].get('num_workers', 4),
        pin_memory=True
    )
    
    return train_loader, val_loader


def create_optimizer(model, config):
    """Create optimizer based on config."""
    
    opt_config = config['training'].get('optimizer', {})
    opt_type = opt_config.get('type', 'adam')
    lr = opt_config.get('lr', 1e-3)
    weight_decay = opt_config.get('weight_decay', 1e-4)
    
    if opt_type == 'adam':
        optimizer = Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif opt_type == 'adamw':
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif opt_type == 'sgd':
        optimizer = SGD(model.parameters(), lr=lr, momentum=0.9, weight_decay=weight_decay)
    else:
        raise ValueError(f"Unknown optimizer type: {opt_type}")
    
    return optimizer


def create_scheduler(optimizer, config, train_loader_len):
    """Create learning rate scheduler."""
    
    scheduler_config = config['training'].get('scheduler', {})
    scheduler_type = scheduler_config.get('type', 'step')
    
    if scheduler_type == 'step':
        scheduler = StepLR(optimizer, step_size=30, gamma=0.1)
    elif scheduler_type == 'cosine':
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=config['training'].get('epochs', 100),
            eta_min=scheduler_config.get('min_lr', 1e-6)
        )
    else:
        scheduler = None
    
    return scheduler


def main():
    parser = argparse.ArgumentParser(description='Train railway track segmentation model')
    parser.add_argument('--config', type=str, 
                       default='configs/railway_track_config.yaml',
                       help='Path to config file')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    
    # Parse known args first
    args, unknown = parser.parse_known_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    config = override_config(config, unknown)
    
    # Print configuration
    print("=" * 80)
    print("Railway Track Segmentation Training")
    print("=" * 80)
    print(f"Config: {args.config}")
    print(f"Batch size: {config['training'].get('batch_size', 8)}")
    print(f"Learning rate: {config['training']['optimizer'].get('lr', 1e-3)}")
    print(f"Epochs: {config['training'].get('epochs', 100)}")
    print("=" * 80)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create data loaders
    print("\nCreating data loaders...")
    train_loader, val_loader = create_data_loaders(config)
    print(f"Train samples: {len(train_loader.dataset)}")
    print(f"Val samples: {len(val_loader.dataset)}")
    
    # Create model
    print("\nCreating model...")
    model = create_model(
        architecture=config['model'].get('architecture', 'unet'),
        backbone=config['model'].get('encoder', 'resnet50'),
        num_classes=config['data'].get('num_classes', 3),
        pretrained=config['model'].get('encoder_weights', 'imagenet') == 'imagenet'
    )
    
    # Create loss function
    print("\nCreating loss function...")
    loss_config = config.get('loss', {})
    
    if config['data'].get('use_multilabel', True):
        # Multi-label loss
        criterion = MultiLabelSegmentationLoss(
            main_weight=1.0,
            fork_weight=2.0,
            overlap_weight=3.0
        )
    else:
        # Simple cross entropy
        criterion = nn.CrossEntropyLoss()
    
    # Create optimizer
    print("\nCreating optimizer...")
    optimizer = create_optimizer(model, config)
    
    # Create scheduler
    scheduler = create_scheduler(optimizer, config, len(train_loader))
    
    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume:
        print(f"\nResuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume)
        model.load_state_dict(checkpoint['model_state_dict'])
        if 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'epoch' in checkpoint:
            start_epoch = checkpoint['epoch']
        print(f"Resumed from epoch {start_epoch}")
    
    # Training parameters
    epochs = config['training'].get('epochs', 100)
    accumulate_steps = config['training'].get('gradient_accumulation_steps', 1)
    clip_grad = config['training'].get('clip_gradient', True)
    
    # Model save path
    save_dir = Path('outputs/checkpoints')
    save_dir.mkdir(parents=True, exist_ok=True)
    model_path = save_dir / 'model.pth'
    
    # Train the model
    print(f"\nStarting training for {epochs} epochs...")
    print("=" * 80)
    
    try:
        history = train(
            model=model,
            optimizer=optimizer,
            criterion=criterion,
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=epochs,
            lr_scheduler=scheduler,
            filename=str(model_path),
            accumulate_every_n_epochs=accumulate_steps,
            clip_gradient=clip_grad
        )
        
        print("\nTraining completed successfully!")
        print(f"Best model saved to: {model_path}")
        
        # Print final statistics
        if history:
            final_stats = history[-1]
            print(f"\nFinal epoch {final_stats.epoch}:")
            print(f"  Train loss: {final_stats.train_loss:.4f}")
            print(f"  Val loss: {final_stats.val_loss:.4f}")
            print(f"  Val IoU: {final_stats.val_jac:.4f}")
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
    except Exception as e:
        print(f"\nTraining failed with error: {str(e)}")
        raise


if __name__ == '__main__':
    main()