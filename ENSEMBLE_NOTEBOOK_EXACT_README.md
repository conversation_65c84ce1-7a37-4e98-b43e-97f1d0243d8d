# 集成训练实现 - Notebook精确复现版

## 概述

本实现严格遵循 `Railway_Infrastructure_Segmentation.ipynb` 的训练逻辑，一比一复现了notebook中的所有数据处理和模型设置，同时增加了以下功能：

1. **每个epoch自动保存checkpoint** - 防止训练中断导致进度丢失
2. **断点续训支持** - 可以从任意checkpoint恢复训练
3. **预训练模型加载** - 支持加载项目目录下的.tar文件作为预训练模型

## 文件说明

- `scripts/ensemble_training_notebook_exact.py` - 主训练脚本
- `run_ensemble_notebook_exact.sh` - 便捷运行脚本
- `docs/ensemble_training_notebook_exact_usage.md` - 详细使用文档

## 快速开始

### 1. 准备数据

确保你的数据符合以下格式：
- 图像：PNG格式
- 掩码：PNG格式，像素值为0(背景), 1(主轨道), 2(分叉轨道)

### 2. 开始训练

最简单的方式：
```bash
./run_ensemble_notebook_exact.sh --data-root notebook_data
```

### 3. 断点续训

如果训练中断，使用resume参数继续：
```bash
./run_ensemble_notebook_exact.sh --data-root notebook_data --resume
```

### 4. 使用预训练模型

将预训练的.tar文件放在项目目录下：
```bash
./run_ensemble_notebook_exact.sh \
    --data-root notebook_data \
    --pretrained-efficientnet ./efficientnetb4.pth.tar \
    --pretrained-nfnet ./eca_nfnet_l2.pth.tar \
    --pretrained-resnet ./seresnet152d.pth.tar
```

## 与Notebook的一致性

### 与Notebook的主要差异
- 掩码格式：使用0,1,2标签格式而非6,7,10
- 数据加载：自定义SegmentationDataset类处理0,1,2格式

### 完全相同的部分
- 数据增强配置 (get_transforms函数)
- 模型架构 (PAN + 三种编码器)
- 训练参数 (学习率、优化器、损失函数等)
- 数据划分方式 (KFold with k=20, fold=0)
- 批量大小和累积梯度

### 新增的功能
- Checkpoint保存和恢复
- 预训练模型加载
- 训练配置记录
- 自动生成训练曲线图
- 每个epoch保存验证集可视化

## 输出结构

```
weights/
├── efficientnetb4.pth.tar          # 最佳模型权重
├── eca_nfnet_l2.pth.tar           
├── seresnet152d.pth.tar           
├── history_*                       # 训练历史(dill格式)
├── loss_curve_*.png                # 损失曲线
├── iou_curve_*.png                 # IoU曲线
├── training_config.json            # 训练配置记录
├── checkpoints/                    # 每个epoch的checkpoint
│   ├── efficientnetb4/
│   │   ├── checkpoint_epoch_1.pth
│   │   ├── checkpoint_epoch_2.pth
│   │   └── ...
│   ├── eca_nfnet_l2/
│   └── seresnet152d/
└── visualizations/                # 验证集可视化
    ├── efficientnetb4/            # 每个epoch的可视化图像
    ├── eca_nfnet_l2/
    └── seresnet152d/
```

## 注意事项

1. **标签格式** - 使用0,1,2像素值格式，仅0表示背景，1表示主轨道，2表示分叉轨道
2. **GPU内存要求** - 建议至少16GB显存
3. **训练时间** - 完整训练3个模型约需要12-24小时
4. **磁盘空间** - checkpoint会占用较多空间，建议预留至少50GB

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小batch size
   ./run_ensemble_notebook_exact.sh --batch-size 4
   ```

2. **找不到数据**
   确保数据结构正确：
   ```
   data_root/
   ├── train/
   │   ├── images/   # PNG图像
   │   └── mask/     # PNG掩码(像素值0,1,2)
   ```

3. **Resume失败**
   检查checkpoint目录是否存在

## 验证结果

训练完成后，可以使用notebook中的推理代码进行验证。模型文件格式与notebook完全兼容。

## 联系支持

如有问题，请检查：
1. 数据格式是否正确
2. GPU驱动和CUDA版本
3. Python依赖是否完整安装