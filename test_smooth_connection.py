#!/usr/bin/env python3
"""
测试平滑连接两条轨道线的逻辑
"""

import sys
import json
import numpy as np
from pathlib import Path
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser

def create_smooth_track_polygon(left_points, right_points):
    """
    创建平滑连接的轨道多边形
    
    Args:
        left_points: 左边线的关键点
        right_points: 右边线的关键点
        
    Returns:
        组成轨道多边形的所有顶点
    """
    if len(left_points) < 2 or len(right_points) < 2:
        return []
    
    polygon_points = []
    
    # 1. 添加左边线的所有点（正向）
    polygon_points.extend(left_points)
    
    # 2. 在两条线的端点之间创建平滑连接
    # 找到两条线的端点
    left_end = left_points[-1]  # 左边线最后一个点
    right_end = right_points[-1]  # 右边线最后一个点
    
    # 3. 从左边线末端平滑连接到右边线末端
    # 这里可以添加插值点来创建平滑过渡
    num_interpolation_points = 5
    for i in range(1, num_interpolation_points + 1):
        t = i / (num_interpolation_points + 1)
        # 线性插值
        x = left_end[0] + t * (right_end[0] - left_end[0])
        y = left_end[1] + t * (right_end[1] - left_end[1])
        polygon_points.append((x, y))
    
    # 4. 添加右边线的所有点（反向）
    polygon_points.extend(reversed(right_points))
    
    # 5. 从右边线起点平滑连接到左边线起点
    left_start = left_points[0]  # 左边线第一个点
    right_start = right_points[0]  # 右边线第一个点
    
    for i in range(num_interpolation_points, 0, -1):
        t = i / (num_interpolation_points + 1)
        # 线性插值
        x = right_start[0] + t * (left_start[0] - right_start[0])
        y = right_start[1] + t * (left_start[1] - right_start[1])
        polygon_points.append((x, y))
    
    return polygon_points

def test_smooth_connection():
    """测试平滑连接逻辑"""
    
    # 目标JSON文件路径
    json_path = Path("/home/<USER>/Downloads/轨道线标注导出/railway_annotation_25mm/20250119132444251.far.avi_frame_830.json")
    
    if not json_path.exists():
        print(f"错误：JSON文件不存在: {json_path}")
        return
    
    print(f"测试平滑连接: {json_path}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    try:
        # 解析JSON文件
        annotation_data = parser.parse_json_file(json_path)
        
        # 获取轨道数据
        main_left = []
        main_right = []
        fork_left = []
        fork_right = []
        
        for track in annotation_data['tracks']:
            if track['label'] == 'Main_Left':
                main_left = track['points']
            elif track['label'] == 'Main_Right':
                main_right = track['points']
            elif track['label'] == 'Fork_Left':
                fork_left = track['points']
            elif track['label'] == 'Fork_Right':
                fork_right = track['points']
        
        # 测试原始方法
        print("\n=== 原始方法（直接拼接）===")
        if main_left and main_right:
            original_polygon = parser.create_track_polygon_from_parallel_lines(main_left, main_right)
            print(f"原始多边形点数: {len(original_polygon)}")
            
            # 显示多边形的关键点
            print("原始多边形关键点:")
            print(f"  左边线起点: {original_polygon[0]}")
            print(f"  左边线终点: {original_polygon[len(main_left)-1]}")
            print(f"  右边线终点: {original_polygon[len(main_left)]}")
            print(f"  右边线起点: {original_polygon[-1]}")
        
        # 测试平滑连接方法
        print("\n=== 平滑连接方法 ===")
        if main_left and main_right:
            smooth_polygon = create_smooth_track_polygon(main_left, main_right)
            print(f"平滑多边形点数: {len(smooth_polygon)}")
            
            # 显示多边形的关键点
            print("平滑多边形关键点:")
            print(f"  左边线起点: {smooth_polygon[0]}")
            print(f"  左边线终点: {smooth_polygon[len(main_left)-1]}")
            print(f"  平滑连接点: {smooth_polygon[len(main_left):len(main_left)+5]}")
            print(f"  右边线终点: {smooth_polygon[len(main_left)+5]}")
            print(f"  右边线起点: {smooth_polygon[-6]}")
        
        # 生成掩码对比
        image_shape = (1080, 1920)
        
        if main_left and main_right:
            # 原始掩码
            original_mask = np.zeros(image_shape, dtype=np.uint8)
            if len(original_polygon) >= 3:
                points_int = np.array([(int(x), int(y)) for x, y in original_polygon], dtype=np.int32)
                cv2.fillPoly(original_mask, [points_int], 1)
            
            # 平滑掩码
            smooth_mask = np.zeros(image_shape, dtype=np.uint8)
            if len(smooth_polygon) >= 3:
                points_int = np.array([(int(x), int(y)) for x, y in smooth_polygon], dtype=np.int32)
                cv2.fillPoly(smooth_mask, [points_int], 1)
            
            # 统计像素数
            original_pixels = np.sum(original_mask > 0)
            smooth_pixels = np.sum(smooth_mask > 0)
            
            print(f"\n=== 掩码对比 ===")
            print(f"原始掩码像素数: {original_pixels}")
            print(f"平滑掩码像素数: {smooth_pixels}")
            print(f"差异: {smooth_pixels - original_pixels}")
            
            # 保存对比图像
            comparison = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
            comparison[:, :, 0] = original_mask * 255  # 蓝色：原始掩码
            comparison[:, :, 1] = smooth_mask * 255    # 绿色：平滑掩码
            
            cv2.imwrite("track_connection_comparison.png", comparison)
            print("对比图像已保存到: track_connection_comparison.png")
        
    except Exception as e:
        print(f"❌ 错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_smooth_connection() 