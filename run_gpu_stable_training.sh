#!/bin/bash
# GPU利用率稳定训练脚本

echo "🚀 GPU利用率稳定训练"
echo "================================"

# 设置稳定的环境变量
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128,expandable_segments:True
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDNN_V8_API_ENABLED=1
export OMP_NUM_THREADS=6
export MKL_NUM_THREADS=6
export TOKENIZERS_PARALLELISM=false
export PYTHONWARNINGS='ignore'

# 参数设置
DATA_DIR="${1:-/media/cidi/0B33112D0B33112D/data/railway_track_dataset_6mm}"
OUTPUT_DIR="${2:-gpu_stable_weights}"

echo "数据目录: $DATA_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

# 检查GPU状态
echo "🔍 GPU状态:"
nvidia-smi --query-gpu=name,memory.total,memory.free,utilization.gpu --format=csv,noheader

echo ""
echo "🔧 启动GPU稳定训练..."

# 运行训练
python scripts/ensemble_training_notebook_exact_with_fusion.py \
    --config configs/gpu_stable_training.yaml \
    --data-dir "$DATA_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --models eca_nfnet_l2 seresnet152d \
    --ensemble-iterations 1000

TRAIN_EXIT_CODE=$?

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo ""
    echo "✅ GPU稳定训练完成!"
    echo "输出目录: $OUTPUT_DIR"
else
    echo ""
    echo "❌ 训练失败，退出代码: $TRAIN_EXIT_CODE"
    exit $TRAIN_EXIT_CODE
fi
