#!/usr/bin/env python3
"""
测试训练稳定性修复
验证修复后的训练是否能避免NaN损失和数值不稳定
"""

import subprocess
import sys
import os
import time
from pathlib import Path
import re

def test_training_stability():
    """测试训练稳定性"""
    print("🧪 测试训练稳定性修复")
    print("=" * 80)
    
    # 设置测试参数
    data_dir = "/home/<USER>/data/railway_track_dataset"
    output_dir = "test_training_stability"
    
    # 检查数据目录
    if not Path(data_dir).exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 清理之前的输出
    output_path = Path(output_dir)
    if output_path.exists():
        import shutil
        shutil.rmtree(output_path)
    
    # 构建命令 - 运行1个epoch测试稳定性
    cmd = [
        sys.executable,
        "scripts/ensemble_training_notebook_exact_with_fusion.py",
        "--config", "configs/railway_track_config.yaml",
        "--data-dir", data_dir,
        "--models", "eca_nfnet_l2",
        "--output-dir", output_dir,
        "--ensemble-iterations", "1",  # 只运行1个epoch
        "--skip-ensemble"  # 跳过集成学习
    ]
    
    # 添加预训练权重（如果存在）
    if Path("eca_nfnet_l2.pth_converted.pth").exists():
        cmd.extend(["--pretrained-nfnet", "eca_nfnet_l2.pth_converted.pth"])
    
    print("🚀 运行训练稳定性测试:")
    print(" ".join(cmd))
    print()
    
    try:
        print("开始训练测试（1个epoch）...")
        start_time = time.time()
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示输出并检查稳定性相关信息
        output_lines = []
        loss_values = []
        nan_detected = False
        stability_issues = []
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # 检查NaN或无穷大损失
                if 'nan' in line.lower() or 'inf' in line.lower():
                    nan_detected = True
                    stability_issues.append(line)
                
                # 检查非有限损失值的警告
                if '检测到非有限损失值' in line:
                    stability_issues.append(line)
                
                # 提取损失值
                loss_match = re.search(r'train_loss: ([\d\.]+)', line)
                if loss_match:
                    try:
                        loss_val = float(loss_match.group(1))
                        loss_values.append(loss_val)
                    except:
                        pass
        
        # 等待进程完成
        return_code = process.poll()
        end_time = time.time()
        
        print(f"\n训练测试完成，耗时: {end_time - start_time:.1f}秒")
        print(f"返回代码: {return_code}")
        
        # 分析稳定性
        print(f"\n📊 训练稳定性分析:")
        print("=" * 60)
        
        if return_code == 0:
            print("✅ 训练脚本运行成功！")
            
            # 检查NaN问题
            if nan_detected:
                print("❌ 检测到NaN或无穷大值")
                for issue in stability_issues:
                    print(f"   {issue}")
                return False
            else:
                print("✅ 未检测到NaN或无穷大值")
            
            # 分析损失趋势
            if len(loss_values) > 10:
                initial_losses = loss_values[:5]
                final_losses = loss_values[-5:]
                
                avg_initial = sum(initial_losses) / len(initial_losses)
                avg_final = sum(final_losses) / len(final_losses)
                
                print(f"📈 损失趋势分析:")
                print(f"   初始损失平均值: {avg_initial:.4f}")
                print(f"   最终损失平均值: {avg_final:.4f}")
                print(f"   损失变化: {((avg_final - avg_initial) / avg_initial * 100):+.1f}%")
                
                # 检查损失是否稳定下降
                if avg_final < avg_initial:
                    print("✅ 损失稳定下降")
                    loss_stable = True
                else:
                    print("⚠️  损失未下降或上升")
                    loss_stable = False
            else:
                print("⚠️  损失数据不足，无法分析趋势")
                loss_stable = False
            
            # 检查稳定性问题
            if len(stability_issues) == 0:
                print("✅ 未发现稳定性问题")
                stability_ok = True
            else:
                print(f"⚠️  发现 {len(stability_issues)} 个稳定性问题")
                stability_ok = False
            
            return loss_stable and stability_ok
        else:
            print("❌ 训练脚本运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 运行训练脚本时出错: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件")
    print("=" * 60)
    
    output_dir = Path("test_training_stability")
    if output_dir.exists():
        import shutil
        try:
            shutil.rmtree(output_dir)
            print("✅ 测试文件已清理")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")

def main():
    """主函数"""
    print("🔧 训练稳定性修复测试")
    print("=" * 80)
    
    # 测试训练稳定性
    stability_ok = test_training_stability()
    
    # 清理测试文件
    cleanup_test_files()
    
    print(f"\n📊 测试结果:")
    print("=" * 80)
    print(f"训练稳定性: {'✅ 稳定' if stability_ok else '❌ 不稳定'}")
    
    if stability_ok:
        print(f"\n🎉 训练稳定性修复成功！")
        print("✅ 修复内容:")
        print("1. 降低学习率 (0.0005 -> 0.0001)")
        print("2. 添加权重衰减 (0.01)")
        print("3. 降低最小学习率 (1e-6 -> 1e-7)")
        print("4. 添加数值稳定性检查")
        print("5. 保留梯度裁剪功能")
        print("\n🚀 现在训练应该能避免NaN损失和数值不稳定！")
        print("🎨 可视化功能也应该正常工作")
        print("💾 最佳模型保存功能也应该正常工作")
    else:
        print(f"\n⚠️  训练稳定性仍有问题")
        print("可能需要进一步调整:")
        print("1. 进一步降低学习率")
        print("2. 调整模型架构")
        print("3. 检查数据预处理")
        print("4. 调整损失函数参数")

if __name__ == '__main__':
    main()
