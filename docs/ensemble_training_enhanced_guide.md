# 增强版集成训练脚本使用指南

## 概述

`scripts/ensemble_training_enhanced.py` 是一个功能完备的集成学习训练脚本，包含了TensorBoard可视化、全面监控、及时断点保存等高级功能。

## 🎯 新增功能

### ✅ TensorBoard可视化
- 实时监控训练损失、验证IoU、学习率变化
- GPU和CPU使用率监控
- 内存使用监控
- 预测结果可视化（每5个epoch）
- 模型架构可视化

### ✅ 全面监控
- **SystemMonitor类**：实时监控系统资源
- **GPU监控**：使用率、显存、温度
- **CPU监控**：使用率、内存占用
- **训练速度**：每epoch时间、剩余时间估算

### ✅ 及时断点保存
- **每epoch保存**：`{model_name}_epoch_{epoch}.pth`
- **最新检查点**：`{model_name}_latest.pth`
- **最佳模型**：`{model_name}_best.pth`
- **自动恢复**：训练中断后自动从最新检查点恢复

### ✅ 早停机制
- **EarlyStopping类**：避免过拟合
- **可配置参数**：patience、最小改进阈值
- **智能停止**：IoU不再改善时自动停止

### ✅ Grad-CAM 可视化
- **热力图分析**：显示模型关注的图像区域
- **类别特定**：为每个类别（背景、主轨、支轨）生成热力图
- **叠加显示**：原图、热力图、叠加图并排显示
- **自动目标层**：自动选择最后一个卷积层

### ✅ 全面验证集可视化
- **基础预测可视化**：原图、真实掩码、预测掩码对比
- **错误案例分析**：最佳/最差样本详细分析
- **置信度分析**：概率分布、校准曲线、阈值分析
- **类别性能统计**：IoU分布、相关性分析
- **差异图**：红色=假阳性，绿色=假阴性，白色=正确

### ✅ 详细日志
- **完整记录**：训练过程的所有关键信息
- **多层级日志**：文件日志 + 控制台输出
- **训练报告**：生成JSON格式的完整报告

## 🚀 使用方法

### 基本用法

```bash
# 基本训练（使用默认配置）
python scripts/ensemble_training_enhanced.py \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --experiment-name "enhanced_experiment"

# 指定配置文件
python scripts/ensemble_training_enhanced.py \
    --config configs/railway_track_config.yaml \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --experiment-name "custom_config_run"

# 跳过训练，只优化权重（当已有训练好的模型时）
python scripts/ensemble_training_enhanced.py \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --experiment-name "weights_optimization" \
    --skip-training
```

### TensorBoard监控

```bash
# 启动TensorBoard（训练开始后）
tensorboard --logdir outputs/enhanced_experiment/tensorboard

# 指定端口
tensorboard --logdir outputs/enhanced_experiment/tensorboard --port 6007

# 访问地址
http://localhost:6006  # 或 6007
```

## 📊 可视化导航指南

### 1. SCALARS 标签页 - 训练监控
```
model_name/
├── train/
│   ├── batch_loss           # 每批次训练损失
│   └── learning_rate        # 学习率变化
├── epoch/
│   ├── train_loss          # 每epoch训练损失
│   ├── val_loss            # 验证损失
│   ├── val_iou             # 验证IoU
│   ├── epoch_time          # 每epoch耗时
│   └── eta_seconds         # 剩余时间估算
├── system/
│   ├── cpu_percent         # CPU使用率
│   ├── memory_percent      # 内存使用率
│   └── memory_used         # 内存使用量(GB)
└── gpu/
    ├── gpu_utilization     # GPU使用率
    ├── gpu_memory_used     # GPU显存使用量
    └── gpu_temperature     # GPU温度
```

### 2. IMAGES 标签页 - 可视化分析

#### 基础预测可视化
```
model_name/predictions/class_{class_idx}/sample_{sample_idx}
```
- 每5个epoch更新
- 显示原图、真实掩码、预测掩码

#### 全面验证集分析
```
model_name/validation/
├── basic/                          # 基础预测对比
│   ├── background/sample_X_iou_Y   # 背景类预测
│   ├── main_track/sample_X_iou_Y   # 主轨预测
│   └── branch_track/sample_X_iou_Y # 支轨预测
├── error_analysis/                 # 错误案例分析
│   ├── background/
│   │   ├── worst_1                 # 最差样本1
│   │   ├── worst_2                 # 最差样本2
│   │   ├── best_1                  # 最佳样本1
│   │   └── best_2                  # 最佳样本2
│   ├── main_track/...
│   └── branch_track/...
├── confidence_analysis/            # 置信度分析
│   ├── background                  # 概率分布+校准曲线
│   ├── main_track
│   └── branch_track
├── class_statistics/               # 类别统计
│   └── overall                     # 箱线图+相关性矩阵
└── gradcam/                       # Grad-CAM热力图
    ├── background/sample_X         # 热力图可视化
    ├── main_track/sample_X
    └── branch_track/sample_X
```

## 🔧 实验目录结构

```
outputs/experiment_name/
├── weights/                        # 最佳模型权重
│   ├── efficientnet_b4.pth
│   ├── eca_nfnet_l2.pth
│   ├── seresnet152d.pth
│   └── ensemble_weights.yaml
├── checkpoints/                    # 训练检查点
│   ├── efficientnet_b4_latest.pth
│   ├── efficientnet_b4_best.pth
│   ├── efficientnet_b4_epoch_X.pth
│   └── ...
├── logs/                          # 训练日志
│   └── training_YYYYMMDD_HHMMSS.log
├── tensorboard/                   # TensorBoard日志
│   └── events.out.tfevents.*
├── visualizations/                # 可视化保存目录
└── training_report.json          # 完整训练报告
```

## ⚙️ 配置说明

### 可视化频率配置
```python
# 在train_single_model方法中可调整
basic_visualization_interval = 5     # 基础预测可视化频率
comprehensive_interval = 10         # 全面分析频率
num_samples = 8                     # 分析样本数量
```

### 早停配置
```python
early_stopping_patience = 15        # 等待轮数
min_delta = 0.001                   # 最小改进阈值
```

### 系统监控配置
```python
system_monitor_interval = 50        # 每50个batch记录一次系统状态
```

## 🚨 注意事项

### 1. 显存使用
- Grad-CAM会占用额外显存
- 全面可视化需要额外内存
- 建议在显存充足时使用

### 2. 性能影响
- 可视化会增加训练时间（约10-15%）
- matplotlib图表生成较慢
- 可通过调整频率来平衡

### 3. 存储空间
- TensorBoard日志占用大量空间
- 建议定期清理旧实验数据
- 可视化图片较大，注意存储容量

### 4. 依赖要求
```bash
# 必须安装的包
pip install opencv-python matplotlib seaborn tensorboard GPUtil psutil
```

## 📈 性能对比

| 功能 | 原版脚本 | 增强版脚本 |
|------|---------|------------|
| 基础训练 | ✅ | ✅ |
| TensorBoard可视化 | ❌ | ✅ |
| 断点保存 | ❌ | ✅ 每epoch+自动恢复 |
| 系统监控 | ❌ | ✅ 完整监控 |
| Grad-CAM | ❌ | ✅ 多类别热力图 |
| 错误分析 | ❌ | ✅ 深度分析 |
| 置信度分析 | ❌ | ✅ 校准+阈值 |
| 类别统计 | ❌ | ✅ 相关性矩阵 |
| 早停机制 | ❌ | ✅ 可配置 |

## 💡 最佳实践

### 1. 实验管理
```bash
# 使用描述性的实验名称
--experiment-name "efficientnet_b4_focal_alpha_0.1_0.3_0.6"
--experiment-name "gradcam_analysis_$(date +%Y%m%d_%H%M%S)"
```

### 2. 可视化分析流程
1. **训练监控**：观察损失和IoU曲线
2. **系统监控**：确保资源使用合理
3. **基础可视化**：每5个epoch检查预测质量
4. **Grad-CAM分析**：理解模型关注区域
5. **错误分析**：找出失败案例的原因
6. **置信度分析**：评估模型可靠性

### 3. 问题诊断
- **过拟合**：查看验证曲线，调整早停参数
- **欠拟合**：分析Grad-CAM，检查模型关注区域
- **类别不平衡**：查看类别统计和相关性
- **标注质量**：查看最差样本的错误分析

## 🎯 下一步

训练完成后，您可以：

1. **模型评估**：使用生成的权重进行推理
2. **结果分析**：查看training_report.json
3. **可视化导出**：保存关键图表用于论文/报告
4. **权重融合**：使用优化的集成权重进行预测

开始您的增强版集成训练之旅吧！🚄 