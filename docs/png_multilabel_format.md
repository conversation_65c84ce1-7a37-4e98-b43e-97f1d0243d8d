# PNG多标签掩码格式说明

## 概述
为了解决NPY格式文件过大的问题，我们实现了使用PNG格式存储多标签语义分割掩码的方案。该方案可以节省约99.5%的存储空间，同时保持数据的完整性。

## 格式说明

### PNG编码方案
使用PNG的BGR三个通道分别存储不同类别的二值掩码：
- **B通道 (Blue)**: 背景掩码 (0或255)
- **G通道 (Green)**: 主轨道掩码 (0或255)  
- **R通道 (Red)**: 分叉轨道掩码 (0或255)

### 优势
1. **极大的空间节省**: 
   - NPY (float32): 23.73 MB/文件
   - PNG (RGB): 0.01 MB/文件
   - 压缩比: 约2000倍

2. **数据完整性**: 
   - 二值掩码无精度损失
   - 支持重叠区域表示

3. **兼容性好**:
   - 标准图像格式，易于查看
   - 所有图像处理工具都支持

### 性能对比
- 加载速度: PNG比NPY慢约8倍，但仍在可接受范围（约30ms/张）
- 对于18000张图像的数据集，可节省约412GB存储空间

## 使用方法

### 1. 配置文件设置
```yaml
data:
  use_multilabel: true
  mask_format: 'png'  # 使用PNG格式
```

### 2. 生成多标签掩码
```bash
python scripts/generate_multilabel_from_json.py \
    --json-dir data/railway_annotation_6mm \
    --output-dir /home/<USER>/data/railway_track_dataset \
    --image-shape 1080 1920
```

### 3. 可视化掩码
```bash
# 可视化单个掩码
python scripts/visualize_npy_masks.py path/to/mask.png --style gradient

# 批量可视化
python scripts/visualize_npy_masks.py path/to/mask/dir --batch
```

### 4. 在代码中使用
```python
# 加载PNG多标签掩码
import cv2
import numpy as np

# 读取PNG文件
img = cv2.imread('mask.png', cv2.IMREAD_COLOR)

# 转换为多标签格式
mask = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.float32)
mask[:, :, 0] = (img[:, :, 0] > 127).astype(np.float32)  # B: 背景
mask[:, :, 1] = (img[:, :, 1] > 127).astype(np.float32)  # G: 主轨道
mask[:, :, 2] = (img[:, :, 2] > 127).astype(np.float32)  # R: 分叉轨道
```

## 数据集转换

如果需要将现有的NPY格式转换为PNG格式，可以使用以下脚本：

```python
import numpy as np
import cv2
from pathlib import Path

def convert_npy_to_png(npy_path, png_path):
    # 加载NPY掩码
    mask = np.load(npy_path)
    
    # 转换为BGR格式
    bgr_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
    bgr_mask[:, :, 0] = (mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
    bgr_mask[:, :, 1] = (mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
    bgr_mask[:, :, 2] = (mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
    
    # 保存为PNG
    cv2.imwrite(str(png_path), bgr_mask)
```

## 注意事项

1. **通道顺序**: OpenCV使用BGR而非RGB顺序，请注意正确的通道映射
2. **阈值处理**: 使用127作为二值化阈值
3. **数据类型**: PNG保存为uint8，加载后需转换为float32进行训练

## 总结

PNG多标签格式是存储语义分割掩码的高效方案，特别适合类别数较少的场景。它在保持数据完整性的同时，大幅减少了存储需求，是生产环境的理想选择。