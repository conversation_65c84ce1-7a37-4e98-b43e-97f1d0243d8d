# 集成学习快速入门

## 5分钟快速上手

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 准备数据
确保你的数据集包含训练集和验证集：
```
/path/to/dataset/
├── train/
│   ├── images/
│   └── masks/
└── val/
    ├── images/
    └── masks/
```

### 3. 一键训练集成模型
```bash
# 训练3个模型并自动优化权重
python scripts/ensemble_training.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/dataset
```

这个命令会：
- ✅ 训练EfficientNet-B4模型（25轮）
- ✅ 训练ECA-NFNet-L2模型（25轮）  
- ✅ 训练SE-ResNet152d模型（30轮）
- ✅ 自动优化集成权重
- ✅ 保存所有模型和权重到 `outputs/ensemble_weights/`

### 4. 使用集成模型预测

#### 预测单张图像
```bash
python scripts/ensemble_prediction.py \
    --weights-dir outputs/ensemble_weights \
    --image-path test.jpg \
    --output-dir predictions/
```

#### 批量预测文件夹
```bash
python scripts/ensemble_prediction.py \
    --weights-dir outputs/ensemble_weights \
    --input-dir test_images/ \
    --output-dir predictions/
```

## 预期结果

训练完成后，你会看到类似的输出：
```
=== 集成学习训练完成 ===
模型权重保存在: outputs/ensemble_weights

最优集成权重:
类别 0 (背景): [0.33, 0.34, 0.33] (IoU: 0.9821)
类别 1 (主轨道): [0.40, 0.35, 0.25] (IoU: 0.8654)  
类别 2 (分叉轨道): [0.25, 0.45, 0.30] (IoU: 0.7832)
```

## 性能提升

相比单模型，集成学习通常能带来：
- 📈 整体IoU提升 3-5%
- 📈 分叉轨道检测IoU提升 5-10%
- 📈 边缘细节更准确

## 下一步

- 查看[详细使用指南](ensemble_learning_guide.md)了解更多配置选项
- 尝试不同的模型组合
- 调整训练参数优化性能 