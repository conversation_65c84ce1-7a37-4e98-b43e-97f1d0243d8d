# NPY File Size Optimization Guide

## Problem Summary

The current NPY mask files are extremely large due to inefficient data storage:

- **Current size**: 23.73 MB per mask file
- **Format**: float32 (4 bytes per pixel) for binary masks that only need 1 bit
- **Dimensions**: 1080×1920×3 = 6,220,800 pixels × 4 bytes = 24,883,200 bytes
- **Total dataset size**: ~419 GB for all mask files

## Root Causes

1. **Data Type Inefficiency**: Using float32 for binary masks (0.0 or 1.0 values only)
2. **No Compression**: NPY format stores raw data without compression
3. **Redundant Precision**: Binary masks need only 1 bit per pixel, not 32 bits

## Solution Comparison

| Format | Size per File | Reduction | Pros | Cons |
|--------|--------------|-----------|------|------|
| float32 NPY (current) | 23.73 MB | 1x | Fast loading | Huge files |
| uint8 NPY | 5.93 MB | 4x | Fast loading, smaller | Still large |
| uint8 NPZ (compressed) | 0.01 MB | 1911x | Tiny files | Slightly slower loading |
| PNG | 0.01 MB | 3024x | Tiny files, viewable | Lossy for multi-channel |

## Recommended Solution

Use **compressed NPZ format with uint8 data type**:

1. **Immediate 1911x reduction** in file size
2. **Minimal impact on loading speed** (decompression is fast)
3. **Preserves multi-channel structure** for multilabel segmentation
4. **Compatible with existing code** (with minor updates)

## Implementation Steps

### 1. Update Preprocessing (Already Done)

The `preprocessing.py` module now supports saving masks in NPZ format:

```python
# In preprocess_json_annotations()
mask_format='npz'  # Instead of 'npy'
```

### 2. Convert Existing Files

Use the provided conversion script:

```bash
# Dry run to see potential savings
python scripts/convert_npy_to_npz.py --data-dir /path/to/data --dry-run

# Convert with backup
python scripts/convert_npy_to_npz.py \
  --data-dir /path/to/data \
  --backup-dir /path/to/backup \
  --delete-original

# Convert without backup (saves space)
python scripts/convert_npy_to_npz.py \
  --data-dir /path/to/data \
  --delete-original
```

### 3. Update Dataset Loader (Already Done)

The `railway_dataset.py` now handles NPZ files automatically:
- Checks for `.npz` files first, then `.npy`
- Automatically converts uint8 (0-255) to float32 (0-1)

## Memory Optimization During Training

If memory is still an issue during training:

1. **Use mixed precision training**: Reduces GPU memory by ~50%
2. **Reduce batch size**: Trade speed for memory
3. **Use gradient accumulation**: Simulate larger batches
4. **Enable gradient checkpointing**: Trade computation for memory

## Code Examples

### Save new masks efficiently:

```python
from src.data.preprocessing import preprocess_json_annotations

# Use NPZ format for new preprocessing
preprocess_json_annotations(
    json_dir=json_path,
    output_dir=output_path,
    mask_format='npz',  # Use compressed format
    use_multilabel=True
)
```

### Load masks efficiently:

```python
# The dataset automatically handles NPZ files
dataset = RailwayTrackDataset(
    data_root='/path/to/data',
    split='train'
)
# NPZ files are loaded and decompressed automatically
```

## Expected Results

After conversion:
- **File size reduction**: 419 GB → 0.22 GB (~1900x reduction)
- **Loading speed**: Minimal impact (~5-10% slower due to decompression)
- **Training performance**: No change (data is identical after loading)
- **Disk I/O**: Much faster (less data to read from disk)

## Future Optimizations

1. **Consider HDF5 format**: For even better performance with large datasets
2. **Use memory mapping**: For datasets larger than RAM
3. **Implement caching**: Keep frequently used masks in memory
4. **Use sparse matrices**: For masks with very low density