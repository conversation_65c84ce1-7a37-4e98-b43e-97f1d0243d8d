# 铁路轨道语义分割预测使用说明

本文档提供了使用铁路轨道语义分割模型进行预测的详细说明。

## 概述

该预测系统能够识别铁路图像中的三种类别：
- **背景（类别0）**：非轨道区域，显示为黑色
- **主轨道（类别1）**：主要铁路轨道，显示为红色
- **分叉轨道（类别2）**：分叉铁路轨道，显示为绿色

## 文件说明

- `scripts/predict_inference.py` - 主要预测脚本
- `requirements_inference.txt` - 依赖包列表
- 本文档 - 详细使用说明

## 环境安装

### 1. 安装Python依赖
```bash
pip install -r requirements_inference.txt
```

### 2. 确保CUDA环境正确（可选，用于GPU加速）
```bash
# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"
```

## 使用方法

### 命令行使用

#### 基本用法 - 预测单张图片
```bash
python scripts/predict_inference.py \
    --input path/to/your/image.jpg \
    --output outputs/predictions \
    --config configs/base_config.yaml \
    --checkpoint models/final/model.pth
```

#### 批量预测目录中的所有图片
```bash
python scripts/predict_inference.py \
    --input data/test_images/ \
    --output outputs/predictions \
    --batch-size 8 \
    --visualize \
    --stats
```

#### 完整参数说明
```bash
python scripts/predict_inference.py \
    --config configs/base_config.yaml        # 配置文件路径
    --checkpoint models/final/model.pth      # 模型权重文件路径
    --input path/to/images                   # 输入图片路径或目录
    --output outputs/predictions             # 输出目录
    --batch-size 4                          # 批处理大小（默认4）
    --visualize                             # 生成可视化结果
    --stats                                 # 显示预测统计信息
```

### Python代码使用

#### 1. 简单预测示例
```python
from scripts.predict_inference import RailwaySegmentationPredictor

# 创建预测器
predictor = RailwaySegmentationPredictor(
    config_path='configs/base_config.yaml',
    checkpoint_path='models/final/model.pth'
)

# 预测单张图片
mask, probabilities = predictor.predict_single('path/to/image.jpg')

# 生成可视化结果
overlay, mask = predictor.visualize_prediction(
    'path/to/image.jpg',
    save_path='outputs/result.png'
)

print(f"检测到的类别: {np.unique(mask)}")
```

#### 2. 批量预测示例
```python
from pathlib import Path

# 获取所有图片路径
image_dir = Path('data/test_images')
image_paths = list(image_dir.glob('*.jpg'))

# 批量预测
results = predictor.predict_batch(image_paths, batch_size=8)

# 保存结果
for i, (image_path, (mask, probs)) in enumerate(zip(image_paths, results)):
    output_path = f'outputs/predictions/{image_path.stem}_prediction.png'
    predictor.save_prediction(mask, output_path)
    print(f"完成 {i+1}/{len(image_paths)}: {image_path.name}")
```

#### 3. 获取预测统计信息
```python
# 预测并获取统计信息
mask, _ = predictor.predict_single('path/to/image.jpg')
stats = predictor.get_prediction_stats(mask)

print("预测统计信息:")
for class_name, info in stats.items():
    print(f"  {class_name}: {info['pixel_count']} 像素 ({info['percentage']}%)")
```

## 输出结果说明

### 文件命名规则
- `{image_name}_prediction.png` - 原始预测掩码（灰度图）
- `{image_name}_prediction_colored.png` - 彩色预测掩码
- `{image_name}_visualization.png` - 原图与预测结果叠加的可视化图

### 类别颜色映射
- **背景**: 黑色 (0, 0, 0)
- **轨道**: 红色 (255, 0, 0)
- **枕木**: 绿色 (0, 255, 0)
- **道床**: 蓝色 (0, 0, 255)
- **其他设施**: 黄色 (255, 255, 0)

## 配置文件结构

确保你的配置文件包含以下基本结构：

```yaml
# configs/base_config.yaml
data:
  image_size: 512    # 输入图像尺寸

model:
  architecture: "PAN"           # 模型架构：PAN/UNet/FPN
  encoder: "efficientnet-b4"    # 编码器类型
  num_classes: 5               # 类别数量
```

## 故障排除

### 常见问题

#### 1. 模型加载失败
```
错误: 加载模型失败
解决: 
- 检查模型权重文件是否存在
- 确认配置文件中的模型架构与权重文件匹配
- 安装 segmentation-models-pytorch 包
```

#### 2. CUDA内存不足
```
错误: CUDA out of memory
解决:
- 减小batch_size参数
- 使用CPU推理（自动切换）
- 减小输入图像尺寸
```

#### 3. 图像读取失败
```
错误: 无法读取图片
解决:
- 检查图片路径是否正确
- 确认图片格式被支持（jpg, png, bmp, tiff）
- 检查文件权限
```

#### 4. 依赖包缺失
```
错误: ImportError
解决:
pip install -r requirements_inference.txt
```

### 性能优化建议

#### 1. GPU加速
```python
# 确保使用GPU
predictor.device  # 应该显示 'cuda'
```

#### 2. 批量处理
```python
# 对于大量图片，使用批量预测
results = predictor.predict_batch(image_paths, batch_size=8)
```

#### 3. 混合精度推理
```python
# 在模型加载后启用混合精度
predictor.model.half()  # 使用FP16
```

## 扩展功能

### 自定义颜色映射
```python
# 修改类别颜色
predictor.color_map = {
    0: [0, 0, 0],      # 背景
    1: [255, 100, 100], # 轨道 - 自定义红色
    2: [100, 255, 100], # 枕木 - 自定义绿色
    # ...
}
```

### 添加新的后处理
```python
def custom_post_process(prediction_mask):
    # 添加形态学操作等后处理
    import cv2
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    cleaned_mask = cv2.morphologyEx(prediction_mask, cv2.MORPH_OPEN, kernel)
    return cleaned_mask
```

### 保存概率图
```python
# 保存每个类别的概率图
mask, probabilities = predictor.predict_single('image.jpg')
for class_id in range(probabilities.shape[-1]):
    prob_map = probabilities[:, :, class_id]
    cv2.imwrite(f'prob_class_{class_id}.png', (prob_map * 255).astype(np.uint8))
```

## 联系支持

如果遇到其他问题，请：
1. 检查日志输出中的详细错误信息
2. 确认所有依赖包版本正确
3. 查看示例配置文件格式
4. 提供完整的错误堆栈信息 