# 配置文件详细指南

本文档详细介绍如何配置铁路轨道分割系统的各个参数。

## 配置文件位置

主配置文件：`configs/railway_track_config.yaml`

## 配置结构概览

```yaml
# 主要配置部分
data:          # 数据相关配置
model:         # 模型架构配置
training:      # 训练参数配置
augmentation:  # 数据增强配置
loss:          # 损失函数配置
optimizer:     # 优化器配置
scheduler:     # 学习率调度器配置
logging:       # 日志和可视化配置
inference:     # 推理配置
```

## 详细配置说明

### 1. 数据配置 (data)

```yaml
data:
  # 数据路径
  train_path: "/home/<USER>/data/railway_track_dataset/train"
  val_path: "/home/<USER>/data/railway_track_dataset/val"
  test_path: "/home/<USER>/data/railway_track_dataset/test"
  
  # 图像参数
  image_size: [1080, 1920]  # [高度, 宽度]
  image_channels: 3          # RGB图像
  
  # 掩码参数
  use_multilabel: true       # 使用多标签分割
  mask_format: "png"         # 掩码格式 (png/npy)
  num_classes: 3             # 类别数：背景、主轨道、分叉轨道
  
  # 数据加载参数
  num_workers: 8             # 数据加载线程数
  pin_memory: true           # GPU内存锁定
  cache_data: false          # 是否缓存数据到内存
  
  # 类别定义
  class_names:
    - "background"
    - "main_track"
    - "fork_track"
  
  # 类别权重（用于不平衡数据）
  class_weights:
    - 0.5    # 背景权重
    - 1.0    # 主轨道权重
    - 2.0    # 分叉轨道权重（较少出现，权重更高）
```

### 2. 模型配置 (model)

```yaml
model:
  # 模型架构
  architecture: "deeplabv3plus"  # 可选: deeplabv3plus, unet, fpn, pspnet
  
  # 骨干网络
  backbone: "resnet50"           # 可选: resnet50, resnet101, efficientnet-b0-b7
  pretrained: true               # 使用ImageNet预训练权重
  
  # 模型参数
  output_channels: 3             # 输出通道数（对应类别数）
  decoder_channels: 256          # 解码器通道数
  
  # DeepLabV3+特定参数
  aspp_dilations: [1, 6, 12, 18] # ASPP模块的膨胀率
  aspp_dropout: 0.1              # ASPP dropout率
  
  # U-Net特定参数
  encoder_depth: 5               # 编码器深度
  decoder_use_batchnorm: true    # 解码器是否使用批归一化
  
  # 其他参数
  activation: "sigmoid"          # 输出激活函数
  aux_output: false              # 是否使用辅助输出
```

### 3. 训练配置 (training)

```yaml
training:
  # 基础参数
  num_epochs: 100               # 训练轮数
  batch_size: 8                 # 批次大小
  gradient_accumulation_steps: 1 # 梯度累积步数
  
  # 验证参数
  val_frequency: 1              # 每N个epoch验证一次
  val_batch_size: 16            # 验证批次大小
  
  # 检查点保存
  save_frequency: 10            # 每N个epoch保存一次
  save_best_only: false         # 是否只保存最佳模型
  checkpoint_dir: "outputs/checkpoints"
  
  # 早停机制
  early_stopping: true          # 是否启用早停
  early_stopping_patience: 20   # 早停耐心值
  early_stopping_min_delta: 0.001 # 最小改善阈值
  
  # 恢复训练
  resume_from_checkpoint: null  # 检查点路径，null表示从头开始
  
  # 高级选项
  mixed_precision: false        # 混合精度训练
  gradient_clip_val: 1.0        # 梯度裁剪值
  distributed: false            # 分布式训练
  sync_batchnorm: true         # 同步批归一化（分布式训练时）
  
  # 训练技巧
  label_smoothing: 0.1         # 标签平滑
  warmup_epochs: 5             # 预热轮数
```

### 4. 数据增强配置 (augmentation)

```yaml
augmentation:
  # 训练时增强
  train:
    # 几何变换
    - type: "RandomHorizontalFlip"
      p: 0.5                    # 概率
      
    - type: "RandomVerticalFlip"
      p: 0.3
      
    - type: "RandomRotate"
      limit: 10                 # 旋转角度范围 (-10, 10)
      p: 0.3
      
    - type: "ShiftScaleRotate"
      shift_limit: 0.1          # 平移范围
      scale_limit: 0.1          # 缩放范围
      rotate_limit: 15          # 旋转范围
      p: 0.3
      
    # 颜色变换
    - type: "RandomBrightnessContrast"
      brightness_limit: 0.2
      contrast_limit: 0.2
      p: 0.3
      
    - type: "HueSaturationValue"
      hue_shift_limit: 20
      sat_shift_limit: 30
      val_shift_limit: 20
      p: 0.3
      
    # 噪声和模糊
    - type: "GaussNoise"
      var_limit: [10.0, 50.0]
      p: 0.2
      
    - type: "GaussianBlur"
      blur_limit: [3, 7]
      p: 0.2
      
    # 天气效果（可选）
    - type: "RandomRain"
      slant_lower: -10
      slant_upper: 10
      p: 0.1
      
    - type: "RandomFog"
      fog_coef_lower: 0.3
      fog_coef_upper: 0.8
      p: 0.1
  
  # 验证时增强（通常只做标准化）
  val:
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]  # ImageNet均值
      std: [0.229, 0.224, 0.225]   # ImageNet标准差
  
  # 测试时增强（TTA）
  test_time_augmentation:
    enable: false
    transforms:
      - type: "HorizontalFlip"
      - type: "VerticalFlip"
      - type: "Rotate90"
```

### 5. 损失函数配置 (loss)

```yaml
loss:
  # 损失函数类型
  type: "combined"              # combined, ce, dice, focal, tversky
  
  # 组合损失权重
  ce_weight: 0.5               # 交叉熵损失权重
  dice_weight: 0.5             # Dice损失权重
  
  # 多标签损失权重
  main_track_weight: 1.0       # 主轨道损失权重
  fork_track_weight: 2.0       # 分叉轨道损失权重
  overlap_weight: 3.0          # 重叠区域损失权重
  
  # Focal Loss参数
  focal_alpha: 0.25
  focal_gamma: 2.0
  
  # Tversky Loss参数
  tversky_alpha: 0.3
  tversky_beta: 0.7
  
  # 其他参数
  smooth: 1e-6                 # 平滑因子（避免除零）
  ignore_index: -100           # 忽略的标签值
```

### 6. 优化器配置 (optimizer)

```yaml
optimizer:
  # 优化器类型
  type: "adamw"                # adam, adamw, sgd, rmsprop
  
  # 学习率
  learning_rate: 0.001         # 初始学习率
  
  # Adam/AdamW参数
  betas: [0.9, 0.999]         # Adam beta参数
  eps: 1e-8                    # Adam epsilon
  weight_decay: 0.01           # 权重衰减（L2正则化）
  
  # SGD参数
  momentum: 0.9                # SGD动量
  nesterov: true               # 是否使用Nesterov动量
  
  # 学习率衰减
  lr_decay: 0.1                # 学习率衰减因子
  lr_decay_epochs: [30, 60, 90] # 衰减的epoch
```

### 7. 学习率调度器配置 (scheduler)

```yaml
scheduler:
  # 调度器类型
  type: "cosine"               # cosine, step, exponential, reduce_on_plateau
  
  # Cosine退火参数
  T_max: 100                   # 余弦退火周期
  eta_min: 1e-6                # 最小学习率
  
  # Step调度器参数
  step_size: 30                # 步长
  gamma: 0.1                   # 衰减因子
  
  # ReduceOnPlateau参数
  mode: "min"                  # min或max
  factor: 0.5                  # 衰减因子
  patience: 10                 # 耐心值
  
  # 预热参数
  warmup: true                 # 是否使用预热
  warmup_epochs: 5             # 预热轮数
  warmup_start_lr: 1e-5        # 预热起始学习率
```

### 8. 日志配置 (logging)

```yaml
logging:
  # 日志目录
  log_dir: "outputs/logs"
  
  # TensorBoard配置
  tensorboard: true
  tensorboard_dir: "outputs/runs"
  
  # 日志级别
  log_level: "INFO"            # DEBUG, INFO, WARNING, ERROR
  
  # 日志内容
  log_every_n_steps: 10        # 每N步记录一次
  log_images: true             # 记录图像
  log_gradients: false         # 记录梯度
  log_graph: true              # 记录模型结构
  
  # 可视化参数
  num_images_to_log: 4         # 记录的图像数量
  log_prediction_interval: 5   # 每N个epoch记录预测结果
```

### 9. 推理配置 (inference)

```yaml
inference:
  # 推理模式
  mode: "single"               # single, batch, video, stream
  
  # 批量推理参数
  batch_size: 16               # 推理批次大小
  num_workers: 4               # 数据加载线程数
  
  # 后处理参数
  confidence_threshold: 0.5    # 置信度阈值
  use_crf: false              # 是否使用CRF后处理
  
  # 输出参数
  save_probability_maps: false # 保存概率图
  save_visualization: true     # 保存可视化结果
  visualization_alpha: 0.5     # 可视化透明度
  
  # 性能优化
  use_fp16: false             # 使用半精度推理
  use_tensorrt: false         # 使用TensorRT加速
  optimize_for_speed: true    # 速度优化模式
```

## 配置最佳实践

### 1. 小数据集配置

```yaml
# 针对小数据集的优化配置
data:
  cache_data: true            # 缓存到内存加速
  
training:
  batch_size: 4               # 减小批次避免过拟合
  gradient_accumulation_steps: 4  # 使用梯度累积
  
augmentation:
  train:
    # 增加更多数据增强
    - type: "RandomRotate"
      limit: 30
      p: 0.5
```

### 2. 大规模训练配置

```yaml
# 针对大规模数据的配置
training:
  distributed: true           # 启用分布式训练
  mixed_precision: true       # 混合精度训练
  batch_size: 32             # 大批次
  
optimizer:
  type: "adamw"
  learning_rate: 0.01        # 较大的学习率
  
scheduler:
  type: "cosine"
  warmup: true               # 使用预热
```

### 3. 快速实验配置

```yaml
# 用于快速验证的配置
training:
  num_epochs: 20
  val_frequency: 2
  save_frequency: 10
  
data:
  image_size: [540, 960]     # 使用较小的图像尺寸
```

## 动态配置覆盖

### 命令行覆盖

```bash
# 覆盖单个参数
python src/train.py training.batch_size=16

# 覆盖多个参数
python src/train.py \
    training.batch_size=16 \
    training.learning_rate=0.001 \
    model.backbone=resnet101
```

### 配置组合

```bash
# 使用不同的配置组合
python src/train.py \
    --config-name=base_config \
    +experiment=large_scale \
    training.num_epochs=200
```

## 配置验证

使用配置验证脚本检查配置的正确性：

```bash
python scripts/validate_config.py --config configs/railway_track_config.yaml
```

这将检查：
- 路径是否存在
- 参数值是否在合理范围内
- 配置组合是否兼容
- 是否有缺失的必需参数

---

更多配置示例请参考 `configs/examples/` 目录。