# API 参考文档

## 目录
- [数据处理模块](#数据处理模块)
- [模型模块](#模型模块)
- [工具函数](#工具函数)
- [训练和推理接口](#训练和推理接口)

## 数据处理模块

### `src.data.preprocessing.RailwayAnnotationParser`

用于解析铁路标注数据的核心类。

```python
from src.data.preprocessing import RailwayAnnotationParser

parser = RailwayAnnotationParser()
```

#### 方法

##### `parse_json_annotation(json_path: str) -> dict`
解析JSON标注文件。

**参数:**
- `json_path` (str): JSON标注文件路径

**返回:**
- `dict`: 包含解析后的标注信息

**示例:**
```python
annotation = parser.parse_json_annotation("path/to/annotation.json")
```

##### `create_multilabel_segmentation_mask(annotation_data: dict, image_shape: tuple) -> np.ndarray`
创建多标签分割掩码。

**参数:**
- `annotation_data` (dict): 标注数据
- `image_shape` (tuple): 图像尺寸 (height, width)

**返回:**
- `np.ndarray`: 形状为 (H, W, 3) 的多标签掩码

**示例:**
```python
mask = parser.create_multilabel_segmentation_mask(annotation_data, (1080, 1920))
```

### `src.data.railway_dataset.RailwayDataset`

PyTorch数据集类，用于加载铁路图像和掩码。

```python
from src.data.railway_dataset import RailwayDataset

dataset = RailwayDataset(
    data_dir="/path/to/data",
    split="train",
    config=config_dict,
    transform=transform
)
```

#### 参数

- `data_dir` (str): 数据目录路径
- `split` (str): 数据集划分 ("train", "val", "test")
- `config` (dict): 配置字典
- `transform` (callable, optional): 数据变换函数

#### 方法

##### `__getitem__(idx: int) -> dict`
获取单个样本。

**返回:**
- `dict`: 包含以下键值:
  - `image`: 图像张量 (C, H, W)
  - `mask`: 掩码张量 (C, H, W)
  - `image_path`: 图像文件路径
  - `mask_path`: 掩码文件路径

##### `__len__() -> int`
返回数据集大小。

### `src.data.augmentations`

数据增强函数集合。

```python
from src.data.augmentations import get_train_transform, get_val_transform

train_transform = get_train_transform(config)
val_transform = get_val_transform(config)
```

#### 函数

##### `get_train_transform(config: dict) -> callable`
获取训练时的数据增强管道。

**参数:**
- `config` (dict): 包含增强配置的字典

**返回:**
- `callable`: 数据增强函数

##### `get_val_transform(config: dict) -> callable`
获取验证时的数据变换管道。

## 模型模块

### `src.models.segmentation_model.SegmentationModel`

分割模型的基类。

```python
from src.models.segmentation_model import SegmentationModel

model = SegmentationModel(
    architecture="deeplabv3plus",
    backbone="resnet50",
    num_classes=3,
    pretrained=True
)
```

#### 参数

- `architecture` (str): 模型架构 ("deeplabv3plus", "unet", "fpn")
- `backbone` (str): 骨干网络 ("resnet50", "resnet101", "efficientnet-b0")
- `num_classes` (int): 输出类别数
- `pretrained` (bool): 是否使用预训练权重

#### 方法

##### `forward(x: torch.Tensor) -> torch.Tensor`
前向传播。

**参数:**
- `x` (torch.Tensor): 输入张量 (B, C, H, W)

**返回:**
- `torch.Tensor`: 输出张量 (B, num_classes, H, W)

### `src.models.multilabel_losses`

多标签损失函数集合。

```python
from src.models.multilabel_losses import MultiLabelSegmentationLoss

criterion = MultiLabelSegmentationLoss(
    main_weight=1.0,
    fork_weight=2.0,
    overlap_weight=3.0
)
```

#### 类

##### `MultiLabelSegmentationLoss`

**参数:**
- `main_weight` (float): 主轨道损失权重
- `fork_weight` (float): 分叉轨道损失权重
- `overlap_weight` (float): 重叠区域损失权重

**方法:**
```python
def forward(pred: torch.Tensor, target: torch.Tensor) -> dict:
    """
    计算多标签分割损失。
    
    返回:
        dict: 包含 'total', 'main', 'fork', 'overlap' 损失
    """
```

## 工具函数

### `src.utils.metrics`

评估指标计算函数。

```python
from src.utils.metrics import calculate_iou, calculate_dice, SegmentationMetrics

# 计算IoU
iou = calculate_iou(pred, target)

# 计算Dice系数
dice = calculate_dice(pred, target)

# 使用度量类
metrics = SegmentationMetrics(num_classes=3)
metrics.update(pred, target)
results = metrics.compute()
```

#### 函数

##### `calculate_iou(pred: torch.Tensor, target: torch.Tensor, smooth: float = 1e-6) -> torch.Tensor`
计算IoU分数。

##### `calculate_dice(pred: torch.Tensor, target: torch.Tensor, smooth: float = 1e-6) -> torch.Tensor`
计算Dice系数。

#### 类

##### `SegmentationMetrics`
分割评估指标累积器。

**方法:**
- `update(pred, target)`: 更新指标
- `compute()`: 计算最终指标
- `reset()`: 重置累积器

### `src.utils.visualization`

可视化工具函数。

```python
from src.utils.visualization import (
    visualize_segmentation,
    create_overlay,
    save_predictions_grid
)

# 可视化单个预测
vis = visualize_segmentation(image, mask, prediction)

# 创建叠加图
overlay = create_overlay(image, mask, alpha=0.5)

# 保存预测网格
save_predictions_grid(images, masks, predictions, save_path)
```

#### 函数

##### `visualize_segmentation(image: np.ndarray, mask: np.ndarray, prediction: np.ndarray = None) -> np.ndarray`
可视化分割结果。

##### `create_overlay(image: np.ndarray, mask: np.ndarray, alpha: float = 0.5) -> np.ndarray`
创建图像和掩码的叠加显示。

##### `save_predictions_grid(images: list, masks: list, predictions: list, save_path: str)`
保存预测结果网格图。

## 训练和推理接口

### 训练接口

```python
from src.train import Trainer

trainer = Trainer(config)
trainer.train()
```

#### `Trainer`类

**参数:**
- `config` (dict or DictConfig): 训练配置

**方法:**
- `train()`: 执行训练循环
- `validate()`: 执行验证
- `save_checkpoint(epoch)`: 保存检查点
- `load_checkpoint(path)`: 加载检查点

### 推理接口

```python
from src.inference import InferenceEngine

engine = InferenceEngine(
    checkpoint_path="path/to/checkpoint.pth",
    config=config,
    device="cuda"
)

# 预测单张图像
prediction = engine.predict_single(image_path)

# 批量预测
predictions = engine.predict_batch(image_list, batch_size=8)

# 预测视频
engine.predict_video(video_path, output_path)
```

#### `InferenceEngine`类

**参数:**
- `checkpoint_path` (str): 模型检查点路径
- `config` (dict): 推理配置
- `device` (str): 计算设备 ("cuda" 或 "cpu")

**方法:**
- `predict_single(image_path)`: 预测单张图像
- `predict_batch(image_list, batch_size)`: 批量预测
- `predict_video(video_path, output_path)`: 视频预测

## 配置管理

### 使用Hydra配置

```python
import hydra
from omegaconf import DictConfig

@hydra.main(config_path="configs", config_name="railway_track_config")
def main(cfg: DictConfig):
    # 访问配置
    batch_size = cfg.training.batch_size
    learning_rate = cfg.training.learning_rate
    
    # 动态修改配置
    cfg.training.num_epochs = 50
```

### 配置结构

```python
# 访问嵌套配置
cfg = {
    "data": {
        "train_path": "/path/to/train",
        "image_size": [1080, 1920],
        "use_multilabel": True
    },
    "model": {
        "architecture": "deeplabv3plus",
        "backbone": "resnet50"
    },
    "training": {
        "batch_size": 8,
        "learning_rate": 0.001
    }
}
```

## 自定义扩展

### 添加新的模型架构

```python
from src.models.base import BaseSegmentationModel

class CustomModel(BaseSegmentationModel):
    def __init__(self, num_classes=3):
        super().__init__()
        # 定义模型层
        
    def forward(self, x):
        # 实现前向传播
        return output
```

### 添加新的损失函数

```python
import torch.nn as nn

class CustomLoss(nn.Module):
    def __init__(self):
        super().__init__()
        
    def forward(self, pred, target):
        # 计算损失
        return loss
```

### 添加新的数据增强

```python
import albumentations as A

def custom_augmentation():
    return A.Compose([
        A.RandomRotate90(p=0.5),
        A.RandomBrightnessContrast(p=0.3),
        # 添加更多增强
    ])
```

## 示例代码

### 完整的训练流程

```python
import yaml
from src.train import Trainer
from src.data.railway_dataset import RailwayDataset
from torch.utils.data import DataLoader

# 加载配置
with open("configs/railway_track_config.yaml", "r") as f:
    config = yaml.safe_load(f)

# 创建数据集
train_dataset = RailwayDataset(
    data_dir=config["data"]["train_path"],
    split="train",
    config=config
)

# 创建数据加载器
train_loader = DataLoader(
    train_dataset,
    batch_size=config["training"]["batch_size"],
    shuffle=True,
    num_workers=4
)

# 训练模型
trainer = Trainer(config)
trainer.train()
```

### 完整的推理流程

```python
from src.inference import InferenceEngine
import cv2

# 初始化推理引擎
engine = InferenceEngine(
    checkpoint_path="outputs/checkpoints/best_model.pth",
    device="cuda"
)

# 预测图像
image_path = "test_image.jpg"
prediction = engine.predict_single(image_path)

# 保存结果
cv2.imwrite("prediction.png", prediction)
```

---

更多示例和详细文档请参考 `examples/` 目录。