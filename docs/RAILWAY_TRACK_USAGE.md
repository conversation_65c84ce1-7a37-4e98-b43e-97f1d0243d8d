# 铁路轨道分割使用指南

本文档介绍如何使用铁路轨道分割功能，包括JSON标注数据处理、训练和推理。

## 1. 数据准备

### 1.1 JSON标注格式

系统支持处理包含轨道关键点标注的JSON文件。标注格式要求如下：

```json
{
  "info": "图像URL",
  "labels": [
    {
      "label": "Main_Left",
      "drawType": "LINE",
      "points": [x1, y1, x2, y2, ...],
      "attr": { "basis": "visibly diverging tracks" }
    },
    {
      "label": "Main_Right",
      "drawType": "LINE",
      "points": [x1, y1, x2, y2, ...],
      "attr": {}
    }
  ]
}
```

标注类别说明：
- `Main_Left`/`Main_Right`: 主轨道左侧/右侧 - 会合并为"主轨道"类别
- `Fork_Left`/`Fork_Right`: 分叉轨道左侧/右侧 - 会合并为"分叉轨道"类别

### 1.2 相机类型

系统支持两种相机类型的数据：
- `6mm`：近距离相机（文件名包含"near"）
- `25mm`：远距离相机（文件名包含"far"）

### 1.3 预处理JSON标注

使用以下命令处理JSON标注文件，将线状标注转换为分割掩码：

```bash
python scripts/prepare_railway_data.py \
    --json-dir data \
    --output-dir data/processed \
    --download-images \
    --split-ratio 0.7 0.15 0.15
```

参数说明：
- `--json-dir`: JSON标注文件目录
- `--output-dir`: 输出目录
- `--download-images`: 是否从URL下载图像（如果未指定，需要手动放置图像）
- `--camera-type`: 可选，处理特定相机类型数据 (`6mm`或`25mm`)
- `--split-ratio`: 训练集、验证集、测试集的比例
- `--k-folds`: 可选，K折交叉验证的折数
- `--random-seed`: 随机种子

## 2. 训练模型

### 2.1 基础训练

使用以下命令训练铁路轨道分割模型：

```bash
python scripts/train.py \
    --config configs/experiment/train_railway_track.yaml \
    --experiment-name railway_track_all
```

### 2.2 针对特定相机训练

针对特定相机类型训练：

```bash
# 仅使用6mm相机数据训练
python scripts/train.py \
    --config configs/experiment/train_railway_track.yaml \
    --camera-type 6mm

# 仅使用25mm相机数据训练
python scripts/train.py \
    --config configs/experiment/train_railway_track.yaml \
    --camera-type 25mm
```

### 2.3 自定义参数

可以通过命令行参数覆盖配置：

```bash
python scripts/train.py \
    --config configs/experiment/train_railway_track.yaml \
    --batch-size 8 \
    --epochs 200 \
    --lr 1e-4 \
    --json-dir custom/json/dir
```

### 2.4 K折交叉验证

使用K折交叉验证训练：

```bash
# 首先创建K折数据集
python scripts/prepare_railway_data.py \
    --json-dir data \
    --output-dir data/processed \
    --k-folds 5

# 然后训练特定折
python scripts/train.py \
    --config configs/experiment/train_railway_track.yaml \
    --fold 0
```

## 3. 推理与评估

### 3.1 评估模型

使用以下命令评估模型：

```bash
python scripts/evaluate.py \
    --config configs/experiment/train_railway_track.yaml \
    --checkpoint models/checkpoints/railway_track_all/best_model.pth \
    --camera-type all  # 或 6mm, 25mm
```

### 3.2 单图预测

使用以下命令对单张图像进行预测：

```bash
python scripts/predict.py \
    --config configs/experiment/train_railway_track.yaml \
    --checkpoint models/checkpoints/railway_track_all/best_model.pth \
    --input path/to/image.jpg \
    --output outputs/predictions
```

### 3.3 批量预测

对整个目录进行预测：

```bash
python scripts/predict.py \
    --config configs/experiment/train_railway_track.yaml \
    --checkpoint models/checkpoints/railway_track_all/best_model.pth \
    --input path/to/images/dir \
    --output outputs/predictions \
    --camera-type 6mm  # 可选，只处理特定相机类型
```

## 4. 数据集结构

处理后的数据集结构如下：

```
data/processed/
├── images/
│   ├── train/
│   ├── val/
│   └── test/
├── masks/
│   ├── train/
│   ├── val/
│   └── test/
├── splits/
│   └── splits.json
└── metadata.json
```

或者K折结构：

```
data/processed/
├── fold_0/
│   ├── images/
│   └── masks/
├── fold_1/
│   ├── images/
│   └── masks/
└── ...
```

## 5. 可视化

### 5.1 数据集可视化

可视化数据集样本：

```python
from src.data import RailwayTrackDataset
from src.data.transforms import get_train_transforms

# 加载数据集
dataset = RailwayTrackDataset(
    data_root='data/processed',
    split='train',
    transform=get_train_transforms({'width': 1280, 'height': 720}),
    camera_type='6mm'  # 可选
)

# 可视化样本
dataset.visualize_sample(0, save_path='sample_visualization.png')
```

### 5.2 模型预测可视化

可视化模型预测结果：

```bash
python scripts/visualize.py \
    --config configs/experiment/train_railway_track.yaml \
    --checkpoint models/checkpoints/railway_track_all/best_model.pth \
    --input path/to/image.jpg \
    --output visualization.png
```

## 6. 注意事项

1. 对于高分辨率图像，建议调整图像尺寸以加快训练速度
2. 对于不同相机类型，可能需要不同的数据增强策略
3. 由于两种相机的特性不同，可以考虑分别训练模型，或使用多任务学习
4. 轨道多边形宽度可通过 `RailwayAnnotationParser` 的 `line_thickness` 参数调整 