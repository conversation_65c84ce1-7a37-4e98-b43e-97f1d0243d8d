# 📚 铁路轨道分割系统 - 文档索引

欢迎使用铁路轨道分割系统！以下是完整的文档索引，帮助您快速找到所需信息。

## 🚀 入门指南

- **[快速开始](../QUICKSTART.md)** - 15分钟快速上手指南
- **[项目介绍](../PROJECT_README.md)** - 项目概览和主要特性
- **[安装指南](README.md#环境配置)** - 详细的环境配置步骤

## 📖 核心文档

### 使用指南
- **[完整使用手册](README.md)** - 从数据准备到模型部署的完整流程
- **[配置文件指南](CONFIGURATION_GUIDE.md)** - 详细的配置参数说明
- **[API参考文档](API_REFERENCE.md)** - 完整的API接口文档

### 技术文档
- **[多标签分割指南](multilabel_pipeline_guide.md)** - 多标签分割技术详解
- **[推理部署指南](inference_usage.md)** - 模型推理和部署说明
- **[多标签分割更新说明](multilabel_segmentation_update.md)** - 多标签功能更新记录

## 🛠️ 开发文档

### 数据处理
- [数据准备流程](README.md#数据准备)
- [从JSON生成掩码](README.md#从json标注生成掩码)
- [PNG多标签编码格式](README.md#png多标签编码格式)

### 模型开发
- [模型训练](README.md#模型训练)
- [模型评估](README.md#模型评估)
- [自定义模型](API_REFERENCE.md#自定义扩展)
- [损失函数配置](CONFIGURATION_GUIDE.md#损失函数配置-loss)

### 工具和脚本
- [常用脚本](README.md#常用脚本)
- [可视化工具](README.md#可视化工具)
- [数据处理脚本](README.md#数据处理脚本)

## 📊 最佳实践

- [小数据集优化](CONFIGURATION_GUIDE.md#小数据集配置)
- [大规模训练](CONFIGURATION_GUIDE.md#大规模训练配置)
- [性能优化](README.md#性能优化)
- [故障排除](README.md#故障排除)

## 🔄 更新记录

- [PNG格式迁移总结](../PNG_FORMAT_MIGRATION_SUMMARY.md)
- [多标签集成总结](../MULTILABEL_INTEGRATION_SUMMARY.md)
- [多标签生成状态](../MULTILABEL_GENERATION_STATUS.md)

## 📋 快速参考

### 常用命令

```bash
# 数据生成
python scripts/generate_multilabel_from_json.py --sample 100

# 模型训练
python src/train.py

# 模型评估
python src/evaluate.py --checkpoint outputs/checkpoints/best_model.pth

# 推理预测
python src/inference.py --checkpoint model.pth --image test.jpg

# 可视化
python scripts/visualize_npy_masks.py mask.png --style gradient
```

### 重要路径

- 配置文件: `configs/railway_track_config.yaml`
- 训练脚本: `src/train.py`
- 推理脚本: `src/inference.py`
- 数据目录: `/home/<USER>/data/railway_track_dataset/`

### 关键参数

- 图像尺寸: 1080 x 1920
- 类别数: 3 (背景、主轨道、分叉轨道)
- 默认批次大小: 8
- 默认学习率: 0.001

## 🤝 获取帮助

- 查看[故障排除](README.md#故障排除)解决常见问题
- 提交Issue报告问题
- 参与社区讨论

## 📝 贡献

欢迎贡献文档！请：
1. Fork项目
2. 创建文档分支
3. 提交Pull Request

---

最后更新: 2025-06-27