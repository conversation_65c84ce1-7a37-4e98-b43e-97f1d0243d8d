# 多标签分割Pipeline使用指南

## 概述

本指南详细说明如何使用集成的多标签分割Pipeline进行铁路轨道分割训练。多标签分割允许像素同时属于多个类别，特别适合处理轨道分叉等复杂场景。

## 快速开始

### 1. 验证配置

确保 `configs/railway_track_config.yaml` 中包含以下配置：

```yaml
data:
  use_multilabel: true  # 启用多标签掩码生成

loss:
  type: multilabel_combined_loss
  jaccard_weight: 0.5
  focal_weight: 0.5
  alpha:
  - 0.1    # 背景权重
  - 0.3    # 主轨道权重
  - 0.6    # 分叉轨道权重
  gamma: 2.0

model:
  activation: sigmoid  # 多标签必须使用sigmoid
```

### 2. 测试Pipeline

运行测试脚本验证多标签Pipeline是否正确配置：

```bash
python scripts/test_multilabel_pipeline.py
```

该脚本会测试：
- 数据加载和多标签格式
- 模型前向传播
- 损失计算
- 指标评估
- 可视化
- 完整的训练步骤

### 3. 开始训练

```bash
python scripts/train.py \
    --config configs/railway_track_config.yaml \
    --experiment-name multilabel_experiment \
    --auto-resume
```

## 数据Pipeline详解

### 1. 掩码生成

多标签掩码生成在 `src/data/preprocessing.py` 的 `create_multilabel_segmentation_mask` 方法中实现：

- **输入**：JSON标注文件，包含主轨道和分叉轨道的标注
- **输出**：(H, W, 3) 的多通道掩码
  - 通道0：背景
  - 通道1：主轨道
  - 通道2：分叉轨道

### 2. 数据增强

数据增强配置在 `src/data/augmentations.py` 中：

```python
# 训练时增强
- HorizontalFlip (p=0.5)
- VerticalFlip (p=0.3)
- Rotate (limit=15, p=0.5)
- Perspective (scale=(0.05, 0.1), p=0.3)
- RandomBrightnessContrast (p=0.5)
- MotionBlur (blur_limit=7, p=0.3)
- CoarseDropout (p=0.5)
```

### 3. 数据加载器

使用 `src/data/dataloader_factory.py` 创建数据加载器：

```python
from src.data import create_train_val_loaders

train_loader, val_loader = create_train_val_loaders(config)
```

## 损失函数

多标签组合损失函数包含：

1. **Jaccard Loss (IoU Loss)**
   - 处理前景-背景不平衡
   - 直接优化IoU指标

2. **Focal Loss**
   - 处理难分类样本
   - Per-class权重：背景(0.1)、主轨道(0.3)、分叉轨道(0.6)

## 评估指标

### 整体指标
- IoU (Intersection over Union)
- Dice Coefficient
- Pixel Accuracy
- Precision, Recall, F1

### Per-class指标
```python
from src.utils.metrics import compute_metrics_per_class

per_class_metrics = compute_metrics_per_class(predictions, targets)
```

## 推理和后处理

### 1. 多标签推理

```python
from src.inference import post_process_multilabel

result = post_process_multilabel(predictions, threshold=0.5)
# result包含：
# - binary_masks: 每个类别的二值掩码
# - colored_mask: 彩色可视化
# - combined_mask: 组合掩码（用于提交）
```

### 2. 集成预测

```bash
python scripts/ensemble_prediction.py \
    --weights-dir path/to/weights \
    --input path/to/images \
    --output path/to/output \
    --threshold 0.5
```

## 可视化

多标签可视化支持：
- 每个类别的独立显示
- 重叠区域高亮（黄色）
- 彩色合成图

```python
from src.utils.visualization import visualize_predictions

visualize_predictions(
    images, true_masks, pred_masks,
    threshold=0.5
)
```

## 常见问题

### 1. 没有发现重叠区域

检查：
- 配置中 `use_multilabel: true`
- 使用 `create_multilabel_segmentation_mask` 方法
- JSON标注中是否有轨道分叉

### 2. 损失不收敛

调整：
- 降低学习率
- 调整类别权重 `alpha`
- 增加 `jaccard_weight`

### 3. 内存不足

优化：
- 减小批量大小
- 使用梯度累积
- 启用混合精度训练

## 完整示例

```python
# 1. 加载配置
import yaml
with open('configs/railway_track_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 2. 创建数据加载器
from src.data import create_train_val_loaders
train_loader, val_loader = create_train_val_loaders(config)

# 3. 创建模型
from src.models.segmentation import SegmentationModel
model = SegmentationModel(config['model'])

# 4. 创建损失函数
from src.models.multilabel_losses import MultilabelCombinedLoss
loss_fn = MultilabelCombinedLoss(**config['loss'])

# 5. 训练
from src.training import Trainer
trainer = Trainer(model, config, train_loader, val_loader, loss_fn, output_dir)
trainer.train()
```

## 性能优化

1. **数据预取**：自动启用 DataPrefetcher
2. **混合精度**：配置中设置 `mixed_precision: true`
3. **梯度累积**：`gradient_accumulation_steps: 2`
4. **多GPU训练**：使用 `--gpu 0,1,2,3`

## 与Notebook兼容性

- 支持加载notebook训练的模型权重
- 使用相同的多标签处理逻辑
- 兼容的集成权重格式