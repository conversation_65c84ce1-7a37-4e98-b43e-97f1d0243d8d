# TensorBoard实时监控指南

本指南介绍如何使用TensorBoard实时监控铁路分割模型的训练过程。

## 🚀 快速开始

### 1. 启用TensorBoard监控

在配置文件中启用TensorBoard：

```yaml
# configs/railway_track_config.yaml
logging:
  tensorboard: true          # 启用TensorBoard
  log_every_n_steps: 10     # 每10步记录一次
  log_images: true          # 记录图像
  max_images_to_log: 8      # 最大图像数量
```

### 2. 启动训练（自动记录到TensorBoard）

```bash
python scripts/train.py --config configs/railway_track_config.yaml
```

### 3. 启动TensorBoard查看实时监控

在另一个终端中运行：

```bash
# 方法1: 直接启动
tensorboard --logdir models/checkpoints/railway_track_config/tensorboard

# 方法2: 使用脚本启动
python scripts/start_tensorboard.py --experiment railway_track_config

# 方法3: 查看所有实验
python scripts/start_tensorboard.py --logdir models/checkpoints
```

访问地址：http://localhost:6006

## 📊 监控功能详览

### 1. **实时指标监控**

#### **损失监控**
- `Train/Loss`: 实时训练损失
- `Validation/Loss`: 验证损失
- `Loss/Comparison`: 训练vs验证损失对比

#### **学习率监控**
- `Learning_Rate`: 学习率变化曲线
- 支持多参数组的学习率监控

#### **评估指标监控**
- `Metrics/Validation/IoU`: 交并比
- `Metrics/Validation/Dice`: Dice系数
- `Metrics/Validation/F1`: F1分数
- `Validation/val_precision`: 精确度
- `Validation/val_recall`: 召回率

### 2. **模型参数监控**

#### **参数分布直方图**
- `Parameters/[layer_name]`: 每层参数分布
- `Gradients/[layer_name]`: 每层梯度分布
- `Gradient_Norms/[layer_name]`: 梯度范数

#### **模型结构图**
- 自动记录模型计算图
- 显示网络架构和数据流向

### 3. **预测可视化**

#### **样本预测对比**
- `Predictions/Samples`: 原图、真实掩码、预测掩码对比
- 每个epoch自动记录验证样本
- 支持多类别分割可视化

#### **训练进度图表**
- `Summary/Training_Curves`: 训练曲线总览
- 损失下降趋势
- 指标提升趋势

### 4. **训练总结**
- `Summary/Training_Summary`: 文本格式的训练总结
- 包含配置信息、最终性能等

## 🎯 高级功能

### 1. **多实验对比**

```bash
# 启动多个实验的TensorBoard
python scripts/start_tensorboard.py --logdir models/checkpoints
```

在TensorBoard界面中可以：
- 对比不同实验的指标
- 分析不同配置的性能差异
- 查看实验间的收敛速度

### 2. **自定义监控指标**

在训练代码中添加自定义指标：

```python
# 在训练器中添加
if self.tb_logger:
    # 记录自定义指标
    custom_metric = compute_custom_metric(outputs, targets)
    self.tb_logger.log_scalar('Custom/MyMetric', custom_metric, step)
    
    # 记录参数统计
    param_mean = torch.mean(self.model.backbone.conv1.weight).item()
    self.tb_logger.log_scalar('Stats/Conv1_Weight_Mean', param_mean, step)
```

### 3. **远程访问设置**

启动TensorBoard服务器供远程访问：

```bash
python scripts/start_tensorboard.py \
    --host 0.0.0.0 \
    --port 6006 \
    --experiment railway_track_config
```

然后可通过 `http://[服务器IP]:6006` 访问。

## 📈 使用场景

### 1. **训练过程监控**

**实时观察训练状态**：
- 损失是否正常下降
- 学习率调度是否合理
- 是否出现过拟合

**及时发现问题**：
- 梯度爆炸/消失
- 损失震荡
- 收敛停滞

### 2. **模型分析**

**参数分析**：
- 查看参数分布变化
- 监控梯度流动
- 识别死神经元

**性能分析**：
- 验证指标趋势
- 最佳模型时机
- 不同类别的性能

### 3. **实验对比**

**超参数调优**：
- 对比不同学习率
- 分析不同架构性能
- 评估数据增强效果

**模型选择**：
- 对比不同模型架构
- 评估ensemble效果
- 选择最佳检查点

## 🔧 常用操作

### 1. **查看当前实验**

```bash
# 查看指定实验
python scripts/start_tensorboard.py --experiment railway_track_config

# 查看实验状态
python scripts/monitor_training.py --log-dir models/checkpoints/railway_track_config
```

### 2. **保存TensorBoard数据**

TensorBoard数据自动保存在：
- `models/checkpoints/[experiment_name]/tensorboard/`
- 包含事件文件和图像数据
- 支持离线查看

### 3. **清理旧数据**

```bash
# 清理指定实验的TensorBoard数据
rm -rf models/checkpoints/railway_track_config/tensorboard/

# 重新开始记录
python scripts/train.py --config configs/railway_track_config.yaml
```

### 4. **导出数据**

在TensorBoard界面中：
1. 点击右上角的"下载"按钮
2. 选择要导出的数据类型
3. 下载CSV格式的指标数据

## 📱 TensorBoard界面导航

### **主要标签页**：

1. **SCALARS** - 标量指标
   - 损失曲线
   - 学习率变化
   - 验证指标

2. **IMAGES** - 图像可视化
   - 预测样本对比
   - 训练进度图表

3. **HISTOGRAMS** - 直方图
   - 参数分布
   - 梯度分布

4. **GRAPHS** - 模型图
   - 网络结构
   - 计算图

5. **TEXT** - 文本日志
   - 训练总结
   - 配置信息

### **实用技巧**：

- **平滑曲线**：调整左侧的Smoothing滑块
- **选择指标**：点击左侧指标名称进行筛选
- **缩放查看**：鼠标滚轮缩放，拖拽平移
- **对比实验**：选择多个实验进行对比

## 🐛 故障排除

### 1. **TensorBoard无法启动**

```bash
# 检查是否安装
pip install tensorboard

# 检查端口占用
lsof -i :6006

# 更换端口
python scripts/start_tensorboard.py --port 6007
```

### 2. **没有数据显示**

- 确认配置文件中 `tensorboard: true`
- 检查日志目录是否正确
- 确认训练已开始并产生数据

### 3. **中文字体问题**

图表中文字体缺失警告可忽略，不影响功能使用。

### 4. **远程访问问题**

```bash
# 绑定所有接口
python scripts/start_tensorboard.py --host 0.0.0.0

# 检查防火墙设置
sudo ufw allow 6006
```

## 🎉 完整使用流程

1. **配置启用**：确保配置文件中启用TensorBoard
2. **开始训练**：运行训练脚本
3. **启动监控**：在新终端启动TensorBoard
4. **实时观察**：访问Web界面查看指标
5. **分析结果**：根据监控数据调整训练策略

现在你可以享受TensorBoard带来的强大实时监控功能！🚀 