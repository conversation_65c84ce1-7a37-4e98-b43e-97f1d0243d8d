# 铁路轨道分割功能集成

本文档总结了对铁路分割项目的修改，以支持铁路轨道JSON标注数据的处理和多相机类型的训练。

## 1. 新增功能

### 1.1 JSON标注解析

增加了对JSON格式轨道关键点标注的支持，可以将轨道关键点自动转换为语义分割掩码。
- 支持主轨道和分叉轨道的区分
- 支持6mm和25mm两种不同相机类型的数据

### 1.2 多相机类型支持

项目现在可以同时支持两种不同相机类型的数据：
- 6mm (近距离)相机：文件名包含"near"
- 25mm (远距离)相机：文件名包含"far"

可以选择联合训练或为每种相机类型分别训练模型。

### 1.3 轨道分割专用配置

提供了专门针对铁路轨道分割的配置文件和训练脚本，并针对不同相机类型提供了差异化配置。

## 2. 新增文件

以下是本次修改新增的主要文件：

| 文件路径 | 描述 |
|---------|------|
| `src/data/preprocessing.py` | JSON标注解析和轨道点转换为分割掩码 |
| `src/data/railway_dataset.py` | 支持JSON标注和不同相机类型的数据集类 |
| `scripts/prepare_railway_data.py` | JSON标注预处理和数据集划分脚本 |
| `configs/railway_track_config.yaml` | 铁路轨道分割的配置文件 |
| `configs/experiment/train_railway_track.yaml` | 训练专用配置 |
| `docs/RAILWAY_TRACK_USAGE.md` | 使用指南 |

## 3. 修改的文件

以下是本次修改中对原有文件的主要修改：

| 文件路径 | 修改内容 |
|---------|---------|
| `src/data/__init__.py` | 添加新模块的导入 |
| `src/data/dataloader.py` | 添加对新数据集和多相机类型的支持 |
| `scripts/train.py` | 添加相机类型选择和数据集类型选项 |

## 4. 数据流程

新的数据处理流程如下：

1. **预处理阶段**
   - 解析JSON标注文件
   - 下载或加载对应图像
   - 将轨道关键点转换为分割掩码
   - 创建训练/验证/测试数据集划分

2. **数据加载阶段**
   - 根据相机类型筛选数据
   - 应用相机特定的图像变换
   - 批量加载数据

3. **训练阶段**
   - 支持不同相机类型的单独或联合训练
   - 针对不同相机类型使用不同的超参数

## 5. 使用示例

### 5.1 数据预处理

```bash
python scripts/prepare_railway_data.py --json-dir data --output-dir data/processed --download-images
```

### 5.2 训练模型

```bash
# 训练所有相机类型
python scripts/train.py --config configs/experiment/train_railway_track.yaml

# 只训练6mm相机数据
python scripts/train.py --config configs/experiment/train_railway_track.yaml --camera-type 6mm
```

### 5.3 推理

```bash
python scripts/predict.py --config configs/experiment/train_railway_track.yaml --checkpoint path/to/model.pth --input path/to/image.jpg
```

## 6. 未来改进方向

1. **多任务学习**：同时学习轨道分割和轨道关键点检测
2. **相机自适应**：自动识别相机类型并应用相应模型
3. **实时处理**：优化推理速度，支持视频流处理
4. **3D轨道重建**：结合不同相机视角，重建3D轨道模型

更多详细信息，请参考 [使用指南](RAILWAY_TRACK_USAGE.md)。 