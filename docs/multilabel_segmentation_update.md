# 多标签分割更新说明

## 概述

本次更新将整个代码库从互斥分类（mutually exclusive classification）转换为多标签分割（multi-label segmentation），与notebook实现保持一致。主要改进包括使用Focal Loss处理类别不平衡，支持像素同时属于多个类别，以及per-class权重优化。

## 核心改动

### 1. 损失函数更新

**新增文件**: `src/models/multilabel_losses.py`

- **MultilabelJaccardLoss**: 与notebook的`smp.losses.JaccardLoss(mode='multilabel')`保持一致
- **MultilabelFocalLoss**: 支持per-class权重，默认值为 [0.1, 0.3, 0.6]
- **MultilabelCombinedLoss**: 组合Jaccard和Focal损失，权重各占0.5

**配置更新**: `configs/railway_track_config.yaml`
```yaml
loss:
  type: multilabel_combined_loss
  jaccard_weight: 0.5
  focal_weight: 0.5
  alpha:
  - 0.1    # 背景权重
  - 0.3    # 主轨道权重
  - 0.6    # 分叉轨道权重
  gamma: 2.0
```

### 2. 数据处理更新

**文件**: `src/data/railway_dataset.py`

- `_single_to_multi_channel_mask` 方法更新为支持多标签格式
- 每个通道独立表示一个类别的二值掩码
- 支持像素同时属于多个类别（如轨道分叉处）

### 3. 模型配置

**文件**: `src/models/segmentation.py`

- 默认使用sigmoid激活函数（第35行）
- 与notebook保持一致，每个类别独立计算概率

### 4. 评估指标更新

**文件**: `src/utils/metrics.py`

- 新增 `compute_metrics_per_class` 函数，计算每个类别的独立指标
- 修复了reshape问题，支持非连续张量
- 支持多标签评估：IoU、Dice、Precision、Recall、F1

### 5. 后处理逻辑

**文件**: `scripts/ensemble_prediction.py`

- `_post_process_prediction` 方法改为多标签处理
- 每个类别独立判断（阈值处理）
- 使用优先级策略生成最终掩码：分叉轨道 > 主轨道 > 背景
- 支持保存每个类别的独立预测结果

**文件**: `src/inference.py`

- 更新 `convert_bool_mask_to_submission` 支持多标签
- 新增 `post_process_multilabel` 函数，返回每个类别的独立掩码

### 6. 可视化更新

**文件**: `src/utils/visualization.py`

- `visualize_predictions` 函数支持多标签显示
- 显示每个类别的独立预测和真实掩码
- 使用彩色编码：主轨道（红色）、分叉轨道（绿色）

### 7. 训练器兼容性

**文件**: `src/training/trainer.py`

- 已支持多标签损失函数
- StreamingMetricsCalculator 兼容多标签格式
- 自动应用sigmoid激活（如果需要）

## 关键特性

1. **多标签支持**: 像素可同时属于多个类别，特别适合轨道分叉处
2. **类别不平衡处理**: 使用Focal Loss with per-class权重
3. **与notebook一致**: 完全兼容notebook中的模型权重和实现方式
4. **灵活的后处理**: 支持阈值调整和优先级策略
5. **完整的评估**: Per-class指标计算和可视化

## 使用示例

### 训练
```bash
python scripts/train.py --config configs/railway_track_config.yaml
```

### 集成预测
```bash
python scripts/ensemble_prediction.py \
    --weights-dir path/to/weights \
    --input path/to/images \
    --output path/to/output \
    --threshold 0.5
```

### 测试多标签实现
```bash
python scripts/test_multilabel_config.py      # 测试配置
python scripts/test_multilabel_integration.py  # 集成测试
```

## 注意事项

1. 模型输出需要sigmoid激活才能得到概率值
2. 阈值默认为0.5，可根据实际需求调整
3. 类别权重可在配置文件中修改
4. 确保训练数据的掩码格式为多通道二值掩码

## 向后兼容性

- 支持加载notebook训练的模型权重
- 自动处理不同的状态字典格式
- 保持了原有的类别映射关系