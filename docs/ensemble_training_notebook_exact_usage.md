# 集成训练脚本使用指南 - Notebook精确复现版

本脚本严格遵循 `Railway_Infrastructure_Segmentation.ipynb` 的训练逻辑，并添加了以下增强功能：
- 每个epoch自动保存checkpoint
- 支持断点续训
- 支持加载预训练模型

## 数据准备

脚本期望的数据结构与notebook完全一致：
```
./
├── train/
│   ├── images/         # 训练图像 (PNG格式)
│   └── mask/           # 训练掩码 (PNG格式)
```

**注意**: 掩码使用特定的像素值：
- 0: 背景 (background)
- 1: 主轨道 (main_track) 
- 2: 分叉轨道 (fork_track)

## 基本用法

### 1. 从头开始训练
```bash
python scripts/ensemble_training_notebook_exact.py \
    --data-root /path/to/data \
    --output-dir weights
```

### 2. 从checkpoint恢复训练
```bash
python scripts/ensemble_training_notebook_exact.py \
    --data-root /path/to/data \
    --output-dir weights \
    --resume
```

### 3. 使用预训练模型
```bash
python scripts/ensemble_training_notebook_exact.py \
    --data-root /path/to/data \
    --output-dir weights \
    --pretrained-efficientnet ./efficientnetb4.pth.tar \
    --pretrained-nfnet ./eca_nfnet_l2.pth.tar \
    --pretrained-resnet ./seresnet152d.pth.tar
```

## 参数说明

- `--data-root`: 数据根目录，包含 train/images 和 train/mask 子目录
- `--output-dir`: 模型权重和checkpoint的输出目录
- `--resume`: 从最新的checkpoint恢复训练
- `--pretrained-efficientnet`: EfficientNet预训练模型路径
- `--pretrained-nfnet`: NFNet预训练模型路径  
- `--pretrained-resnet`: ResNet预训练模型路径
- `--seed`: 随机种子 (默认: 0)
- `--batch-size`: 批量大小 (默认: 8)
- `--num-workers`: 数据加载线程数 (默认: 2)

## 输出文件

训练完成后，输出目录将包含：

```
weights/
├── efficientnetb4.pth.tar          # EfficientNet模型权重
├── eca_nfnet_l2.pth.tar           # NFNet模型权重
├── seresnet152d.pth.tar           # ResNet模型权重
├── history_efficientnetb4          # 训练历史
├── history_eca_nfnet_l2           
├── history_seresnet152d           
├── loss_curve_*.png               # 损失曲线图
├── iou_curve_*.png                # IoU曲线图
├── training_config.json           # 训练配置
├── checkpoints/                   # 各模型的checkpoint
│   ├── efficientnetb4/
│   ├── eca_nfnet_l2/
│   └── seresnet152d/
└── visualizations/                # 验证可视化
    ├── efficientnetb4/            # 每个epoch随机保存的可视化图像
    ├── eca_nfnet_l2/
    └── seresnet152d/
```

## 模型配置

脚本训练3个模型，与notebook完全一致：

1. **PAN + EfficientNet B4**
   - 编码器: tu-tf_efficientnet_b4_ns
   - 预训练权重: noisy-student
   - 训练轮数: 25

2. **PAN + ECA NFNet L2**
   - 编码器: tu-eca_nfnet_l2
   - 预训练权重: imagenet
   - 训练轮数: 25

3. **PAN + SE-ResNet 152d**
   - 编码器: tu-seresnet152d
   - 预训练权重: imagenet
   - 训练轮数: 30

## 训练参数

所有参数与notebook保持一致：
- 优化器: AdamW (lr=0.0005)
- 损失函数: JaccardLoss (multilabel mode)
- 学习率调度: CosineAnnealingLR
- 梯度累积: 每2个batch
- 梯度裁剪: 启用
- 混合精度训练: 启用

## 可视化功能

训练过程中会自动生成可视化：
- 每个epoch随机保存3张验证集图像
- 可视化包括：原始图像、真实掩码、预测结果、叠加显示
- 保存位置：weights/visualizations/模型名/
- 文件命名：epoch_XXX_sample_X.png

## Checkpoint功能

### 自动保存
- 每个epoch自动保存checkpoint
- 保留最近的5个checkpoint
- checkpoint包含完整的训练状态

### 断点续训
使用 `--resume` 参数自动从最新的checkpoint恢复：
- 恢复模型权重
- 恢复优化器状态
- 恢复学习率调度器
- 恢复随机数状态
- 从中断的epoch继续训练

## 注意事项

1. 确保数据格式与notebook一致
2. 掩码格式必须是PNG格式，使用0,1,2像素值
3. 需要CUDA支持的GPU
4. 建议至少16GB GPU内存
5. 首次运行会下载预训练权重
6. 训练过程会产生较多checkpoint文件，注意磁盘空间
7. 每个epoch会随机保存3张验证集的可视化图像

## 故障排除

### 内存不足
减小batch size：
```bash
python scripts/ensemble_training_notebook_exact.py --batch-size 4
```

### 数据路径错误
确保数据结构正确：
- train/images/ 包含PNG图像
- train/mask/ 包含对应的PNG掩码
- 文件名必须匹配

### Resume失败
检查checkpoint目录是否存在有效的checkpoint文件