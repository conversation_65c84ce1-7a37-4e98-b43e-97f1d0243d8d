# 铁路轨道分割集成学习使用指南

## 目录
1. [概述](#概述)
2. [集成学习原理](#集成学习原理)
3. [实施步骤](#实施步骤)
4. [配置说明](#配置说明)
5. [使用示例](#使用示例)
6. [性能优化](#性能优化)
7. [常见问题](#常见问题)

## 概述

本项目的集成学习模块实现了基于多个深度学习模型的融合预测，通过结合不同架构的优势来提升铁路轨道分割的准确性，特别是在分叉轨道检测方面。

### 主要特性
- **多模型融合**：支持多个不同backbone的模型集成
- **权重优化**：基于验证集自动优化各模型权重
- **按类别加权**：为每个类别（背景、主轨道、分叉轨道）独立优化权重
- **灵活配置**：支持自定义模型组合和融合策略

### 默认模型组合
系统默认使用以下三个经过验证的模型组合：
1. **EfficientNet-B4**：高效的特征提取能力
2. **ECA-NFNet-L2**：无归一化架构，训练稳定
3. **SE-ResNet152d**：强大的特征重标定能力

## 集成学习原理

### 1. 多模型预测
每个模型独立对输入图像进行预测，生成概率掩码：
```
Model_1(image) → Prediction_1
Model_2(image) → Prediction_2  
Model_3(image) → Prediction_3
```

### 2. 加权融合
使用优化后的权重对各模型预测进行融合：
```
Final_Prediction = Σ(weight_i × Prediction_i)
```

### 3. 类别独立权重
每个类别使用不同的权重组合：
- **背景**：通常权重较均衡
- **主轨道**：偏向准确率高的模型
- **分叉轨道**：偏向对细节敏感的模型

## 实施步骤

### 步骤1：准备数据集
确保数据集结构符合要求：
```
data/
├── train/
│   ├── images/
│   └── masks/
├── val/
│   ├── images/
│   └── masks/
└── test/
    ├── images/
    └── masks/
```

### 步骤2：训练单个模型
可以选择两种方式：

#### 方式A：使用集成训练脚本（推荐）
```bash
python scripts/ensemble_training.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/dataset
```

#### 方式B：分别训练各模型
```bash
# 训练EfficientNet-B4模型
python train_v2.py \
    --config configs/models/efficientnet_config.yaml \
    --experiment efficientnet_b4

# 训练ECA-NFNet-L2模型
python train_v2.py \
    --config configs/models/nfnet_config.yaml \
    --experiment eca_nfnet_l2

# 训练SE-ResNet152d模型
python train_v2.py \
    --config configs/models/seresnet_config.yaml \
    --experiment seresnet152d
```

### 步骤3：优化集成权重
如果已有训练好的模型权重：
```bash
python scripts/ensemble_training.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/dataset \
    --skip-training
```

### 步骤4：使用集成模型预测

#### 预测单张图像
```bash
python scripts/ensemble_prediction.py \
    --weights-dir checkpoints/ensemble_weights \
    --image-path test_image.jpg \
    --output-dir outputs/predictions
```

#### 批量预测
```bash
python scripts/ensemble_prediction.py \
    --weights-dir checkpoints/ensemble_weights \
    --input-dir test_images/ \
    --output-dir outputs/predictions \
    --batch-size 4
```

## 配置说明

### 集成模型配置示例
```yaml
# configs/ensemble_config.yaml
ensemble:
  enabled: true
  fusion_method: weighted_average  # 融合方法：weighted_average, equal_weight
  models:
    - name: efficientnet_b4
      architecture: pan
      encoder: tu-tf_efficientnet_b4_ns
      encoder_weights: noisy-student
      epochs: 25
      
    - name: eca_nfnet_l2
      architecture: pan
      encoder: tu-eca_nfnet_l2
      encoder_weights: imagenet
      epochs: 25
      
    - name: seresnet152d
      architecture: pan
      encoder: tu-seresnet152d
      encoder_weights: imagenet
      epochs: 30
  
  # 权重优化设置
  optimization:
    method: grid_search  # 优化方法
    num_iterations: 9801  # 网格搜索迭代次数
    metric: iou  # 优化指标
```

### 训练参数配置
```yaml
training:
  batch_size: 4
  learning_rate: 0.0005
  optimizer: adamw
  scheduler: cosine
  
loss:
  type: multilabel_combined_loss
  jaccard_weight: 0.5
  focal_weight: 0.5
  alpha: [0.1, 0.3, 0.6]  # 类别权重
```

## 使用示例

### Python API使用

```python
from src.models.ensemble import EnsemblePredictor
import torch

# 加载集成模型
def load_ensemble_model(weights_dir):
    # 加载各个模型
    models = []
    model_names = ['efficientnet_b4', 'eca_nfnet_l2', 'seresnet152d']
    
    for name in model_names:
        model = create_model_from_config(name)  # 根据配置创建模型
        model.load_state_dict(torch.load(f"{weights_dir}/{name}.pth"))
        models.append(model)
    
    # 加载融合权重
    with open(f"{weights_dir}/ensemble_weights.yaml", 'r') as f:
        weights_config = yaml.safe_load(f)
    
    # 构建权重矩阵
    fusion_weights = torch.zeros(3, 3)  # 3个类别，3个模型
    for class_idx, info in weights_config.items():
        fusion_weights[class_idx] = torch.tensor(info['weights'])
    
    # 创建集成预测器
    predictor = EnsemblePredictor(models, fusion_weights, device)
    return predictor

# 使用示例
predictor = load_ensemble_model("checkpoints/ensemble_weights")

# 预测
with torch.no_grad():
    prediction = predictor.predict(input_tensor)
```

### 集成到现有训练流程

```python
# 在train_v2.py中添加集成训练支持
if config.get('ensemble', {}).get('enabled', False):
    # 训练多个模型
    models = []
    for model_config in config['ensemble']['models']:
        model = create_model(model_config)
        # 训练模型...
        models.append(model)
    
    # 创建集成模型
    ensemble_model = EnsembleModel(config)
    ensemble_model.load_model_weights(model_paths)
    
    # 优化权重
    best_weights = ensemble_model.optimize_fusion_weights(
        val_loader, device, num_iterations=100
    )
```

## 性能优化

### 1. 内存优化
- **分批预测**：对大批量数据分批处理
- **模型权重共享**：使用相同backbone的模型共享特征提取器
- **混合精度**：使用FP16进行推理

### 2. 速度优化
- **并行推理**：多GPU并行处理不同模型
- **模型剪枝**：对集成中的模型进行剪枝
- **动态批处理**：根据GPU内存动态调整批大小

### 3. 精度优化
- **测试时增强（TTA）**：对每个模型应用TTA
- **多尺度预测**：不同分辨率下的预测融合
- **后处理优化**：CRF等后处理技术

## 常见问题

### Q1: 如何选择合适的模型组合？
**A**: 建议选择架构差异较大的模型，如：
- CNN系列（ResNet、EfficientNet）
- Transformer系列（Swin、ViT）
- 混合架构（ConvNeXt）

### Q2: 权重优化需要多长时间？
**A**: 取决于验证集大小和搜索空间：
- 快速模式（随机搜索）：5-10分钟
- 标准模式（网格搜索）：30-60分钟
- 精细模式（贝叶斯优化）：2-3小时

### Q3: 集成学习一定会提升性能吗？
**A**: 通常会提升，但需要注意：
- 单个模型质量要足够好
- 模型之间要有互补性
- 权重优化要充分

### Q4: 如何评估集成效果？
**A**: 使用以下指标：
```python
# 评估脚本
python scripts/evaluate_ensemble.py \
    --weights-dir checkpoints/ensemble_weights \
    --test-dir /path/to/test \
    --metrics iou dice precision recall
```

### Q5: 内存不足怎么办？
**A**: 可以尝试：
1. 减小批处理大小
2. 使用更小的backbone
3. 逐个加载模型进行预测
4. 使用模型量化技术

## 最佳实践

1. **模型多样性**：选择不同类型的backbone
2. **充分训练**：确保每个模型都达到收敛
3. **验证集选择**：使用有代表性的验证集优化权重
4. **定期更新**：随着数据增加，定期重新优化权重
5. **监控性能**：记录各模型和集成的性能指标

## 总结

集成学习是提升模型性能的有效方法，特别适合：
- 需要高精度的生产环境
- 有充足计算资源的场景
- 对特定类别（如分叉轨道）要求高的任务

通过合理配置和优化，集成学习可以显著提升铁路轨道分割的准确率，特别是在复杂场景下的表现。 