# NPY掩码可视化指南

本指南介绍如何使用新增的可视化工具查看和分析NPY格式的多标签掩码。

## 1. 可视化工具概览

### 1.1 颜色方案
我们使用了美观且易于区分的现代化颜色方案：

- 🟠 **主轨道 (Main Track)**: 橙色 `RGB(255, 140, 0)` - 活力橙色，醒目易识别
- 🔵 **分叉轨道 (Fork Track)**: 天蓝色 `RGB(0, 191, 255)` - 深天蓝色，与主轨道形成对比
- 🔴 **重叠区域 (Overlap)**: 深粉色 `RGB(255, 20, 147)` - 热粉红色，突出显示重要的重叠区域
- ⬛ **背景 (Background)**: 深灰色 `RGB(30, 30, 30)` - 不抢眼的深色背景

### 1.2 可视化风格
支持三种可视化风格：
- **Solid（实心）**: 清晰的色块显示，适合精确查看
- **Blend（混合）**: 重叠区域使用混合色，更自然
- **Gradient（渐变）**: 带有渐变效果，更美观

## 2. 命令行可视化工具

### 2.1 查看单个掩码
```bash
# 基本用法
python scripts/visualize_npy_masks.py path/to/mask.npy

# 指定风格
python scripts/visualize_npy_masks.py path/to/mask.npy --style gradient

# 与原始图像叠加显示
python scripts/visualize_npy_masks.py path/to/mask.npy --image path/to/image.png

# 保存可视化结果
python scripts/visualize_npy_masks.py path/to/mask.npy --save output.png

# 显示统计信息
python scripts/visualize_npy_masks.py path/to/mask.npy --stats
```

### 2.2 批量可视化
```bash
# 批量查看目录中的掩码
python scripts/visualize_npy_masks.py /path/to/mask/dir --batch

# 指定样本数量
python scripts/visualize_npy_masks.py /path/to/mask/dir --batch --num-samples 9

# 保存批量结果
python scripts/visualize_npy_masks.py /path/to/mask/dir --batch --save batch_output.png
```

## 3. 交互式查看器

### 3.1 启动交互式工具
```bash
python scripts/interactive_mask_viewer.py
```

### 3.2 功能特性
- **文件加载**: 通过GUI选择NPY掩码和原始图像
- **实时切换**: 在不同显示风格间实时切换
- **缩放功能**: 支持10%到300%的缩放
- **透明度调节**: 调整叠加显示的透明度
- **统计信息**: 实时显示像素统计
- **快捷键支持**: 
  - `Ctrl+O`: 打开掩码
  - `Ctrl+I`: 打开图像
  - `Ctrl+S`: 保存当前视图
  - `Ctrl++/-`: 缩放
  - `Space`: 切换叠加
  - `1/2/3`: 切换风格

## 4. TensorBoard集成

### 4.1 在训练中使用
```python
from src.utils.visualization import log_multilabel_predictions_to_tensorboard

# 在训练循环中
log_multilabel_predictions_to_tensorboard(
    writer=tensorboard_writer,
    images=batch_images,
    masks=batch_masks,
    predictions=model_outputs,
    global_step=current_step,
    tag_prefix='Train',
    num_samples=4
)
```

### 4.2 查看TensorBoard
```bash
tensorboard --logdir=outputs/logs
```

在TensorBoard中，您将看到：
- **MultiLabel标签页**: 显示原图、真实掩码、预测掩码和叠加视图
- **各类别IoU曲线**: 分别显示背景、主轨道、分叉轨道的IoU
- **重叠区域统计**: 显示重叠区域的比例变化

## 5. Python API使用

### 5.1 基本使用
```python
from scripts.visualize_npy_masks import MaskVisualizer

# 创建可视化器
visualizer = MaskVisualizer()

# 加载并可视化掩码
mask = visualizer.load_npy_mask('path/to/mask.npy')
colored_mask = visualizer.create_color_mask(mask, style='gradient')

# 获取统计信息
stats = visualizer.get_statistics(mask)
print(f"主轨道占比: {stats['main_track_ratio']:.1%}")
print(f"分叉轨道占比: {stats['fork_track_ratio']:.1%}")
print(f"重叠区域占比: {stats['overlap_ratio']:.1%}")
```

### 5.2 批量处理
```python
from pathlib import Path

mask_dir = Path('/path/to/masks')
visualizer.visualize_batch(
    mask_dir, 
    num_samples=9,
    style='solid',
    save_dir=Path('output_dir')
)
```

## 6. 最佳实践

### 6.1 选择合适的风格
- **调试和分析**: 使用`solid`风格，边界清晰
- **展示和报告**: 使用`gradient`风格，更美观
- **对比分析**: 使用`blend`风格，便于查看重叠

### 6.2 性能优化
- 批量处理时使用`--no-show`选项，只保存不显示
- 大图像使用缩放功能查看细节
- 使用`--sample`参数限制处理数量进行快速预览

### 6.3 结果分析
- 关注重叠区域的比例，过高可能表示标注问题
- 检查主轨道和分叉轨道的连续性
- 使用统计信息验证掩码的合理性

## 7. 故障排除

### 7.1 常见问题
- **中文字体警告**: 可忽略，不影响功能
- **内存不足**: 减少批量处理的样本数量
- **文件未找到**: 确保NPY文件路径正确

### 7.2 依赖项
确保安装了必要的依赖：
```bash
pip install numpy opencv-python matplotlib pillow
```

## 8. 示例输出

可视化工具会生成如下类型的输出：
- 单个掩码可视化：显示轨道分布和重叠区域
- 批量可视化网格：同时查看多个掩码
- 统计报告：像素级的定量分析
- TensorBoard集成：训练过程的实时监控

通过这些工具，您可以直观地理解和分析多标签掩码，确保数据质量和模型训练效果。