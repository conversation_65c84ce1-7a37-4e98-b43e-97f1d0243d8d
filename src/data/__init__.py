"""
数据处理模块
包含数据集、数据加载器、数据增强等组件
"""

from .dataset import RailwaySegmentationDataset
from .railway_dataset import RailwayTrackDataset
from .dataloader import get_dataloader, create_data_splits
from .dataloader_factory import (
    create_dataloader,
    create_train_val_loaders,
    create_test_loader,
    verify_multilabel_format,
    DataPrefetcher
)
from .transforms import get_train_transforms, get_val_transforms, get_test_transforms
from .augmentations import get_train_transform as get_train_augmentations
from .preprocessing import (
    RailwayAnnotationParser, 
    preprocess_json_annotations,
    preprocess_image, 
    preprocess_mask
)

# 为了向后兼容，保留create_dataloaders函数
def create_dataloaders(config, camera_type=None):
    """向后兼容的数据加载器创建函数"""
    # 更新配置中的相机类型
    if camera_type:
        config['data']['camera_type'] = camera_type
    
    train_loader, val_loader = create_train_val_loaders(config)
    return train_loader, val_loader

# 导入legacy函数以保持向后兼容性
try:
    import sys
    from pathlib import Path
    import importlib.util
    
    # 获取src/data.py文件的路径
    data_py_path = Path(__file__).parent.parent / 'data.py'
    
    if data_py_path.exists():
        # 动态导入src/data.py中的函数
        spec = importlib.util.spec_from_file_location("legacy_data", data_py_path)
        legacy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(legacy_module)
        
        # 导入函数
        get_loaders = legacy_module.get_loaders
        get_transforms = legacy_module.get_transforms
    else:
        # 如果文件不存在，创建空函数避免导入错误
        def get_loaders(*args, **kwargs):
            raise NotImplementedError("get_loaders function not available")
        
        def get_transforms(*args, **kwargs):
            raise NotImplementedError("get_transforms function not available")
            
except Exception as e:
    # 如果导入失败，创建空函数避免错误
    def get_loaders(*args, **kwargs):
        raise NotImplementedError(f"get_loaders function not available: {e}")
    
    def get_transforms(*args, **kwargs):
        raise NotImplementedError(f"get_transforms function not available: {e}")

__all__ = [
    'RailwaySegmentationDataset',
    'RailwayTrackDataset',
    'get_dataloader',
    'create_data_splits',
    'create_dataloaders',
    'create_dataloader',
    'create_train_val_loaders',
    'create_test_loader',
    'verify_multilabel_format',
    'DataPrefetcher',
    'get_train_transforms',
    'get_val_transforms',
    'get_test_transforms',
    'get_train_augmentations',
    'RailwayAnnotationParser',
    'preprocess_json_annotations',
    'preprocess_image',
    'preprocess_mask',
    'get_loaders',  # 添加legacy函数
    'get_transforms'  # 添加legacy函数
] 