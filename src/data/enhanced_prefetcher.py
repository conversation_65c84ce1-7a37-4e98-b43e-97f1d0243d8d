"""
增强版数据预取器
提供更高效的数据加载和GPU传输机制
"""

import torch
import threading
import queue
import time
from typing import Dict, Any, Optional, Union, List, Tuple
import logging


class EnhancedDataPrefetcher:
    """
    增强版数据预取器
    使用单独的线程和队列进行数据预取，减少GPU等待时间
    """
    
    def __init__(
        self, 
        loader: torch.utils.data.DataLoader, 
        device: torch.device,
        queue_size: int = 3,
        non_blocking: bool = True,
        use_channels_last: bool = False,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化增强版数据预取器
        
        Args:
            loader: 数据加载器
            device: 设备（通常是GPU）
            queue_size: 预取队列大小
            non_blocking: 是否使用非阻塞传输
            use_channels_last: 是否使用channels_last内存格式
            logger: 日志记录器
        """
        self.loader = loader
        self.device = device
        self.non_blocking = non_blocking
        self.use_channels_last = use_channels_last
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建队列和事件
        self.queue = queue.Queue(maxsize=queue_size)
        self.stop_event = threading.Event()
        self.prefetch_thread = None
        self.loader_iter = None
        
        # 统计信息
        self.total_prefetch_time = 0
        self.num_batches_prefetched = 0
        self.total_wait_time = 0
        self.num_batches_waited = 0
        
        # 当前批次
        self.current_batch = None
    
    def start_prefetching(self):
        """启动预取线程"""
        if self.prefetch_thread is not None and self.prefetch_thread.is_alive():
            return
            
        self.stop_event.clear()
        self.loader_iter = iter(self.loader)
        self.prefetch_thread = threading.Thread(target=self._prefetch_worker, daemon=True)
        self.prefetch_thread.start()
        self.logger.debug("启动预取线程")
    
    def stop_prefetching(self):
        """停止预取线程"""
        if self.prefetch_thread is not None and self.prefetch_thread.is_alive():
            self.stop_event.set()
            self.prefetch_thread.join(timeout=1.0)
            self.logger.debug("停止预取线程")
            
        # 清空队列
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
            except queue.Empty:
                break
    
    def _prefetch_worker(self):
        """预取工作线程"""
        try:
            while not self.stop_event.is_set():
                try:
                    # 获取下一批数据
                    start_time = time.time()
                    batch = next(self.loader_iter)
                    prefetch_time = time.time() - start_time
                    
                    # 更新统计信息
                    self.total_prefetch_time += prefetch_time
                    self.num_batches_prefetched += 1
                    
                    # 将数据移到设备
                    batch = self._to_device(batch)
                    
                    # 放入队列
                    if not self.stop_event.is_set():
                        self.queue.put(batch, block=True, timeout=5.0)
                    
                except StopIteration:
                    # 数据加载器迭代完成
                    self.queue.put(StopIteration, block=True, timeout=5.0)
                    break
                except queue.Full:
                    # 队列已满，继续尝试
                    continue
                except Exception as e:
                    self.logger.error(f"预取线程异常: {e}")
                    self.queue.put(e, block=True, timeout=5.0)
                    break
        except Exception as e:
            self.logger.error(f"预取工作线程崩溃: {e}")
    
    def _to_device(self, batch: Any) -> Any:
        """
        将批次数据移到设备
        
        Args:
            batch: 批次数据
            
        Returns:
            移动到设备的批次数据
        """
        if isinstance(batch, dict):
            return {
                k: self._process_tensor(v) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()
            }
        elif isinstance(batch, (list, tuple)):
            return [
                self._process_tensor(v) if isinstance(v, torch.Tensor) else v
                for v in batch
            ]
        elif isinstance(batch, torch.Tensor):
            return self._process_tensor(batch)
        else:
            return batch
    
    def _process_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """
        处理张量：移动到设备并应用内存格式
        
        Args:
            tensor: 输入张量
            
        Returns:
            处理后的张量
        """
        # 移动到设备
        tensor = tensor.to(self.device, non_blocking=self.non_blocking)
        
        # 应用channels_last内存格式（如果启用且张量是4D）
        if self.use_channels_last and tensor.dim() == 4:
            tensor = tensor.to(memory_format=torch.channels_last)
            
        return tensor
    
    def __iter__(self):
        """迭代器"""
        self.start_prefetching()
        return self
    
    def __next__(self):
        """获取下一批数据"""
        if self.prefetch_thread is None or not self.prefetch_thread.is_alive():
            raise RuntimeError("预取线程未运行")
            
        # 等待队列中的数据
        start_time = time.time()
        try:
            batch = self.queue.get(block=True, timeout=30.0)
            wait_time = time.time() - start_time
            
            # 更新统计信息
            self.total_wait_time += wait_time
            self.num_batches_waited += 1
            
            # 检查是否是异常
            if batch is StopIteration:
                self.stop_prefetching()
                raise StopIteration
            elif isinstance(batch, Exception):
                self.stop_prefetching()
                raise batch
                
            self.current_batch = batch
            return batch
            
        except queue.Empty:
            self.logger.warning("预取队列超时")
            self.stop_prefetching()
            self.start_prefetching()
            raise StopIteration
    
    def __len__(self):
        """返回数据加载器长度"""
        return len(self.loader)
    
    def get_stats(self) -> Dict[str, float]:
        """
        获取性能统计信息
        
        Returns:
            统计信息字典
        """
        avg_prefetch_time = (
            self.total_prefetch_time / self.num_batches_prefetched 
            if self.num_batches_prefetched > 0 else 0
        )
        
        avg_wait_time = (
            self.total_wait_time / self.num_batches_waited
            if self.num_batches_waited > 0 else 0
        )
        
        return {
            'avg_prefetch_time': avg_prefetch_time,
            'num_batches_prefetched': self.num_batches_prefetched,
            'avg_wait_time': avg_wait_time,
            'num_batches_waited': self.num_batches_waited,
            'prefetch_efficiency': (
                avg_prefetch_time / (avg_prefetch_time + avg_wait_time)
                if avg_prefetch_time + avg_wait_time > 0 else 0
            )
        }


class MultiDeviceDataPrefetcher:
    """
    多设备数据预取器
    支持多GPU并行数据预取
    """
    
    def __init__(
        self,
        loader: torch.utils.data.DataLoader,
        devices: List[torch.device],
        queue_size: int = 3,
        non_blocking: bool = True,
        use_channels_last: bool = False,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化多设备数据预取器
        
        Args:
            loader: 数据加载器
            devices: 设备列表
            queue_size: 预取队列大小
            non_blocking: 是否使用非阻塞传输
            use_channels_last: 是否使用channels_last内存格式
            logger: 日志记录器
        """
        self.loader = loader
        self.devices = devices
        self.non_blocking = non_blocking
        self.use_channels_last = use_channels_last
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建队列和事件
        self.queue = queue.Queue(maxsize=queue_size)
        self.stop_event = threading.Event()
        self.prefetch_thread = None
        self.loader_iter = None
        
        # 当前设备索引
        self.current_device_idx = 0
        
        # 统计信息
        self.total_prefetch_time = 0
        self.num_batches_prefetched = 0
    
    def start_prefetching(self):
        """启动预取线程"""
        if self.prefetch_thread is not None and self.prefetch_thread.is_alive():
            return
            
        self.stop_event.clear()
        self.loader_iter = iter(self.loader)
        self.prefetch_thread = threading.Thread(target=self._prefetch_worker, daemon=True)
        self.prefetch_thread.start()
        self.logger.debug("启动多设备预取线程")
    
    def stop_prefetching(self):
        """停止预取线程"""
        if self.prefetch_thread is not None and self.prefetch_thread.is_alive():
            self.stop_event.set()
            self.prefetch_thread.join(timeout=1.0)
            self.logger.debug("停止多设备预取线程")
            
        # 清空队列
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
            except queue.Empty:
                break
    
    def _prefetch_worker(self):
        """预取工作线程"""
        try:
            while not self.stop_event.is_set():
                try:
                    # 获取下一批数据
                    start_time = time.time()
                    batch = next(self.loader_iter)
                    prefetch_time = time.time() - start_time
                    
                    # 更新统计信息
                    self.total_prefetch_time += prefetch_time
                    self.num_batches_prefetched += 1
                    
                    # 将数据分发到多个设备
                    device_batches = self._distribute_to_devices(batch)
                    
                    # 放入队列
                    if not self.stop_event.is_set():
                        self.queue.put(device_batches, block=True, timeout=5.0)
                    
                except StopIteration:
                    # 数据加载器迭代完成
                    self.queue.put(StopIteration, block=True, timeout=5.0)
                    break
                except queue.Full:
                    # 队列已满，继续尝试
                    continue
                except Exception as e:
                    self.logger.error(f"多设备预取线程异常: {e}")
                    self.queue.put(e, block=True, timeout=5.0)
                    break
        except Exception as e:
            self.logger.error(f"多设备预取工作线程崩溃: {e}")
    
    def _distribute_to_devices(self, batch: Any) -> List[Any]:
        """
        将批次数据分发到多个设备
        
        Args:
            batch: 批次数据
            
        Returns:
            分发到多个设备的批次数据列表
        """
        # 对于字典类型的批次
        if isinstance(batch, dict):
            # 为每个设备创建一个批次副本
            device_batches = []
            for device in self.devices:
                device_batch = {
                    k: self._process_tensor(v, device) if isinstance(v, torch.Tensor) else v
                    for k, v in batch.items()
                }
                device_batches.append(device_batch)
            return device_batches
        
        # 对于列表/元组类型的批次
        elif isinstance(batch, (list, tuple)):
            # 为每个设备创建一个批次副本
            device_batches = []
            for device in self.devices:
                device_batch = [
                    self._process_tensor(v, device) if isinstance(v, torch.Tensor) else v
                    for v in batch
                ]
                device_batches.append(device_batch)
            return device_batches
        
        # 对于张量类型的批次
        elif isinstance(batch, torch.Tensor):
            return [self._process_tensor(batch, device) for device in self.devices]
        
        # 其他类型的批次
        else:
            return [batch] * len(self.devices)
    
    def _process_tensor(self, tensor: torch.Tensor, device: torch.device) -> torch.Tensor:
        """
        处理张量：移动到设备并应用内存格式
        
        Args:
            tensor: 输入张量
            device: 目标设备
            
        Returns:
            处理后的张量
        """
        # 移动到设备
        tensor = tensor.to(device, non_blocking=self.non_blocking)
        
        # 应用channels_last内存格式（如果启用且张量是4D）
        if self.use_channels_last and tensor.dim() == 4:
            tensor = tensor.to(memory_format=torch.channels_last)
            
        return tensor
    
    def __iter__(self):
        """迭代器"""
        self.start_prefetching()
        return self
    
    def __next__(self):
        """获取下一批数据"""
        if self.prefetch_thread is None or not self.prefetch_thread.is_alive():
            raise RuntimeError("多设备预取线程未运行")
            
        # 等待队列中的数据
        try:
            device_batches = self.queue.get(block=True, timeout=30.0)
            
            # 检查是否是异常
            if device_batches is StopIteration:
                self.stop_prefetching()
                raise StopIteration
            elif isinstance(device_batches, Exception):
                self.stop_prefetching()
                raise device_batches
                
            # 轮询选择设备
            batch = device_batches[self.current_device_idx]
            self.current_device_idx = (self.current_device_idx + 1) % len(self.devices)
            
            return batch
            
        except queue.Empty:
            self.logger.warning("多设备预取队列超时")
            self.stop_prefetching()
            self.start_prefetching()
            raise StopIteration
    
    def __len__(self):
        """返回数据加载器长度"""
        return len(self.loader)
    
    def get_stats(self) -> Dict[str, float]:
        """
        获取性能统计信息
        
        Returns:
            统计信息字典
        """
        avg_prefetch_time = (
            self.total_prefetch_time / self.num_batches_prefetched 
            if self.num_batches_prefetched > 0 else 0
        )
        
        return {
            'avg_prefetch_time': avg_prefetch_time,
            'num_batches_prefetched': self.num_batches_prefetched,
            'num_devices': len(self.devices)
        } 