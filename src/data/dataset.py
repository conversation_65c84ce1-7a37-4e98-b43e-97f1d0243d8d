"""
数据集模块
定义铁路分割数据集
"""

from typing import Optional, List, Dict, Any
from pathlib import Path
import numpy as np
import torch
import cv2
from ..core.base import BaseDataset
from ..core.registry import register_dataset


@register_dataset('railway_segmentation')
class RailwaySegmentationDataset(BaseDataset):
    """
    铁路分割数据集
    """
    
    # 类别映射
    CLASS_MAPPING = {
        'railway_track': {'color': [6, 6, 6], 'id': 0},    # 铁路轨道
        'rolling_stock': {'color': [7, 7, 7], 'id': 1},    # 机车车辆
        'platform': {'color': [10, 10, 10], 'id': 2}       # 站台
    }
    
    def __init__(self, 
                 data_root: str,
                 split: str = 'train',
                 transform: Optional[Any] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化数据集
        
        Args:
            data_root: 数据根目录
            split: 数据集划分 ('train', 'val', 'test')
            transform: 数据变换
            config: 数据集配置
        """
        self.images_dir = Path(data_root) / 'images' / split
        self.masks_dir = Path(data_root) / 'masks' / split
        
        super().__init__(data_root, split, transform, config)
        
    def _load_data(self) -> List[Dict[str, Any]]:
        """
        加载数据文件列表
        
        Returns:
            数据列表
        """
        data = []
        
        # 获取所有图像文件
        image_files = sorted(self.images_dir.glob('*.jpg'))
        
        for image_path in image_files:
            # 构建对应的掩码路径
            mask_path = self.masks_dir / image_path.name.replace('.jpg', '.png')
            
            # 只有当掩码文件存在时才添加到数据集
            if mask_path.exists():
                data.append({
                    'image_path': image_path,
                    'mask_path': mask_path,
                    'filename': image_path.name
                })
        
        print(f"加载 {self.split} 数据集: {len(data)} 个样本")
        
        return data
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取一个数据样本
        
        Args:
            idx: 索引
            
        Returns:
            数据样本字典
        """
        item = self.data[idx]
        
        # 读取图像
        image = cv2.imread(str(item['image_path']))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        original_shape = image.shape[:2]
        
        # 归一化
        image = image.astype('float32') / 255.0
        
        # 读取掩码
        mask = None
        if 'mask_path' in item:
            mask = cv2.imread(str(item['mask_path']))
            mask = self._process_mask(mask)
            mask = mask.astype('float32')
        
        # 构建样本字典
        sample = {
            'image': image,
            'filename': item['filename'],
            'original_shape': original_shape
        }
        
        if mask is not None:
            sample['mask'] = mask
        
        # 应用变换
        if self.transform is not None:
            sample = self.transform(**sample)
            
            # 调整掩码维度
            if 'mask' in sample and hasattr(sample['mask'], 'permute'):
                sample['mask'] = sample['mask'].permute(2, 0, 1)
        
        return sample
    
    def _process_mask(self, mask: np.ndarray) -> np.ndarray:
        """
        处理掩码，将RGB值转换为类别通道
        
        Args:
            mask: 原始掩码 (H, W, 3)
            
        Returns:
            处理后的掩码 (H, W, num_classes)
        """
        h, w = mask.shape[:2]
        num_classes = len(self.CLASS_MAPPING)
        processed_mask = np.zeros((h, w, num_classes), dtype=np.uint8)
        
        # 为每个类别创建二值掩码
        for class_name, class_info in self.CLASS_MAPPING.items():
            color = class_info['color']
            class_id = class_info['id']
            
            # 创建类别掩码
            class_mask = np.all(mask == color, axis=2)
            processed_mask[:, :, class_id] = class_mask.astype(np.uint8)
        
        return processed_mask
    
    def get_class_weights(self) -> torch.Tensor:
        """
        计算类别权重（用于处理类别不平衡）
        
        Returns:
            类别权重张量
        """
        if self.split != 'train':
            raise ValueError("类别权重只能从训练集计算")
        
        # 统计每个类别的像素数
        class_counts = np.zeros(len(self.CLASS_MAPPING))
        
        for item in self.data[:100]:  # 使用前100个样本估计
            mask = cv2.imread(str(item['mask_path']))
            mask = self._process_mask(mask)
            
            for class_id in range(len(self.CLASS_MAPPING)):
                class_counts[class_id] += np.sum(mask[:, :, class_id])
        
        # 计算权重
        total_pixels = np.sum(class_counts)
        class_weights = total_pixels / (len(self.CLASS_MAPPING) * class_counts + 1e-6)
        
        # 归一化权重
        class_weights = class_weights / np.sum(class_weights) * len(self.CLASS_MAPPING)
        
        return torch.FloatTensor(class_weights)
    
    def visualize_sample(self, idx: int, save_path: Optional[str] = None):
        """
        可视化一个样本
        
        Args:
            idx: 样本索引
            save_path: 保存路径（可选）
        """
        import matplotlib.pyplot as plt
        
        sample = self[idx]
        
        fig, axes = plt.subplots(1, 2 + len(self.CLASS_MAPPING), figsize=(15, 5))
        
        # 显示原图
        image = sample['image']
        if isinstance(image, torch.Tensor):
            image = image.permute(1, 2, 0).numpy()
        axes[0].imshow(image)
        axes[0].set_title('原始图像')
        axes[0].axis('off')
        
        # 显示每个类别的掩码
        if 'mask' in sample:
            mask = sample['mask']
            if isinstance(mask, torch.Tensor):
                mask = mask.permute(1, 2, 0).numpy()
            
            # 合成掩码
            combined_mask = np.zeros((*mask.shape[:2], 3))
            colors = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]  # RGB
            
            for i, (class_name, class_info) in enumerate(self.CLASS_MAPPING.items()):
                class_id = class_info['id']
                axes[i + 1].imshow(mask[:, :, class_id], cmap='gray')
                axes[i + 1].set_title(class_name)
                axes[i + 1].axis('off')
                
                # 添加到合成掩码
                combined_mask += mask[:, :, class_id:class_id+1] * colors[i]
            
            # 显示合成掩码
            axes[-1].imshow(combined_mask)
            axes[-1].set_title('合成掩码')
            axes[-1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        else:
            plt.show()
        
        plt.close() 