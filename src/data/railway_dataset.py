"""
铁路轨道数据集模块
支持JSON标注格式和多相机类型
"""

import json
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path
import numpy as np
import torch
import cv2
from ..core.base import BaseDataset
from ..core.registry import register_dataset
from .preprocessing import RailwayAnnotationParser


@register_dataset('railway_track_dataset')
class RailwayTrackDataset(BaseDataset):
    """
    铁路轨道分割数据集
    支持JSON标注格式和6mm/25mm两种相机类型
    """
    
    # 类别定义
    CLASSES = {
        'background': 0,
        'main_track': 1,    # 主轨道
        'fork_track': 2     # 分叉轨道
    }
    
    # 类别颜色（用于可视化）
    CLASS_COLORS = {
        0: [0, 0, 0],       # 背景 - 黑色
        1: [255, 0, 0],     # 主轨道 - 红色
        2: [0, 255, 0]      # 分叉轨道 - 绿色
    }
    
    def __init__(self,
                 data_root: str,
                 split: str = 'train',
                 transform: Optional[Any] = None,
                 config: Optional[Dict[str, Any]] = None,
                 camera_type: Optional[str] = None,
                 json_dir: Optional[str] = None):
        """
        初始化数据集
        
        Args:
            data_root: 数据根目录
            split: 数据集划分 ('train', 'val', 'test')
            transform: 数据变换
            config: 数据集配置
            camera_type: 相机类型 ('6mm', '25mm', None表示全部)
            json_dir: JSON标注文件目录
        """
        self.camera_type = camera_type
        self.json_dir = Path(json_dir) if json_dir else None
        self.annotation_parser = RailwayAnnotationParser()
        
        # 设置目录路径
        self.images_dir = Path(data_root) / split / 'images'
        self.masks_dir = Path(data_root) / split / 'masks'
        
        super().__init__(data_root, split, transform, config)
    
    def _load_data(self) -> List[Dict[str, Any]]:
        """
        加载数据文件列表
        
        智能选择数据源：
        1. 优先使用已分割的处理数据（如果存在）
        2. 如果没有分割数据，才从JSON文件加载并进行动态分割
        
        Returns:
            数据列表
        """
        data = []
        
        # 检查是否存在已分割的处理数据
        has_processed_splits = (
            self.images_dir.exists() and 
            self.masks_dir.exists() and
            len(list(self.images_dir.glob('*.png'))) > 0
        )
        
        if has_processed_splits:
            # 优先使用已分割的处理数据
            print(f"使用已分割的处理数据: {self.images_dir}")
            data.extend(self._load_from_processed())
        
        elif self.json_dir and self.json_dir.exists():
            # 如果没有分割数据，从JSON加载并动态分割
            print(f"从JSON文件动态分割数据: {self.json_dir}")
            data.extend(self._load_from_json_with_split())
        
        else:
            print(f"警告: 没有找到有效的数据源")
            print(f"  已处理数据目录: {self.images_dir} (存在: {self.images_dir.exists()})")
            print(f"  JSON目录: {self.json_dir} (存在: {self.json_dir and self.json_dir.exists()})")
        
        # 根据相机类型筛选
        if self.camera_type:
            data = [item for item in data if item.get('camera_type') == self.camera_type]
        
        print(f"加载 {self.split} 数据集: {len(data)} 个样本")
        if self.camera_type:
            print(f"相机类型: {self.camera_type}")
        
        return data
    
    def _load_from_json_with_split(self) -> List[Dict[str, Any]]:
        """
        从JSON文件加载数据并按配置比例进行分割
        
        Returns:
            当前split的数据列表
        """
        # 获取所有JSON文件
        json_files = list(self.json_dir.glob('*.json'))
        all_data = []
        
        # 解析所有JSON文件
        for json_file in json_files:
            try:
                # 读取原始JSON数据
                with open(json_file, 'r', encoding='utf-8') as f:
                    raw_json_data = json.load(f)
                
                annotation_data = self.annotation_parser.parse_json_file(json_file)
                camera_type = annotation_data['camera_type']
                
                data_item = {
                    'json_path': json_file,
                    'annotation_data': annotation_data,
                    'raw_json_data': raw_json_data,  # 保存原始JSON数据
                    'filename': annotation_data['filename'],
                    'camera_type': camera_type,
                    'source': 'json'
                }
                
                all_data.append(data_item)
                
            except Exception as e:
                print(f"加载JSON文件 {json_file} 失败: {e}")
                continue
        
        # 获取分割配置
        split_config = self.config.get('data', {}).get('split_ratio', {
            'train': 0.7, 'val': 0.15, 'test': 0.15
        })
        
        # 使用文件名作为种子确保一致性分割
        import hashlib
        random_state = 42
        
        # 按文件名排序确保一致性
        all_data.sort(key=lambda x: x['filename'])
        
        # 计算分割点
        total = len(all_data)
        train_end = int(total * split_config['train'])
        val_end = train_end + int(total * split_config['val'])
        
        # 根据当前split返回对应数据
        if self.split == 'train':
            split_data = all_data[:train_end]
        elif self.split == 'val':
            split_data = all_data[train_end:val_end]
        elif self.split == 'test':
            split_data = all_data[val_end:]
        else:
            print(f"警告: 未知的split: {self.split}，返回空数据")
            split_data = []
        
        print(f"JSON数据分割结果: 总计={total}, {self.split}={len(split_data)}")
        
        return split_data
    
    def _load_from_json(self) -> List[Dict[str, Any]]:
        """
        从JSON文件加载数据
        
        Returns:
            数据列表
        """
        data = []
        json_files = list(self.json_dir.glob('*.json'))
        
        for json_file in json_files:
            try:
                # 读取原始JSON数据
                with open(json_file, 'r', encoding='utf-8') as f:
                    raw_json_data = json.load(f)
                
                # 解析JSON文件
                annotation_data = self.annotation_parser.parse_json_file(json_file)
                
                # 判断相机类型
                camera_type = annotation_data['camera_type']
                
                data_item = {
                    'json_path': json_file,
                    'annotation_data': annotation_data,
                    'raw_json_data': raw_json_data,  # 保存原始JSON数据
                    'filename': annotation_data['filename'],
                    'camera_type': camera_type,
                    'source': 'json'
                }
                
                # 如果图像已存在，添加路径
                image_path = self.images_dir / annotation_data['filename']
                if image_path.exists():
                    data_item['image_path'] = image_path
                
                data.append(data_item)
                
            except Exception as e:
                print(f"加载JSON文件 {json_file} 失败: {e}")
                continue
        
        return data
    
    def _load_from_processed(self) -> List[Dict[str, Any]]:
        """
        从已处理的图像目录加载数据
        
        Returns:
            数据列表
        """
        data = []
        
        # 获取所有图像文件
        image_files = sorted(self.images_dir.glob('*.png'))
        
        # 检查是否使用多标签格式
        use_multilabel = self.config.get('data', {}).get('use_multilabel', True)
        
        for image_path in image_files:
            # 构建对应的掩码路径
            # 统一使用PNG格式
            mask_path = self.masks_dir / image_path.name
            
            # 只有当掩码文件存在时才添加到数据集
            if mask_path and mask_path.exists():
                # 从文件名推断相机类型
                camera_type = '6mm' if 'near' in image_path.name else '25mm'
                
                data.append({
                    'image_path': image_path,
                    'mask_path': mask_path,
                    'filename': image_path.name,
                    'camera_type': camera_type,
                    'source': 'processed'
                })
        
        return data
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取数据样本
        
        Args:
            idx: 样本索引
            
        Returns:
            数据样本字典
        """
        # 最多重试5次
        max_retries = 5
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                item = self.data[idx]
                
                # 获取图像
                image = self._load_image(item)
                if image is None:
                    print(f"警告: 无法加载图像 {item['filename']}，跳过...")
                    # 选择下一个索引重试
                    idx = (idx + 1) % len(self.data)
                    retry_count += 1
                    continue
                
                original_shape = image.shape[:2]
                
                # 归一化
                image = image.astype('float32') / 255.0
                
                # 获取掩码
                mask = self._load_mask(item, original_shape)
                
                # 构建样本字典
                sample = {
                    'image': image,
                    'filename': item['filename'],
                    'original_shape': original_shape,
                    'camera_type': item['camera_type']
                }
                
                if mask is not None:
                    sample['mask'] = mask
                
                # 应用变换
                if self.transform is not None:
                    sample = self.transform(**sample)
                    
                    # 调整掩码维度
                    if 'mask' in sample and hasattr(sample['mask'], 'permute'):
                        sample['mask'] = sample['mask'].permute(2, 0, 1)
                
                return sample
                
            except Exception as e:
                print(f"警告: 处理样本 {idx} 时出错: {e}，重试...")
                # 选择下一个索引重试
                idx = (idx + 1) % len(self.data)
                retry_count += 1
                continue
        
        # 如果重试次数用完，返回一个虚拟样本
        print(f"错误: 无法加载任何有效样本，返回虚拟样本")
        return self._create_dummy_sample()
    
    def _load_image(self, item: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        加载图像
        
        Args:
            item: 数据项
            
        Returns:
            图像数组
        """
        # 如果有图像路径，直接加载
        if 'image_path' in item and item['image_path'].exists():
            image = cv2.imread(str(item['image_path']))
            if image is not None:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return image
        
        # 如果是JSON源，尝试下载
        if item['source'] == 'json' and 'annotation_data' in item:
            image_url = item['annotation_data']['image_url']
            if image_url:
                # 临时下载到内存
                import urllib.request
                import numpy as np
                try:
                    resp = urllib.request.urlopen(image_url)
                    image = np.asarray(bytearray(resp.read()), dtype="uint8")
                    image = cv2.imdecode(image, cv2.IMREAD_COLOR)
                    if image is not None:
                        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    return image
                except Exception as e:
                    print(f"下载图像失败: {image_url}, 错误: {e}")
        
        return None
    
    def _load_mask(self, item: Dict[str, Any], image_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        加载或生成掩码
        
        Args:
            item: 数据项
            image_shape: 图像尺寸
            
        Returns:
            掩码数组 (H, W, C) 格式
        """
        # 如果有掩码路径，直接加载
        if 'mask_path' in item and item['mask_path'].exists():
            mask_path = item['mask_path']
            
            # 检查是否是.npz文件（压缩格式）
            if mask_path.suffix == '.npz':
                # 加载压缩的numpy数组
                data = np.load(str(mask_path))
                # NPZ文件包含一个名为'mask'的数组
                mask = data['mask']
                if mask is not None:
                    # 如果是uint8格式（0-255），转换为float32 (0-1)
                    if mask.dtype == np.uint8:
                        mask = mask.astype(np.float32) / 255.0
                    # 如果已经是多通道格式，直接返回
                    if len(mask.shape) == 3 and mask.shape[2] == 3:
                        return mask.astype(np.float32)
                    # 否则转换为多通道格式
                    else:
                        return self._single_to_multi_channel_mask(mask)
            # 检查是否是.npy文件
            elif mask_path.suffix == '.npy':
                # 加载numpy数组
                mask = np.load(str(mask_path))
                if mask is not None:
                    # 如果是uint8格式（0-255），转换为float32 (0-1)
                    if mask.dtype == np.uint8:
                        mask = mask.astype(np.float32) / 255.0
                    # 如果已经是多通道格式，直接返回
                    if len(mask.shape) == 3 and mask.shape[2] == 3:
                        return mask.astype(np.float32)
                    # 否则转换为多通道格式
                    else:
                        return self._single_to_multi_channel_mask(mask)
            else:
                # 加载图像格式的掩码
                if self.config.get('data', {}).get('use_multilabel', True) and self.config.get('data', {}).get('mask_format', 'png') == 'png':
                    # 读取BGR PNG作为多标签掩码 (OpenCV格式)
                    mask = cv2.imread(str(mask_path), cv2.IMREAD_COLOR)
                    if mask is not None:
                        # BGR通道转换为多标签格式
                        h, w, _ = mask.shape
                        multilabel_mask = np.zeros((h, w, 3), dtype=np.float32)
                        # B通道: 背景
                        multilabel_mask[:, :, 0] = (mask[:, :, 0] > 127).astype(np.float32)
                        # G通道: 主轨道
                        multilabel_mask[:, :, 1] = (mask[:, :, 1] > 127).astype(np.float32)
                        # R通道: 分叉轨道
                        multilabel_mask[:, :, 2] = (mask[:, :, 2] > 127).astype(np.float32)
                        return multilabel_mask
                else:
                    # 原始灰度掩码格式
                    mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
                    if mask is not None:
                        # 转换为多通道格式
                        return self._single_to_multi_channel_mask(mask)
        
        # 如果是JSON源，生成掩码
        if item['source'] == 'json' and 'annotation_data' in item:
            # 使用多标签掩码生成（如果配置中启用）
            use_multilabel = self.config.get('data', {}).get('use_multilabel', True)
            
            if use_multilabel and hasattr(self.annotation_parser, 'create_multilabel_segmentation_mask'):
                # 使用原始JSON数据生成多标签掩码
                json_data = item.get('raw_json_data', None)
                if json_data:
                    mask = self.annotation_parser.create_multilabel_segmentation_mask(
                        json_data, image_shape
                    )
                    return mask.astype(np.float32)
                else:
                    # 兼容旧数据格式
                    mask = self.annotation_parser.create_segmentation_mask(
                        item['annotation_data'], image_shape
                    )
                    return self._single_to_multi_channel_mask(mask)
            else:
                # 使用原始单标签方法
                mask = self.annotation_parser.create_segmentation_mask(
                    item['annotation_data'], image_shape
                )
                # 将单通道掩码转换为多通道格式
                return self._single_to_multi_channel_mask(mask)
        
        return None
    
    def _single_to_multi_channel_mask(self, mask: np.ndarray) -> np.ndarray:
        """
        将单通道掩码转换为多通道格式 - 支持多标签
        
        Args:
            mask: 单通道掩码
            
        Returns:
            多通道掩码 (H, W, C) - 每个通道是独立的二值掩码
        """
        h, w = mask.shape
        num_classes = len(self.CLASSES)
        multi_channel_mask = np.zeros((h, w, num_classes), dtype=np.float32)
        
        # 多标签处理：每个类别独立判断
        # 注意：在轨道分叉处，像素可能同时属于主轨道和分叉轨道
        for class_id in range(num_classes):
            if class_id == 0:  # 背景
                # 背景是所有非轨道区域
                multi_channel_mask[:, :, class_id] = (mask == class_id).astype(np.float32)
            elif class_id == 1:  # 主轨道
                # 主轨道包括单独的主轨道和分叉处的主轨道部分
                multi_channel_mask[:, :, class_id] = (mask == class_id).astype(np.float32)
                # 在分叉处（mask==2），主轨道也可能存在
                # 这里可以根据实际需求调整逻辑
            elif class_id == 2:  # 分叉轨道
                multi_channel_mask[:, :, class_id] = (mask == class_id).astype(np.float32)
        
        return multi_channel_mask
    
    def _create_dummy_sample(self) -> Dict[str, Any]:
        """
        创建一个虚拟样本（用于错误恢复）
        
        Returns:
            虚拟样本字典
        """
        import numpy as np
        import torch
        
        # 创建虚拟图像和掩码
        if hasattr(self, 'config') and self.config:
            height = self.config.get('image_size', {}).get('height', 544)
            width = self.config.get('image_size', {}).get('width', 960)
        else:
            height, width = 544, 960
        
        # 创建黑色图像
        image = np.zeros((height, width, 3), dtype=np.float32)
        
        # 创建全背景掩码
        mask = np.zeros((height, width, len(self.CLASSES)), dtype=np.float32)
        mask[:, :, 0] = 1.0  # 全部设为背景类
        
        sample = {
            'image': torch.from_numpy(image).permute(2, 0, 1),
            'mask': torch.from_numpy(mask).permute(2, 0, 1),
            'filename': 'dummy_sample.png',
            'original_shape': (height, width),
            'camera_type': '6mm'
        }
        
        return sample
    
    def get_sample_weights(self) -> torch.Tensor:
        """
        计算样本权重（用于处理不同相机类型的样本不平衡）
        
        Returns:
            样本权重张量
        """
        # 统计每种相机类型的样本数
        camera_counts = {'6mm': 0, '25mm': 0}
        for item in self.data:
            camera_type = item.get('camera_type', '6mm')
            if camera_type in camera_counts:
                camera_counts[camera_type] += 1
        
        # 计算权重
        total_samples = len(self.data)
        weights = []
        
        for item in self.data:
            camera_type = item.get('camera_type', '6mm')
            if camera_type in camera_counts and camera_counts[camera_type] > 0:
                weight = total_samples / (2.0 * camera_counts[camera_type])
            else:
                weight = 1.0
            weights.append(weight)
        
        return torch.FloatTensor(weights)
    
    def visualize_sample(self, idx: int, save_path: Optional[str] = None):
        """
        可视化一个样本
        
        Args:
            idx: 样本索引
            save_path: 保存路径（可选）
        """
        import matplotlib.pyplot as plt
        
        sample = self[idx]
        
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        # 显示原图
        image = sample['image']
        if isinstance(image, torch.Tensor):
            image = image.permute(1, 2, 0).numpy()
        axes[0].imshow(image)
        axes[0].set_title(f'原始图像 ({sample["camera_type"]})')
        axes[0].axis('off')
        
        # 显示每个类别的掩码
        if 'mask' in sample:
            mask = sample['mask']
            if isinstance(mask, torch.Tensor):
                mask = mask.permute(1, 2, 0).numpy()
            
            # 背景
            axes[1].imshow(mask[:, :, 0], cmap='gray')
            axes[1].set_title('背景')
            axes[1].axis('off')
            
            # 主轨道
            axes[2].imshow(mask[:, :, 1], cmap='Reds')
            axes[2].set_title('主轨道')
            axes[2].axis('off')
            
            # 分叉轨道
            axes[3].imshow(mask[:, :, 2], cmap='Greens')
            axes[3].set_title('分叉轨道')
            axes[3].axis('off')
        
        plt.suptitle(f'样本 {idx}: {sample["filename"]}')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        else:
            plt.show()
        
        plt.close()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据集统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            'total_samples': len(self.data),
            'camera_distribution': {'6mm': 0, '25mm': 0},
            'source_distribution': {'json': 0, 'processed': 0}
        }
        
        for item in self.data:
            camera_type = item.get('camera_type', 'unknown')
            if camera_type in stats['camera_distribution']:
                stats['camera_distribution'][camera_type] += 1
            
            source = item.get('source', 'unknown')
            if source in stats['source_distribution']:
                stats['source_distribution'][source] += 1
        
        return stats 