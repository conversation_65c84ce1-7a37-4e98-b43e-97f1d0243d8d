"""
数据加载器工厂模块
支持多标签分割的数据加载
"""

import torch
from torch.utils.data import DataLoader, Dataset
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np

from .railway_dataset import RailwayTrackDataset
from .augmentations import get_train_transform, get_val_transform, get_test_transforms
from ..core.registry import DATASET_REGISTRY


class DataPrefetcher:
    """
    数据预取器，提高GPU利用率
    """
    
    def __init__(self, loader: DataLoader, device: torch.device):
        self.loader = loader
        self.device = device
        self.stream = torch.cuda.Stream()
        self.mean = torch.tensor([0.485, 0.456, 0.406]).to(device).view(1, 3, 1, 1)
        self.std = torch.tensor([0.229, 0.224, 0.225]).to(device).view(1, 3, 1, 1)
        self.preload()
    
    def preload(self):
        try:
            self.next_batch = next(self.loader)
        except StopIteration:
            self.next_batch = None
            return
        
        with torch.cuda.stream(self.stream):
            self.next_batch = {k: v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                               for k, v in self.next_batch.items()}
    
    def __iter__(self):
        return self
    
    def __next__(self):
        torch.cuda.current_stream().wait_stream(self.stream)
        batch = self.next_batch
        
        if batch is None:
            raise StopIteration
        
        self.preload()
        return batch
    
    def __len__(self):
        return len(self.loader)


def create_dataloader(
    config: Dict[str, Any],
    split: str = 'train',
    shuffle: Optional[bool] = None,
    drop_last: Optional[bool] = None
) -> DataLoader:
    """
    创建数据加载器
    
    Args:
        config: 配置字典
        split: 数据集划分 ('train', 'val', 'test')
        shuffle: 是否打乱数据
        drop_last: 是否丢弃最后不完整的批次
        
    Returns:
        数据加载器
    """
    # 获取数据集配置
    data_config = config.get('data', {})
    dataset_type = data_config.get('dataset_type', 'railway_track_dataset')
    
    # 获取数据集类
    if dataset_type in DATASET_REGISTRY:
        dataset_class = DATASET_REGISTRY.get(dataset_type)
    else:
        dataset_class = RailwayTrackDataset
    
    # 获取数据路径
    data_root = data_config.get('processed_data_path', 'data/processed')
    json_dir = data_config.get('json_dir', None)
    
    # 获取相机类型
    camera_type = data_config.get('camera_type', None)
    camera_specific = data_config.get('camera_specific_training', False)
    
    # 选择数据增强
    if split == 'train':
        transform = get_train_transforms(config)
        default_shuffle = True
        default_drop_last = True
    elif split == 'val':
        transform = get_val_transforms(config)
        default_shuffle = False
        default_drop_last = False
    else:  # test
        transform = get_test_transforms(config)
        default_shuffle = False
        default_drop_last = False
    
    # 使用提供的值或默认值
    if shuffle is None:
        shuffle = default_shuffle
    if drop_last is None:
        drop_last = default_drop_last
    
    # 创建数据集
    dataset = dataset_class(
        data_root=data_root,
        split=split,
        transform=transform,
        config=config,
        camera_type=camera_type if camera_specific else None,
        json_dir=json_dir
    )
    
    # 获取批量大小
    batch_sizes = data_config.get('batch_size', {})
    if isinstance(batch_sizes, dict):
        batch_size = batch_sizes.get(split, 4)
    else:
        batch_size = batch_sizes
    
    # 获取其他参数
    num_workers = config.get('project', {}).get('num_workers', 4)
    pin_memory = config.get('project', {}).get('gpu_optimization', {}).get('pin_memory', True)
    persistent_workers = config.get('project', {}).get('gpu_optimization', {}).get('persistent_workers', True)
    prefetch_factor = config.get('project', {}).get('gpu_optimization', {}).get('prefetch_factor', 2)
    
    # 创建数据加载器
    loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=drop_last,
        persistent_workers=persistent_workers and num_workers > 0,
        prefetch_factor=prefetch_factor if num_workers > 0 else None
    )
    
    return loader


def create_train_val_loaders(config: Dict[str, Any]) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        config: 配置字典
        
    Returns:
        (train_loader, val_loader)
    """
    train_loader = create_dataloader(config, split='train')
    val_loader = create_dataloader(config, split='val')
    
    return train_loader, val_loader


def create_test_loader(config: Dict[str, Any]) -> DataLoader:
    """
    创建测试数据加载器
    
    Args:
        config: 配置字典
        
    Returns:
        test_loader
    """
    return create_dataloader(config, split='test')


def verify_multilabel_format(dataloader: DataLoader, num_samples: int = 3):
    """
    验证数据加载器是否正确加载多标签格式
    
    Args:
        dataloader: 数据加载器
        num_samples: 要检查的样本数
    """
    print("\n=== 验证多标签数据格式 ===")
    
    for i, batch in enumerate(dataloader):
        if i >= num_samples:
            break
        
        print(f"\n批次 {i+1}:")
        print(f"  图像形状: {batch['image'].shape}")
        print(f"  掩码形状: {batch['mask'].shape}")
        
        # 检查掩码格式
        mask = batch['mask']
        if len(mask.shape) == 4 and mask.shape[1] == 3:
            print("  ✓ 掩码为多通道格式")
            
            # 分析每个通道
            for c in range(mask.shape[1]):
                channel_mask = mask[:, c, :, :]
                num_positive = (channel_mask > 0.5).sum().item()
                ratio = num_positive / (mask.shape[0] * mask.shape[2] * mask.shape[3])
                class_name = ['背景', '主轨道', '分叉轨道'][c]
                print(f"    {class_name}: {num_positive} 像素 ({ratio*100:.2f}%)")
            
            # 检查重叠
            overlap = torch.logical_and(mask[:, 1, :, :] > 0.5, mask[:, 2, :, :] > 0.5)
            overlap_pixels = overlap.sum().item()
            if overlap_pixels > 0:
                print(f"  ✓ 发现重叠区域: {overlap_pixels} 像素")
            else:
                print("  ⚠ 未发现重叠区域")
        else:
            print("  ✗ 掩码格式不正确")
    
    print("\n验证完成")


if __name__ == '__main__':
    # 测试数据加载器
    import yaml
    
    config_path = Path(__file__).parent.parent.parent / 'configs' / 'railway_track_config.yaml'
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器
    train_loader = create_dataloader(config, split='train')
    
    # 验证格式
    verify_multilabel_format(train_loader)