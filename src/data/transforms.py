"""
数据增强模块
定义训练、验证和测试的数据变换
"""

from typing import Optional, Dict, Any
import numpy as np
import torch
import albumentations as A
import albumentations.pytorch
import cv2
from ..core.registry import register_transform


def get_normalization() -> A.Normalize:
    """获取标准化变换"""
    return A.Normalize(
        mean=(0.485, 0.456, 0.406),
        std=(0.229, 0.224, 0.225),
        max_pixel_value=1.0
    )


@register_transform('train_transforms')
def get_train_transforms(
    image_size: Dict[str, int],
    augmentation_config: Optional[Dict[str, Any]] = None
) -> A.Compose:
    """
    获取训练数据增强
    
    Args:
        image_size: 图像尺寸字典 {'width': width, 'height': height}
        augmentation_config: 增强配置
        
    Returns:
        Albumentations组合变换
    """
    width = image_size['width']
    height = image_size['height']
    
    # 默认增强配置
    if augmentation_config is None:
        augmentation_config = {
            'horizontal_flip': {'p': 0.5},
            'perspective': {'scale': (0.05, 0.1), 'p': 0.25},
            'coarse_dropout': {
                'max_holes': 12,
                'max_height': 256,
                'max_width': 256,
                'min_holes': 6,
                'min_height': 128,
                'min_width': 128,
                'p': 0.5
            },
            'brightness_contrast': {
                'brightness_limit': 0.1,
                'contrast_limit': 0.1,
                'p': 0.5
            },
            'multiplicative_noise': {
                'multiplier': (0.5, 1.5),
                'per_channel': True,
                'p': 0.25
            }
        }
    
    transforms_list = []
    
    # 几何变换
    if 'horizontal_flip' in augmentation_config:
        transforms_list.append(
            A.HorizontalFlip(**augmentation_config['horizontal_flip'])
        )
    
    if 'vertical_flip' in augmentation_config:
        transforms_list.append(
            A.VerticalFlip(**augmentation_config['vertical_flip'])
        )
    
    if 'perspective' in augmentation_config:
        transforms_list.append(
            A.Perspective(**augmentation_config['perspective'])
        )
    
    if 'rotate' in augmentation_config:
        transforms_list.append(
            A.Rotate(**augmentation_config['rotate'])
        )
    
    # 颜色变换
    if 'brightness_contrast' in augmentation_config:
        transforms_list.append(
            A.RandomBrightnessContrast(**augmentation_config['brightness_contrast'])
        )
    
    if 'hue_saturation' in augmentation_config:
        transforms_list.append(
            A.HueSaturationValue(**augmentation_config['hue_saturation'])
        )
    
    # 噪声和模糊
    if 'multiplicative_noise' in augmentation_config:
        transforms_list.append(
            A.MultiplicativeNoise(**augmentation_config['multiplicative_noise'])
        )
    
    if 'gaussian_blur' in augmentation_config:
        transforms_list.append(
            A.GaussianBlur(**augmentation_config['gaussian_blur'])
        )
    
    # Dropout
    if 'coarse_dropout' in augmentation_config:
        transforms_list.append(
            A.CoarseDropout(
                fill_value=0,
                mask_fill_value=0,
                **augmentation_config['coarse_dropout']
            )
        )
    
    # 调整大小和标准化
    transforms_list.extend([
        A.LongestMaxSize(max(width, height)),
        A.PadIfNeeded(
            min_height=height,
            min_width=width,
            border_mode=cv2.BORDER_CONSTANT,
            value=0,
            mask_value=0
        ),
        get_normalization(),
        A.pytorch.ToTensorV2()
    ])
    
    return A.Compose(transforms_list)


@register_transform('val_transforms')
def get_val_transforms(
    image_size: Dict[str, int]
) -> A.Compose:
    """
    获取验证数据变换
    
    Args:
        image_size: 图像尺寸字典
        
    Returns:
        Albumentations组合变换
    """
    width = image_size['width']
    height = image_size['height']
    
    transforms_list = [
        A.LongestMaxSize(max(width, height)),
        A.PadIfNeeded(
            min_height=height,
            min_width=width,
            border_mode=cv2.BORDER_CONSTANT,
            value=0,
            mask_value=0
        ),
        get_normalization(),
        A.pytorch.ToTensorV2()
    ]
    
    return A.Compose(transforms_list)


@register_transform('test_transforms')
def get_test_transforms(
    image_size: Dict[str, int]
) -> A.Compose:
    """
    获取测试数据变换
    
    Args:
        image_size: 图像尺寸字典
        
    Returns:
        Albumentations组合变换
    """
    # 测试时使用与验证相同的变换
    return get_val_transforms(image_size)


@register_transform('tta_transforms')
def get_tta_transforms(
    image_size: Dict[str, int],
    tta_config: Optional[Dict[str, Any]] = None
) -> list:
    """
    获取测试时增强(TTA)变换列表
    
    Args:
        image_size: 图像尺寸字典
        tta_config: TTA配置
        
    Returns:
        变换列表
    """
    if tta_config is None:
        tta_config = {
            'horizontal_flip': True,
            'vertical_flip': True,
            'rotate90': False
        }
    
    width = image_size['width']
    height = image_size['height']
    
    # 基础变换
    base_transform = A.Compose([
        A.LongestMaxSize(max(width, height)),
        A.PadIfNeeded(
            min_height=height,
            min_width=width,
            border_mode=cv2.BORDER_CONSTANT,
            value=0,
            mask_value=0
        ),
        get_normalization(),
        A.pytorch.ToTensorV2()
    ])
    
    transforms_list = [base_transform]  # 原始图像
    
    # 水平翻转
    if tta_config.get('horizontal_flip', False):
        transforms_list.append(
            A.Compose([
                A.HorizontalFlip(p=1.0),
                A.LongestMaxSize(max(width, height)),
                A.PadIfNeeded(
                    min_height=height,
                    min_width=width,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    mask_value=0
                ),
                get_normalization(),
                A.pytorch.ToTensorV2()
            ])
        )
    
    # 垂直翻转
    if tta_config.get('vertical_flip', False):
        transforms_list.append(
            A.Compose([
                A.VerticalFlip(p=1.0),
                A.LongestMaxSize(max(width, height)),
                A.PadIfNeeded(
                    min_height=height,
                    min_width=width,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    mask_value=0
                ),
                get_normalization(),
                A.pytorch.ToTensorV2()
            ])
        )
    
    # 90度旋转
    if tta_config.get('rotate90', False):
        for angle in [90, 180, 270]:
            transforms_list.append(
                A.Compose([
                    A.Rotate(limit=(angle, angle), p=1.0),
                    A.LongestMaxSize(max(width, height)),
                    A.PadIfNeeded(
                        min_height=height,
                        min_width=width,
                        border_mode=cv2.BORDER_CONSTANT,
                        value=0,
                        mask_value=0
                    ),
                    get_normalization(),
                    A.pytorch.ToTensorV2()
                ])
            )
    
    return transforms_list


def inverse_transform(image: torch.Tensor) -> np.ndarray:
    """
    反转标准化变换
    
    Args:
        image: 标准化后的图像张量 (C, H, W)
        
    Returns:
        原始图像数组 (H, W, C)
    """
    # 反标准化
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    
    image = image * std + mean
    image = torch.clamp(image, 0, 1)
    
    # 转换为numpy数组
    image = image.permute(1, 2, 0).cpu().numpy()
    image = (image * 255).astype(np.uint8)
    
    return image


# === 新增: 通用包装函数，供脚本快速调用 ===
def get_transforms(image_size, is_train: bool = True, augmentation_config: Optional[Dict[str, Any]] = None):
    """根据 is_train 标志返回训练或验证变换

    兼容 `scripts/ensemble_training.py` 中的调用：
    ```python
    train_transforms = get_transforms(image_size=(h, w), is_train=True)
    ```
    
    Args:
        image_size: 可以是 (H, W) 元组，也可以是包含 'height' / 'width' 的字典
        is_train: True 返回训练增强，False 返回验证增强
        augmentation_config: 训练增强自定义配置（可选）
    """

    # 将 image_size 统一转换为字典格式
    if isinstance(image_size, (list, tuple)) and len(image_size) == 2:
        height, width = image_size
        image_size = {'height': int(height), 'width': int(width)}
    elif isinstance(image_size, dict):
        # 确保键为 height / width
        if 'height' not in image_size or 'width' not in image_size:
            raise ValueError("image_size 字典需要包含 'height' 和 'width' 键")
    else:
        raise TypeError("image_size 应为 (H, W) 元组或包含 'height'/'width' 的字典")

    if is_train:
        return get_train_transforms(image_size, augmentation_config)
    else:
        return get_val_transforms(image_size) 