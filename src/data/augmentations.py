"""
数据增强模块
支持多标签分割的数据增强
"""

import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
from typing import Dict, Any, List, Optional


def get_train_transform(config: Dict[str, Any]) -> <PERSON><PERSON>Compose:
    """
    获取训练时的数据增强
    
    Args:
        config: 配置字典
        
    Returns:
        数据增强组合
    """
    image_size = config.get('data', {}).get('image_size', {})
    height = image_size.get('height', 544)
    width = image_size.get('width', 960)
    
    augmentations = config.get('augmentation', {}).get('train', [])
    
    # 使用与notebook相同的resize策略：保持长宽比+padding
    transforms_list = [
        A.LongestMaxSize(max_size=width, interpolation=cv2.INTER_LINEAR),
        A.PadIfNeeded(min_height=height, min_width=width, 
                     border_mode=cv2.BORDER_CONSTANT)
    ]
    
    # 添加配置的增强
    for aug_config in augmentations:
        # 创建配置的副本，避免修改原始配置
        aug_config_copy = aug_config.copy()
        aug_type = aug_config_copy.pop('type')
        p = aug_config_copy.pop('p', 0.5)
        
        if aug_type == 'horizontal_flip':
            transforms_list.append(A.HorizontalFlip(p=p))
        elif aug_type == 'vertical_flip':
            transforms_list.append(A.VerticalFlip(p=p))
        elif aug_type == 'rotate':
            limit = aug_config_copy.get('limit', 15)
            transforms_list.append(A.Rotate(limit=limit, p=p))
        elif aug_type == 'perspective':
            scale = aug_config_copy.get('scale', (0.05, 0.1))
            transforms_list.append(A.Perspective(scale=scale, p=p))
        elif aug_type == 'brightness_contrast':
            brightness_limit = aug_config_copy.get('brightness_limit', 0.2)
            contrast_limit = aug_config_copy.get('contrast_limit', 0.2)
            transforms_list.append(
                A.RandomBrightnessContrast(
                    brightness_limit=brightness_limit,
                    contrast_limit=contrast_limit,
                    p=p
                )
            )
        elif aug_type == 'motion_blur':
            blur_limit = aug_config_copy.get('blur_limit', 7)
            transforms_list.append(A.MotionBlur(blur_limit=blur_limit, p=p))
        elif aug_type == 'coarse_dropout':
            max_holes = aug_config_copy.get('max_holes', 8)
            max_height = aug_config_copy.get('max_height', 128)
            max_width = aug_config_copy.get('max_width', 128)
            transforms_list.append(
                A.CoarseDropout(
                    max_holes=max_holes,
                    max_height=max_height,
                    max_width=max_width,
                    p=p
                )
            )
    
    # 添加标准化和转换
    # 注意：RailwayDataset已经将图像归一化到[0,1]，所以需要设置max_pixel_value=1.0
    transforms_list.extend([
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], max_pixel_value=1.0),
        ToTensorV2()
    ])
    
    return A.Compose(transforms_list)


def get_val_transform(config: Dict[str, Any]) -> A.Compose:
    """
    获取验证时的数据增强
    
    Args:
        config: 配置字典
        
    Returns:
        数据增强组合
    """
    image_size = config.get('data', {}).get('image_size', {})
    height = image_size.get('height', 544)
    width = image_size.get('width', 960)
    
    # 使用与notebook相同的resize策略：保持长宽比+padding
    transforms_list = [
        A.LongestMaxSize(max_size=width, interpolation=cv2.INTER_LINEAR),
        A.PadIfNeeded(min_height=height, min_width=width, 
                     border_mode=cv2.BORDER_CONSTANT),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], max_pixel_value=1.0),
        ToTensorV2()
    ]
    
    return A.Compose(transforms_list)


def get_test_transforms(config: Dict[str, Any]) -> A.Compose:
    """
    获取测试时的数据增强
    
    Args:
        config: 配置字典
        
    Returns:
        数据增强组合
    """
    return get_val_transforms(config)


def get_tta_transforms(config: Dict[str, Any]) -> List[A.Compose]:
    """
    获取测试时增强(TTA)的变换列表
    
    Args:
        config: 配置字典
        
    Returns:
        TTA变换列表
    """
    image_size = config.get('data', {}).get('image_size', {})
    height = image_size.get('height', 544)
    width = image_size.get('width', 960)
    
    tta_transforms = []
    
    # 原始图像
    tta_transforms.append(get_test_transforms(config))
    
    # 水平翻转
    tta_transforms.append(A.Compose([
        A.LongestMaxSize(max_size=width),
        A.PadIfNeeded(min_height=height, min_width=width, border_mode=cv2.BORDER_CONSTANT),
        A.HorizontalFlip(p=1.0),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], max_pixel_value=1.0),
        ToTensorV2()
    ]))
    
    # 垂直翻转
    tta_transforms.append(A.Compose([
        A.LongestMaxSize(max_size=width),
        A.PadIfNeeded(min_height=height, min_width=width, border_mode=cv2.BORDER_CONSTANT),
        A.VerticalFlip(p=1.0),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], max_pixel_value=1.0),
        ToTensorV2()
    ]))
    
    return tta_transforms