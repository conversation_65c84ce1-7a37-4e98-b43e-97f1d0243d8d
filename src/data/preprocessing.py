"""
数据预处理模块
处理JSON标注文件，将轨道点转换为分割掩码
使用完整多边形算法，严格连接每个关键点形成边界
"""

import json
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import urllib.request
from tqdm import tqdm


class RailwayAnnotationParser:
    """
    铁路轨道标注解析器
    将JSON格式的关键点标注转换为语义分割掩码
    使用完整多边形算法，确保边界严格连接每个关键点
    """
    
    # 轨道类别映射
    TRACK_CLASSES = {
        'Main_Left': 1,     # 主轨道左
        'Main_Right': 1,    # 主轨道右（都标记为主轨道）
        'Fork_Left': 2,     # 分叉轨道左
        'Fork_Right': 2     # 分叉轨道右（都标记为分叉轨道）
    }
    
    # 相机类型
    CAMERA_TYPES = {
        'near': '6mm',      # 近距离相机
        'far': '25mm'       # 远距离相机
    }
    
    def __init__(self, line_thickness: int = 20):
        """
        初始化解析器
        
        Args:
            line_thickness: 废弃参数，保留为了兼容性（现在使用完整多边形算法）
        """
        self.line_thickness = line_thickness  # 保留兼容性，但不再使用
    
    def parse_json_file(self, json_path: Path) -> Dict[str, Any]:
        """
        解析JSON标注文件
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            解析后的标注数据
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取相机类型
        image_url = data['info']
        filename = Path(image_url).name
        camera_type = self.CAMERA_TYPES.get('near' if 'near' in filename else 'far', '6mm')
        
        # 检查并修复主轨道标注错误（两个轨道标记为同一标签）
        labels_by_type = {}
        for label_data in data['labels']:
            label = label_data['label']
            if label not in labels_by_type:
                labels_by_type[label] = []
            labels_by_type[label].append(label_data)

        # 修复重复标签的情况
        fixed_labels = []
        for label, label_list in labels_by_type.items():
            if len(label_list) == 2 and label in ['Main_Left', 'Main_Right']:
                # 两个轨道标记为同一标签，根据X位置重新分配
                label1, label2 = label_list

                # 解析点并计算X中心位置
                points1 = self._parse_points(label1['points'], image_width=1920, image_height=1080)
                points2 = self._parse_points(label2['points'], image_width=1920, image_height=1080)

                x1_center = sum(p[0] for p in points1) / len(points1) if points1 else 0
                x2_center = sum(p[0] for p in points2) / len(points2) if points2 else 0

                # 根据X位置分配左右标签
                if x1_center < x2_center:
                    label1['label'] = 'Main_Left'
                    label2['label'] = 'Main_Right'
                else:
                    label1['label'] = 'Main_Right'
                    label2['label'] = 'Main_Left'

                print(f"修复标注错误：两个{label}轨道重新分配为Main_Left和Main_Right")
                fixed_labels.extend([label1, label2])
            else:
                fixed_labels.extend(label_list)

        # 解析修复后的轨道数据
        tracks = []
        for label_data in fixed_labels:
            label = label_data['label']
            if label in self.TRACK_CLASSES:
                points = self._parse_points(label_data['points'], image_width=1920, image_height=1080)
                if len(points) >= 2:  # 轨道线至少需要2个点
                    tracks.append({
                        'label': label,
                        'class_id': self.TRACK_CLASSES[label],
                        'points': points
                    })
        
        return {
            'filename': filename,
            'camera_type': camera_type,
            'tracks': tracks,
            'image_url': image_url
        }
    
    def _parse_points(self, points_list: List[float], image_width: int = 1920, image_height: int = 1080) -> List[Tuple[float, float]]:
        """
        解析点坐标列表，并进行边界检查

        Args:
            points_list: 扁平化的点坐标列表 [x1, y1, x2, y2, ...]
            image_width: 图像宽度，用于边界检查
            image_height: 图像高度，用于边界检查

        Returns:
            点坐标元组列表（已修正边界）
        """
        points = []
        for i in range(0, len(points_list), 2):
            if i + 1 < len(points_list):
                x, y = float(points_list[i]), float(points_list[i + 1])

                # 边界检查和修正
                x = max(0, min(x, image_width - 1))
                y = max(0, min(y, image_height - 1))

                points.append((x, y))

        return points
    
    def points_to_mask(self, 
                       points: List[Tuple[float, float]], 
                       image_shape: Tuple[int, int],
                       class_id: int) -> np.ndarray:
        """
        将轨道关键点转换为多边形分割掩码
        严格连接每一个关键点形成边界，不使用凸包
        
        Args:
            points: 轨道关键点列表
            image_shape: 图像尺寸 (height, width)
            class_id: 类别ID
            
        Returns:
            掩码图像
        """
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        if len(points) < 3:
            return mask
        
        # 将点转换为整数坐标
        points_int = np.array([(int(x), int(y)) for x, y in points], dtype=np.int32)
        
        try:
            # 直接使用所有关键点形成多边形边界
            cv2.fillPoly(mask, [points_int], class_id)
        except Exception:
            # 备用方案：尝试绘制轮廓
            try:
                cv2.drawContours(mask, [points_int], -1, class_id, -1)
            except Exception:
                pass  # 静默失败，返回空掩码
        
        return mask
    
    def create_track_polygon_from_parallel_lines(self,
                                                left_points: List[Tuple[float, float]],
                                                right_points: List[Tuple[float, float]],
                                                image_width: int = 1920,
                                                image_height: int = 1080) -> List[Tuple[float, float]]:
        """
        从两条平行线创建轨道多边形，支持单边轨道处理
        将左边线和右边线连接形成一个封闭的轨道区域
        当一边轨道缺失时，根据距离使用对应的图像边界补全

        Args:
            left_points: 左边线的关键点
            right_points: 右边线的关键点
            image_width: 图像宽度，用于边界补全
            image_height: 图像高度，用于边界补全

        Returns:
            组成轨道多边形的所有顶点
        """
        # 检查轨道情况
        has_left = len(left_points) >= 2
        has_right = len(right_points) >= 2

        if not has_left and not has_right:
            return []

        # 处理完全单边轨道情况
        if not has_left or not has_right:
            return self._create_single_side_track_polygon(
                left_points if has_left else right_points,
                is_left_side=has_left,
                image_width=image_width,
                image_height=image_height
            )

        # 双边轨道情况：检查是否有部分截断
        return self._create_dual_side_track_polygon(
            left_points, right_points, image_width, image_height
        )
    
    def _adjust_points_by_top_to_bottom(self, points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """
        调整轨道点顺序，确保从上到下（Y坐标从小到大）

        Args:
            points: 轨道线的关键点列表

        Returns:
            调整后的点列表（从上到下）
        """
        if len(points) < 2:
            return points

        # 简单策略：检查第一个点和最后一个点的Y坐标
        # 如果第一个点的Y坐标大于最后一个点，说明是从下到上，需要反转
        first_y = points[0][1]
        last_y = points[-1][1]

        if first_y > last_y:
            # 从下到上，需要反转为从上到下
            return list(reversed(points))
        else:
            # 已经是从上到下，保持原顺序
            return points
    
    def polygon_to_mask(self, points: List[Tuple[float, float]], image_shape: Tuple[int, int]) -> np.ndarray:
        """
        将多边形点转换为二值掩码
        
        Args:
            points: 多边形顶点列表
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            二值掩码
        """
        mask = np.zeros(image_shape, dtype=np.float32)
        
        if len(points) < 3:
            return mask
        
        # 将点转换为整数坐标
        points_int = np.array([(int(x), int(y)) for x, y in points], dtype=np.int32)
        
        try:
            # 填充多边形
            cv2.fillPoly(mask, [points_int], 1.0)
        except Exception:
            pass
        
        return mask

    def _create_single_side_track_polygon(self,
                                         track_points: List[Tuple[float, float]],
                                         is_left_side: bool,
                                         image_width: int = 1920,
                                         image_height: int = 1080) -> List[Tuple[float, float]]:
        """
        为单边轨道创建多边形，根据轨道位置使用对应的图像边界
        底部的点和底部连在一起，上面的点和上面的连在一起，不要交叉

        Args:
            track_points: 单边轨道的关键点
            is_left_side: True表示是左边轨道，False表示是右边轨道
            image_width: 图像宽度
            image_height: 图像高度

        Returns:
            组成轨道多边形的所有顶点
        """
        if len(track_points) < 2:
            return []

        # 调整点的顺序（从上到下）
        adjusted_points = self._adjust_points_by_top_to_bottom(track_points)

        # 分析轨道点的范围
        x_coords = [p[0] for p in adjusted_points]
        y_coords = [p[1] for p in adjusted_points]

        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)

        # 判断轨道更靠近哪边
        center_x = (min_x + max_x) / 2
        closer_to_left = center_x < image_width / 2

        polygon_points = []

        if closer_to_left:
            # 轨道靠近左边，使用左边界补全
            # 正确的连接顺序：左边界上端 -> 轨道线 -> 左边界下端 -> 闭合
            polygon_points = [
                (0, min_y),  # 左边界上端
            ]
            # 添加轨道点（从上到下）
            polygon_points.extend(adjusted_points)
            # 连接到左边界下端
            polygon_points.append((0, max_y))  # 左边界下端
        else:
            # 轨道靠近右边，使用右边界补全
            # 正确的连接顺序：轨道线 -> 右边界下端 -> 右边界上端 -> 闭合
            polygon_points.extend(adjusted_points)  # 轨道点（从上到下）
            # 连接到右边界
            polygon_points.extend([
                (image_width - 1, max_y),  # 右边界下端
                (image_width - 1, min_y),  # 右边界上端
            ])

        return polygon_points

    def _create_dual_side_track_polygon(self,
                                       left_points: List[Tuple[float, float]],
                                       right_points: List[Tuple[float, float]],
                                       image_width: int = 1920,
                                       image_height: int = 1080) -> List[Tuple[float, float]]:
        """
        为双边轨道创建多边形，不使用边界补全
        只使用原始轨道点，过滤掉边界点

        Args:
            left_points: 左边轨道的关键点
            right_points: 右边轨道的关键点
            image_width: 图像宽度
            image_height: 图像高度

        Returns:
            组成轨道多边形的所有顶点
        """
        # 检测部分截断情况：只有少量点接触边界，且不是自然延伸
        def detect_partial_truncation(points, is_left_track=True):
            """检测部分截断情况并返回处理策略"""
            if not points:
                return "none", []

            left_boundary_points = [(x, y) for x, y in points if x <= 5]
            right_boundary_points = [(x, y) for x, y in points if x >= image_width - 5]
            bottom_boundary_points = [(x, y) for x, y in points if y >= image_height - 5]

            # 检查左边界部分截断
            if left_boundary_points:
                boundary_ratio = len(left_boundary_points) / len(points)
                if boundary_ratio < 0.3:  # 少量点接触左边界
                    # 对于左边界截断，检查是否是真正的截断
                    # 不仅检查底部，也检查轨道的整体特征

                    # 检查是否是自然延伸：左轨道的最上点比最下点更靠近左边界
                    if is_left_track:
                        y_coords = [p[1] for p in points]
                        min_y_idx = y_coords.index(min(y_coords))  # 最上点
                        max_y_idx = y_coords.index(max(y_coords))  # 最下点

                        top_point_x = points[min_y_idx][0]
                        bottom_point_x = points[max_y_idx][0]

                        # 如果最上点更靠近左边界，说明是自然延伸，不需要补全
                        if top_point_x <= bottom_point_x:
                            print(f"左轨道自然延伸到左边界，不需要补全")
                            return "none", []

                        # 如果最下点更靠近左边界，说明是截断
                        print(f"检测到左侧截断，边界点{len(left_boundary_points)}个")
                        return "left_bottom_partial", left_boundary_points
                    else:
                        # 对于右轨道接触左边界，通常是截断
                        print(f"检测到左侧截断，边界点{len(left_boundary_points)}个")
                        return "left_bottom_partial", left_boundary_points

            # 检查右边界部分截断
            if right_boundary_points:
                boundary_ratio = len(right_boundary_points) / len(points)
                if boundary_ratio < 0.3:  # 少量点接触右边界
                    # 检查是否是底部截断
                    bottom_right_points = [(x, y) for x, y in right_boundary_points if y > image_height * 0.7]
                    if bottom_right_points:
                        # 检查是否是自然延伸：右轨道的最上点比最下点更靠近右边界
                        if not is_left_track:
                            y_coords = [p[1] for p in points]
                            min_y_idx = y_coords.index(min(y_coords))  # 最上点
                            max_y_idx = y_coords.index(max(y_coords))  # 最下点

                            top_point_x = points[min_y_idx][0]
                            bottom_point_x = points[max_y_idx][0]

                            # 如果最上点更靠近右边界，说明是自然延伸，不需要补全
                            if top_point_x >= bottom_point_x:
                                print(f"右轨道自然延伸到右边界，不需要补全")
                                return "none", []

                        print(f"检测到右下角部分截断，边界点{len(right_boundary_points)}个")
                        return "right_bottom_partial", right_boundary_points

            return "none", []

        def should_use_track_enclosure(left_points, right_points, img_width, img_height):
            """判断是否应该使用轨道包围而非截断补全"""

            # 计算轨道的整体位置和特征
            left_x = [p[0] for p in left_points]
            left_y = [p[1] for p in left_points]
            right_x = [p[0] for p in right_points]
            right_y = [p[1] for p in right_points]

            # 计算轨道中心位置
            left_center_x = (min(left_x) + max(left_x)) / 2
            right_center_x = (min(right_x) + max(right_x)) / 2
            overall_center_x = (left_center_x + right_center_x) / 2

            # 检查边界接触情况
            left_touches_left = min(left_x) <= 5
            left_touches_right = max(left_x) >= img_width - 5
            right_touches_left = min(right_x) <= 5
            right_touches_right = max(right_x) >= img_width - 5

            # 计算轨道跨度
            total_span = max(max(left_x), max(right_x)) - min(min(left_x), min(right_x))
            span_ratio = total_span / img_width

            # 首先检查是否是真正的部分截断（优先级最高）
            if is_true_partial_truncation_case(left_points, right_points, img_width, img_height):
                print(f"检测到真正的部分截断，不使用包围")
                return False

            # 规则1: 轨道在左侧且接触左边界，但跨度大 → 使用包围
            if overall_center_x < img_width / 3 and (left_touches_left or right_touches_left):
                if span_ratio > 0.3:  # 跨度超过30%才使用包围
                    print(f"轨道位于左侧且接触左边界，跨度大({span_ratio:.1%})，使用包围")
                    return True

            # 规则2: 轨道在右侧且接触右边界，但跨度大 → 使用包围
            if overall_center_x > img_width * 2 / 3 and (left_touches_right or right_touches_right):
                if span_ratio > 0.3:  # 跨度超过30%才使用包围
                    print(f"轨道位于右侧且接触右边界，跨度大({span_ratio:.1%})，使用包围")
                    return True

            # 规则3: 轨道在中央 → 使用包围
            if img_width / 3 <= overall_center_x <= img_width * 2 / 3:
                print(f"轨道位于中央，使用包围")
                return True

            # 规则4: 轨道跨度很大 → 使用包围
            if span_ratio > 0.5:  # 轨道跨度超过图像宽度的50%
                print(f"轨道跨度大({span_ratio:.1%})，使用包围")
                return True

            return False

        def is_true_partial_truncation_case(left_points, right_points, img_width, img_height):
            """检测是否是真正的部分截断情况"""

            left_x = [p[0] for p in left_points]
            left_y = [p[1] for p in left_points]
            right_x = [p[0] for p in right_points]
            right_y = [p[1] for p in right_points]

            # 检查左边界截断
            left_boundary_points = [(x, y) for x, y in left_points + right_points if x <= 10]
            if left_boundary_points:
                # 计算边界点的Y范围占比
                boundary_y_range = max(p[1] for p in left_boundary_points) - min(p[1] for p in left_boundary_points)
                total_y_range = max(max(left_y), max(right_y)) - min(min(left_y), min(right_y))

                if total_y_range > 0:
                    boundary_y_ratio = boundary_y_range / total_y_range

                    # 如果边界点Y范围很小，说明是部分截断
                    if boundary_y_ratio < 0.2:  # 边界点Y范围小于总范围的20%
                        print(f"左边界部分截断：边界Y范围占比{boundary_y_ratio:.1%}")
                        return True

            # 检查右边界截断
            right_boundary_points = [(x, y) for x, y in left_points + right_points if x >= img_width - 10]
            if right_boundary_points:
                # 计算边界点的Y范围占比
                boundary_y_range = max(p[1] for p in right_boundary_points) - min(p[1] for p in right_boundary_points)
                total_y_range = max(max(left_y), max(right_y)) - min(min(left_y), min(right_y))

                if total_y_range > 0:
                    boundary_y_ratio = boundary_y_range / total_y_range

                    # 如果边界点Y范围很小，说明是部分截断
                    if boundary_y_ratio < 0.2:  # 边界点Y范围小于总范围的20%
                        print(f"右边界部分截断：边界Y范围占比{boundary_y_ratio:.1%}")
                        return True

            return False

        def add_partial_truncation_completion(points, truncation_type, boundary_points):
            """为部分截断添加边界补全，返回原始点和补全点"""
            if truncation_type == "none":
                return points, []

            completion_points = []

            if truncation_type == "left_bottom_partial":
                # 左侧截断：直接连接到左下角，然后连接到右轨道
                completion_points = [
                    (0, image_height - 1),  # 左下角
                ]
                print(f"添加左下角补全：连接到左下角")

            elif truncation_type == "right_bottom_partial":
                # 右侧截断：直接连接到右下角，然后连接到左轨道
                completion_points = [
                    (image_width - 1, image_height - 1),  # 右下角
                ]
                print(f"添加右下角补全：连接到右下角")

            return points, completion_points

        # 智能截断检测：考虑整体轨道情况
        left_truncation_type, left_boundary_points = detect_partial_truncation(left_points, is_left_track=True)
        right_truncation_type, right_boundary_points = detect_partial_truncation(right_points, is_left_track=False)

        # 智能判断：当两条轨道都自然延伸到边界时，不需要截断，直接包围
        should_use_enclosure = should_use_track_enclosure(left_points, right_points, image_width, image_height)

        if should_use_enclosure:
            print("检测到轨道自然延伸，使用轨道包围而非截断补全")
            left_truncation_type, left_boundary_points = "none", []
            right_truncation_type, right_boundary_points = "none", []

        # 智能处理双边界截断：当两条轨道都接触同一边界时，共享补全点
        left_original_points, left_completion_points = add_partial_truncation_completion(left_points, left_truncation_type, left_boundary_points)
        right_original_points, right_completion_points = add_partial_truncation_completion(right_points, right_truncation_type, right_boundary_points)

        # 检查是否存在双边界截断冲突
        if (left_truncation_type == "right_bottom_partial" and right_truncation_type == "right_bottom_partial"):
            print("检测到双轨道右下角截断，使用共享补全点")
            # 使用共享的右下角补全点，避免重复
            shared_completion = [(image_width - 1, image_height - 1)]  # 只需要右下角点
            left_completion_points = []
            right_completion_points = shared_completion
        elif (left_truncation_type == "left_bottom_partial" and right_truncation_type == "left_bottom_partial"):
            print("检测到双轨道左下角截断，使用共享补全点")
            # 使用共享的左下角补全点，避免重复
            shared_completion = [(0, image_height - 1)]  # 只需要左下角点
            left_completion_points = shared_completion
            right_completion_points = []

        # 调整点的顺序（从上到下）
        adjusted_left_points = self._adjust_points_by_top_to_bottom(left_original_points)
        adjusted_right_points = self._adjust_points_by_top_to_bottom(right_original_points)

        # 创建正确的多边形连接顺序
        polygon_points = []

        # 根据截断类型确定正确的连接顺序
        if left_truncation_type == "left_bottom_partial":
            # 左侧截断：左上 → 左下 → 左下角 → 右下 → 右上 → 回到左上
            print(f"左侧截断连接：左轨道({len(adjusted_left_points)}) → 左下角 → 右轨道({len(adjusted_right_points)})")

            # 1. 添加左轨道点（从上到下）
            polygon_points.extend(adjusted_left_points)

            # 2. 添加左下角补全点
            if left_completion_points:
                polygon_points.extend(left_completion_points)

            # 3. 添加右轨道点（从下到上）
            polygon_points.extend(list(reversed(adjusted_right_points)))

        elif right_truncation_type == "right_bottom_partial":
            # 右侧截断：左上 → 左下 → 右下 → 右下角 → 右上 → 回到左上
            print(f"右侧截断连接：左轨道({len(adjusted_left_points)}) → 右轨道 → 右下角")

            # 1. 添加左轨道点（从上到下）
            polygon_points.extend(adjusted_left_points)

            # 2. 添加右轨道点（从下到上）
            polygon_points.extend(list(reversed(adjusted_right_points)))

            # 3. 添加右下角补全点
            if right_completion_points:
                polygon_points.extend(right_completion_points)

        else:
            # 无截断或双边界截断：标准连接
            print(f"标准连接：左轨道({len(adjusted_left_points)}) → 补全 → 右轨道({len(adjusted_right_points)})")

            # 1. 添加左轨道点（从上到下）
            polygon_points.extend(adjusted_left_points)

            # 2. 添加左侧补全点（如果有）
            if left_completion_points:
                polygon_points.extend(left_completion_points)

            # 3. 添加右侧补全点（如果有，需要反转顺序以正确连接）
            if right_completion_points:
                polygon_points.extend(list(reversed(right_completion_points)))

            # 4. 添加右轨道点（从下到上）
            polygon_points.extend(list(reversed(adjusted_right_points)))

        return polygon_points

    def _fix_duplicate_fork_labels(self, fork_left_tracks: List[List[Tuple[float, float]]],
                                  fork_right_tracks: List[List[Tuple[float, float]]]) -> Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]:
        """
        修复重复的分叉轨道标签问题

        Args:
            fork_left_tracks: 所有标记为Fork_Left的轨道列表
            fork_right_tracks: 所有标记为Fork_Right的轨道列表

        Returns:
            (fork_left_points, fork_right_points): 修正后的左右分叉轨道点
        """
        # 合并所有分叉轨道
        all_fork_tracks = fork_left_tracks + fork_right_tracks

        if len(all_fork_tracks) == 0:
            return [], []
        elif len(all_fork_tracks) == 1:
            # 只有一条分叉轨道，直接忽略分叉轨道
            print(f"警告: 只有一条分叉轨道，忽略分叉轨道，只使用主轨道")
            return [], []
        elif len(all_fork_tracks) == 2:
            # 正常情况：两条分叉轨道
            track1, track2 = all_fork_tracks

            # 根据X坐标中心位置判断左右
            def get_center_x(points):
                if not points:
                    return 0
                x_coords = [p[0] for p in points]
                return (min(x_coords) + max(x_coords)) / 2

            center1 = get_center_x(track1)
            center2 = get_center_x(track2)

            if center1 < center2:
                return track1, track2  # track1在左，track2在右
            else:
                return track2, track1  # track2在左，track1在右
        else:
            # 多于两条分叉轨道，选择最合适的两条
            print(f"警告: 发现{len(all_fork_tracks)}条分叉轨道，只使用前两条")

            # 按X坐标中心排序
            def get_center_x(points):
                if not points:
                    return 0
                x_coords = [p[0] for p in points]
                return (min(x_coords) + max(x_coords)) / 2

            sorted_tracks = sorted(all_fork_tracks, key=get_center_x)
            return sorted_tracks[0], sorted_tracks[1]

    def _can_pair_tracks(self, left_track: List[Tuple[float, float]],
                        right_track: List[Tuple[float, float]]) -> bool:
        """
        判断两条轨道是否可以配对

        Args:
            left_track: 左边轨道点
            right_track: 右边轨道点

        Returns:
            是否可以配对
        """
        if not left_track or not right_track:
            return False

        # 获取Y坐标范围
        left_y = [p[1] for p in left_track]
        right_y = [p[1] for p in right_track]

        left_y_min, left_y_max = min(left_y), max(left_y)
        right_y_min, right_y_max = min(right_y), max(right_y)

        # 计算Y方向重叠
        y_overlap_start = max(left_y_min, right_y_min)
        y_overlap_end = min(left_y_max, right_y_max)
        y_overlap = max(0, y_overlap_end - y_overlap_start)

        # 如果Y方向重叠超过50像素，认为可以配对
        return y_overlap > 50

    def create_segmentation_mask(self,
                                 annotation_data: Dict[str, Any],
                                 image_shape: Tuple[int, int]) -> np.ndarray:
        """
        创建完整的语义分割掩码
        将同一条轨道的左右两条线连接形成轨道区域
        
        Args:
            annotation_data: 解析后的标注数据
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            单通道语义分割掩码 (0=背景, 1=主轨道, 2=分叉轨道)
        """
        # 创建单通道掩码
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        # 分别收集左右轨道线
        main_left = []
        main_right = []
        fork_left = []
        fork_right = []
        
        # 收集分叉轨道（处理重复标签）
        fork_left_tracks = []
        fork_right_tracks = []

        for track in annotation_data['tracks']:
            if track['label'] == 'Main_Left':
                main_left = track['points']
            elif track['label'] == 'Main_Right':
                main_right = track['points']
            elif track['label'] == 'Fork_Left':
                fork_left_tracks.append(track['points'])
            elif track['label'] == 'Fork_Right':
                fork_right_tracks.append(track['points'])

        # 修复分叉轨道的重复标签问题
        fork_left, fork_right = self._fix_duplicate_fork_labels(fork_left_tracks, fork_right_tracks)
        
        # 生成主轨道多边形（标签=1）
        if main_left and main_right:
            # 从左右两条线创建轨道多边形
            track_polygon = self.create_track_polygon_from_parallel_lines(main_left, main_right)
            if len(track_polygon) >= 3:
                track_mask = self.points_to_mask(track_polygon, image_shape, 1)
                mask[track_mask > 0] = 1
        
        # 生成分叉轨道多边形（标签=2）
        if fork_left and fork_right:
            # 从左右两条线创建轨道多边形
            track_polygon = self.create_track_polygon_from_parallel_lines(fork_left, fork_right)
            if len(track_polygon) >= 3:
                track_mask = self.points_to_mask(track_polygon, image_shape, 2)
                mask[track_mask > 0] = 2  # 分叉轨道会覆盖主轨道重叠区域
        
        return mask
    
    def create_multilabel_segmentation_mask(self, annotation_data: Dict[str, Any], image_shape: Tuple[int, int]) -> np.ndarray:
        """
        创建多标签分割掩码 - 支持轨道重叠区域
        
        Args:
            annotation_data: 已解析的标注数据 (包含tracks字段)
            image_shape: 图像尺寸 (H, W)
            
        Returns:
            多标签掩码 (H, W, 3) - 每个通道是独立的二值掩码
        """
        height, width = image_shape
        
        # 创建多通道掩码
        multilabel_mask = np.zeros((height, width, 3), dtype=np.float32)
        
        # 使用已解析的tracks数据
        if 'tracks' in annotation_data:
            main_left = []
            main_right = []
            fork_left = []
            fork_right = []

            # 检查并修复主轨道标注错误（两个轨道标记为同一标签）
            tracks_by_label = {}
            for track in annotation_data['tracks']:
                label = track['label']
                if label not in tracks_by_label:
                    tracks_by_label[label] = []
                tracks_by_label[label].append(track)

            # 修复重复标签的情况
            fixed_tracks = []
            for label, tracks in tracks_by_label.items():
                if len(tracks) == 2 and label in ['Main_Left', 'Main_Right']:
                    # 两个轨道标记为同一标签，根据X位置重新分配
                    track1, track2 = tracks

                    # 计算X中心位置
                    points1 = track1['points']
                    points2 = track2['points']
                    x1_center = sum(points1[i] for i in range(0, len(points1), 2)) / (len(points1) // 2)
                    x2_center = sum(points2[i] for i in range(0, len(points2), 2)) / (len(points2) // 2)

                    # 根据X位置分配左右标签
                    if x1_center < x2_center:
                        track1['label'] = 'Main_Left'
                        track2['label'] = 'Main_Right'
                    else:
                        track1['label'] = 'Main_Right'
                        track2['label'] = 'Main_Left'

                    print(f"修复标注错误：两个{label}轨道重新分配为Main_Left和Main_Right")
                    fixed_tracks.extend([track1, track2])
                else:
                    fixed_tracks.extend(tracks)

            # 从修复后的tracks中收集轨道线数据
            fork_left_tracks = []
            fork_right_tracks = []

            for track in fixed_tracks:
                label = track['label']
                points = track['points']

                if label == 'Main_Left':
                    main_left.extend(points)  # 合并而不是覆盖
                elif label == 'Main_Right':
                    main_right.extend(points)  # 合并而不是覆盖
                elif label == 'Fork_Left':
                    fork_left_tracks.append(points)  # 收集所有Fork_Left轨道
                elif label == 'Fork_Right':
                    fork_right_tracks.append(points)  # 收集所有Fork_Right轨道

            # 处理分叉轨道的重复标签问题
            fork_left, fork_right = self._fix_duplicate_fork_labels(fork_left_tracks, fork_right_tracks)
            
            # 生成主轨道掩码（通道1）
            if main_left or main_right:  # 支持单边轨道
                track_polygon = self.create_track_polygon_from_parallel_lines(
                    main_left, main_right, image_shape[1], image_shape[0])
                if len(track_polygon) >= 3:
                    track_mask = self.points_to_mask(track_polygon, image_shape, 1)
                    multilabel_mask[:, :, 1] = (track_mask > 0).astype(np.float32)

            # 生成分叉轨道掩码（通道2）
            if fork_left or fork_right:  # 支持单边轨道
                track_polygon = self.create_track_polygon_from_parallel_lines(
                    fork_left, fork_right, image_shape[1], image_shape[0])
                if len(track_polygon) >= 3:
                    track_mask = self.points_to_mask(track_polygon, image_shape, 1)
                    multilabel_mask[:, :, 2] = (track_mask > 0).astype(np.float32)
        
        # 如果没有tracks，尝试处理原始JSON格式
        elif 'labels' in annotation_data:
            main_left = []
            main_right = []
            fork_left = []
            fork_right = []

            # 检查并修复主轨道标注错误（两个轨道标记为同一标签）
            labels_by_type = {}
            for label_data in annotation_data['labels']:
                label = label_data['label']
                if label not in labels_by_type:
                    labels_by_type[label] = []
                labels_by_type[label].append(label_data)

            # 修复重复标签的情况
            fixed_labels = []
            for label, label_list in labels_by_type.items():
                if len(label_list) == 2 and label in ['Main_Left', 'Main_Right']:
                    # 两个轨道标记为同一标签，根据X位置重新分配
                    label1, label2 = label_list

                    # 解析点并计算X中心位置
                    points1 = self._parse_points(label1['points'], image_width=image_shape[1], image_height=image_shape[0])
                    points2 = self._parse_points(label2['points'], image_width=image_shape[1], image_height=image_shape[0])

                    x1_center = sum(p[0] for p in points1) / len(points1) if points1 else 0
                    x2_center = sum(p[0] for p in points2) / len(points2) if points2 else 0

                    # 根据X位置分配左右标签
                    if x1_center < x2_center:
                        label1['label'] = 'Main_Left'
                        label2['label'] = 'Main_Right'
                    else:
                        label1['label'] = 'Main_Right'
                        label2['label'] = 'Main_Left'

                    print(f"修复标注错误：两个{label}轨道重新分配为Main_Left和Main_Right")
                    fixed_labels.extend([label1, label2])
                else:
                    fixed_labels.extend(label_list)

            # 使用修复后的标签数据
            for label_data in fixed_labels:
                label = label_data['label']
                if label in self.TRACK_CLASSES and 'points' in label_data:
                    points = self._parse_points(label_data['points'], image_width=image_shape[1], image_height=image_shape[0])

                    if label == 'Main_Left':
                        main_left.extend(points)  # 合并而不是覆盖
                    elif label == 'Main_Right':
                        main_right.extend(points)  # 合并而不是覆盖
                    elif label == 'Fork_Left':
                        fork_left.extend(points)  # 合并而不是覆盖
                    elif label == 'Fork_Right':
                        fork_right.extend(points)  # 合并而不是覆盖
            
            # 生成主轨道掩码（通道1）
            if main_left or main_right:  # 支持单边轨道
                track_polygon = self.create_track_polygon_from_parallel_lines(
                    main_left, main_right, image_shape[1], image_shape[0])
                if len(track_polygon) >= 3:
                    track_mask = self.points_to_mask(track_polygon, image_shape, 1)
                    multilabel_mask[:, :, 1] = (track_mask > 0).astype(np.float32)

            # 生成分叉轨道掩码（通道2）
            if fork_left or fork_right:  # 支持单边轨道
                track_polygon = self.create_track_polygon_from_parallel_lines(
                    fork_left, fork_right, image_shape[1], image_shape[0])
                if len(track_polygon) >= 3:
                    track_mask = self.points_to_mask(track_polygon, image_shape, 1)
                    multilabel_mask[:, :, 2] = (track_mask > 0).astype(np.float32)
        
        # 设置背景通道（通道0）
        track_area = np.logical_or(multilabel_mask[:, :, 1] > 0, multilabel_mask[:, :, 2] > 0)
        multilabel_mask[:, :, 0] = (~track_area).astype(np.float32)
        
        return multilabel_mask
    
    def download_image(self, url: str, save_path: Path) -> bool:
        """
        从URL下载图像
        
        Args:
            url: 图像URL
            save_path: 保存路径
            
        Returns:
            是否成功
        """
        try:
            save_path.parent.mkdir(parents=True, exist_ok=True)
            urllib.request.urlretrieve(url, save_path)
            return True
        except Exception as e:
            print(f"下载图像失败: {url}, 错误: {e}")
            return False
    
    def save_mask(self, mask: np.ndarray, save_path: Path, use_multilabel: bool = False) -> bool:
        """
        保存掩码到文件
        
        Args:
            mask: 掩码数组
            save_path: 保存路径
            use_multilabel: 是否保存为多标签格式
            
        Returns:
            是否成功
        """
        try:
            # 统一使用PNG格式 - 使用BGR通道存储多标签信息
            if use_multilabel:
                if len(mask.shape) == 3:
                        # 已经是多标签格式，转换为BGR PNG (OpenCV格式)
                        bgr_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
                        # B通道: 背景(255) / 非背景(0)
                        bgr_mask[:, :, 0] = (mask[:, :, 0] > 0.5).astype(np.uint8) * 255
                        # G通道: 主轨道(255) / 非主轨道(0)
                        bgr_mask[:, :, 1] = (mask[:, :, 1] > 0.5).astype(np.uint8) * 255
                        # R通道: 分叉轨道(255) / 非分叉轨道(0)
                        bgr_mask[:, :, 2] = (mask[:, :, 2] > 0.5).astype(np.uint8) * 255
                        cv2.imwrite(str(save_path.with_suffix('.png')), bgr_mask)
                else:
                    # 单通道掩码，转换为BGR多标签格式
                        bgr_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
                        bgr_mask[:, :, 0] = (mask == 0).astype(np.uint8) * 255  # B: 背景
                        bgr_mask[:, :, 1] = (mask == 1).astype(np.uint8) * 255  # G: 主轨道
                        bgr_mask[:, :, 2] = (mask == 2).astype(np.uint8) * 255  # R: 分叉轨道
                        cv2.imwrite(str(save_path.with_suffix('.png')), bgr_mask)
            else:
                # 单标签格式（灰度）
                if len(mask.shape) == 2:
                    visual_mask = np.zeros_like(mask, dtype=np.uint8)
                    visual_mask[mask == 1] = 85   # 主轨道
                    visual_mask[mask == 2] = 170  # 分叉轨道
                    cv2.imwrite(str(save_path.with_suffix('.png')), visual_mask)
                else:
                    # 多通道掩码，转换为单通道可视化
                    single_channel = np.zeros((mask.shape[0], mask.shape[1]), dtype=np.uint8)
                    single_channel[mask[:, :, 1] > 0] = 85   # 主轨道
                    single_channel[mask[:, :, 2] > 0] = 170  # 分叉轨道
                    cv2.imwrite(str(save_path.with_suffix('.png')), single_channel)
            return True
        except Exception as e:
            print(f"保存掩码失败: {save_path}, 错误: {e}")
            return False


def preprocess_json_annotations(
    json_dir: Path,
    output_dir: Path,
    download_images: bool = True,
    camera_type: Optional[str] = None,
    max_files: Optional[int] = None,
    use_multilabel: bool = False
):
    """
    预处理JSON标注文件
    
    Args:
        json_dir: JSON文件目录
        output_dir: 输出目录
        download_images: 是否下载图像
        camera_type: 相机类型筛选（'6mm' 或 '25mm'）
        max_files: 最大处理文件数
        mask_format: 掩码保存格式 ('png' 或 'npy')
        use_multilabel: 是否使用多标签格式
    """
    parser = RailwayAnnotationParser()
    
    # 创建输出目录
    images_dir = output_dir / 'images'
    masks_dir = output_dir / 'masks'
    images_dir.mkdir(parents=True, exist_ok=True)
    masks_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有JSON文件
    json_files = list(json_dir.glob('*.json'))
    
    # 根据相机类型筛选
    if camera_type:
        camera_keyword = 'near' if camera_type == '6mm' else 'far'
        json_files = [f for f in json_files if camera_keyword in f.name]
    
    # 限制文件数量
    if max_files:
        json_files = json_files[:max_files]
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 处理每个JSON文件
    processed_count = 0
    failed_count = 0
    
    for json_file in tqdm(json_files, desc="处理标注文件"):
        try:
            # 解析JSON
            annotation_data = parser.parse_json_file(json_file)
            
            # 设置文件路径
            image_filename = annotation_data['filename']
            image_path = images_dir / image_filename
            mask_path = masks_dir / image_filename
            
            # 检查是否有有效轨道数据
            if not annotation_data['tracks']:
                failed_count += 1
                continue
            
            # 下载图像（如果需要）
            image_shape = None
            if download_images and annotation_data['image_url']:
                success = parser.download_image(annotation_data['image_url'], image_path)
                if not success:
                    failed_count += 1
                    continue
            
            # 读取图像获取尺寸
            if image_path.exists():
                image = cv2.imread(str(image_path))
                if image is None:
                    failed_count += 1
                    continue
                image_shape = image.shape[:2]
            else:
                # 如果没有图像，使用标准尺寸
                image_shape = (1080, 1920)
            
            # 创建分割掩码
            if use_multilabel:
                # 创建多标签掩码
                mask = parser.create_multilabel_segmentation_mask(annotation_data, image_shape)
            else:
                # 创建单通道掩码
                mask = parser.create_segmentation_mask(annotation_data, image_shape)
            
            # 检查掩码是否有内容
            if len(mask.shape) == 2 and np.max(mask) == 0:
                failed_count += 1
                continue
            elif len(mask.shape) == 3 and np.max(mask[:, :, 1:]) == 0:
                failed_count += 1
                continue
            
            # 保存掩码
            success = parser.save_mask(mask, mask_path, use_multilabel=use_multilabel)
            if not success:
                failed_count += 1
                continue
                
            processed_count += 1
            
        except Exception as e:
            # 只在调试模式下打印错误
            if max_files and max_files <= 10:  # 仅测试模式下显示错误
                print(f"处理 {json_file.name} 时出错: {e}")
            failed_count += 1
            continue
    
    print(f"成功处理 {processed_count}/{len(json_files)} 个文件")
    if failed_count > 0:
        print(f"失败 {failed_count} 个文件")
    
    # 保存元数据
    metadata = {
        'num_classes': 3,
        'classes': ['background', 'main_track', 'fork_track'],
        'class_mapping': {
            0: 'background',
            85: 'main_track',  # 可视化值
            170: 'fork_track'  # 可视化值
        },
        'camera_types': ['6mm', '25mm'],
        'processed_files': processed_count,
        'failed_files': failed_count,
        'algorithm': 'full_polygon_all_keypoints'
    }
    
    with open(output_dir / 'metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    return processed_count, failed_count


def preprocess_image(image: np.ndarray) -> np.ndarray:
    """
    图像预处理
    
    Args:
        image: 输入图像
        
    Returns:
        预处理后的图像
    """
    # 可以添加额外的预处理步骤
    return image


def preprocess_mask(mask: np.ndarray) -> np.ndarray:
    """
    掩码预处理
    
    Args:
        mask: 输入掩码
        
    Returns:
        预处理后的掩码
    """
    # 可以添加额外的预处理步骤
    return mask 