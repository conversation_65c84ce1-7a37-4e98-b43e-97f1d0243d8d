"""
数据加载器模块
负责创建数据集划分和数据加载器
"""

from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import numpy as np
import torch
from sklearn.model_selection import train_test_split, KFold
from .dataset import RailwaySegmentationDataset
from .railway_dataset import RailwayTrackDataset
from .transforms import get_train_transforms, get_val_transforms, get_test_transforms


def create_data_splits(
    images_dir: str,
    masks_dir: str,
    split_config: Dict[str, float],
    k_folds: Optional[int] = None,
    random_state: int = 42,
    save_dir: Optional[str] = None
) -> Dict[str, List[Tuple[Path, Path]]]:
    """
    创建数据集划分
    
    Args:
        images_dir: 图像目录
        masks_dir: 掩码目录
        split_config: 划分配置 {'train': 0.7, 'val': 0.15, 'test': 0.15}
        k_folds: K折交叉验证的折数
        random_state: 随机种子
        save_dir: 保存划分信息的目录
        
    Returns:
        数据划分字典
    """
    images_dir = Path(images_dir)
    masks_dir = Path(masks_dir)
    
    # 获取所有图像和对应的掩码
    image_files = sorted(images_dir.glob('*.jpg')) + sorted(images_dir.glob('*.png'))
    data_pairs = []
    
    for image_path in image_files:
        # 支持jpg和png格式掩码
        mask_path_png = masks_dir / image_path.with_suffix('.png').name
        mask_path_jpg = masks_dir / image_path.with_suffix('.jpg').name
        
        if mask_path_png.exists():
            data_pairs.append((image_path, mask_path_png))
        elif mask_path_jpg.exists():
            data_pairs.append((image_path, mask_path_jpg))
    
    print(f"找到 {len(data_pairs)} 对图像-掩码数据")
    
    indices = np.arange(len(data_pairs))
    
    if k_folds is not None:
        # K折交叉验证划分
        splits = {}
        kf = KFold(n_splits=k_folds, shuffle=True, random_state=random_state)
        
        for fold_idx, (train_idx, test_idx) in enumerate(kf.split(indices)):
            # 进一步划分训练集和验证集
            train_idx, val_idx = train_test_split(
                train_idx,
                test_size=split_config['val'] / (split_config['train'] + split_config['val']),
                random_state=random_state
            )
            
            splits[f'fold_{fold_idx}'] = {
                'train': [data_pairs[i] for i in train_idx],
                'val': [data_pairs[i] for i in val_idx],
                'test': [data_pairs[i] for i in test_idx]
            }
            
            print(f"Fold {fold_idx}: Train: {len(train_idx)}, Val: {len(val_idx)}, Test: {len(test_idx)}")
    else:
        # 普通划分
        # 首先划分出测试集
        train_val_idx, test_idx = train_test_split(
            indices,
            test_size=split_config['test'],
            random_state=random_state
        )
        
        # 再划分训练集和验证集
        train_idx, val_idx = train_test_split(
            train_val_idx,
            test_size=split_config['val'] / (split_config['train'] + split_config['val']),
            random_state=random_state
        )
        
        splits = {
            'train': [data_pairs[i] for i in train_idx],
            'val': [data_pairs[i] for i in val_idx],
            'test': [data_pairs[i] for i in test_idx]
        }
        
        print(f"Train: {len(train_idx)}, Val: {len(val_idx)}, Test: {len(test_idx)}")
    
    # 保存划分信息
    if save_dir is not None:
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        import json
        
        # 转换为可序列化的格式
        save_splits = {}
        if k_folds is not None:
            for fold_key, fold_splits in splits.items():
                save_splits[fold_key] = {}
                for split_name, split_data in fold_splits.items():
                    save_splits[fold_key][split_name] = [
                        [str(img), str(mask)] for img, mask in split_data
                    ]
        else:
            for split_name, split_data in splits.items():
                save_splits[split_name] = [
                    [str(img), str(mask)] for img, mask in split_data
                ]
        
        with open(save_dir / 'splits.json', 'w') as f:
            json.dump(save_splits, f, indent=2)
        
        print(f"数据划分信息已保存到 {save_dir / 'splits.json'}")
    
    return splits


def get_dataloader(
    data_root: str,
    split: str,
    config: Dict[str, Any],
    transform: Optional[Any] = None,
    shuffle: Optional[bool] = None,
    camera_type: Optional[str] = None,
    json_dir: Optional[str] = None,
    dataset_type: Optional[str] = None
) -> torch.utils.data.DataLoader:
    """
    创建数据加载器
    
    Args:
        data_root: 数据根目录
        split: 数据集划分 ('train', 'val', 'test')
        config: 配置字典
        transform: 数据变换
        shuffle: 是否打乱数据
        camera_type: 相机类型 ('6mm', '25mm', None表示全部)
        json_dir: JSON标注文件目录
        dataset_type: 数据集类型
        
    Returns:
        数据加载器
    """
    # 获取批次大小
    batch_size = config['data']['batch_size'].get(split, 1)
    num_workers = config['project'].get('num_workers', 4)
    
    # 获取GPU优化参数
    gpu_config = config['project'].get('gpu_optimization', {})
    pin_memory = gpu_config.get('pin_memory', True)
    persistent_workers = gpu_config.get('persistent_workers', False)
    prefetch_factor = gpu_config.get('prefetch_factor', 2)
    
    # 默认打乱设置
    if shuffle is None:
        shuffle = (split == 'train')
    
    # 确定数据集类型
    if dataset_type is None:
        dataset_type = config['data'].get('dataset_type', 'railway_segmentation')
    
    # 如果指定了相机类型和相机特定配置，使用对应的批次大小
    if camera_type and 'camera_configs' in config and camera_type in config['camera_configs']:
        camera_config = config['camera_configs'][camera_type]
        if 'batch_size' in camera_config and split in camera_config['batch_size']:
            batch_size = camera_config['batch_size'][split]
    
    # 创建数据集
    if dataset_type == 'railway_track_dataset':
        dataset = RailwayTrackDataset(
            data_root=data_root,
            split=split,
            transform=transform,
            config=config,
            camera_type=camera_type,
            json_dir=json_dir
        )
    else:
        dataset = RailwaySegmentationDataset(
            data_root=data_root,
            split=split,
            transform=transform,
            config=config
        )
    
    # 检查数据集大小
    if len(dataset) == 0:
        print(f"错误: {split} 数据集为空！")
        print(f"数据根目录: {data_root}")
        print(f"数据集类型: {dataset_type}")
        print(f"相机类型: {camera_type}")
        print(f"JSON目录: {json_dir}")
        
        # 提供详细的诊断信息
        if hasattr(dataset, 'images_dir') and hasattr(dataset, 'masks_dir'):
            print(f"图像目录: {dataset.images_dir} (存在: {dataset.images_dir.exists()})")
            if dataset.images_dir.exists():
                image_files = list(dataset.images_dir.glob('*'))
                print(f"  图像文件数量: {len(image_files)}")
                if len(image_files) > 0:
                    print(f"  图像文件示例: {image_files[:3]}")
            
            print(f"掩码目录: {dataset.masks_dir} (存在: {dataset.masks_dir.exists()})")
            if dataset.masks_dir.exists():
                mask_files = list(dataset.masks_dir.glob('*'))
                print(f"  掩码文件数量: {len(mask_files)}")
                if len(mask_files) > 0:
                    print(f"  掩码文件示例: {mask_files[:3]}")
        
        if hasattr(dataset, 'json_dir') and dataset.json_dir:
            print(f"JSON目录: {dataset.json_dir} (存在: {dataset.json_dir.exists()})")
            if dataset.json_dir.exists():
                json_files = list(dataset.json_dir.glob('*.json'))
                print(f"  JSON文件数量: {len(json_files)}")
                if len(json_files) > 0:
                    print(f"  JSON文件示例: {json_files[:3]}")
        
        # 如果是评估模式且只有测试集为空，可以跳过
        if split == 'test':
            print("警告: 测试集为空，跳过测试...")
            # 创建一个虚拟数据集
            from ..core.dummy_dataset import DummyDataset
            dataset = DummyDataset(config)
        else:
            raise ValueError(f"{split} 数据集为空，无法创建数据加载器。请检查数据路径和配置。")
    
    # 对于空数据集，调整参数
    if len(dataset) == 0:
        batch_size = 1
        shuffle = False
        num_workers = 0
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=(split == 'train' and len(dataset) > 0),
        persistent_workers=persistent_workers if num_workers > 0 else False,
        prefetch_factor=prefetch_factor if num_workers > 0 else None,
        # 添加额外的优化参数
        generator=torch.Generator().manual_seed(config['project'].get('seed', 42)) if shuffle else None
    )
    
    return dataloader


def create_dataloaders(
    config: Dict[str, Any],
    fold: Optional[int] = None,
    camera_type: Optional[str] = None
) -> Dict[str, torch.utils.data.DataLoader]:
    """
    创建所有数据加载器
    
    Args:
        config: 配置字典
        fold: K折交叉验证的折数（如果使用）
        camera_type: 相机类型 ('6mm', '25mm', None表示全部)
        
    Returns:
        数据加载器字典
    """
    data_root = config['data']['processed_data_path']
    image_size = config['data']['image_size']
    json_dir = config['data'].get('json_dir')
    dataset_type = config['data'].get('dataset_type', 'railway_segmentation')
    
    # 如果指定了相机类型和相机特定配置，使用对应的图像尺寸
    if camera_type and 'camera_configs' in config and camera_type in config['camera_configs']:
        camera_config = config['camera_configs'][camera_type]
        if 'image_size' in camera_config:
            image_size = camera_config['image_size']
    
    # 获取数据增强
    train_transform = get_train_transforms(
        image_size=image_size,
        augmentation_config=config.get('augmentation', {}).get('train')
    )
    val_transform = get_val_transforms(image_size=image_size)
    test_transform = get_test_transforms(image_size=image_size)
    
    # 创建数据加载器
    dataloaders = {
        'train': get_dataloader(
            data_root=data_root,
            split='train',
            config=config,
            transform=train_transform,
            shuffle=True,
            camera_type=camera_type,
            json_dir=json_dir,
            dataset_type=dataset_type
        ),
        'val': get_dataloader(
            data_root=data_root,
            split='val',
            config=config,
            transform=val_transform,
            shuffle=False,
            camera_type=camera_type,
            json_dir=json_dir,
            dataset_type=dataset_type
        ),
        'test': get_dataloader(
            data_root=data_root,
            split='test',
            config=config,
            transform=test_transform,
            shuffle=False,
            camera_type=camera_type,
            json_dir=json_dir,
            dataset_type=dataset_type
        )
    }
    
    return dataloaders


class DataPrefetcher:
    """
    数据预取器，用于GPU加速
    """
    
    def __init__(self, loader: torch.utils.data.DataLoader, device: torch.device):
        """
        初始化预取器
        
        Args:
            loader: 数据加载器
            device: 设备
        """
        self.loader = loader
        self.device = device
        self.stream = torch.cuda.Stream()
        
    def __iter__(self):
        """迭代器"""
        self.iter = iter(self.loader)
        self.preload()
        return self
    
    def __next__(self):
        """获取下一批数据"""
        torch.cuda.current_stream().wait_stream(self.stream)
        batch = self.batch
        self.preload()
        return batch
    
    def preload(self):
        """预加载下一批数据"""
        try:
            self.batch = next(self.iter)
        except StopIteration:
            self.batch = None
            raise StopIteration
        
        with torch.cuda.stream(self.stream):
            # 将数据移到GPU
            if isinstance(self.batch, dict):
                for k, v in self.batch.items():
                    if isinstance(v, torch.Tensor):
                        self.batch[k] = v.to(self.device, non_blocking=True)
            elif isinstance(self.batch, (list, tuple)):
                self.batch = [
                    v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v
                    for v in self.batch
                ]
    
    def __len__(self):
        """返回数据加载器长度"""
        return len(self.loader) 