"""
注册器模块
用于管理和注册各种组件（模型、损失函数、评估指标等）
"""

from typing import Dict, Any, Callable, Optional, Type
import inspect


class Registry:
    """
    通用注册器类
    用于动态注册和获取组件
    """
    
    def __init__(self, name: str):
        """
        初始化注册器
        
        Args:
            name: 注册器名称
        """
        self.name = name
        self._registry: Dict[str, Any] = {}
    
    def register(self, name: Optional[str] = None) -> Callable:
        """
        注册装饰器
        
        Args:
            name: 注册名称，如果为None则使用类名
            
        Returns:
            装饰器函数
        """
        def decorator(cls_or_func: Any) -> Any:
            # 获取注册名称
            register_name = name or cls_or_func.__name__
            
            # 检查是否已注册
            if register_name in self._registry:
                raise ValueError(
                    f"'{register_name}' 已经在 {self.name} 注册器中注册"
                )
            
            # 注册组件
            self._registry[register_name] = cls_or_func
            
            # 添加注册信息到类或函数
            setattr(cls_or_func, '_registry_name', register_name)
            setattr(cls_or_func, '_registry', self)
            
            return cls_or_func
        
        return decorator
    
    def get(self, name: str) -> Any:
        """
        获取注册的组件
        
        Args:
            name: 组件名称
            
        Returns:
            注册的组件
            
        Raises:
            KeyError: 如果组件未注册
        """
        if name not in self._registry:
            raise KeyError(
                f"'{name}' 未在 {self.name} 注册器中注册。"
                f"可用的组件: {list(self._registry.keys())}"
            )
        
        return self._registry[name]
    
    def build(self, cfg: Dict[str, Any], **kwargs) -> Any:
        """
        根据配置构建组件实例
        
        Args:
            cfg: 配置字典，必须包含 'type' 字段
            **kwargs: 额外的构建参数
            
        Returns:
            组件实例
        """
        if 'type' not in cfg:
            raise ValueError("配置字典必须包含 'type' 字段")
        
        # 获取组件类型
        component_type = cfg['type']
        component_cls = self.get(component_type)
        
        # 检查构造函数签名
        init_signature = inspect.signature(component_cls.__init__)
        init_params = list(init_signature.parameters.keys())
        
        # 如果构造函数只接受self和config参数，则传递整个配置字典（除了type）
        if len(init_params) == 2 and 'config' in init_params:
            build_args = {k: v for k, v in cfg.items() if k != 'type'}
            return component_cls(config=build_args, **kwargs)
        else:
            # 否则将配置作为关键字参数传递
            build_args = {k: v for k, v in cfg.items() if k != 'type'}
            build_args.update(kwargs)
            return component_cls(**build_args)
    
    def list(self) -> list:
        """
        列出所有注册的组件
        
        Returns:
            组件名称列表
        """
        return list(self._registry.keys())
    
    def __contains__(self, name: str) -> bool:
        """检查组件是否已注册"""
        return name in self._registry
    
    def __repr__(self) -> str:
        return f"Registry(name='{self.name}', items={self.list()})"
    
    def clear(self):
        """清空注册器"""
        self._registry.clear()


# 创建全局注册器实例
MODEL_REGISTRY = Registry('models')
LOSS_REGISTRY = Registry('losses')
METRIC_REGISTRY = Registry('metrics')
OPTIMIZER_REGISTRY = Registry('optimizers')
SCHEDULER_REGISTRY = Registry('schedulers')
TRANSFORM_REGISTRY = Registry('transforms')
DATASET_REGISTRY = Registry('datasets')


def register_model(name: Optional[str] = None) -> Callable:
    """模型注册装饰器"""
    return MODEL_REGISTRY.register(name)


def register_loss(name: Optional[str] = None) -> Callable:
    """损失函数注册装饰器"""
    return LOSS_REGISTRY.register(name)


def register_metric(name: Optional[str] = None) -> Callable:
    """评估指标注册装饰器"""
    return METRIC_REGISTRY.register(name)


def register_optimizer(name: Optional[str] = None) -> Callable:
    """优化器注册装饰器"""
    return OPTIMIZER_REGISTRY.register(name)


def register_scheduler(name: Optional[str] = None) -> Callable:
    """学习率调度器注册装饰器"""
    return SCHEDULER_REGISTRY.register(name)


def register_transform(name: Optional[str] = None) -> Callable:
    """数据增强注册装饰器"""
    return TRANSFORM_REGISTRY.register(name)


def register_dataset(name: Optional[str] = None) -> Callable:
    """数据集注册装饰器"""
    return DATASET_REGISTRY.register(name) 