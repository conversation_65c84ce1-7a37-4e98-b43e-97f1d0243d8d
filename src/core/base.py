"""
基础类模块
定义项目中各种组件的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
import torch
import torch.nn as nn
from pathlib import Path


class BaseModel(nn.Module, ABC):
    """
    模型基类
    所有模型都应继承此类
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化模型
        
        Args:
            config: 模型配置
        """
        super().__init__()
        self.config = config or {}
    
    @abstractmethod
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量
            
        Returns:
            输出张量
        """
        pass
    
    def save_pretrained(self, save_path: str):
        """
        保存模型权重和配置
        
        Args:
            save_path: 保存路径
        """
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存权重
        torch.save(self.state_dict(), save_path / 'model.pth')
        
        # 保存配置
        import json
        with open(save_path / 'config.json', 'w') as f:
            json.dump(self.config, f, indent=2)
    
    @classmethod
    def from_pretrained(cls, load_path: str) -> 'BaseModel':
        """
        加载预训练模型
        
        Args:
            load_path: 加载路径
            
        Returns:
            模型实例
        """
        load_path = Path(load_path)
        
        # 加载配置
        import json
        with open(load_path / 'config.json', 'r') as f:
            config = json.load(f)
        
        # 创建模型实例
        model = cls(config)
        
        # 加载权重
        model.load_state_dict(torch.load(load_path / 'model.pth'))
        
        return model
    
    def count_parameters(self) -> int:
        """统计模型参数量"""
        return sum(p.numel() for p in self.parameters())
    
    def freeze(self):
        """冻结模型参数"""
        for param in self.parameters():
            param.requires_grad = False
    
    def unfreeze(self):
        """解冻模型参数"""
        for param in self.parameters():
            param.requires_grad = True


class BaseTrainer(ABC):
    """
    训练器基类
    所有训练器都应继承此类
    """
    
    def __init__(self, 
                 model: BaseModel,
                 config: Dict[str, Any],
                 train_loader: torch.utils.data.DataLoader,
                 val_loader: Optional[torch.utils.data.DataLoader] = None):
        """
        初始化训练器
        
        Args:
            model: 模型
            config: 训练配置
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
        """
        self.model = model
        self.config = config
        self.train_loader = train_loader
        self.val_loader = val_loader
        
        # 设备
        self.device = torch.device(config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu'))
        self.model.to(self.device)
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_metric = None
    
    @abstractmethod
    def train_epoch(self) -> Dict[str, float]:
        """
        训练一个epoch
        
        Returns:
            训练指标字典
        """
        pass
    
    @abstractmethod
    def validate(self) -> Dict[str, float]:
        """
        验证模型
        
        Returns:
            验证指标字典
        """
        pass
    
    @abstractmethod
    def train(self) -> List[Dict[str, float]]:
        """
        完整训练流程
        
        Returns:
            训练历史
        """
        pass
    
    def save_checkpoint(self, save_path: str, **kwargs):
        """
        保存检查点
        
        Args:
            save_path: 保存路径
            **kwargs: 额外的保存内容
        """
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'best_metric': self.best_metric,
        }
        checkpoint.update(kwargs)
        
        torch.save(checkpoint, save_path)
    
    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """
        加载检查点
        
        Args:
            checkpoint_path: 检查点路径
            
        Returns:
            检查点内容
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.current_epoch = checkpoint.get('epoch', 0)
        self.global_step = checkpoint.get('global_step', 0)
        self.best_metric = checkpoint.get('best_metric')
        
        return checkpoint


class BaseDataset(torch.utils.data.Dataset, ABC):
    """
    数据集基类
    所有数据集都应继承此类
    """
    
    def __init__(self, 
                 data_root: str,
                 split: str = 'train',
                 transform: Optional[Any] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化数据集
        
        Args:
            data_root: 数据根目录
            split: 数据集划分 ('train', 'val', 'test')
            transform: 数据变换
            config: 数据集配置
        """
        self.data_root = Path(data_root)
        self.split = split
        self.transform = transform
        self.config = config or {}
        
        # 加载数据
        self.data = self._load_data()
    
    @abstractmethod
    def _load_data(self) -> List[Any]:
        """
        加载数据
        
        Returns:
            数据列表
        """
        pass
    
    @abstractmethod
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取一个数据样本
        
        Args:
            idx: 索引
            
        Returns:
            数据样本字典
        """
        pass
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data)
    
    def get_labels(self) -> List[Any]:
        """获取所有标签"""
        return [item.get('label') for item in self.data]
    
    def get_class_distribution(self) -> Dict[Any, int]:
        """获取类别分布"""
        from collections import Counter
        labels = self.get_labels()
        return dict(Counter(labels))


class BaseMetric(ABC):
    """
    评估指标基类
    所有评估指标都应继承此类
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化评估指标
        
        Args:
            config: 配置
        """
        self.config = config or {}
        self.reset()
    
    @abstractmethod
    def update(self, preds: torch.Tensor, targets: torch.Tensor):
        """
        更新指标
        
        Args:
            preds: 预测值
            targets: 真实值
        """
        pass
    
    @abstractmethod
    def compute(self) -> float:
        """
        计算最终指标值
        
        Returns:
            指标值
        """
        pass
    
    @abstractmethod
    def reset(self):
        """重置指标状态"""
        pass
    
    def __call__(self, preds: torch.Tensor, targets: torch.Tensor) -> float:
        """
        直接调用计算指标
        
        Args:
            preds: 预测值
            targets: 真实值
            
        Returns:
            指标值
        """
        self.reset()
        self.update(preds, targets)
        return self.compute() 