"""
配置管理模块
负责加载、合并和管理项目配置
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from copy import deepcopy


class Config:
    """配置管理类"""
    
    def __init__(self, config_dict: Dict[str, Any]):
        """
        初始化配置对象
        
        Args:
            config_dict: 配置字典
        """
        self._config = config_dict
        self._set_attributes(config_dict)
    
    def _set_attributes(self, config_dict: Dict[str, Any], parent_key: str = ''):
        """
        递归设置配置属性，支持嵌套访问
        
        Args:
            config_dict: 配置字典
            parent_key: 父键名
        """
        for key, value in config_dict.items():
            if isinstance(value, dict):
                # 创建嵌套的Config对象
                setattr(self, key, Config(value))
            else:
                setattr(self, key, value)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键（如 'model.encoder'）
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 递归创建嵌套字典
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        # 更新属性
        self._set_attributes(self._config)
    
    def update(self, updates: Dict[str, Any]):
        """
        批量更新配置
        
        Args:
            updates: 更新的配置字典
        """
        def deep_update(d: Dict, u: Dict) -> Dict:
            for k, v in u.items():
                if isinstance(v, dict):
                    d[k] = deep_update(d.get(k, {}), v)
                else:
                    d[k] = v
            return d
        
        self._config = deep_update(self._config, updates)
        self._set_attributes(self._config)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return deepcopy(self._config)
    
    def save(self, path: Union[str, Path]):
        """
        保存配置到YAML文件
        
        Args:
            path: 保存路径
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
    
    def __repr__(self) -> str:
        return f"Config({self._config})"
    
    def __str__(self) -> str:
        return yaml.dump(self._config, default_flow_style=False, allow_unicode=True)


def load_config(
    config_path: Union[str, Path],
    overrides: Optional[Dict[str, Any]] = None,
    base_config_path: Optional[Union[str, Path]] = None
) -> Config:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        overrides: 覆盖的配置项
        base_config_path: 基础配置文件路径（用于继承）
        
    Returns:
        Config对象
    """
    config_path = Path(config_path)
    
    # 加载基础配置
    if base_config_path is None:
        base_config_path = Path(__file__).parent.parent.parent / 'configs' / 'base_config.yaml'
    
    config_dict = {}
    
    # 如果存在基础配置，先加载
    if base_config_path and Path(base_config_path).exists():
        with open(base_config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f) or {}
    
    # 加载指定配置文件
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            specific_config = yaml.safe_load(f) or {}
        
        # 深度合并配置
        def deep_merge(base: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    base[key] = deep_merge(base[key], value)
                else:
                    base[key] = value
            return base
        
        config_dict = deep_merge(config_dict, specific_config)
    
    # 应用覆盖配置
    if overrides:
        config = Config(config_dict)
        config.update(overrides)
    else:
        config = Config(config_dict)
    
    return config


def merge_configs(*configs: Config) -> Config:
    """
    合并多个配置对象
    
    Args:
        *configs: 配置对象列表
        
    Returns:
        合并后的配置对象
    """
    merged_dict = {}
    
    for config in configs:
        def deep_merge(base: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    base[key] = deep_merge(base[key], value)
                else:
                    base[key] = value
            return base
        
        merged_dict = deep_merge(merged_dict, config.to_dict())
    
    return Config(merged_dict) 