"""
虚拟数据集模块
用于处理数据集为空的情况
"""

import torch
import numpy as np
from typing import Dict, Any, Optional


class DummyDataset(torch.utils.data.Dataset):
    """
    虚拟数据集，用于处理数据为空的情况
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化虚拟数据集
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.length = 1  # 只有一个虚拟样本
        
        # 获取图像尺寸
        if 'data' in config and 'image_size' in config['data']:
            self.height = config['data']['image_size'].get('height', 544)
            self.width = config['data']['image_size'].get('width', 960)
        else:
            self.height = 544
            self.width = 960
            
        # 获取类别数量
        self.num_classes = config.get('data', {}).get('num_classes', 3)
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return self.length
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取虚拟样本
        
        Args:
            idx: 索引
            
        Returns:
            虚拟样本字典
        """
        # 创建黑色图像
        image = torch.zeros(3, self.height, self.width, dtype=torch.float32)
        
        # 创建空掩码
        mask = torch.zeros(self.num_classes, self.height, self.width, dtype=torch.float32)
        
        return {
            'image': image,
            'mask': mask,
            'filename': 'dummy_sample.png',
            'original_shape': (self.height, self.width),
            'camera_type': 'dummy'
        } 