"""
训练器模块
实现模型训练和验证的核心逻辑
"""

import gc
import json
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any, Optional
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import autocast, GradScaler
from tqdm import tqdm

from ..core.base import BaseTrainer
from ..utils import RunningAverage, compute_metrics
from ..utils.memory_monitor import MemoryMonitor, cleanup_cuda_memory, optimize_model_memory


class StreamingMetricsCalculator:
    """
    流式指标计算器
    避免累积大量数据到内存中
    """
    
    def __init__(self, threshold: float = 0.5):
        """
        初始化流式指标计算器
        
        Args:
            threshold: 二值化阈值
        """
        self.threshold = threshold
        self.reset()
    
    def reset(self):
        """重置所有累积的指标"""
        self.total_samples = 0
        self.total_intersection = 0.0
        self.total_union = 0.0
        self.total_pred_positive = 0.0
        self.total_true_positive = 0.0
        self.total_false_positive = 0.0
        self.total_false_negative = 0.0
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """
        更新指标计算
        
        Args:
            predictions: 预测结果 [N, C, H, W]
            targets: 真实标签 [N, C, H, W]
        """
        # 二值化预测
        pred_binary = (predictions > self.threshold).float()
        
        # 计算每个样本的指标并累积
        batch_size = predictions.size(0)
        self.total_samples += batch_size
        
        # 使用reshape以处理可能的非连续张量
        pred_flat = pred_binary.reshape(batch_size, -1)
        target_flat = targets.reshape(batch_size, -1)
        
        # 计算交集和并集
        intersection = (pred_flat * target_flat).sum(dim=1)
        pred_area = pred_flat.sum(dim=1)
        target_area = target_flat.sum(dim=1)
        union = pred_area + target_area - intersection
        
        # 累积统计量
        self.total_intersection += intersection.sum().item()
        self.total_union += union.sum().item()
        self.total_pred_positive += pred_area.sum().item()
        self.total_true_positive += intersection.sum().item()
        self.total_false_positive += (pred_area - intersection).sum().item()
        self.total_false_negative += (target_area - intersection).sum().item()
        
        # 立即清理临时变量
        del pred_binary, pred_flat, target_flat, intersection, pred_area, target_area, union
    
    def compute(self) -> Dict[str, float]:
        """
        计算最终指标
        
        Returns:
            包含各种指标的字典
        """
        if self.total_samples == 0:
            return {
                'val_iou': 0.0,
                'val_dice': 0.0,
                'val_precision': 0.0,
                'val_recall': 0.0,
                'val_f1': 0.0
            }
        
        # 避免除零错误
        eps = 1e-7
        
        # IoU
        iou = self.total_intersection / (self.total_union + eps)
        
        # Dice
        dice = (2 * self.total_intersection) / (self.total_pred_positive + self.total_true_positive + eps)
        
        # Precision
        precision = self.total_true_positive / (self.total_true_positive + self.total_false_positive + eps)
        
        # Recall
        recall = self.total_true_positive / (self.total_true_positive + self.total_false_negative + eps)
        
        # F1
        f1 = 2 * precision * recall / (precision + recall + eps)
        
        return {
            'val_iou': float(iou),
            'val_dice': float(dice),
            'val_precision': float(precision),
            'val_recall': float(recall),
            'val_f1': float(f1)
        }


class Trainer(BaseTrainer):
    """
    主训练器类
    负责模型训练、验证和内存管理，支持断点重启
    """
    
    def __init__(self, 
                 model: nn.Module,
                 config: Dict[str, Any],
                 train_loader: torch.utils.data.DataLoader,
                 val_loader: torch.utils.data.DataLoader,
                 loss_fn: nn.Module,
                 output_dir: Path,
                 logger: Optional[logging.Logger] = None):
        """
        初始化训练器
        
        Args:
            model: 训练模型
            config: 训练配置
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            loss_fn: 损失函数
            output_dir: 输出目录
            logger: 日志记录器
        """
        super().__init__(model, config, train_loader, val_loader)
        
        self.loss_fn = loss_fn
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logger or logging.getLogger(__name__)
        
        # 训练配置
        training_config = config.get('training', {})
        self.epochs = training_config.get('epochs', 100)
        self.accumulation_steps = training_config.get('gradient_accumulation_steps', 1)
        self.clip_gradient = training_config.get('clip_gradient', True)
        self.use_amp = training_config.get('mixed_precision', True)
        self.eval_interval = training_config.get('eval_interval', 1)
        self.save_interval = training_config.get('save_interval', 5)  # 更频繁的保存间隔
        
        # 性能优化配置
        perf_config = training_config.get('performance_optimization', {})
        self.use_cudnn_benchmark = perf_config.get('cudnn_benchmark', False)
        self.use_channels_last = perf_config.get('use_channels_last', False)
        self.use_data_prefetcher = perf_config.get('use_data_prefetcher', False)
        self.use_gradient_checkpointing = perf_config.get('gradient_checkpointing', False)
        self.max_grad_norm = perf_config.get('max_grad_norm', 1.0)
        
        # 应用性能优化配置
        if self.use_cudnn_benchmark:
            torch.backends.cudnn.benchmark = True
            self.logger.info("启用cuDNN基准测试模式")
            
        if self.use_channels_last and torch.cuda.is_available():
            self.model = self.model.to(memory_format=torch.channels_last)
            self.logger.info("使用channels_last内存格式")
            
        if self.use_gradient_checkpointing and hasattr(self.model, 'gradient_checkpointing_enable'):
            self.model.gradient_checkpointing_enable()
            self.logger.info("启用梯度检查点")
            
        # 应用模型内存优化（综合优化）
        self.model = optimize_model_memory(self.model)
        self.logger.info("应用模型内存优化")
            
        # CUDA图优化（如果启用）
        self.use_cuda_graph = gpu_config = config['project'].get('gpu_optimization', {}).get('use_cuda_graph', False)
        if self.use_cuda_graph and torch.cuda.is_available():
            self.logger.info("准备使用CUDA图优化")
            # CUDA图优化将在第一次前向传播后应用
        
        # 基于步数的保存配置
        self.save_every_n_steps = training_config.get('save_every_n_steps', 500)  # 每500步保存
        self.enable_step_saving = training_config.get('enable_step_saving', True)  # 启用步数保存
        self.last_step_save = 0  # 上次步数保存的记录
        self.keep_step_checkpoints = training_config.get('keep_step_checkpoints', 3)  # 保留的步数检查点数量
        
        # 检查点配置
        checkpointing_config = config.get('checkpointing', {})
        self.auto_resume = checkpointing_config.get('auto_resume', True)
        self.save_last = checkpointing_config.get('save_last', True)
        self.save_top_k = checkpointing_config.get('save_top_k', 5)  # 保留更多检查点
        self.monitor = checkpointing_config.get('monitor', 'val_iou')
        self.mode = checkpointing_config.get('mode', 'max')
        
        # 内存管理配置
        self.memory_cleanup_interval = training_config.get('memory_cleanup_interval', 5)  # 更频繁清理
        self.enable_memory_monitoring = training_config.get('enable_memory_monitoring', True)
        self.memory_leak_threshold = training_config.get('memory_leak_threshold', 300.0)  # 降低阈值
        
        # 初始化内存监控器
        if self.enable_memory_monitoring:
            self.memory_monitor = MemoryMonitor(self.logger)
            self.memory_monitor.log_memory_usage("初始化训练器 - ")
        else:
            self.memory_monitor = None
        
        # 创建优化器和调度器
        self.optimizer = self._create_optimizer(training_config.get('optimizer', {}))
        self.scheduler = self._create_scheduler(training_config.get('scheduler', {}))
        
        # AMP scaler
        self.scaler = GradScaler() if self.use_amp else None
        
        # 训练状态
        self.history = []
        self.best_metric = float('-inf') if self.mode == 'max' else float('inf')
        self.best_metric_name = self.monitor
        self.saved_checkpoints = []  # 保存的检查点列表
        
        # 随机种子状态（用于恢复）
        self.random_state = None
        
        # TensorBoard日志（如果配置）
        self.tb_logger = None
        if config.get('logging', {}).get('tensorboard', False):
            from ..utils.tensorboard_logger import TensorBoardLogger
            tb_dir = self.output_dir / 'tensorboard'
            self.tb_logger = TensorBoardLogger(tb_dir)
    
    def _create_optimizer(self, optimizer_config: Dict[str, Any]) -> optim.Optimizer:
        """创建优化器"""
        optimizer_type = optimizer_config.get('type', 'adamw').lower()
        lr = optimizer_config.get('lr', 0.001)
        weight_decay = optimizer_config.get('weight_decay', 0.0001)
        
        if optimizer_type == 'adamw':
            betas = optimizer_config.get('betas', [0.9, 0.999])
            return optim.AdamW(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=betas
            )
        elif optimizer_type == 'adam':
            return optim.Adam(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        elif optimizer_type == 'sgd':
            momentum = optimizer_config.get('momentum', 0.9)
            return optim.SGD(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                momentum=momentum
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    def _create_scheduler(self, scheduler_config: Dict[str, Any]) -> Optional[optim.lr_scheduler._LRScheduler]:
        """创建学习率调度器"""
        if not scheduler_config:
            return None
        
        scheduler_type = scheduler_config.get('type', 'cosine').lower()
        
        if scheduler_type == 'cosine':
            warmup_epochs = scheduler_config.get('warmup_epochs', 5)
            min_lr = scheduler_config.get('min_lr', 1e-6)
            return optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=warmup_epochs,
                eta_min=min_lr
            )
        elif scheduler_type == 'step':
            step_size = scheduler_config.get('step_size', 30)
            gamma = scheduler_config.get('gamma', 0.1)
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=step_size,
                gamma=gamma
            )
        elif scheduler_type == 'reduce_on_plateau':
            factor = scheduler_config.get('factor', 0.5)
            patience = scheduler_config.get('patience', 10)
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=factor,
                patience=patience
            )
        else:
            return None
    
    def _save_random_state(self):
        """保存随机数状态"""
        self.random_state = {
            'python': random.getstate(),
            'numpy': np.random.get_state(),
            'torch': torch.get_rng_state().clone().to(dtype=torch.uint8),  # 确保是正确的ByteTensor类型
        }
        if torch.cuda.is_available():
            self.random_state['torch_cuda'] = torch.cuda.get_rng_state().clone().to(dtype=torch.uint8)  # 确保是正确的ByteTensor类型
    
    def _restore_random_state(self):
        """恢复随机数状态"""
        if self.random_state is None:
            return
            
        random.setstate(self.random_state['python'])
        np.random.set_state(self.random_state['numpy'])
        
        # 确保torch随机状态是ByteTensor类型
        if isinstance(self.random_state['torch'], torch.Tensor) and self.random_state['torch'].dtype == torch.uint8:
            torch.set_rng_state(self.random_state['torch'])
        else:
            try:
                # 尝试转换为ByteTensor
                if isinstance(self.random_state['torch'], torch.Tensor):
                    torch_state = self.random_state['torch'].byte()
                else:
                    torch_state = torch.ByteTensor(self.random_state['torch'])
                torch.set_rng_state(torch_state)
            except Exception as e:
                self.logger.warning(f"无法恢复PyTorch随机状态: {e}，使用新的随机状态")
                # 如果转换失败，不恢复torch随机状态
        
        # 恢复CUDA随机状态
        if torch.cuda.is_available() and 'torch_cuda' in self.random_state:
            try:
                if isinstance(self.random_state['torch_cuda'], torch.Tensor) and self.random_state['torch_cuda'].dtype == torch.uint8:
                    torch.cuda.set_rng_state(self.random_state['torch_cuda'])
                else:
                    # 尝试转换为ByteTensor
                    if isinstance(self.random_state['torch_cuda'], torch.Tensor):
                        cuda_state = self.random_state['torch_cuda'].byte()
                    else:
                        cuda_state = torch.ByteTensor(self.random_state['torch_cuda'])
                    torch.cuda.set_rng_state(cuda_state)
            except Exception as e:
                self.logger.warning(f"无法恢复CUDA随机状态: {e}，使用新的随机状态")
                # 如果转换失败，不恢复CUDA随机状态
    
    def _cleanup_memory(self, force: bool = False):
        """清理内存"""
        if force or torch.cuda.is_available():
            # 清理CUDA缓存
            cleanup_cuda_memory()
            
            # Python垃圾回收
            gc.collect()
            
            # 使用内存监控器记录状态
            if self.memory_monitor:
                self.memory_monitor.log_memory_usage("内存清理后 - ")
    
    def find_latest_checkpoint(self) -> Optional[Path]:
        """查找最新的检查点文件（包括epoch和step检查点）"""
        # 优先查找last_checkpoint.pth
        last_checkpoint = self.output_dir / 'last_checkpoint.pth'
        if last_checkpoint.exists():
            return last_checkpoint
        
        # 查找最新的步数检查点
        latest_step_checkpoint = self.find_latest_step_checkpoint()
        
        # 查找最新的epoch检查点
        checkpoint_pattern = "checkpoint_epoch_*.pth"
        epoch_checkpoints = list(self.output_dir.glob(checkpoint_pattern))
        
        latest_epoch_checkpoint = None
        if epoch_checkpoints:
            # 按epoch号排序，获取最新的
            epoch_checkpoints.sort(key=lambda x: int(x.stem.split('_')[-1]))
            latest_epoch_checkpoint = epoch_checkpoints[-1]
        
        # 比较两种检查点，选择最新的
        candidates = []
        
        if latest_step_checkpoint:
            try:
                step_checkpoint = torch.load(latest_step_checkpoint, map_location='cpu')
                step_global_step = step_checkpoint.get('global_step', 0)
                candidates.append((latest_step_checkpoint, step_global_step, 'step'))
            except Exception as e:
                self.logger.warning(f"无法读取步数检查点 {latest_step_checkpoint}: {e}")
        
        if latest_epoch_checkpoint:
            try:
                epoch_checkpoint = torch.load(latest_epoch_checkpoint, map_location='cpu')
                epoch_global_step = epoch_checkpoint.get('global_step', 0)
                candidates.append((latest_epoch_checkpoint, epoch_global_step, 'epoch'))
            except Exception as e:
                self.logger.warning(f"无法读取epoch检查点 {latest_epoch_checkpoint}: {e}")
        
        if not candidates:
            return None
        
        # 选择global_step最大的检查点
        candidates.sort(key=lambda x: x[1])
        latest_checkpoint, latest_step, checkpoint_type = candidates[-1]
        
        self.logger.info(f"找到最新检查点: {latest_checkpoint} (类型: {checkpoint_type}, 步数: {latest_step})")
        return latest_checkpoint

    def find_latest_step_checkpoint(self) -> Optional[Path]:
        """查找最新的步数检查点"""
        checkpoint_pattern = "checkpoint_step_*.pth"
        step_checkpoints = list(self.output_dir.glob(checkpoint_pattern))
        
        if not step_checkpoints:
            return None
        
        # 按步数排序，获取最新的
        step_checkpoints.sort(key=lambda x: int(x.stem.split('_')[-1]))
        return step_checkpoints[-1]

    def _save_step_checkpoint(self):
        """保存基于步数的检查点"""
        if not self.enable_step_saving:
            return
            
        checkpoint_name = f'checkpoint_step_{self.global_step}.pth'
        self._save_checkpoint(checkpoint_name)
        
        # 清理旧的步数检查点
        self._cleanup_old_step_checkpoints()

    def _save_checkpoint(self, checkpoint_name: str, is_last: bool = False):
        """保存检查点"""
        checkpoint_path = self.output_dir / checkpoint_name
        
        # 准备检查点数据
        checkpoint_data = {
            'epoch': self.current_epoch + 1,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_metric': self.best_metric,
            'best_metric_name': self.best_metric_name,
            'history': self.history,
            'config': self.config,
            'random_state': {
                'torch': torch.get_rng_state(),
                'numpy': np.random.get_state(),
                'python': random.getstate()
            }
        }
        
        # 添加调度器状态
        if self.scheduler:
            checkpoint_data['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 添加混合精度状态
        if self.use_amp and self.scaler:
            checkpoint_data['scaler_state_dict'] = self.scaler.state_dict()
        
        # 保存检查点
        try:
            torch.save(checkpoint_data, checkpoint_path)
            self.logger.debug(f"检查点已保存: {checkpoint_path}")
        except Exception as e:
            self.logger.error(f"保存检查点失败 {checkpoint_path}: {e}")

    def _save_best_model(self):
        """保存最佳模型"""
        best_model_path = self.output_dir / 'best_model.pth'
        
        # 只保存模型状态和基本信息
        best_model_data = {
            'epoch': self.current_epoch + 1,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'best_metric': self.best_metric,
            'best_metric_name': self.best_metric_name,
            'config': self.config
        }
        
        try:
            torch.save(best_model_data, best_model_path)
            self.logger.debug(f"最佳模型已保存: {best_model_path}")
        except Exception as e:
            self.logger.error(f"保存最佳模型失败 {best_model_path}: {e}")

    def _cleanup_old_step_checkpoints(self):
        """清理旧的步数检查点"""
        if self.keep_step_checkpoints <= 0:
            return
        
        checkpoint_pattern = "checkpoint_step_*.pth"
        step_checkpoints = list(self.output_dir.glob(checkpoint_pattern))
        
        if len(step_checkpoints) <= self.keep_step_checkpoints:
            return
        
        # 按步数排序
        step_checkpoints.sort(key=lambda x: int(x.stem.split('_')[-1]))
        
        # 删除多余的检查点
        checkpoints_to_remove = step_checkpoints[:-self.keep_step_checkpoints]
        
        for ckpt_path in checkpoints_to_remove:
            try:
                ckpt_path.unlink()
                self.logger.debug(f"删除旧步数检查点: {ckpt_path}")
            except Exception as e:
                self.logger.warning(f"删除步数检查点失败 {ckpt_path}: {e}")

    def get_checkpoint_info(self) -> Dict[str, Any]:
        """获取当前训练状态的检查点信息"""
        # 查找所有检查点文件
        checkpoint_files = []
        
        # 查找epoch检查点
        epoch_checkpoints = list(self.output_dir.glob("checkpoint_epoch_*.pth"))
        checkpoint_files.extend([(p, 'epoch') for p in epoch_checkpoints])
        
        # 查找步数检查点
        step_checkpoints = list(self.output_dir.glob("checkpoint_step_*.pth"))
        checkpoint_files.extend([(p, 'step') for p in step_checkpoints])
        
        # 查找其他检查点
        last_checkpoint = self.output_dir / 'last_checkpoint.pth'
        if last_checkpoint.exists():
            checkpoint_files.append((last_checkpoint, 'last'))
        
        best_model = self.output_dir / 'best_model.pth'
        if best_model.exists():
            checkpoint_files.append((best_model, 'best'))
        
        final_checkpoints = list(self.output_dir.glob("final_checkpoint_*.pth"))
        checkpoint_files.extend([(p, 'final') for p in final_checkpoints])
        
        backup_checkpoints = list(self.output_dir.glob("backup_*.pth"))
        checkpoint_files.extend([(p, 'backup') for p in backup_checkpoints])
        
        return {
            'current_epoch': self.current_epoch + 1,
            'global_step': self.global_step,
            'best_metric': self.best_metric,
            'best_metric_name': self.best_metric_name,
            'training_progress': f"{self.current_epoch + 1}/{self.epochs}",
            'saved_checkpoints': [{'path': str(p), 'type': t} for p, t in checkpoint_files],
            'total_checkpoints': len(checkpoint_files),
            'output_dir': str(self.output_dir)
        }

    def auto_resume_training(self) -> bool:
        """自动恢复训练"""
        if not self.auto_resume:
            return False
        
        latest_checkpoint = self.find_latest_checkpoint()
        if latest_checkpoint and latest_checkpoint.exists():
            self.logger.info(f"发现检查点，自动恢复训练: {latest_checkpoint}")
            self.load_checkpoint(str(latest_checkpoint))
            return True
        
        self.logger.info("未发现检查点文件，从头开始训练")
        return False
    
    def _cleanup_old_checkpoints(self):
        """清理旧的检查点文件，只保留最佳的几个（但保留备份文件）"""
        if self.save_top_k <= 0:
            return
        
        # 获取所有检查点文件（排除备份文件）
        checkpoint_pattern = "checkpoint_epoch_*.pth"
        all_checkpoints = list(self.output_dir.glob(checkpoint_pattern))
        
        # 过滤掉备份文件（包含时间戳的文件）
        regular_checkpoints = []
        for ckpt_path in all_checkpoints:
            # 备份文件格式: backup_epoch_N_YYYYMMDD_HHMMSS.pth
            if not ckpt_path.name.startswith('backup_'):
                regular_checkpoints.append(ckpt_path)
        
        if len(regular_checkpoints) <= self.save_top_k:
            return
        
        # 读取每个检查点的指标
        checkpoint_metrics = []
        for ckpt_path in regular_checkpoints:
            try:
                checkpoint = torch.load(ckpt_path, map_location='cpu')
                metric_value = checkpoint.get('best_metric', 0)
                checkpoint_metrics.append((ckpt_path, metric_value))
            except Exception as e:
                self.logger.warning(f"无法读取检查点 {ckpt_path}: {e}")
                continue
        
        # 根据mode排序
        reverse = (self.mode == 'max')
        checkpoint_metrics.sort(key=lambda x: x[1], reverse=reverse)
        
        # 删除多余的检查点（保留最佳的）
        checkpoints_to_keep = checkpoint_metrics[:self.save_top_k]
        checkpoints_to_remove = checkpoint_metrics[self.save_top_k:]
        
        for ckpt_path, metric_value in checkpoints_to_remove:
            try:
                ckpt_path.unlink()
                self.logger.debug(f"删除旧检查点: {ckpt_path} (指标: {metric_value:.4f})")
            except Exception as e:
                self.logger.warning(f"删除检查点失败 {ckpt_path}: {e}")
        
        # 报告保留的检查点
        if checkpoints_to_remove:
            self.logger.info(f"清理了 {len(checkpoints_to_remove)} 个旧检查点，保留 {len(checkpoints_to_keep)} 个最佳检查点")
    
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        # 训练指标
        train_loss = RunningAverage()
        
        # 内存监控
        if self.memory_monitor:
            self.memory_monitor.log_memory_usage(f"Epoch {self.current_epoch + 1} 训练开始 - ")
        
        # 数据加载（使用预取器提高效率）
        if self.use_data_prefetcher:
            from src.data.dataloader import DataPrefetcher
            train_loader = DataPrefetcher(self.train_loader, self.device)
            self.logger.debug("使用数据预取器")
        else:
            train_loader = self.train_loader
        
        # 进度条
        pbar = tqdm(
            train_loader,
            desc=f'Epoch {self.current_epoch + 1}/{self.epochs}',
            leave=False
        )
        
        self.optimizer.zero_grad(set_to_none=True)  # 使用set_to_none=True节省内存
        
        for batch_idx, batch in enumerate(pbar):
            # 内存清理和监控（降低频率）
            if batch_idx % self.memory_cleanup_interval == 0 and batch_idx > 0:
                self._cleanup_memory()
                
                # 检查内存泄漏
                if self.memory_monitor:
                    if self.memory_monitor.check_memory_leak(self.memory_leak_threshold):
                        # 强制清理并重置基线
                        self._cleanup_memory(force=True)
                        self.memory_monitor.reset_baseline()
            
            # 对于非预取器加载的数据，需要手动移动到设备
            if not self.use_data_prefetcher:
                batch = {k: v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
            
            images = batch['image']
            masks = batch['mask']
            
            # 转换为channels last格式（如果启用）
            if self.use_channels_last:
                images = images.to(memory_format=torch.channels_last)
            
            # 前向传播
            if self.use_amp:
                with autocast():
                    outputs = self.model(images)
                    loss = self.loss_fn(outputs, masks)
                    loss = loss / self.accumulation_steps
            else:
                outputs = self.model(images)
                loss = self.loss_fn(outputs, masks)
                loss = loss / self.accumulation_steps
            
            # 反向传播
            if self.use_amp:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # 更新参数
            if (batch_idx + 1) % self.accumulation_steps == 0:
                if self.clip_gradient:
                    if self.use_amp:
                        self.scaler.unscale_(self.optimizer)
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
                        self.optimizer.step()
                else:
                    if self.use_amp:
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                    else:
                        self.optimizer.step()
                
                self.optimizer.zero_grad(set_to_none=True)
                self.global_step += 1
                
                # 基于步数的保存检查
                if self.enable_step_saving and self.global_step % self.save_every_n_steps == 0:
                    self._save_step_checkpoint()
            
            # 更新指标
            train_loss.update(loss.item() * self.accumulation_steps)
            
            # 显式删除不需要的变量
            del outputs, loss
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f'{train_loss.avg:.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
            })
            
            # TensorBoard记录
            if self.tb_logger and self.global_step % 10 == 0:
                self.tb_logger.log_scalar('Train/Loss_Step', train_loss.avg, self.global_step)
                self.tb_logger.log_scalar('Train/LR', self.optimizer.param_groups[0]['lr'], self.global_step)
        
        # 更新学习率调度器
        if self.scheduler and not isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            self.scheduler.step()
        
        # 最终内存清理
        self._cleanup_memory()
        
        if self.memory_monitor:
            self.memory_monitor.log_memory_usage(f"Epoch {self.current_epoch + 1} 训练结束 - ")
        
        return {
            'train_loss': train_loss.avg,
            'lr': self.optimizer.param_groups[0]['lr']
        }
    
    def validate(self) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()
        
        val_loss = RunningAverage()
        
        # 使用流式计算指标，避免累积大量数据
        metric_calculator = StreamingMetricsCalculator()
        
        sample_images = []
        sample_targets = []
        sample_predictions = []
        
        # 预先清理内存
        self._cleanup_memory()
        
        if self.memory_monitor:
            self.memory_monitor.log_memory_usage("验证开始 - ")
        
        # 使用数据预取器提高效率
        if self.use_data_prefetcher:
            from src.data.dataloader import DataPrefetcher
            val_loader = DataPrefetcher(self.val_loader, self.device)
            self.logger.debug("验证阶段使用数据预取器")
        else:
            val_loader = self.val_loader
        
        # 限制验证批次数量以控制内存使用
        validation_config = self.config.get('training', {}).get('validation', {})
        max_val_batches = min(len(val_loader), validation_config.get('max_batches', 500))
        
        # 转换为列表更耗内存，改为计数器方式
        batch_count = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(val_loader, desc='验证中', leave=False)):
                if batch_idx >= max_val_batches:
                    break
                    
                # 对于非预取器加载的数据，需要手动移动到设备
                if not self.use_data_prefetcher:
                    batch = {k: v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                
                images = batch['image']
                masks = batch['mask']
                
                # 转换为channels last格式（如果启用）
                if self.use_channels_last:
                    images = images.to(memory_format=torch.channels_last)
                
                if self.use_amp:
                    with autocast():
                        outputs = self.model(images)
                        loss = self.loss_fn(outputs, masks)
                else:
                    outputs = self.model(images)
                    loss = self.loss_fn(outputs, masks)
                
                val_loss.update(loss.item())
                
                # 应用激活函数
                if hasattr(self.model, 'final_activation'):
                    predictions = outputs
                else:
                    predictions = torch.sigmoid(outputs)
                
                # 流式计算指标，避免累积数据
                predictions_cpu = predictions.detach().cpu()
                targets_cpu = masks.detach().cpu()
                
                # 更新流式指标计算器
                metric_calculator.update(predictions_cpu, targets_cpu)
                
                # 收集少量样本用于可视化（只在第一个batch）
                if batch_idx == 0 and self.tb_logger:
                    sample_count = validation_config.get('sample_for_visualization', 2)
                    sample_images = images[:sample_count].detach().cpu()
                    sample_targets = masks[:sample_count].detach().cpu()
                    sample_predictions = predictions[:sample_count].detach().cpu()
                
                # 立即清理不需要的变量
                del outputs, predictions, predictions_cpu, targets_cpu, loss
                
                # 增量清理内存
                if batch_idx > 0 and batch_idx % 50 == 0:  # 每50个批次清理一次
                    self._cleanup_memory()
                
                batch_count += 1
        
        self.logger.debug(f"完成验证，处理了 {batch_count} 批次")
        
        # 获取最终指标
        try:
            metrics = metric_calculator.compute()
        except Exception as e:
            self.logger.warning(f"指标计算失败: {e}，使用默认值")
            metrics = {'val_iou': 0.0, 'val_dice': 0.0, 'val_precision': 0.0, 'val_recall': 0.0, 'val_f1': 0.0}
        
        metrics['val_loss'] = val_loss.avg
        
        # TensorBoard记录验证指标
        if self.tb_logger:
            # 记录所有验证指标
            self.tb_logger.log_training_metrics(metrics, self.current_epoch, prefix="Validation")
            
            # 记录预测样本可视化（如果有）
            if len(sample_images) > 0:
                try:
                    self.tb_logger.log_prediction_samples(
                        sample_images, sample_targets, sample_predictions, 
                        step=self.current_epoch
                    )
                except Exception as e:
                    self.logger.warning(f"可视化记录失败: {e}")
        
        # 如果使用ReduceLROnPlateau调度器
        if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            self.scheduler.step(metrics.get(self.best_metric_name, 0))
        
        # 清理内存
        if len(sample_images) > 0:
            del sample_images, sample_targets, sample_predictions
        del metric_calculator
        self._cleanup_memory(force=True)
        
        if self.memory_monitor:
            self.memory_monitor.log_memory_usage("验证结束 - ")
        
        return metrics
    
    def train(self) -> List[Dict[str, float]]:
        """完整训练流程"""
        self.logger.info(f"开始训练，总共 {self.epochs} 个epoch")
        self.logger.info(f"训练集大小: {len(self.train_loader.dataset)}")
        self.logger.info(f"验证集大小: {len(self.val_loader.dataset)}")
        
        if self.memory_monitor:
            self.memory_monitor.log_memory_usage("训练开始前 - ")
        
        # 自动恢复训练
        if self.auto_resume:
            self.auto_resume_training()
        
        # 记录模型图结构（如果使用TensorBoard）
        if self.tb_logger:
            try:
                # 获取一个样本用于模型图记录
                sample_batch = next(iter(self.train_loader))
                sample_input = sample_batch['image'][:1].to(self.device)
                self.tb_logger.log_model_graph(self.model, sample_input)
                self.logger.info("已记录模型图结构到TensorBoard")
                del sample_batch, sample_input
                self._cleanup_memory()
            except Exception as e:
                self.logger.warning(f"无法记录模型图: {e}")
        
        start_time = time.time()
        
        for epoch in range(self.current_epoch, self.epochs):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # 保存随机数状态
            self._save_random_state()
            
            # 训练一个epoch
            train_metrics = self.train_epoch()
            
            # 验证
            if epoch % self.eval_interval == 0:
                val_metrics = self.validate()
            else:
                val_metrics = {}
            
            # 合并指标
            epoch_metrics = {
                'epoch': epoch + 1,
                'time': time.time() - epoch_start_time,
                **train_metrics,
                **val_metrics
            }
            
            self.history.append(epoch_metrics)
            
            # TensorBoard记录epoch级别的指标
            if self.tb_logger:
                # 记录训练指标
                self.tb_logger.log_training_metrics(train_metrics, epoch, prefix="Train_Epoch")
                
                # 记录损失对比
                if val_metrics:
                    loss_dict = {
                        'Train': train_metrics.get('train_loss', 0),
                        'Validation': val_metrics.get('val_loss', 0)
                    }
                    self.tb_logger.log_scalars('Loss/Comparison', loss_dict, epoch)
                    
                    # 记录关键指标
                    key_metrics = {
                        'IoU': val_metrics.get('val_iou', 0),
                        'Dice': val_metrics.get('val_dice', 0),
                        'F1': val_metrics.get('val_f1', 0)
                    }
                    self.tb_logger.log_scalars('Metrics/Validation', key_metrics, epoch)
                
                # 记录内存使用情况
                if self.memory_monitor:
                    memory_summary = self.memory_monitor.get_memory_summary()
                    self.tb_logger.log_scalars('Memory/Usage', {
                        'CPU_Memory_MB': memory_summary['current_cpu_memory_mb'],
                        'GPU_Memory_MB': memory_summary['current_gpu_memory_mb'],
                        'GPU_Utilization_%': memory_summary['gpu_memory_utilization']
                    }, epoch)
                
                # 强制刷新TensorBoard缓冲区
                self.tb_logger.flush()
            
            # 记录日志
            log_str = f"Epoch {epoch + 1}/{self.epochs}"
            for key, value in epoch_metrics.items():
                if key not in ['epoch']:
                    if isinstance(value, float):
                        log_str += f" | {key}: {value:.4f}"
                    else:
                        log_str += f" | {key}: {value}"
            
            self.logger.info(log_str)
            
            # 检查是否需要更新最佳模型
            model_saved_this_epoch = False
            if val_metrics and self.best_metric_name in val_metrics:
                current_metric = val_metrics[self.best_metric_name]
                is_better = (
                    (self.mode == 'max' and current_metric > self.best_metric) or
                    (self.mode == 'min' and current_metric < self.best_metric)
                )
                
                if is_better:
                    self.best_metric = current_metric
                    self._save_best_model()
                    model_saved_this_epoch = True
                    self.logger.info(f"保存新的最佳模型，{self.best_metric_name}: {current_metric:.4f}")
            
            # 强制定期保存检查点（不管性能是否提升）
            should_save_checkpoint = (
                (epoch + 1) % self.save_interval == 0 or  # 定期保存
                epoch == 0 or  # 第一个epoch
                epoch == self.epochs - 1 or  # 最后一个epoch
                model_saved_this_epoch  # 保存了最佳模型时也保存检查点
            )
            
            if should_save_checkpoint:
                checkpoint_name = f'checkpoint_epoch_{epoch + 1}.pth'
                self._save_checkpoint(checkpoint_name)
                self.logger.info(f"已保存检查点: {checkpoint_name}")
                
            # 总是保存最新检查点（每个epoch）
            if self.save_last:
                self._save_checkpoint('last_checkpoint.pth', is_last=True)
            
            # 每隔几个epoch保存一个带时间戳的检查点（防止意外删除）
            if (epoch + 1) % (self.save_interval * 2) == 0:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                backup_name = f'backup_epoch_{epoch + 1}_{timestamp}.pth'
                self._save_checkpoint(backup_name)
                self.logger.info(f"已保存备份检查点: {backup_name}")
            
            # 清理旧检查点（但保留备份）
            self._cleanup_old_checkpoints()
            
            # 定期强制内存清理和内存状态报告
            if (epoch + 1) % 3 == 0:  # 更频繁的内存清理
                self._cleanup_memory(force=True)
                if self.memory_monitor:
                    memory_summary = self.memory_monitor.get_memory_summary()
                    self.logger.info(
                        f"内存状态报告 - 当前: {memory_summary['current_cpu_memory_mb']:.1f}MB, "
                        f"峰值: {memory_summary['peak_cpu_memory_mb']:.1f}MB, "
                        f"增长: {memory_summary['memory_growth_mb']:.1f}MB, "
                        f"GPU利用率: {memory_summary['gpu_memory_utilization']:.1f}%"
                    )
                    
                    # 如果内存增长过快，强制清理
                    if memory_summary['memory_growth_mb'] > self.memory_leak_threshold:
                        self.logger.warning("检测到内存增长过快，执行强制清理")
                        for _ in range(3):  # 多次清理
                            self._cleanup_memory(force=True)
                        self.memory_monitor.reset_baseline()
        
        total_time = time.time() - start_time
        self.logger.info(f"训练完成！总用时: {total_time:.2f}秒")
        self.logger.info(f"最佳 {self.best_metric_name}: {self.best_metric:.4f}")
        
        # 训练结束时保存最终检查点
        final_checkpoint_name = f'final_checkpoint_epoch_{self.current_epoch + 1}.pth'
        self._save_checkpoint(final_checkpoint_name)
        self.logger.info(f"已保存最终检查点: {final_checkpoint_name}")
        
        # 最终内存报告
        if self.memory_monitor:
            memory_summary = self.memory_monitor.get_memory_summary()
            self.logger.info(f"最终内存使用情况: {memory_summary}")
        
        # 关闭TensorBoard记录器
        if self.tb_logger:
            self.tb_logger.close()
            self.logger.info("TensorBoard记录器已关闭")
        
        # 最终清理
        self._cleanup_memory(force=True)
        
        return self.history