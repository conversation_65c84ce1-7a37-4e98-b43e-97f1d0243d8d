"""
分割模型模块
定义铁路分割模型
"""

from typing import Dict, Any, Optional
import torch
import torch.nn as nn
import segmentation_models_pytorch as smp
from ..core.base import BaseModel
from ..core.registry import register_model


@register_model('segmentation_model')
class SegmentationModel(BaseModel):
    """
    语义分割模型
    支持多种架构和编码器
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模型
        
        Args:
            config: 模型配置
        """
        super().__init__(config)
        
        # 获取模型参数
        architecture = config.get('architecture', 'pan')
        encoder_name = config.get('encoder', 'efficientnet-b4')
        encoder_weights = config.get('encoder_weights', 'imagenet')
        num_classes = config.get('classes', 3)
        activation = config.get('activation', 'sigmoid')
        
        # 创建模型
        self.model = self._create_model(
            architecture=architecture,
            encoder_name=encoder_name,
            encoder_weights=encoder_weights,
            num_classes=num_classes,
            activation=activation
        )
        
        # 保存配置
        self.architecture = architecture
        self.encoder_name = encoder_name
        self.num_classes = num_classes
        
    def _create_model(self,
                      architecture: str,
                      encoder_name: str,
                      encoder_weights: Optional[str],
                      num_classes: int,
                      activation: Optional[str]) -> nn.Module:
        """
        创建分割模型
        
        Args:
            architecture: 模型架构
            encoder_name: 编码器名称
            encoder_weights: 预训练权重
            num_classes: 类别数
            activation: 激活函数
            
        Returns:
            模型实例
        """
        # 通用参数
        params = {
            'encoder_name': encoder_name,
            'encoder_weights': encoder_weights,
            'in_channels': 3,
            'classes': num_classes,
            'activation': activation
        }
        
        # 根据架构创建模型
        if architecture.lower() == 'unet':
            model = smp.Unet(**params)
        elif architecture.lower() == 'unet++' or architecture.lower() == 'unetplusplus':
            model = smp.UnetPlusPlus(**params)
        elif architecture.lower() == 'pan':
            model = smp.PAN(**params)
        elif architecture.lower() == 'fpn':
            model = smp.FPN(**params)
        elif architecture.lower() == 'pspnet':
            model = smp.PSPNet(**params)
        elif architecture.lower() == 'deeplabv3':
            model = smp.DeepLabV3(**params)
        elif architecture.lower() == 'deeplabv3+' or architecture.lower() == 'deeplabv3plus':
            model = smp.DeepLabV3Plus(**params)
        elif architecture.lower() == 'linknet':
            model = smp.Linknet(**params)
        elif architecture.lower() == 'manet':
            model = smp.MAnet(**params)
        else:
            raise ValueError(f"不支持的架构: {architecture}")
        
        return model
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像 (B, C, H, W)
            
        Returns:
            分割结果 (B, num_classes, H, W)
        """
        return self.model(x)
    
    def count_parameters(self) -> int:
        """
        计算模型参数量
        
        Returns:
            参数总数
        """
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_encoder_features(self, x: torch.Tensor) -> list:
        """
        获取编码器特征
        
        Args:
            x: 输入图像
            
        Returns:
            编码器各层特征列表
        """
        features = []
        
        # 获取编码器
        encoder = self.model.encoder
        
        # 逐层提取特征
        stages = encoder.get_stages()
        x = encoder.conv_stem(x)
        x = encoder.bn1(x)
        x = encoder.act1(x)
        features.append(x)
        
        x = encoder.blocks[0](x)
        features.append(x)
        
        x = encoder.blocks[1](x)
        features.append(x)
        
        x = encoder.blocks[2](x)
        features.append(x)
        
        x = encoder.blocks[3](x)
        features.append(x)
        
        x = encoder.blocks[4](x)
        features.append(x)
        
        x = encoder.blocks[5](x)
        features.append(x)
        
        x = encoder.blocks[6](x)
        features.append(x)
        
        return features
    
    def freeze_encoder(self):
        """冻结编码器参数"""
        for param in self.model.encoder.parameters():
            param.requires_grad = False
    
    def unfreeze_encoder(self):
        """解冻编码器参数"""
        for param in self.model.encoder.parameters():
            param.requires_grad = True
    
    def get_params_groups(self, lr: float, encoder_lr_mult: float = 0.1) -> list:
        """
        获取参数组（用于不同学习率）
        
        Args:
            lr: 基础学习率
            encoder_lr_mult: 编码器学习率倍数
            
        Returns:
            参数组列表
        """
        encoder_params = []
        decoder_params = []
        
        # 分离编码器和解码器参数
        for name, param in self.named_parameters():
            if 'encoder' in name:
                encoder_params.append(param)
            else:
                decoder_params.append(param)
        
        return [
            {'params': encoder_params, 'lr': lr * encoder_lr_mult},
            {'params': decoder_params, 'lr': lr}
        ]


def create_segmentation_model(config: Dict[str, Any]) -> SegmentationModel:
    """
    创建分割模型的工厂函数
    
    Args:
        config: 模型配置
        
    Returns:
        分割模型实例
    """
    return SegmentationModel(config)


@register_model('multi_scale_segmentation')
class MultiScaleSegmentationModel(SegmentationModel):
    """
    多尺度分割模型
    在不同尺度上进行预测并融合结果
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多尺度模型
        
        Args:
            config: 模型配置
        """
        super().__init__(config)
        
        # 多尺度配置
        self.scales = config.get('scales', [0.5, 1.0, 1.5])
        self.fusion_method = config.get('fusion_method', 'mean')
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        多尺度前向传播
        
        Args:
            x: 输入图像 (B, C, H, W)
            
        Returns:
            融合后的分割结果 (B, num_classes, H, W)
        """
        B, C, H, W = x.shape
        
        # 存储不同尺度的预测结果
        predictions = []
        
        for scale in self.scales:
            # 调整图像尺寸
            if scale != 1.0:
                scaled_h = int(H * scale)
                scaled_w = int(W * scale)
                scaled_x = nn.functional.interpolate(
                    x, size=(scaled_h, scaled_w), 
                    mode='bilinear', align_corners=False
                )
            else:
                scaled_x = x
            
            # 预测
            pred = self.model(scaled_x)
            
            # 调整预测结果尺寸
            if scale != 1.0:
                pred = nn.functional.interpolate(
                    pred, size=(H, W), 
                    mode='bilinear', align_corners=False
                )
            
            predictions.append(pred)
        
        # 融合预测结果
        predictions = torch.stack(predictions, dim=0)
        
        if self.fusion_method == 'mean':
            fused = torch.mean(predictions, dim=0)
        elif self.fusion_method == 'max':
            fused, _ = torch.max(predictions, dim=0)
        elif self.fusion_method == 'weighted':
            # 使用学习的权重融合
            weights = nn.functional.softmax(self.fusion_weights, dim=0)
            weights = weights.view(-1, 1, 1, 1, 1)
            fused = torch.sum(predictions * weights, dim=0)
        else:
            raise ValueError(f"不支持的融合方法: {self.fusion_method}")
        
        return fused 