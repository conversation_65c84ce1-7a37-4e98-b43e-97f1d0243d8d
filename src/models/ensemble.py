"""
集成学习模块
基于notebook中的集成学习方法实现
"""

from typing import Dict, List, Any, Optional, Union, Tuple
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import segmentation_models_pytorch as smp
from ..core.base import BaseModel
from ..core.registry import register_model
from ..utils.metrics import iou_coef


@register_model('ensemble_model')
class EnsembleModel(BaseModel):
    """
    集成学习模型
    基于notebook中的实现，支持多个模型的加权平均融合
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化集成模型
        
        Args:
            config: 模型配置
        """
        super().__init__(config)
        
        # 集成配置
        self.ensemble_config = config.get('ensemble', {})
        self.models_config = self.ensemble_config.get('models', [])
        self.fusion_method = self.ensemble_config.get('fusion_method', 'weighted_average')
        self.num_classes = config.get('classes', 3)
        
        # 创建子模型
        self.models = nn.ModuleList()
        self.model_names = []
        self._create_models()
        
        # 融合权重（每个类别每个模型的权重）
        self.fusion_weights = None
        self._init_fusion_weights()
        
        # 是否已训练权重
        self.weights_optimized = False
        
    def _create_models(self):
        """创建子模型"""
        # 默认配置：基于notebook的三个模型
        if not self.models_config:
            self.models_config = [
                {
                    'name': 'efficientnet_b4',
                    'architecture': 'pan',
                    'encoder': 'tu-tf_efficientnet_b4_ns',
                    'encoder_weights': 'noisy-student'
                },
                {
                    'name': 'eca_nfnet_l2',
                    'architecture': 'pan', 
                    'encoder': 'tu-eca_nfnet_l2',
                    'encoder_weights': 'imagenet'
                },
                {
                    'name': 'seresnet152d',
                    'architecture': 'pan',
                    'encoder': 'tu-seresnet152d', 
                    'encoder_weights': 'imagenet'
                }
            ]
        
        for model_cfg in self.models_config:
            # 创建模型
            model = self._create_single_model(model_cfg)
            self.models.append(model)
            self.model_names.append(model_cfg['name'])
    
    def _create_single_model(self, config: Dict[str, Any]) -> nn.Module:
        """
        创建单个分割模型
        
        Args:
            config: 模型配置
            
        Returns:
            模型实例
        """
        architecture = config.get('architecture', 'pan')
        encoder_name = config.get('encoder', 'efficientnet-b4')
        encoder_weights = config.get('encoder_weights', 'imagenet')
        
        # 通用参数
        params = {
            'encoder_name': encoder_name,
            'encoder_weights': encoder_weights,
            'in_channels': 3,
            'classes': self.num_classes,
            'activation': None  # 不使用激活函数，返回logits
        }
        
        # 根据架构创建模型
        if architecture.lower() == 'pan':
            model = smp.PAN(**params)
        elif architecture.lower() == 'unet':
            model = smp.Unet(**params)
        elif architecture.lower() == 'fpn':
            model = smp.FPN(**params)
        elif architecture.lower() == 'deeplabv3plus':
            model = smp.DeepLabV3Plus(**params)
        else:
            raise ValueError(f"不支持的架构: {architecture}")
        
        return model
    
    def _init_fusion_weights(self):
        """初始化融合权重"""
        num_models = len(self.models)
        
        if self.fusion_method == 'equal_weight':
            # 平均权重
            weights = torch.ones(self.num_classes, num_models) / num_models
        elif self.fusion_method == 'weighted_average':
            # 初始化权重（后续会优化）
            weights = torch.ones(self.num_classes, num_models) / num_models
        else:
            raise ValueError(f"不支持的融合方法: {self.fusion_method}")
        
        self.register_parameter('fusion_weights', nn.Parameter(weights))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像 (B, C, H, W)
            
        Returns:
            融合后的预测结果 (B, num_classes, H, W)
        """
        # 获取所有模型的预测
        predictions = []
        for model in self.models:
            pred = model(x)
            predictions.append(pred)
        
        # 融合预测结果
        fused_pred = self._fuse_predictions(predictions)
        
        return fused_pred
    
    def _fuse_predictions(self, predictions: List[torch.Tensor]) -> torch.Tensor:
        """
        融合多个模型的预测结果
        
        Args:
            predictions: 模型预测列表
            
        Returns:
            融合后的预测结果
        """
        if self.fusion_method == 'equal_weight':
            # 简单平均
            fused = torch.stack(predictions, dim=0).mean(dim=0)
        
        elif self.fusion_method == 'weighted_average':
            # 加权平均
            batch_size, num_classes, height, width = predictions[0].shape
            
            # 将权重扩展到与预测相同的维度
            weights = self.fusion_weights.unsqueeze(0).unsqueeze(-1).unsqueeze(-1)  # (1, C, M, 1, 1)
            weights = weights.expand(batch_size, -1, -1, height, width)  # (B, C, M, H, W)
            
            # 堆叠预测结果
            stacked_preds = torch.stack(predictions, dim=2)  # (B, C, M, H, W)
            
            # 加权求和
            fused = torch.sum(stacked_preds * weights, dim=2)  # (B, C, H, W)
        
        else:
            raise ValueError(f"不支持的融合方法: {self.fusion_method}")
        
        return fused
    
    def optimize_fusion_weights(self, 
                              dataloader: torch.utils.data.DataLoader,
                              device: torch.device,
                              num_iterations: int = 100) -> Dict[int, Dict[str, float]]:
        """
        优化融合权重
        基于notebook中的网格搜索方法
        
        Args:
            dataloader: 验证数据加载器
            device: 设备
            num_iterations: 搜索迭代次数
            
        Returns:
            每个类别的最优权重
        """
        print("开始优化集成权重...")
        
        # 设置为评估模式
        self.eval()
        
        # 收集所有模型的预测和真实标签
        all_predictions = [[] for _ in range(len(self.models))]
        all_targets = []
        
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="收集预测结果"):
                images = batch['image'].to(device)
                targets = batch['mask'].to(device)
                
                # 获取每个模型的预测
                for i, model in enumerate(self.models):
                    pred = model(images)
                    pred = torch.sigmoid(pred)  # 转换为概率
                    all_predictions[i].append(pred.cpu())
                
                all_targets.append(targets.cpu())
        
        # 合并所有批次
        for i in range(len(self.models)):
            all_predictions[i] = torch.cat(all_predictions[i], dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        
        # 为每个类别优化权重
        best_weights = {}
        
        for class_idx in range(self.num_classes):
            print(f"\n优化类别 {class_idx} 的权重...")
            
            best_iou = 0.0
            best_class_weights = None
            
            # 网格搜索（基于notebook的方法）
            with tqdm(total=num_iterations, desc=f"类别 {class_idx}") as pbar:
                for _ in range(num_iterations):
                    # 随机生成权重
                    weights = np.random.dirichlet(np.ones(len(self.models)))
                    
                    # 计算加权预测
                    weighted_pred = torch.zeros_like(all_predictions[0][:, class_idx:class_idx+1])
                    for i, weight in enumerate(weights):
                        weighted_pred += weight * all_predictions[i][:, class_idx:class_idx+1]
                    
                    # 计算IoU
                    target_class = all_targets[:, class_idx:class_idx+1]
                    iou = iou_coef(target_class, weighted_pred)
                    
                    # 更新最佳权重
                    if iou > best_iou:
                        best_iou = iou
                        best_class_weights = weights.copy()
                    
                    pbar.update(1)
            
            # 保存最佳权重
            best_weights[class_idx] = {
                'weights': best_class_weights,
                'iou': best_iou
            }
            
            print(f"类别 {class_idx} 最佳权重: {best_class_weights}")
            print(f"类别 {class_idx} 最佳IoU: {best_iou:.4f}")
        
        # 更新模型权重
        new_weights = torch.zeros(self.num_classes, len(self.models))
        for class_idx, weight_info in best_weights.items():
            new_weights[class_idx] = torch.tensor(weight_info['weights'])
        
        self.fusion_weights.data = new_weights.to(self.fusion_weights.device)
        self.weights_optimized = True
        
        print("\n权重优化完成！")
        return best_weights
    
    def load_model_weights(self, weights_paths: List[str]):
        """
        加载各个子模型的权重
        
        Args:
            weights_paths: 权重文件路径列表
        """
        if len(weights_paths) != len(self.models):
            raise ValueError(f"权重文件数量({len(weights_paths)})与模型数量({len(self.models)})不匹配")
        
        for i, (model, weights_path) in enumerate(zip(self.models, weights_paths)):
            print(f"加载模型 {self.model_names[i]} 权重: {weights_path}")
            state_dict = torch.load(weights_path, map_location='cpu')
            
            # 处理可能的键名不匹配
            if 'model' in state_dict:
                state_dict = state_dict['model']
            elif 'state_dict' in state_dict:
                state_dict = state_dict['state_dict']
            
            model.load_state_dict(state_dict, strict=False)
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'num_models': len(self.models),
            'model_names': self.model_names,
            'fusion_method': self.fusion_method,
            'num_classes': self.num_classes,
            'weights_optimized': self.weights_optimized,
            'fusion_weights': self.fusion_weights.detach().cpu().numpy().tolist() if self.fusion_weights is not None else None
        }


@register_model('ensemble_predictor')
class EnsemblePredictor:
    """
    集成预测器
    用于对新图像进行集成预测
    """
    
    def __init__(self, 
                 models: List[nn.Module],
                 fusion_weights: torch.Tensor,
                 device: torch.device):
        """
        初始化集成预测器
        
        Args:
            models: 模型列表
            fusion_weights: 融合权重 (num_classes, num_models)
            device: 设备
        """
        self.models = models
        self.fusion_weights = fusion_weights.to(device)
        self.device = device
        self.num_classes = fusion_weights.shape[0]
        
        # 设置为评估模式
        for model in self.models:
            model.eval()
    
    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """
        集成预测
        
        Args:
            x: 输入图像 (B, C, H, W)
            
        Returns:
            预测结果 (B, num_classes, H, W)
        """
        x = x.to(self.device)
        
        with torch.no_grad():
            # 获取所有模型的预测
            predictions = []
            for model in self.models:
                pred = model(x)
                pred = torch.sigmoid(pred)  # 转换为概率
                predictions.append(pred)
            
            # 融合预测
            batch_size, num_classes, height, width = predictions[0].shape
            
            # 将权重扩展到与预测相同的维度
            weights = self.fusion_weights.unsqueeze(0).unsqueeze(-1).unsqueeze(-1)  # (1, C, M, 1, 1)
            weights = weights.expand(batch_size, -1, -1, height, width)  # (B, C, M, H, W)
            
            # 堆叠预测结果
            stacked_preds = torch.stack(predictions, dim=2)  # (B, C, M, H, W)
            
            # 加权求和
            fused = torch.sum(stacked_preds * weights, dim=2)  # (B, C, H, W)
        
        return fused 