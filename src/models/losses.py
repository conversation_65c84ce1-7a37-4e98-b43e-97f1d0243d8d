"""
损失函数模块
定义用于分割任务的各种损失函数
"""

from typing import Optional, List, Dict, Any
import torch
import torch.nn as nn
import torch.nn.functional as F
from ..core.registry import register_loss


@register_loss('dice_loss')
class DiceLoss(nn.Module):
    """
    Dice损失函数
    用于处理类别不平衡问题
    """
    
    def __init__(self, 
                 smooth: float = 1.0,
                 eps: float = 1e-7,
                 reduction: str = 'mean'):
        """
        初始化Dice损失
        
        Args:
            smooth: 平滑参数
            eps: 防止除零的小值
            reduction: 归约方式 ('none', 'mean', 'sum')
        """
        super().__init__()
        self.smooth = smooth
        self.eps = eps
        self.reduction = reduction
    
    def forward(self, 
                pred: torch.Tensor, 
                target: torch.Tensor,
                weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算Dice损失
        
        Args:
            pred: 预测值 (B, C, H, W)
            target: 真实值 (B, C, H, W)
            weight: 类别权重 (C,)
            
        Returns:
            损失值
        """
        # 确保预测值在[0, 1]范围内
        if pred.dim() != target.dim():
            raise ValueError(f"预测值和目标值维度不匹配: {pred.dim()} vs {target.dim()}")
        
        # 计算交集和并集
        intersection = (pred * target).sum(dim=(2, 3))
        cardinality = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3))
        
        # 计算Dice系数
        dice_score = (2.0 * intersection + self.smooth) / (cardinality + self.smooth + self.eps)
        
        # 应用类别权重
        if weight is not None:
            dice_score = dice_score * weight.unsqueeze(0)
        
        # 计算损失
        dice_loss = 1.0 - dice_score
        
        # 归约
        if self.reduction == 'none':
            return dice_loss
        elif self.reduction == 'mean':
            return dice_loss.mean()
        elif self.reduction == 'sum':
            return dice_loss.sum()
        else:
            raise ValueError(f"不支持的归约方式: {self.reduction}")


@register_loss('bce_dice_loss')
@register_loss('dice_bce')
class BCEDiceLoss(nn.Module):
    """
    组合BCE和Dice损失
    同时优化像素级别和区域级别的指标
    """
    
    def __init__(self,
                 bce_weight: float = 0.5,
                 dice_weight: float = 0.5,
                 smooth: float = 1.0,
                 eps: float = 1e-7):
        """
        初始化BCE-Dice损失
        
        Args:
            bce_weight: BCE损失权重
            dice_weight: Dice损失权重
            smooth: Dice损失的平滑参数
            eps: 防止除零的小值
        """
        super().__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.bce_loss = nn.BCEWithLogitsLoss(reduction='mean')
        self.dice_loss = DiceLoss(smooth=smooth, eps=eps, reduction='mean')
    
    def forward(self,
                pred: torch.Tensor,
                target: torch.Tensor,
                weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算BCE-Dice组合损失
        
        Args:
            pred: 预测值 (B, C, H, W) - logits
            target: 真实值 (B, C, H, W)
            weight: 类别权重 (C,)
            
        Returns:
            损失值
        """
        # BCE损失（使用logits）
        bce = self.bce_loss(pred, target)
        
        # Dice损失（使用sigmoid激活后的预测值）
        pred_sigmoid = torch.sigmoid(pred)
        dice = self.dice_loss(pred_sigmoid, target, weight)
        
        # 组合损失
        loss = self.bce_weight * bce + self.dice_weight * dice
        
        return loss


@register_loss('focal_loss')
class FocalLoss(nn.Module):
    """
    Focal损失函数
    用于处理困难样本和类别不平衡
    """
    
    def __init__(self,
                 alpha: Optional[List[float]] = None,
                 gamma: float = 2.0,
                 reduction: str = 'mean'):
        """
        初始化Focal损失
        
        Args:
            alpha: 类别权重
            gamma: 聚焦参数
            reduction: 归约方式
        """
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self,
                pred: torch.Tensor,
                target: torch.Tensor) -> torch.Tensor:
        """
        计算Focal损失
        
        Args:
            pred: 预测值 (B, C, H, W) - logits
            target: 真实值 (B, C, H, W)
            
        Returns:
            损失值
        """
        # 计算BCE损失
        ce_loss = F.binary_cross_entropy_with_logits(
            pred, target, reduction='none'
        )
        
        # 计算pt
        p = torch.sigmoid(pred)
        pt = p * target + (1 - p) * (1 - target)
        
        # 计算focal权重
        focal_weight = (1 - pt) ** self.gamma
        
        # 应用类别权重
        if self.alpha is not None:
            alpha_t = torch.tensor(self.alpha, device=pred.device)
            alpha_t = alpha_t.view(1, -1, 1, 1)
            focal_weight = focal_weight * alpha_t
        
        # 计算focal损失
        focal_loss = focal_weight * ce_loss
        
        # 归约
        if self.reduction == 'none':
            return focal_loss
        elif self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            raise ValueError(f"不支持的归约方式: {self.reduction}")


@register_loss('combined_loss')
class CombinedLoss(nn.Module):
    """
    组合多种损失函数
    支持动态权重调整
    """
    
    def __init__(self,
                 losses: List[Dict[str, Any]],
                 weights: Optional[List[float]] = None):
        """
        初始化组合损失
        
        Args:
            losses: 损失函数配置列表
                   [{'type': 'dice_loss', 'smooth': 1.0}, ...]
            weights: 各损失函数的权重
        """
        super().__init__()
        self.losses = nn.ModuleList()
        
        # 创建损失函数
        from ..core.registry import LOSS_REGISTRY
        for loss_cfg in losses:
            loss_type = loss_cfg.pop('type')
            loss_fn = LOSS_REGISTRY.get(loss_type)(**loss_cfg)
            self.losses.append(loss_fn)
        
        # 设置权重
        if weights is None:
            weights = [1.0] * len(self.losses)
        self.weights = weights
    
    def forward(self,
                pred: torch.Tensor,
                target: torch.Tensor,
                **kwargs) -> torch.Tensor:
        """
        计算组合损失
        
        Args:
            pred: 预测值
            target: 真实值
            **kwargs: 其他参数
            
        Returns:
            损失值
        """
        total_loss = 0.0
        
        for loss_fn, weight in zip(self.losses, self.weights):
            loss = loss_fn(pred, target, **kwargs)
            total_loss += weight * loss
        
        return total_loss


@register_loss('lovasz_loss')
class LovaszLoss(nn.Module):
    """
    Lovasz损失函数
    IoU的平滑近似，适用于分割任务
    """
    
    def __init__(self, per_image: bool = False):
        """
        初始化Lovasz损失
        
        Args:
            per_image: 是否对每张图像单独计算损失
        """
        super().__init__()
        self.per_image = per_image
    
    def lovasz_grad(self, gt_sorted):
        """计算Lovasz梯度"""
        p = len(gt_sorted)
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.float().cumsum(0)
        union = gts + (1 - gt_sorted).float().cumsum(0)
        jaccard = 1. - intersection / union
        if p > 1:
            jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
        return jaccard
    
    def lovasz_softmax_flat(self, probas, labels):
        """Lovasz损失的平坦版本"""
        C = probas.size(1)
        losses = []
        for c in range(C):
            fg = (labels == c).float()
            if fg.sum() == 0:
                continue
            errors = (fg - probas[:, c]).abs()
            errors_sorted, perm = torch.sort(errors, 0, descending=True)
            perm = perm.data
            fg_sorted = fg[perm]
            losses.append(torch.dot(errors_sorted, self.lovasz_grad(fg_sorted)))
        return torch.stack(losses).mean() if losses else 0.
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算Lovasz损失
        
        Args:
            pred: 预测值 (B, C, H, W) - 概率值
            target: 真实值 (B, C, H, W)
            
        Returns:
            损失值
        """
        if self.per_image:
            losses = []
            for prob, lab in zip(pred, target):
                losses.append(self.lovasz_softmax_flat(
                    prob.flatten(1), lab.flatten(1)
                ))
            return torch.stack(losses).mean()
        else:
            return self.lovasz_softmax_flat(
                pred.flatten(0, 2), target.flatten(0, 2)
            ) 