"""
多标签分割损失函数
基于notebook实现的损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List
import segmentation_models_pytorch as smp
from ..core.registry import register_loss


@register_loss('multilabel_jaccard_loss')
class MultilabelJaccardLoss(nn.Module):
    """
    多标签Jaccard损失函数
    与notebook中的实现保持一致
    """
    
    def __init__(self, mode: str = 'multilabel', smooth: float = 0.0):
        """
        初始化多标签Jaccard损失
        
        Args:
            mode: 模式，'multilabel'表示多标签分割
            smooth: 平滑参数
        """
        super().__init__()
        self.loss_fn = smp.losses.JaccardLoss(mode=mode, smooth=smooth)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算损失
        
        Args:
            pred: 预测值 (B, C, H, W) - logits
            target: 真实值 (B, C, H, W)
            
        Returns:
            损失值
        """
        return self.loss_fn(pred, target)


@register_loss('multilabel_focal_loss')
class MultilabelFocalLoss(nn.Module):
    """
    多标签Focal损失函数
    用于处理类别不平衡问题
    """
    
    def __init__(self,
                 alpha: Optional[List[float]] = None,
                 gamma: float = 2.0,
                 mode: str = 'multilabel',
                 reduction: str = 'mean'):
        """
        初始化多标签Focal损失
        
        Args:
            alpha: 类别权重 [背景, 主轨道, 分叉轨道]
            gamma: 聚焦参数
            mode: 模式
            reduction: 归约方式
        """
        super().__init__()
        self.alpha = alpha if alpha is not None else [0.1, 0.3, 0.6]  # 默认权重
        self.gamma = gamma
        self.mode = mode
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算多标签Focal损失
        
        Args:
            pred: 预测值 (B, C, H, W) - logits
            target: 真实值 (B, C, H, W) - binary masks
            
        Returns:
            损失值
        """
        # 计算BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(
            pred, target, reduction='none'
        )
        
        # 计算概率
        p = torch.sigmoid(pred)
        
        # 计算pt
        pt = p * target + (1 - p) * (1 - target)
        
        # 计算focal权重
        focal_weight = (1 - pt) ** self.gamma
        
        # 应用类别权重
        if self.alpha is not None:
            alpha_t = torch.tensor(self.alpha, device=pred.device, dtype=pred.dtype)
            # 为每个类别应用不同的权重
            for c in range(len(self.alpha)):
                if c < pred.shape[1]:
                    focal_weight[:, c] = focal_weight[:, c] * alpha_t[c]
        
        # 计算focal损失
        focal_loss = focal_weight * bce_loss
        
        # 归约
        if self.reduction == 'none':
            return focal_loss
        elif self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            raise ValueError(f"不支持的归约方式: {self.reduction}")


@register_loss('multilabel_combined_loss')
class MultilabelCombinedLoss(nn.Module):
    """
    组合多标签损失函数
    结合Jaccard损失和Focal损失
    """
    
    def __init__(self,
                 jaccard_weight: float = 0.5,
                 focal_weight: float = 0.5,
                 alpha: Optional[List[float]] = None,
                 gamma: float = 2.0):
        """
        初始化组合损失
        
        Args:
            jaccard_weight: Jaccard损失权重
            focal_weight: Focal损失权重
            alpha: Focal损失的类别权重
            gamma: Focal损失的聚焦参数
        """
        super().__init__()
        self.jaccard_weight = jaccard_weight
        self.focal_weight = focal_weight
        
        # 初始化子损失
        self.jaccard_loss = MultilabelJaccardLoss(mode='multilabel')
        self.focal_loss = MultilabelFocalLoss(alpha=alpha, gamma=gamma)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算组合损失
        
        Args:
            pred: 预测值 (B, C, H, W) - logits
            target: 真实值 (B, C, H, W)
            
        Returns:
            损失值
        """
        jaccard = self.jaccard_loss(pred, target)
        focal = self.focal_loss(pred, target)
        
        total_loss = self.jaccard_weight * jaccard + self.focal_weight * focal
        
        return total_loss