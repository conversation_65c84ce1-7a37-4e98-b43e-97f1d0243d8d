"""
Segmentation model creation utilities.
"""

import torch
import torch.nn as nn
import segmentation_models_pytorch as smp


def create_model(architecture='unet', 
                backbone='resnet50', 
                num_classes=3,
                pretrained=True,
                **kwargs):
    """
    Create a segmentation model.
    
    Args:
        architecture: Model architecture ('unet', 'deeplabv3plus', 'fpn', 'pan', 'linknet')
        backbone: Encoder backbone name
        num_classes: Number of output classes
        pretrained: Whether to use pretrained weights
        **kwargs: Additional model parameters
    
    Returns:
        model: PyTorch model
    """
    
    # Model parameters
    model_params = {
        'encoder_name': backbone,
        'encoder_weights': 'imagenet' if pretrained else None,
        'in_channels': 3,
        'classes': num_classes,
    }
    
    # Update with additional parameters
    model_params.update(kwargs)
    
    # Create model based on architecture
    if architecture.lower() == 'unet':
        model = smp.Unet(**model_params)
    elif architecture.lower() == 'deeplabv3plus' or architecture.lower() == 'deeplabv3+':
        model = smp.DeepLabV3Plus(**model_params)
    elif architecture.lower() == 'fpn':
        model = smp.FPN(**model_params)
    elif architecture.lower() == 'pan':
        model = smp.PAN(**model_params)
    elif architecture.lower() == 'linknet':
        model = smp.Linknet(**model_params)
    elif architecture.lower() == 'pspnet':
        model = smp.PSPNet(**model_params)
    elif architecture.lower() == 'manet':
        model = smp.MAnet(**model_params)
    elif architecture.lower() == 'unetplusplus' or architecture.lower() == 'unet++':
        model = smp.UnetPlusPlus(**model_params)
    else:
        raise ValueError(f"Unknown architecture: {architecture}")
    
    return model


class SegmentationModel(nn.Module):
    """
    Wrapper class for segmentation models with additional functionality.
    """
    
    def __init__(self, architecture='unet', backbone='resnet50', 
                 num_classes=3, pretrained=True, **kwargs):
        super().__init__()
        
        self.architecture = architecture
        self.backbone = backbone
        self.num_classes = num_classes
        
        # Create base model
        self.model = create_model(
            architecture=architecture,
            backbone=backbone,
            num_classes=num_classes,
            pretrained=pretrained,
            **kwargs
        )
        
        # Add auxiliary outputs if needed
        self.aux_output = kwargs.get('aux_output', False)
        
    def forward(self, x):
        """Forward pass."""
        return self.model(x)
    
    def get_params_groups(self):
        """
        Get parameter groups for different learning rates.
        
        Returns:
            List of parameter groups
        """
        # Encoder parameters (backbone) - lower learning rate
        encoder_params = []
        decoder_params = []
        
        for name, param in self.model.named_parameters():
            if 'encoder' in name:
                encoder_params.append(param)
            else:
                decoder_params.append(param)
        
        return [
            {'params': encoder_params, 'lr_mult': 0.1},  # 10x lower LR for backbone
            {'params': decoder_params, 'lr_mult': 1.0}
        ]
    
    def freeze_encoder(self):
        """Freeze encoder weights."""
        for param in self.model.encoder.parameters():
            param.requires_grad = False
    
    def unfreeze_encoder(self):
        """Unfreeze encoder weights."""
        for param in self.model.encoder.parameters():
            param.requires_grad = True


def load_checkpoint(model, checkpoint_path, strict=True):
    """
    Load model checkpoint.
    
    Args:
        model: Model to load weights into
        checkpoint_path: Path to checkpoint file
        strict: Whether to strictly enforce matching keys
    
    Returns:
        model: Model with loaded weights
        checkpoint: Full checkpoint dictionary
    """
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Handle different checkpoint formats
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint
    
    # Load state dict
    model.load_state_dict(state_dict, strict=strict)
    
    return model, checkpoint


def count_parameters(model):
    """Count trainable and total parameters."""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total': total_params,
        'trainable': trainable_params,
        'non_trainable': total_params - trainable_params
    }


if __name__ == '__main__':
    # Test model creation
    model = create_model(
        architecture='unet',
        backbone='resnet50',
        num_classes=3,
        pretrained=True
    )
    
    print(f"Model created: {model.__class__.__name__}")
    
    # Count parameters
    params = count_parameters(model)
    print(f"Total parameters: {params['total']:,}")
    print(f"Trainable parameters: {params['trainable']:,}")
    
    # Test forward pass
    x = torch.randn(2, 3, 256, 256)
    y = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {y.shape}")