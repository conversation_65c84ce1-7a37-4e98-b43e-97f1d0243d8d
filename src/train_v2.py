#!/usr/bin/env python3
"""
Enhanced training script for railway track segmentation with modern techniques.
Integrates with existing codebase while adding advanced training features.
"""

import os
import sys
import yaml
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from torch.utils.tensorboard import SummaryWriter

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np

# Try to import tensorboard, but make it optional
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    print("Warning: TensorBoard not installed. Logging will be disabled.")

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.railway_dataset import RailwayTrackDataset as RailwayDataset
from src.data.augmentations import get_train_transform, get_val_transform
from src.models.segmentation_model import create_model
from src.models.multilabel_losses import MultilabelCombinedLoss as MultiLabelSegmentationLoss
# from src.utils.visualization import visualize_predictions_batch  # Not used for now
from src.train_modern import (
    train_modern, create_modern_optimizer, create_scheduler,
    EMA, CosineAnnealingWarmupRestarts
)


def setup_logging(log_dir: Path) -> logging.Logger:
    """Setup logging configuration."""
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Create logger
    logger = logging.getLogger('railway_train')
    logger.setLevel(logging.INFO)
    
    # File handler
    log_file = log_dir / f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def create_dataloaders(config: Dict) -> tuple:
    """Create train and validation dataloaders."""
    
    # Get transforms
    train_transform = get_train_transform(config)
    val_transform = get_val_transform(config)
    
    # Create datasets
    train_dataset = RailwayDataset(
        data_root=config['data']['train_path'],
        split='train',
        config=config,
        transform=train_transform
    )
    
    val_dataset = RailwayDataset(
        data_root=config['data']['val_path'],
        split='val',
        config=config,
        transform=val_transform
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=True,
        num_workers=config['data'].get('num_workers', 4),
        pin_memory=config['data'].get('pin_memory', True),
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training'].get('val_batch_size', 
                                         config['training']['batch_size'] * 2),
        shuffle=False,
        num_workers=config['data'].get('num_workers', 4),
        pin_memory=config['data'].get('pin_memory', True)
    )
    
    return train_loader, val_loader


def log_metrics_to_tensorboard(writer: Optional['SummaryWriter'], 
                              metrics: Dict, 
                              epoch: int, 
                              phase: str = 'train'):
    """Log metrics to TensorBoard."""
    if writer is not None:
        for key, value in metrics.items():
            writer.add_scalar(f'{phase}/{key}', value, epoch)


def visualize_predictions_tensorboard(writer: Optional['SummaryWriter'],
                                    model: nn.Module,
                                    dataloader: DataLoader,
                                    epoch: int,
                                    device: torch.device,
                                    num_images: int = 4):
    """Visualize predictions in TensorBoard."""
    if writer is None:
        return
    model.eval()
    
    images_logged = 0
    with torch.no_grad():
        for batch in dataloader:
            if images_logged >= num_images:
                break
                
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            outputs = model(images)
            predictions = torch.sigmoid(outputs)
            
            # Select images to log
            batch_size = min(num_images - images_logged, images.size(0))
            
            for i in range(batch_size):
                # Create visualization
                img = images[i].cpu()
                mask = masks[i].cpu()
                pred = predictions[i].cpu()
                
                # Denormalize image if needed
                mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                img = img * std + mean
                img = torch.clamp(img, 0, 1)
                
                # Create grid: [image, ground truth, prediction]
                grid = torch.cat([
                    img,
                    mask.repeat(3, 1, 1) if mask.dim() == 2 else mask[:3],
                    pred.repeat(3, 1, 1) if pred.dim() == 2 else pred[:3]
                ], dim=2)
                
                writer.add_image(f'predictions/sample_{images_logged}', 
                               grid, epoch)
                images_logged += 1
            
            if images_logged >= num_images:
                break


def main():
    parser = argparse.ArgumentParser(description='Train railway segmentation model')
    parser.add_argument('--config', type=str, 
                       default='configs/railway_track_config.yaml',
                       help='Path to config file')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    parser.add_argument('--experiment', type=str, default='default',
                       help='Experiment name')
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup directories
    output_dir = Path(config['training'].get('output_dir', 'outputs'))
    experiment_dir = output_dir / args.experiment
    checkpoint_dir = experiment_dir / 'checkpoints'
    log_dir = experiment_dir / 'logs'
    tensorboard_dir = experiment_dir / 'runs'
    
    # Create directories
    for dir_path in [checkpoint_dir, log_dir, tensorboard_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(log_dir)
    logger.info(f"Starting training experiment: {args.experiment}")
    logger.info(f"Configuration: {args.config}")
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # Create dataloaders
    logger.info("Creating dataloaders...")
    train_loader, val_loader = create_dataloaders(config)
    logger.info(f"Train samples: {len(train_loader.dataset)}")
    logger.info(f"Val samples: {len(val_loader.dataset)}")
    
    # Create model
    logger.info("Creating model...")
    model = create_model(
        architecture=config['model']['architecture'],
        backbone=config['model']['backbone'],
        num_classes=config['data']['num_classes'],
        pretrained=config['model']['pretrained']
    )
    
    # Create loss function
    loss_config = config.get('loss', {})
    
    # 直接从配置文件中读取alpha权重
    alpha_weights = loss_config.get('alpha', [0.1, 0.3, 0.6])
    
    criterion = MultiLabelSegmentationLoss(
        jaccard_weight=loss_config.get('dice_weight', 0.5),
        focal_weight=loss_config.get('ce_weight', 0.5),
        alpha=alpha_weights,
        gamma=loss_config.get('focal_gamma', 2.0)
    )
    
    # Create optimizer
    optimizer_config = {
        'learning_rate': config['training']['learning_rate'],
        'weight_decay': config['training'].get('weight_decay', 0.01),
        'optimizer_type': config['optimizer']['type']
    }
    optimizer = create_modern_optimizer(model, optimizer_config)
    
    # Setup TensorBoard
    writer = SummaryWriter(tensorboard_dir) if TENSORBOARD_AVAILABLE else None
    
    # Log model architecture
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    if writer is not None:
        image_size = config['data']['image_size']
        dummy_input = torch.zeros(1, 3, 
                                 image_size['height'], 
                                 image_size['width']).to(device)
        writer.add_graph(model, dummy_input)
    
    # Training configuration
    train_config = {
        'num_epochs': config['training']['num_epochs'],
        'learning_rate': config['training']['learning_rate'],
        'min_lr': config['training'].get('min_lr', 1e-6),
        'optimizer_type': config['optimizer']['type'],
        'scheduler_type': config['scheduler']['type'],
        'warmup_epochs': config['scheduler'].get('warmup_epochs', 5),
        'gradient_accumulation_steps': config['training'].get('gradient_accumulation_steps', 1),
        'gradient_clip_val': config['training'].get('gradient_clip_val', 1.0),
        'use_ema': config['training'].get('use_ema', True),
        'ema_decay': config['training'].get('ema_decay', 0.999),
        'use_swa': config['training'].get('use_swa', True),
        'swa_start_epoch': config['training'].get('swa_start_epoch', 75),
        'swa_lr': config['training'].get('swa_lr', 1e-4),
        'label_smoothing': config['loss'].get('label_smoothing', 0.0),
        'weight_decay': config['optimizer'].get('weight_decay', 0.01),
        'num_classes': config['data']['num_classes']
    }
    
    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume:
        logger.info(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch']
        train_config['start_epoch'] = start_epoch
    
    # Training loop with modern techniques
    logger.info("Starting training...")
    logger.info(f"Training config: {train_config}")
    
    try:
        # Custom training loop with TensorBoard integration
        history = []
        best_val_iou = 0.0
        
        # Create scheduler
        steps_per_epoch = len(train_loader)
        scheduler = create_scheduler(optimizer, train_config, steps_per_epoch)
        
        # EMA
        ema = EMA(model, decay=train_config['ema_decay'], device=device) \
              if train_config['use_ema'] else None
        
        for epoch in range(start_epoch, train_config['num_epochs']):
            # Train for one epoch
            epoch_stats = train_modern(
                model, optimizer, criterion, 
                train_loader, val_loader, 
                train_config, str(checkpoint_dir)
            )
            
            # Log to TensorBoard
            if epoch_stats:
                latest_stats = epoch_stats[-1]
                log_metrics_to_tensorboard(writer, {
                    'loss': latest_stats.train_loss,
                    'learning_rate': latest_stats.learning_rate
                }, epoch, 'train')
                
                log_metrics_to_tensorboard(writer, {
                    'loss': latest_stats.val_loss,
                    'iou': latest_stats.val_iou,
                    'dice': latest_stats.val_dice
                }, epoch, 'val')
                
                # Visualize predictions
                if epoch % config['logging'].get('log_prediction_interval', 5) == 0:
                    visualize_predictions_tensorboard(
                        writer, model, val_loader, epoch, device,
                        num_images=config['logging'].get('num_images_to_log', 4)
                    )
                
                # Log to file
                logger.info(f"Epoch {epoch+1}/{train_config['num_epochs']}: "
                          f"train_loss={latest_stats.train_loss:.4f}, "
                          f"val_loss={latest_stats.val_loss:.4f}, "
                          f"val_iou={latest_stats.val_iou:.4f}, "
                          f"val_dice={latest_stats.val_dice:.4f}, "
                          f"lr={latest_stats.learning_rate:.6f}")
        
        logger.info("Training completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
    except Exception as e:
        logger.error(f"Training failed with error: {str(e)}", exc_info=True)
    finally:
        if writer is not None:
            writer.close()
            logger.info("TensorBoard writer closed")


if __name__ == '__main__':
    main()