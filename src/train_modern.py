"""
Modern training script with advanced techniques:
- Cosine learning rate scheduling with warmup
- Exponential Moving Average (EMA)
- Gradient accumulation and clipping
- Mixed precision training
- Learning rate warmup
- Label smoothing
- Stochastic Weight Averaging (SWA)
- Advanced augmentation scheduling
"""

from typing import Optional, List, Dict, Tuple
import sys
import gc
import math
import copy
from collections import namedtuple
from pathlib import Path

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim.lr_scheduler import _LRScheduler
from torch.optim.swa_utils import AveragedModel, SWALR
from tqdm import tqdm
import numpy as np

from .utils import RunningAverage, iou_coef
# from .utils.metrics import calculate_dice, SegmentationMetrics  # Import issues, define inline


EpochStats = namedtuple('EpochStats', 
    'epoch learning_rate train_loss val_loss val_iou val_dice time')


def calculate_dice(pred: torch.Tensor, target: torch.Tensor, smooth: float = 1e-6) -> torch.Tensor:
    """Calculate Dice coefficient."""
    pred = pred.float()
    target = target.float()
    
    intersection = (pred * target).sum(dim=(2, 3))
    union = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3))
    
    dice = (2.0 * intersection + smooth) / (union + smooth)
    return dice.mean()


class SegmentationMetrics:
    """Simple metrics tracker for segmentation."""
    
    def __init__(self, num_classes=3):
        self.num_classes = num_classes
        self.reset()
    
    def reset(self):
        self.total_iou = 0
        self.total_dice = 0
        self.count = 0
    
    def update(self, pred, target):
        iou = iou_coef(target, pred)
        dice = calculate_dice(pred, target)
        
        self.total_iou += iou.item()
        self.total_dice += dice.item()
        self.count += 1
    
    def compute(self):
        return {
            'iou': self.total_iou / (self.count + 1e-6),
            'dice': self.total_dice / (self.count + 1e-6)
        }


class CosineAnnealingWarmupRestarts(_LRScheduler):
    """
    Cosine annealing with warm restarts and linear warmup.
    
    Parameters:
        optimizer (Optimizer): Wrapped optimizer.
        first_cycle_steps (int): Number of steps for the first cycle.
        cycle_mult (float): Cycle length multiplier after each cycle.
        max_lr (float): Maximum learning rate.
        min_lr (float): Minimum learning rate.
        warmup_steps (int): Number of warmup steps.
        gamma (float): Decrease rate of max learning rate by cycle.
    """
    
    def __init__(self,
                 optimizer: torch.optim.Optimizer,
                 first_cycle_steps: int,
                 cycle_mult: float = 1.0,
                 max_lr: float = 0.1,
                 min_lr: float = 0.001,
                 warmup_steps: int = 0,
                 gamma: float = 1.0,
                 last_epoch: int = -1):
        
        assert warmup_steps < first_cycle_steps
        
        self.first_cycle_steps = first_cycle_steps
        self.cycle_mult = cycle_mult
        self.base_max_lr = max_lr
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma
        
        self.cur_cycle_steps = first_cycle_steps
        self.cycle = 0
        self.step_in_cycle = last_epoch
        
        super(CosineAnnealingWarmupRestarts, self).__init__(optimizer, last_epoch)
        
        # Initialize learning rates
        self.init_lr()
    
    def init_lr(self):
        self.base_lrs = []
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = self.min_lr
            self.base_lrs.append(self.min_lr)
    
    def get_lr(self):
        if self.step_in_cycle < self.warmup_steps:
            return [(self.max_lr - base_lr) * self.step_in_cycle / self.warmup_steps + base_lr 
                    for base_lr in self.base_lrs]
        else:
            return [base_lr + (self.max_lr - base_lr) * 
                    (1 + math.cos(math.pi * (self.step_in_cycle - self.warmup_steps) / 
                    (self.cur_cycle_steps - self.warmup_steps))) / 2
                    for base_lr in self.base_lrs]
    
    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
            self.step_in_cycle = self.step_in_cycle + 1
            if self.step_in_cycle >= self.cur_cycle_steps:
                self.cycle += 1
                self.step_in_cycle = self.step_in_cycle - self.cur_cycle_steps
                self.cur_cycle_steps = int((self.cur_cycle_steps - self.warmup_steps) * 
                                         self.cycle_mult) + self.warmup_steps
        else:
            if epoch >= self.first_cycle_steps:
                if self.cycle_mult == 1.:
                    self.step_in_cycle = epoch % self.first_cycle_steps
                    self.cycle = epoch // self.first_cycle_steps
                else:
                    n = int(math.log((epoch / self.first_cycle_steps * (self.cycle_mult - 1) + 1), 
                           self.cycle_mult))
                    self.cycle = n
                    self.step_in_cycle = epoch - int(self.first_cycle_steps * 
                                                    (self.cycle_mult ** n - 1) / (self.cycle_mult - 1))
                    self.cur_cycle_steps = self.first_cycle_steps * self.cycle_mult ** (n)
            else:
                self.cur_cycle_steps = self.first_cycle_steps
                self.step_in_cycle = epoch
                
        self.max_lr = self.base_max_lr * (self.gamma ** self.cycle)
        self.last_epoch = math.floor(epoch)
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr


class EMA:
    """
    Exponential Moving Average for model parameters.
    
    Parameters:
        model: Model to apply EMA to.
        decay: EMA decay rate.
        device: Device to store shadow parameters.
    """
    
    def __init__(self, model, decay=0.999, device=None):
        self.model = model
        self.decay = decay
        self.device = device
        self.shadow = {}
        self.backup = {}
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone().to(device)
    
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
    
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]
    
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}


class LabelSmoothingLoss(nn.Module):
    """
    Label smoothing loss for segmentation tasks.
    """
    
    def __init__(self, smoothing=0.1, num_classes=3):
        super().__init__()
        self.smoothing = smoothing
        self.num_classes = num_classes
        self.confidence = 1.0 - smoothing
    
    def forward(self, pred, target):
        """
        Args:
            pred: (B, C, H, W) predictions
            target: (B, C, H, W) one-hot encoded targets
        """
        pred = pred.log_softmax(dim=1)
        with torch.no_grad():
            true_dist = target * self.confidence + self.smoothing / self.num_classes
        
        return torch.mean(torch.sum(-true_dist * pred, dim=1))


def create_modern_optimizer(model: nn.Module, config: Dict) -> torch.optim.Optimizer:
    """Create optimizer with modern techniques."""
    
    # Separate parameters for different learning rates
    backbone_params = []
    head_params = []
    
    for name, param in model.named_parameters():
        if 'backbone' in name:
            backbone_params.append(param)
        else:
            head_params.append(param)
    
    # Use different learning rates for backbone and head
    optimizer_type = config.get('optimizer_type', 'adamw')
    base_lr = config.get('learning_rate', 1e-3)
    
    if optimizer_type == 'adamw':
        optimizer = torch.optim.AdamW([
            {'params': backbone_params, 'lr': base_lr * 0.1},  # Lower LR for backbone
            {'params': head_params, 'lr': base_lr}
        ], weight_decay=config.get('weight_decay', 0.01))
    elif optimizer_type == 'sgd':
        optimizer = torch.optim.SGD([
            {'params': backbone_params, 'lr': base_lr * 0.1},
            {'params': head_params, 'lr': base_lr}
        ], momentum=0.9, weight_decay=config.get('weight_decay', 1e-4))
    else:
        raise ValueError(f"Unknown optimizer type: {optimizer_type}")
    
    return optimizer


def create_scheduler(optimizer: torch.optim.Optimizer, 
                    config: Dict, 
                    steps_per_epoch: int) -> _LRScheduler:
    """Create learning rate scheduler."""
    
    scheduler_type = config.get('scheduler_type', 'cosine')
    num_epochs = config.get('num_epochs', 100)
    warmup_epochs = config.get('warmup_epochs', 5)
    
    if scheduler_type == 'cosine':
        scheduler = CosineAnnealingWarmupRestarts(
            optimizer,
            first_cycle_steps=num_epochs * steps_per_epoch,
            cycle_mult=1.0,
            max_lr=config.get('learning_rate', 1e-3),
            min_lr=config.get('min_lr', 1e-6),
            warmup_steps=warmup_epochs * steps_per_epoch,
            gamma=config.get('lr_gamma', 1.0)
        )
    elif scheduler_type == 'onecycle':
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=config.get('learning_rate', 1e-3),
            epochs=num_epochs,
            steps_per_epoch=steps_per_epoch,
            pct_start=0.3,  # 30% of training for warmup
            anneal_strategy='cos'
        )
    else:
        # Fallback to step scheduler
        scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=30,
            gamma=0.1
        )
    
    return scheduler


def train_modern(model: nn.Module,
                optimizer: torch.optim.Optimizer,
                criterion: nn.Module,
                train_loader: torch.utils.data.DataLoader,
                val_loader: torch.utils.data.DataLoader,
                config: Dict,
                save_dir: str = "outputs/checkpoints") -> List[EpochStats]:
    """
    Modern training loop with advanced techniques.
    
    Parameters:
        model: PyTorch model to train
        optimizer: Optimizer
        criterion: Loss function
        train_loader: Training data loader
        val_loader: Validation data loader
        config: Training configuration dict
        save_dir: Directory to save checkpoints
    
    Returns:
        history: List of training statistics
    """
    
    # Extract config parameters
    epochs = config.get('num_epochs', 100)
    accumulate_steps = config.get('gradient_accumulation_steps', 1)
    clip_grad = config.get('gradient_clip_val', 1.0)
    use_ema = config.get('use_ema', True)
    ema_decay = config.get('ema_decay', 0.999)
    use_swa = config.get('use_swa', True)
    swa_start = config.get('swa_start_epoch', int(0.75 * epochs))
    label_smoothing = config.get('label_smoothing', 0.0)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Mixed precision
    scaler = torch.cuda.amp.GradScaler()
    
    # Learning rate scheduler
    steps_per_epoch = len(train_loader)
    scheduler = create_scheduler(optimizer, config, steps_per_epoch)
    
    # EMA
    ema = EMA(model, decay=ema_decay, device=device) if use_ema else None
    
    # SWA
    swa_model = AveragedModel(model) if use_swa else None
    swa_scheduler = SWALR(optimizer, swa_lr=config.get('swa_lr', 1e-4)) if use_swa else None
    
    # Label smoothing
    if label_smoothing > 0:
        smooth_criterion = LabelSmoothingLoss(smoothing=label_smoothing, 
                                             num_classes=config.get('num_classes', 3))
    
    # Metrics
    metrics = SegmentationMetrics(num_classes=config.get('num_classes', 3))
    
    # Training history
    history = []
    best_val_iou = 0.0
    
    # Training loop
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss_avg = RunningAverage()
        
        with tqdm(total=len(train_loader), desc=f'Epoch {epoch+1}/{epochs}') as pbar:
            for batch_idx, batch in enumerate(train_loader):
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass with mixed precision
                with torch.cuda.amp.autocast():
                    outputs = model(images)
                    
                    # Calculate loss
                    loss = criterion(outputs, masks)
                    
                    # Add label smoothing if enabled
                    if label_smoothing > 0:
                        loss = loss * (1 - label_smoothing) + \
                               smooth_criterion(outputs, masks) * label_smoothing
                    
                    # Scale loss for gradient accumulation
                    loss = loss / accumulate_steps
                
                # Backward pass
                scaler.scale(loss).backward()
                
                # Update weights every accumulate_steps
                if (batch_idx + 1) % accumulate_steps == 0:
                    # Gradient clipping
                    if clip_grad > 0:
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
                    
                    # Optimizer step
                    scaler.step(optimizer)
                    scaler.update()
                    optimizer.zero_grad()
                    
                    # Update EMA
                    if ema is not None:
                        ema.update()
                
                # Update learning rate (for OneCycle)
                if isinstance(scheduler, torch.optim.lr_scheduler.OneCycleLR):
                    scheduler.step()
                
                # Update metrics
                train_loss_avg.update(loss.item() * accumulate_steps)
                
                # Update progress bar
                current_lr = optimizer.param_groups[0]['lr']
                pbar.set_postfix({
                    'loss': f'{train_loss_avg():.4f}',
                    'lr': f'{current_lr:.6f}'
                })
                pbar.update()
        
        # Update learning rate (for other schedulers)
        if not isinstance(scheduler, torch.optim.lr_scheduler.OneCycleLR):
            scheduler.step()
        
        # SWA update
        if use_swa and epoch >= swa_start:
            swa_model.update_parameters(model)
            swa_scheduler.step()
        
        # Validation phase
        model.eval()
        val_loss_avg = RunningAverage()
        val_iou_avg = RunningAverage()
        val_dice_avg = RunningAverage()
        
        # Apply EMA for validation
        if ema is not None:
            ema.apply_shadow()
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(images)
                loss = criterion(outputs, masks)
                
                # Calculate metrics
                pred_masks = torch.sigmoid(outputs)
                iou = iou_coef(masks, pred_masks)
                dice = calculate_dice(pred_masks, masks)
                
                val_loss_avg.update(loss.item())
                val_iou_avg.update(iou.item())
                val_dice_avg.update(dice.item())
        
        # Restore original weights after validation
        if ema is not None:
            ema.restore()
        
        # Record epoch statistics
        stats = EpochStats(
            epoch=epoch + 1,
            learning_rate=optimizer.param_groups[0]['lr'],
            train_loss=train_loss_avg(),
            val_loss=val_loss_avg(),
            val_iou=val_iou_avg(),
            val_dice=val_dice_avg(),
            time=pbar.format_dict['elapsed']
        )
        history.append(stats)
        
        # Print epoch summary
        print(f"\nEpoch {stats.epoch}: "
              f"train_loss={stats.train_loss:.4f}, "
              f"val_loss={stats.val_loss:.4f}, "
              f"val_iou={stats.val_iou:.4f}, "
              f"val_dice={stats.val_dice:.4f}")
        
        # Save best model
        if val_iou_avg() > best_val_iou:
            best_val_iou = val_iou_avg()
            
            # Save regular model
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_val_iou': best_val_iou,
                'config': config
            }, save_dir / 'best_model.pth')
            
            # Save EMA model if available
            if ema is not None:
                ema.apply_shadow()
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'best_val_iou': best_val_iou,
                    'config': config
                }, save_dir / 'best_model_ema.pth')
                ema.restore()
            
            print(f"Saved best model with IoU: {best_val_iou:.4f}")
        
        # Save latest checkpoint
        torch.save({
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'history': history,
            'config': config
        }, save_dir / 'latest_checkpoint.pth')
        
        # Clear cache
        torch.cuda.empty_cache()
        gc.collect()
    
    # Save final SWA model
    if use_swa:
        # Update batch norm statistics
        torch.optim.swa_utils.update_bn(train_loader, swa_model, device)
        torch.save({
            'model_state_dict': swa_model.state_dict(),
            'config': config
        }, save_dir / 'swa_model.pth')
        print("Saved SWA model")
    
    return history


def resume_training(checkpoint_path: str,
                   model: nn.Module,
                   train_loader: torch.utils.data.DataLoader,
                   val_loader: torch.utils.data.DataLoader,
                   config: Dict) -> List[EpochStats]:
    """Resume training from checkpoint."""
    
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Create optimizer and load state
    optimizer = create_modern_optimizer(model, config)
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    # Update config with checkpoint info
    config['start_epoch'] = checkpoint['epoch']
    history = checkpoint.get('history', [])
    
    # Continue training
    return train_modern(model, optimizer, criterion, train_loader, val_loader, config)


if __name__ == '__main__':
    # Example usage
    config = {
        'num_epochs': 100,
        'learning_rate': 1e-3,
        'min_lr': 1e-6,
        'optimizer_type': 'adamw',
        'scheduler_type': 'cosine',
        'warmup_epochs': 5,
        'gradient_accumulation_steps': 1,
        'gradient_clip_val': 1.0,
        'use_ema': True,
        'ema_decay': 0.999,
        'use_swa': True,
        'swa_start_epoch': 75,
        'swa_lr': 1e-4,
        'label_smoothing': 0.1,
        'weight_decay': 0.01,
        'num_classes': 3
    }
    
    print("Modern training script with advanced techniques ready!")