from typing import List
import torch
import numpy as np
import cv2

if __name__ == '__main__':
    pass


def get_batch_predictions(models: List[torch.nn.Module],
                          test_batch: torch.Tensor,
                          device: torch.device) -> torch.Tensor:
    """
    Returns the average predicted logits mask for each model in provided models list

    Parameters
    ----------
    models (List[torch.nn.Module]): list of loaded pytorch models
    test_batch (torch.Tensor): images batch to get predictions for
    device (str): device for pytorch tensors

    Returns
    -------
    test_output_batch (torch.Tensor): averaged predicted logits mask
    """
    for model in models:
        model.eval()
        model.to(device)

    test_batch = test_batch.to(device)
    test_output_batch = None

    with torch.inference_mode():
        for model in models:
            if test_output_batch is None:
                test_output_batch = model(test_batch)
            else:
                test_output_batch += model(test_batch)

        test_output_batch /= len(models)
        test_output_batch = torch.nn.Sigmoid()(test_output_batch)

    return test_output_batch


def upscale_mask(mask: np.ndarray, desired_height: int, desired_width: int) -> np.ndarray:
    """
    Reverses image size augmentations and returns upscaled mask with desired resolution

    Parameters
    ----------
    mask (np.ndarray): boolean segmentation mask
    desired_height (int): desired image height
    desired_width (int): desired image width

    Returns
    -------
    running_avg (float): Current running average
    """
    image_height, image_width = mask.shape[:2]

    ratio = desired_width / desired_height
    image_width_after_maxsize = image_width
    image_height_after_maxsize = round(image_width_after_maxsize / ratio)

    pad_top = int((image_height - image_height_after_maxsize) / 2.0)
    pad_bottom = image_height - image_height_after_maxsize - pad_top

    mask = mask[pad_top:-pad_bottom, :, :]
    mask = cv2.resize(mask, (desired_width, desired_height), cv2.INTER_NEAREST_EXACT)

    return mask


def convert_bool_mask_to_submission(mask: np.ndarray, threshold: float = 0.5) -> np.ndarray:
    """
    Converts the boolean mask to format required for submission - 支持多标签

    Parameters
    ----------
    mask (np.ndarray): boolean segmentation mask (H, W, C)
    threshold (float): threshold to convert logits to binary

    Returns
    -------
    output_mask (np.ndarray): segmentation mask in format required for submission
    """
    # 多标签处理：每个类别独立判断
    class_0_bool_mask = mask[:, :, 0] > threshold  # 背景
    class_1_bool_mask = mask[:, :, 1] > threshold  # 主轨道 (对应原class 6)
    class_2_bool_mask = mask[:, :, 2] > threshold  # 分叉轨道 (对应原class 7)
    
    # 创建输出掩码 - 使用优先级策略
    output_mask = np.zeros(mask.shape[:2], dtype=np.uint8)
    
    # 按优先级赋值（分叉轨道 > 主轨道 > 背景）
    output_mask[class_0_bool_mask] = 0     # 背景
    output_mask[class_1_bool_mask] = 6     # 主轨道
    output_mask[class_2_bool_mask] = 7     # 分叉轨道（最高优先级）
    
    return output_mask


def post_process_multilabel(mask: np.ndarray, threshold: float = 0.5) -> dict:
    """
    多标签后处理 - 返回每个类别的独立掩码

    Parameters
    ----------
    mask (np.ndarray): 预测掩码 (H, W, C)
    threshold (float): 二值化阈值

    Returns
    -------
    dict: 包含每个类别独立掩码的字典
    """
    h, w = mask.shape[:2]
    
    # 创建每个类别的二值掩码
    binary_masks = {
        'background': (mask[:, :, 0] > threshold).astype(np.uint8),
        'main_track': (mask[:, :, 1] > threshold).astype(np.uint8),
        'fork_track': (mask[:, :, 2] > threshold).astype(np.uint8)
    }
    
    # 创建彩色可视化掩码
    colored_mask = np.zeros((h, w, 3), dtype=np.uint8)
    colored_mask[binary_masks['main_track'] > 0] = [255, 0, 0]    # 红色
    colored_mask[binary_masks['fork_track'] > 0] = [0, 255, 0]    # 绿色
    
    return {
        'binary_masks': binary_masks,
        'colored_mask': colored_mask,
        'combined_mask': convert_bool_mask_to_submission(mask, threshold)
    }
