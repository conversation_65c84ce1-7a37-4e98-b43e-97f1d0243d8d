"""
铁路语义分割项目

一个模块化、可扩展的深度学习项目，用于铁路的语义分割。
"""

__version__ = '2.0.0'
__author__ = 'Railway Segmentation Team'
__license__ = 'Apache 2.0'

# 导入主要组件，便于使用
from .core import Config, load_config
from .data import RailwaySegmentationDataset, create_dataloaders
from .models import SegmentationModel, create_segmentation_model

__all__ = [
    'Config',
    'load_config',
    'RailwaySegmentationDataset',
    'create_dataloaders',
    'SegmentationModel',
    'create_segmentation_model',
]

# 项目信息
PROJECT_INFO = {
    'name': 'Railway Infrastructure Segmentation',
    'description': '用于铁路轨道的语义分割',
    'version': __version__,
    'components': {
        'core': '核心功能模块（配置管理、注册器、基类）',
        'data': '数据处理模块（数据集、数据加载器、数据增强）',
        'models': '模型定义模块（分割模型、损失函数、集成模型）',
        'training': '训练模块（训练器、回调函数、优化器）',
        'evaluation': '评估模块（评估指标、评估器）',
        'inference': '推理模块（预测器、后处理）',
        'utils': '工具函数模块（IO、可视化、日志）'
    }
} 