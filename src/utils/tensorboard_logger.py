"""
TensorBoard日志记录器
提供训练过程的可视化功能（可选）
"""

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    SummaryWriter = None
    TENSORBOARD_AVAILABLE = False
    print("警告: TensorBoard未安装，可视化功能将被禁用。运行 'pip install tensorboard' 来启用。")

import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, Any, Optional, Union
import numpy as np
import matplotlib.pyplot as plt
import io
from PIL import Image


class TensorBoardLogger:
    """
    TensorBoard日志记录器
    """
    
    def __init__(self, log_dir: Union[str, Path]):
        """
        初始化TensorBoard记录器
        
        Args:
            log_dir: 日志保存目录
        """
        if not TENSORBOARD_AVAILABLE:
            self.writer = None
            self.enabled = False
            return
            
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            self.writer = SummaryWriter(str(self.log_dir))
            self.enabled = True
        except Exception as e:
            print(f"警告: 无法初始化TensorBoard记录器: {e}")
            self.writer = None
            self.enabled = False
    
    def log_scalar(self, tag: str, value: float, step: int):
        """记录标量值"""
        if self.enabled and self.writer is not None:
            self.writer.add_scalar(tag, value, step)
    
    def log_scalars(self, main_tag: str, tag_scalar_dict: Dict[str, float], step: int):
        """记录多个标量值"""
        if self.enabled and self.writer is not None:
            self.writer.add_scalars(main_tag, tag_scalar_dict, step)
    
    def log_image(self, tag: str, image: torch.Tensor, step: int):
        """记录图像"""
        if self.enabled and self.writer is not None:
            self.writer.add_image(tag, image, step)
    
    def log_images(self, tag: str, images: torch.Tensor, step: int):
        """记录多张图像"""
        if self.enabled and self.writer is not None:
            self.writer.add_images(tag, images, step)
    
    def log_histogram(self, tag: str, values: torch.Tensor, step: int):
        """记录直方图"""
        if self.enabled and self.writer is not None:
            self.writer.add_histogram(tag, values, step)
    
    def log_model_graph(self, model: nn.Module, input_tensor: torch.Tensor):
        """记录模型图结构"""
        if self.enabled and self.writer is not None:
            try:
                self.writer.add_graph(model, input_tensor)
            except Exception as e:
                print(f"警告: 无法记录模型图: {e}")
    
    def log_training_metrics(self, metrics: Dict[str, Any], step: int, prefix: str = ""):
        """记录训练指标"""
        if not self.enabled or self.writer is None:
            return
            
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                tag = f"{prefix}/{key}" if prefix else key
                self.log_scalar(tag, value, step)
    
    def log_prediction_samples(self, 
                              images: torch.Tensor,
                              targets: torch.Tensor, 
                              predictions: torch.Tensor,
                              step: int,
                              max_samples: int = 4):
        """记录预测样本对比"""
        if not self.enabled or self.writer is None:
            return
            
        try:
            # 限制样本数量
            n_samples = min(max_samples, images.size(0))
            
            # 选择前n个样本
            sample_images = images[:n_samples]
            sample_targets = targets[:n_samples] 
            sample_predictions = predictions[:n_samples]
            
            # 创建对比图像网格
            comparison = self._create_prediction_comparison(
                sample_images, sample_targets, sample_predictions
            )
            
            if comparison is not None:
                self.log_image('Predictions/Comparison', comparison, step)
                
        except Exception as e:
            print(f"警告: 无法记录预测样本: {e}")
    
    def _create_prediction_comparison(self,
                                    images: torch.Tensor,
                                    targets: torch.Tensor,
                                    predictions: torch.Tensor) -> Optional[torch.Tensor]:
        """创建预测对比图像"""
        try:
            import torchvision.utils as vutils
            
            # 归一化图像到[0,1]
            if images.min() < 0:
                images = (images + 1) / 2
            
            # 二值化预测
            pred_binary = (predictions > 0.5).float()
            
            # 创建网格：原图 | 真实标签 | 预测结果
            grid_images = []
            for img, target, pred in zip(images, targets, pred_binary):
                # 原图（如果是多通道，取前3个通道）
                if img.size(0) >= 3:
                    rgb_img = img[:3]
                else:
                    rgb_img = img.repeat(3, 1, 1)
                
                # 真实标签（转为RGB）
                if target.size(0) == 1:
                    target_rgb = target.repeat(3, 1, 1)
                else:
                    target_rgb = target[:3]
                
                # 预测结果（转为RGB）
                if pred.size(0) == 1:
                    pred_rgb = pred.repeat(3, 1, 1)
                else:
                    pred_rgb = pred[:3]
                
                grid_images.extend([rgb_img, target_rgb, pred_rgb])
            
            # 创建网格
            grid = vutils.make_grid(grid_images, nrow=3, padding=2, normalize=False)
            return grid
            
        except Exception as e:
            print(f"警告: 无法创建预测对比图: {e}")
            return None
    
    def flush(self):
        """刷新缓冲区"""
        if self.enabled and self.writer is not None:
            self.writer.flush()
    
    def close(self):
        """关闭记录器"""
        if self.enabled and self.writer is not None:
            self.writer.close()


def create_tensorboard_logger(log_dir: Union[str, Path]) -> Optional[TensorBoardLogger]:
    """
    创建TensorBoard记录器的工厂函数
    
    Args:
        log_dir: 日志目录
        
    Returns:
        TensorBoard记录器实例，如果TensorBoard不可用则返回None
    """
    if not TENSORBOARD_AVAILABLE:
        return None
    
    try:
        return TensorBoardLogger(log_dir)
    except Exception as e:
        print(f"警告: 无法创建TensorBoard记录器: {e}")
        return None 