"""
内存监控工具
用于监控和管理训练过程中的内存使用
"""

import gc
import psutil
import torch
import logging
from typing import Dict, Optional, Union
from contextlib import contextmanager
import numpy as np


def cleanup_cuda_memory():
    """清理CUDA缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()


def optimize_model_memory(model: torch.nn.Module) -> torch.nn.Module:
    """
    优化模型内存使用，将模型设置为最优内存格式
    
    Args:
        model: PyTorch模型
        
    Returns:
        优化后的模型
    """
    if not torch.cuda.is_available():
        return model
        
    # 使用channels_last内存格式，对卷积操作更高效
    model = model.to(memory_format=torch.channels_last)
    
    # 转换为FP16精度（如适用）
    # if torch.cuda.is_bf16_supported():
    #     model = model.to(dtype=torch.bfloat16)  # bfloat16对动态范围更友好
    # elif torch.cuda.is_fp16_supported():
    #     model = model.to(dtype=torch.float16)
    
    # 设置为CUDA图兼容模式（如果模型支持）
    if hasattr(model, 'is_cuda_graph_compatible') and model.is_cuda_graph_compatible:
        # 使用CUDA图可以缓存和重用计算图
        # 注意：这需要模型没有动态控制流
        for param in model.parameters():
            param.requires_grad_(False)
            
    # 清理不必要的缓存
    torch.cuda.empty_cache()
    
    return model


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化内存监控器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.process = psutil.Process()
        self.peak_memory = 0
        self.baseline_memory = self._get_memory_info()
    
    def _get_memory_info(self) -> Dict[str, float]:
        """获取内存信息"""
        info = {}
        
        # CPU内存
        memory_info = self.process.memory_info()
        info['cpu_memory_mb'] = memory_info.rss / 1024 / 1024  # MB
        info['cpu_memory_percent'] = self.process.memory_percent()
        
        # GPU内存
        if torch.cuda.is_available():
            info['gpu_memory_allocated_mb'] = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            info['gpu_memory_reserved_mb'] = torch.cuda.memory_reserved() / 1024 / 1024    # MB
            info['gpu_memory_total_mb'] = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
            info['gpu_memory_free_mb'] = info['gpu_memory_total_mb'] - info['gpu_memory_allocated_mb']
        else:
            info.update({
                'gpu_memory_allocated_mb': 0,
                'gpu_memory_reserved_mb': 0,
                'gpu_memory_total_mb': 0,
                'gpu_memory_free_mb': 0
            })
        
        return info
    
    def log_memory_usage(self, prefix: str = "", force_gc: bool = False):
        """
        记录当前内存使用情况
        
        Args:
            prefix: 日志前缀
            force_gc: 是否强制垃圾回收
        """
        if force_gc:
            self.cleanup_memory()
        
        info = self._get_memory_info()
        
        # 更新峰值内存
        current_memory = info['cpu_memory_mb']
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
        
        # 计算内存增长
        memory_growth = current_memory - self.baseline_memory['cpu_memory_mb']
        
        log_msg = f"{prefix}内存使用情况: "
        log_msg += f"CPU={info['cpu_memory_mb']:.1f}MB (+{memory_growth:.1f}MB), "
        log_msg += f"GPU分配={info['gpu_memory_allocated_mb']:.1f}MB, "
        log_msg += f"GPU保留={info['gpu_memory_reserved_mb']:.1f}MB, "
        log_msg += f"GPU可用={info['gpu_memory_free_mb']:.1f}MB"
        
        self.logger.info(log_msg)
        
        return info
    
    def cleanup_memory(self):
        """清理内存"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理CUDA缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.ipc_collect()
    
    @contextmanager
    def monitor_block(self, block_name: str):
        """
        监控代码块的内存使用
        
        Args:
            block_name: 代码块名称
        """
        start_info = self._get_memory_info()
        self.logger.debug(f"开始执行 {block_name}")
        
        try:
            yield
        finally:
            end_info = self._get_memory_info()
            
            cpu_diff = end_info['cpu_memory_mb'] - start_info['cpu_memory_mb']
            gpu_diff = end_info['gpu_memory_allocated_mb'] - start_info['gpu_memory_allocated_mb']
            
            self.logger.debug(
                f"完成 {block_name}: "
                f"CPU内存变化={cpu_diff:+.1f}MB, "
                f"GPU内存变化={gpu_diff:+.1f}MB"
            )
    
    def check_memory_leak(self, threshold_mb: float = 100.0) -> bool:
        """
        检查是否有内存泄漏
        
        Args:
            threshold_mb: 内存增长阈值(MB)
            
        Returns:
            是否检测到内存泄漏
        """
        current_info = self._get_memory_info()
        memory_growth = current_info['cpu_memory_mb'] - self.baseline_memory['cpu_memory_mb']
        
        if memory_growth > threshold_mb:
            self.logger.warning(
                f"检测到可能的内存泄漏: "
                f"内存增长 {memory_growth:.1f}MB (阈值: {threshold_mb}MB)"
            )
            return True
        
        return False
    
    def reset_baseline(self):
        """重置基线内存"""
        self.cleanup_memory()
        self.baseline_memory = self._get_memory_info()
        self.logger.info(f"重置内存基线: CPU={self.baseline_memory['cpu_memory_mb']:.1f}MB")
    
    def get_memory_summary(self) -> Dict[str, float]:
        """获取内存使用摘要"""
        current_info = self._get_memory_info()
        
        return {
            'current_cpu_memory_mb': current_info['cpu_memory_mb'],
            'peak_cpu_memory_mb': self.peak_memory,
            'memory_growth_mb': current_info['cpu_memory_mb'] - self.baseline_memory['cpu_memory_mb'],
            'current_gpu_memory_mb': current_info['gpu_memory_allocated_mb'],
            'gpu_memory_utilization': (
                current_info['gpu_memory_allocated_mb'] / current_info['gpu_memory_total_mb'] * 100 
                if current_info['gpu_memory_total_mb'] > 0 else 0
            )
        }


def log_gpu_memory_status(logger: logging.Logger):
    """记录GPU内存状态"""
    if not torch.cuda.is_available():
        logger.info("GPU不可用")
        return
    
    allocated = torch.cuda.memory_allocated() / 1024**3  # GB
    reserved = torch.cuda.memory_reserved() / 1024**3    # GB
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
    
    logger.info(
        f"GPU内存状态: "
        f"已分配={allocated:.2f}GB, "
        f"已保留={reserved:.2f}GB, "
        f"总计={total:.2f}GB, "
        f"利用率={allocated/total*100:.1f}%"
    ) 