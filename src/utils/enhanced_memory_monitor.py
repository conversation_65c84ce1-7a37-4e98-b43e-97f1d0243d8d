"""
增强版内存监控器
提供更精确的内存监控和管理功能
"""

import gc
import time
import psutil
import torch
import logging
import threading
import numpy as np
from typing import Dict, Optional, List, Tuple, Any
from contextlib import contextmanager


def get_gpu_memory_info() -> Dict[str, Any]:
    """
    获取详细的GPU内存信息
    
    Returns:
        GPU内存信息字典
    """
    if not torch.cuda.is_available():
        return {
            'allocated_mb': 0,
            'reserved_mb': 0,
            'cached_mb': 0,
            'total_mb': 0,
            'free_mb': 0,
            'utilization': 0,
            'active_allocations': 0
        }
    
    # 获取基本内存统计
    allocated = torch.cuda.memory_allocated() / (1024 ** 2)  # MB
    reserved = torch.cuda.memory_reserved() / (1024 ** 2)    # MB
    cached = reserved - allocated  # 缓存的内存
    total = torch.cuda.get_device_properties(0).total_memory / (1024 ** 2)  # MB
    free = total - reserved
    
    # 获取活跃分配数量（仅在PyTorch 1.10+可用）
    active_allocations = 0
    try:
        active_allocations = torch.cuda.memory_stats()['num_alloc_retries']
    except (KeyError, RuntimeError):
        pass
    
    return {
        'allocated_mb': allocated,
        'reserved_mb': reserved,
        'cached_mb': cached,
        'total_mb': total,
        'free_mb': free,
        'utilization': (allocated / total) * 100 if total > 0 else 0,
        'active_allocations': active_allocations
    }


def cleanup_cuda_memory(force_cache_clear: bool = False):
    """
    清理CUDA缓存
    
    Args:
        force_cache_clear: 是否强制清理所有缓存
    """
    if not torch.cuda.is_available():
        return
    
    # 强制垃圾回收
    gc.collect()
    
    # 清理CUDA缓存
    torch.cuda.empty_cache()
    
    # 如果强制清理，则尝试释放所有未使用的内存
    if force_cache_clear:
        if hasattr(torch.cuda, 'memory_stats'):
            # 在支持的PyTorch版本上，尝试更彻底的清理
            torch.cuda.reset_peak_memory_stats()
            torch.cuda.reset_accumulated_memory_stats()
            
        # 尝试使用CUDA IPC收集
        if hasattr(torch.cuda, 'ipc_collect'):
            torch.cuda.ipc_collect()


def optimize_model_memory(model: torch.nn.Module) -> torch.nn.Module:
    """
    优化模型内存使用
    
    Args:
        model: PyTorch模型
        
    Returns:
        优化后的模型
    """
    if not torch.cuda.is_available():
        return model
    
    # 使用channels_last内存格式
    model = model.to(memory_format=torch.channels_last)
    
    # 清理缓存
    cleanup_cuda_memory()
    
    return model


class EnhancedMemoryMonitor:
    """增强版内存监控器"""
    
    def __init__(
        self, 
        logger: Optional[logging.Logger] = None,
        check_interval: float = 5.0,
        auto_monitoring: bool = False,
        alert_threshold_mb: float = 1000.0,
        log_to_tensorboard: bool = False
    ):
        """
        初始化内存监控器
        
        Args:
            logger: 日志记录器
            check_interval: 自动监控的检查间隔（秒）
            auto_monitoring: 是否启用自动监控
            alert_threshold_mb: 内存使用警报阈值（MB）
            log_to_tensorboard: 是否记录到TensorBoard
        """
        self.logger = logger or logging.getLogger(__name__)
        self.check_interval = check_interval
        self.alert_threshold_mb = alert_threshold_mb
        self.log_to_tensorboard = log_to_tensorboard
        self.tensorboard_writer = None
        
        # 进程信息
        self.process = psutil.Process()
        
        # 内存使用历史
        self.history = {
            'timestamps': [],
            'cpu_memory_mb': [],
            'gpu_allocated_mb': [],
            'gpu_reserved_mb': [],
            'gpu_utilization': []
        }
        
        # 基线内存
        self.baseline = self._get_memory_info()
        self.peak_memory = {
            'cpu_memory_mb': self.baseline['cpu_memory_mb'],
            'gpu_allocated_mb': self.baseline['gpu_allocated_mb'],
            'timestamp': time.time()
        }
        
        # 自动监控
        self.auto_monitoring = auto_monitoring
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        
        if auto_monitoring:
            self.start_auto_monitoring()
    
    def _get_memory_info(self) -> Dict[str, float]:
        """获取内存信息"""
        info = {}
        
        # CPU内存
        memory_info = self.process.memory_info()
        info['cpu_memory_mb'] = memory_info.rss / (1024 * 1024)  # MB
        info['cpu_memory_percent'] = self.process.memory_percent()
        
        # GPU内存
        gpu_info = get_gpu_memory_info()
        info['gpu_allocated_mb'] = gpu_info['allocated_mb']
        info['gpu_reserved_mb'] = gpu_info['reserved_mb']
        info['gpu_cached_mb'] = gpu_info['cached_mb']
        info['gpu_free_mb'] = gpu_info['free_mb']
        info['gpu_total_mb'] = gpu_info['total_mb']
        info['gpu_utilization'] = gpu_info['utilization']
        
        return info
    
    def log_memory_usage(self, prefix: str = "", force_gc: bool = False, log_level: int = logging.INFO):
        """
        记录当前内存使用情况
        
        Args:
            prefix: 日志前缀
            force_gc: 是否强制垃圾回收
            log_level: 日志级别
        """
        if force_gc:
            cleanup_cuda_memory()
        
        info = self._get_memory_info()
        timestamp = time.time()
        
        # 更新历史记录
        self.history['timestamps'].append(timestamp)
        self.history['cpu_memory_mb'].append(info['cpu_memory_mb'])
        self.history['gpu_allocated_mb'].append(info['gpu_allocated_mb'])
        self.history['gpu_reserved_mb'].append(info['gpu_reserved_mb'])
        self.history['gpu_utilization'].append(info['gpu_utilization'])
        
        # 限制历史记录大小
        max_history = 1000
        if len(self.history['timestamps']) > max_history:
            for key in self.history:
                self.history[key] = self.history[key][-max_history:]
        
        # 更新峰值内存
        if info['cpu_memory_mb'] > self.peak_memory['cpu_memory_mb']:
            self.peak_memory['cpu_memory_mb'] = info['cpu_memory_mb']
            self.peak_memory['timestamp'] = timestamp
            
        if info['gpu_allocated_mb'] > self.peak_memory['gpu_allocated_mb']:
            self.peak_memory['gpu_allocated_mb'] = info['gpu_allocated_mb']
            self.peak_memory['timestamp'] = timestamp
        
        # 计算内存增长
        memory_growth = info['cpu_memory_mb'] - self.baseline['cpu_memory_mb']
        gpu_growth = info['gpu_allocated_mb'] - self.baseline['gpu_allocated_mb']
        
        # 构建日志消息
        log_msg = f"{prefix}内存使用情况: "
        log_msg += f"CPU={info['cpu_memory_mb']:.1f}MB (+{memory_growth:.1f}MB), "
        log_msg += f"GPU分配={info['gpu_allocated_mb']:.1f}MB (+{gpu_growth:.1f}MB), "
        log_msg += f"GPU保留={info['gpu_reserved_mb']:.1f}MB, "
        log_msg += f"GPU利用率={info['gpu_utilization']:.1f}%, "
        log_msg += f"GPU可用={info['gpu_free_mb']:.1f}MB"
        
        self.logger.log(log_level, log_msg)
        
        # 记录到TensorBoard
        if self.log_to_tensorboard and self.tensorboard_writer is not None:
            global_step = len(self.history['timestamps'])
            self.tensorboard_writer.add_scalar('Memory/CPU_MB', info['cpu_memory_mb'], global_step)
            self.tensorboard_writer.add_scalar('Memory/GPU_Allocated_MB', info['gpu_allocated_mb'], global_step)
            self.tensorboard_writer.add_scalar('Memory/GPU_Reserved_MB', info['gpu_reserved_mb'], global_step)
            self.tensorboard_writer.add_scalar('Memory/GPU_Utilization', info['gpu_utilization'], global_step)
        
        # 检查是否超过警报阈值
        if memory_growth > self.alert_threshold_mb:
            self.logger.warning(
                f"CPU内存使用超过警报阈值: {memory_growth:.1f}MB > {self.alert_threshold_mb:.1f}MB"
            )
            
        if gpu_growth > self.alert_threshold_mb:
            self.logger.warning(
                f"GPU内存使用超过警报阈值: {gpu_growth:.1f}MB > {self.alert_threshold_mb:.1f}MB"
            )
        
        return info
    
    def cleanup_memory(self, force: bool = False):
        """
        清理内存
        
        Args:
            force: 是否强制清理
        """
        # 记录清理前的内存状态
        before = self._get_memory_info()
        
        # 清理内存
        cleanup_cuda_memory(force_cache_clear=force)
        
        # 记录清理后的内存状态
        after = self._get_memory_info()
        
        # 计算清理效果
        cpu_freed = before['cpu_memory_mb'] - after['cpu_memory_mb']
        gpu_freed = before['gpu_allocated_mb'] - after['gpu_allocated_mb']
        
        self.logger.info(
            f"内存清理完成: CPU释放={cpu_freed:.1f}MB, GPU释放={gpu_freed:.1f}MB"
        )
    
    @contextmanager
    def monitor_block(self, block_name: str, log_level: int = logging.DEBUG):
        """
        监控代码块的内存使用
        
        Args:
            block_name: 代码块名称
            log_level: 日志级别
        """
        start_info = self._get_memory_info()
        self.logger.log(log_level, f"开始执行 {block_name}")
        
        start_time = time.time()
        
        try:
            yield
        finally:
            end_time = time.time()
            end_info = self._get_memory_info()
            
            execution_time = end_time - start_time
            cpu_diff = end_info['cpu_memory_mb'] - start_info['cpu_memory_mb']
            gpu_diff = end_info['gpu_allocated_mb'] - start_info['gpu_allocated_mb']
            
            self.logger.log(
                log_level,
                f"完成 {block_name}: "
                f"耗时={execution_time:.3f}秒, "
                f"CPU内存变化={cpu_diff:+.1f}MB, "
                f"GPU内存变化={gpu_diff:+.1f}MB"
            )
    
    def check_memory_leak(self, threshold_mb: float = 100.0) -> bool:
        """
        检查是否有内存泄漏
        
        Args:
            threshold_mb: 内存增长阈值(MB)
            
        Returns:
            是否检测到内存泄漏
        """
        current_info = self._get_memory_info()
        
        # 计算内存增长
        cpu_growth = current_info['cpu_memory_mb'] - self.baseline['cpu_memory_mb']
        gpu_growth = current_info['gpu_allocated_mb'] - self.baseline['gpu_allocated_mb']
        
        # 检查是否超过阈值
        cpu_leak = cpu_growth > threshold_mb
        gpu_leak = gpu_growth > threshold_mb
        
        if cpu_leak:
            self.logger.warning(
                f"检测到可能的CPU内存泄漏: "
                f"内存增长 {cpu_growth:.1f}MB (阈值: {threshold_mb}MB)"
            )
            
        if gpu_leak:
            self.logger.warning(
                f"检测到可能的GPU内存泄漏: "
                f"内存增长 {gpu_growth:.1f}MB (阈值: {threshold_mb}MB)"
            )
        
        return cpu_leak or gpu_leak
    
    def reset_baseline(self, force_cleanup: bool = True):
        """
        重置基线内存
        
        Args:
            force_cleanup: 是否强制清理内存
        """
        if force_cleanup:
            self.cleanup_memory(force=True)
            
        self.baseline = self._get_memory_info()
        self.logger.info(
            f"重置内存基线: "
            f"CPU={self.baseline['cpu_memory_mb']:.1f}MB, "
            f"GPU={self.baseline['gpu_allocated_mb']:.1f}MB"
        )
    
    def get_memory_summary(self) -> Dict[str, float]:
        """
        获取内存使用摘要
        
        Returns:
            内存使用摘要字典
        """
        current_info = self._get_memory_info()
        
        return {
            'current_cpu_memory_mb': current_info['cpu_memory_mb'],
            'peak_cpu_memory_mb': self.peak_memory['cpu_memory_mb'],
            'cpu_memory_growth_mb': current_info['cpu_memory_mb'] - self.baseline['cpu_memory_mb'],
            'current_gpu_memory_mb': current_info['gpu_allocated_mb'],
            'peak_gpu_memory_mb': self.peak_memory['gpu_allocated_mb'],
            'gpu_memory_growth_mb': current_info['gpu_allocated_mb'] - self.baseline['gpu_allocated_mb'],
            'gpu_memory_utilization': current_info['gpu_utilization'],
            'gpu_memory_reserved_mb': current_info['gpu_reserved_mb'],
            'gpu_memory_cached_mb': current_info['gpu_cached_mb'],
            'gpu_memory_free_mb': current_info['gpu_free_mb']
        }
    
    def get_memory_history(self) -> Dict[str, List]:
        """
        获取内存使用历史
        
        Returns:
            内存使用历史字典
        """
        return self.history
    
    def start_auto_monitoring(self):
        """启动自动监控"""
        if self.monitoring_thread is not None and self.monitoring_thread.is_alive():
            return
            
        self.stop_monitoring.clear()
        self.monitoring_thread = threading.Thread(target=self._monitoring_worker, daemon=True)
        self.monitoring_thread.start()
        self.logger.info(f"启动自动内存监控，检查间隔: {self.check_interval}秒")
    
    def stop_auto_monitoring(self):
        """停止自动监控"""
        if self.monitoring_thread is not None and self.monitoring_thread.is_alive():
            self.stop_monitoring.set()
            self.monitoring_thread.join(timeout=1.0)
            self.logger.info("停止自动内存监控")
    
    def _monitoring_worker(self):
        """监控工作线程"""
        try:
            while not self.stop_monitoring.is_set():
                # 记录内存使用
                self.log_memory_usage(prefix="[自动监控] ", log_level=logging.DEBUG)
                
                # 检查内存泄漏
                self.check_memory_leak(threshold_mb=self.alert_threshold_mb)
                
                # 等待下一次检查
                self.stop_monitoring.wait(timeout=self.check_interval)
        except Exception as e:
            self.logger.error(f"内存监控线程异常: {e}")
    
    def set_tensorboard_writer(self, writer):
        """设置TensorBoard写入器"""
        self.tensorboard_writer = writer
        self.log_to_tensorboard = True
    
    def plot_memory_history(self, save_path: Optional[str] = None):
        """
        绘制内存使用历史图表
        
        Args:
            save_path: 保存路径
        """
        try:
            import matplotlib.pyplot as plt
            from matplotlib.dates import DateFormatter
            import datetime
            
            # 转换时间戳为datetime对象
            dates = [datetime.datetime.fromtimestamp(ts) for ts in self.history['timestamps']]
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
            
            # CPU内存图表
            ax1.plot(dates, self.history['cpu_memory_mb'], 'b-', label='CPU Memory')
            ax1.set_ylabel('Memory (MB)')
            ax1.set_title('CPU Memory Usage')
            ax1.grid(True)
            ax1.legend()
            
            # GPU内存图表
            ax2.plot(dates, self.history['gpu_allocated_mb'], 'r-', label='GPU Allocated')
            ax2.plot(dates, self.history['gpu_reserved_mb'], 'g-', label='GPU Reserved')
            ax2.set_ylabel('Memory (MB)')
            ax2.set_title('GPU Memory Usage')
            ax2.grid(True)
            ax2.legend()
            
            # 设置x轴格式
            date_format = DateFormatter('%H:%M:%S')
            ax2.xaxis.set_major_formatter(date_format)
            fig.autofmt_xdate()
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
                self.logger.info(f"内存使用历史图表已保存到: {save_path}")
            else:
                plt.show()
                
        except ImportError:
            self.logger.warning("无法绘制图表，请安装matplotlib")


def log_gpu_memory_status(logger: logging.Logger):
    """记录GPU内存状态"""
    if not torch.cuda.is_available():
        logger.info("GPU不可用")
        return
    
    gpu_info = get_gpu_memory_info()
    
    logger.info(
        f"GPU内存状态: "
        f"已分配={gpu_info['allocated_mb']:.2f}MB, "
        f"已保留={gpu_info['reserved_mb']:.2f}MB, "
        f"总计={gpu_info['total_mb']:.2f}MB, "
        f"利用率={gpu_info['utilization']:.1f}%"
    ) 