"""
评估指标模块
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Tuple


class RunningAverage:
    """
    运行平均值计算器
    """

    def __init__(self) -> None:
        self.count = 0
        self.total = 0.0
        self.avg = 0.0

    def update(self, value: float, count: int = 1) -> None:
        """
        更新运行平均值

        Args:
            value: 新值
            count: 样本数量
        """
        self.total += value * count
        self.count += count
        self.avg = self.total / (self.count + 1e-15)

    def reset(self):
        """重置计数器"""
        self.count = 0
        self.total = 0.0
        self.avg = 0.0

    def __call__(self) -> float:
        """
        返回当前运行平均值

        Returns:
            运行平均值
        """
        return self.avg


def iou_coef(y_true: torch.Tensor, y_pred: torch.Tensor, smooth: float = 1e-6) -> torch.Tensor:
    """
    计算IoU系数 (Jaccard系数)

    Args:
        y_true: 真实标签 [B, C, H, W]
        y_pred: 预测结果 [B, C, H, W]
        smooth: 平滑参数

    Returns:
        IoU系数
    """
    # 🔥 修复channels_last兼容性：使用reshape代替view
    y_true_f = y_true.reshape(y_true.size(0), -1)
    y_pred_f = y_pred.reshape(y_pred.size(0), -1)
    
    # 计算交集和并集
    intersection = (y_true_f * y_pred_f).sum(dim=1)
    union = y_true_f.sum(dim=1) + y_pred_f.sum(dim=1) - intersection
    
    # 计算IoU
    iou = (intersection + smooth) / (union + smooth)
    return iou.mean()


def dice_coef(y_true: torch.Tensor, y_pred: torch.Tensor, smooth: float = 1e-6) -> torch.Tensor:
    """
    计算Dice系数

    Args:
        y_true: 真实标签 [B, C, H, W]
        y_pred: 预测结果 [B, C, H, W]
        smooth: 平滑参数

    Returns:
        Dice系数
    """
    # 🔥 修复channels_last兼容性：使用reshape代替view
    y_true_f = y_true.reshape(y_true.size(0), -1)
    y_pred_f = y_pred.reshape(y_pred.size(0), -1)
    
    # 计算交集
    intersection = (y_true_f * y_pred_f).sum(dim=1)
    
    # 计算Dice系数
    dice = (2. * intersection + smooth) / (y_true_f.sum(dim=1) + y_pred_f.sum(dim=1) + smooth)
    return dice.mean()


def pixel_accuracy(y_true: torch.Tensor, y_pred: torch.Tensor, threshold: float = 0.5) -> torch.Tensor:
    """
    计算像素准确率
    
    Args:
        y_true: 真实标签 [B, C, H, W]
        y_pred: 预测结果 [B, C, H, W] 
        threshold: 二值化阈值
        
    Returns:
        像素准确率
    """
    y_pred_binary = (y_pred > threshold).float()
    correct = (y_pred_binary == y_true).float()
    return correct.mean()


def precision_recall_f1(y_true: torch.Tensor, y_pred: torch.Tensor, threshold: float = 0.5) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    计算精确率、召回率和F1分数
    
    Args:
        y_true: 真实标签 [B, C, H, W]
        y_pred: 预测结果 [B, C, H, W]
        threshold: 二值化阈值
        
    Returns:
        (precision, recall, f1)
    """
    y_pred_binary = (y_pred > threshold).float()
    
    # 使用reshape代替view以处理非连续张量
    y_true_f = y_true.reshape(-1)
    y_pred_f = y_pred_binary.reshape(-1)
    
    # 计算TP, FP, FN
    tp = (y_true_f * y_pred_f).sum()
    fp = ((1 - y_true_f) * y_pred_f).sum()
    fn = (y_true_f * (1 - y_pred_f)).sum()
    
    # 计算指标
    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)
    f1 = 2 * precision * recall / (precision + recall + 1e-8)
    
    return precision, recall, f1


def compute_metrics(y_pred: torch.Tensor, y_true: torch.Tensor, threshold: float = 0.5) -> Dict[str, float]:
    """
    计算所有评估指标 - 支持多标签分割
    
    Args:
        y_pred: 预测结果 [B, C, H, W]
        y_true: 真实标签 [B, C, H, W]
        threshold: 二值化阈值
        
    Returns:
        包含所有指标的字典
    """
    # 确保数据类型正确
    y_pred = y_pred.float()
    y_true = y_true.float()
    
    # 计算各种指标
    iou = iou_coef(y_true, y_pred)
    dice = dice_coef(y_true, y_pred)
    pixel_acc = pixel_accuracy(y_true, y_pred, threshold)
    precision, recall, f1 = precision_recall_f1(y_true, y_pred, threshold)
    
    return {
        'val_iou': iou.item(),
        'val_dice': dice.item(),
        'val_pixel_acc': pixel_acc.item(),
        'val_precision': precision.item(),
        'val_recall': recall.item(),
        'val_f1': f1.item()
    }


def compute_metrics_per_class(y_pred: torch.Tensor, y_true: torch.Tensor, threshold: float = 0.5, num_classes: int = 3) -> Dict[str, Dict[str, float]]:
    """
    计算每个类别的评估指标 - 多标签分割
    
    Args:
        y_pred: 预测结果 [B, C, H, W]
        y_true: 真实标签 [B, C, H, W]
        threshold: 二值化阈值
        num_classes: 类别数量
        
    Returns:
        每个类别的指标字典
    """
    class_names = ['background', 'main_track', 'fork_track']
    per_class_metrics = {}
    
    for class_idx in range(num_classes):
        # 提取单个类别的预测和真实标签
        y_pred_class = y_pred[:, class_idx:class_idx+1, :, :]
        y_true_class = y_true[:, class_idx:class_idx+1, :, :]
        
        # 计算该类别的指标
        iou = iou_coef(y_true_class, y_pred_class)
        dice = dice_coef(y_true_class, y_pred_class)
        precision, recall, f1 = precision_recall_f1(y_true_class, y_pred_class, threshold)
        
        class_name = class_names[class_idx] if class_idx < len(class_names) else f'class_{class_idx}'
        per_class_metrics[class_name] = {
            'iou': iou.item(),
            'dice': dice.item(),
            'precision': precision.item(),
            'recall': recall.item(),
            'f1': f1.item()
        }
    
    return per_class_metrics 