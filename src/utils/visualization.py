"""
可视化工具模块
"""

import matplotlib.pyplot as plt
import numpy as np
import torch
import cv2
from typing import List, Dict, Any, Optional, Union
from pathlib import Path


def visualize_predictions(
    images: torch.Tensor,
    true_masks: torch.Tensor,
    pred_masks: torch.Tensor,
    save_path: Optional[Path] = None,
    num_samples: int = 4,
    figsize: tuple = (15, 10),
    threshold: float = 0.5,
    style: str = 'modern'
):
    """
    可视化预测结果 - 支持多标签分割
    
    Args:
        images: 原始图像 [B, C, H, W]
        true_masks: 真实掩码 [B, C, H, W]
        pred_masks: 预测掩码 [B, C, H, W]
        save_path: 保存路径
        num_samples: 显示样本数量
        figsize: 图像大小
        threshold: 二值化阈值
        style: 可视化风格 ('classic', 'modern')
    """
    batch_size = images.size(0)
    num_samples = min(num_samples, batch_size)
    num_classes = true_masks.size(1)
    
    # 现代化颜色方案
    if style == 'modern':
        class_colors = {
            0: [30, 30, 30],        # 背景 - 深灰色
            1: [255, 140, 0],       # 主轨道 - 橙色
            2: [0, 191, 255],       # 分叉轨道 - 天蓝色
            'overlap': [255, 20, 147]  # 重叠 - 深粉色
        }
    else:
        class_colors = {
            0: [0, 0, 0],           # 背景 - 黑色
            1: [255, 0, 0],         # 主轨道 - 红色
            2: [0, 255, 0],         # 分叉轨道 - 绿色
            'overlap': [255, 255, 0]   # 重叠 - 黄色
        }
    
    # 创建子图：原图 + 每个类别的真实/预测 + 合成图
    cols = 2 + num_classes * 2  # 原图 + 合成图 + 每个类别的真实/预测
    fig, axes = plt.subplots(num_samples, cols, figsize=(cols * 3, num_samples * 3))
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_samples):
        # 转换为numpy数组
        img = images[i].cpu().numpy()
        true_mask = true_masks[i].cpu().numpy()
        pred_mask = pred_masks[i].cpu().numpy()
        
        # 处理图像通道
        if img.shape[0] == 3:
            img = np.transpose(img, (1, 2, 0))
        else:
            img = img[0]
        
        # 显示原始图像
        axes[i, 0].imshow(img, cmap='gray' if len(img.shape) == 2 else None)
        axes[i, 0].set_title(f'原始图像 {i+1}')
        axes[i, 0].axis('off')
        
        # 显示每个类别的真实和预测掩码
        col_idx = 1
        for class_idx in range(num_classes):
            class_name = ['背景', '主轨道', '分叉轨道'][class_idx]
            
            # 真实掩码
            axes[i, col_idx].imshow(true_mask[class_idx], cmap='gray', vmin=0, vmax=1)
            axes[i, col_idx].set_title(f'{class_name} 真实')
            axes[i, col_idx].axis('off')
            col_idx += 1
            
            # 预测掩码（二值化）
            pred_binary = (pred_mask[class_idx] > threshold).astype(float)
            axes[i, col_idx].imshow(pred_binary, cmap='gray', vmin=0, vmax=1)
            axes[i, col_idx].set_title(f'{class_name} 预测')
            axes[i, col_idx].axis('off')
            col_idx += 1
        
        # 创建合成彩色掩码
        h, w = true_mask[0].shape
        true_colored = np.zeros((h, w, 3), dtype=np.uint8)
        pred_colored = np.zeros((h, w, 3), dtype=np.uint8)
        
        for class_idx in range(1, num_classes):  # 跳过背景
            # 真实掩码彩色化
            true_colored[true_mask[class_idx] > 0.5] = class_colors[class_idx]
            # 预测掩码彩色化
            pred_colored[pred_mask[class_idx] > threshold] = class_colors[class_idx]
        
        # 显示合成图
        axes[i, -1].imshow(pred_colored)
        axes[i, -1].set_title(f'合成预测 {i+1}')
        axes[i, -1].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, bbox_inches='tight', dpi=150)
        plt.close()
    else:
        plt.show()


def plot_training_history(
    history: List[Dict[str, Any]],
    save_path: Optional[Path] = None,
    figsize: tuple = (15, 10)
):
    """
    绘制训练历史曲线
    
    Args:
        history: 训练历史记录
        save_path: 保存路径
        figsize: 图像大小
    """
    if not history:
        print("训练历史为空")
        return
    
    # 提取指标
    epochs = [h['epoch'] for h in history]
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    axes = axes.flatten()
    
    # 绘制损失曲线
    if 'train_loss' in history[0]:
        train_losses = [h['train_loss'] for h in history]
        axes[0].plot(epochs, train_losses, label='训练损失', color='blue')
    
    if 'val_loss' in history[0]:
        val_losses = [h['val_loss'] for h in history if 'val_loss' in h]
        val_epochs = [h['epoch'] for h in history if 'val_loss' in h]
        axes[0].plot(val_epochs, val_losses, label='验证损失', color='red')
    
    axes[0].set_title('损失曲线')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # 绘制IoU曲线
    if 'val_iou' in history[0]:
        val_ious = [h['val_iou'] for h in history if 'val_iou' in h]
        val_epochs = [h['epoch'] for h in history if 'val_iou' in h]
        axes[1].plot(val_epochs, val_ious, label='IoU', color='green')
        axes[1].set_title('IoU曲线')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('IoU')
        axes[1].legend()
        axes[1].grid(True)
    
    # 绘制学习率曲线
    if 'lr' in history[0]:
        lrs = [h['lr'] for h in history]
        axes[2].plot(epochs, lrs, label='学习率', color='orange')
        axes[2].set_title('学习率曲线')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('Learning Rate')
        axes[2].legend()
        axes[2].grid(True)
        axes[2].set_yscale('log')
    
    # 绘制其他指标
    metric_names = ['val_dice', 'val_f1', 'val_precision', 'val_recall']
    available_metrics = []
    for metric in metric_names:
        if metric in history[0]:
            available_metrics.append(metric)
    
    if available_metrics:
        for metric in available_metrics[:4]:  # 最多显示4个指标
            values = [h[metric] for h in history if metric in h]
            epochs_with_metric = [h['epoch'] for h in history if metric in h]
            axes[3].plot(epochs_with_metric, values, label=metric.replace('val_', ''))
        
        axes[3].set_title('其他评估指标')
        axes[3].set_xlabel('Epoch')
        axes[3].set_ylabel('Score')
        axes[3].legend()
        axes[3].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, bbox_inches='tight', dpi=150)
        plt.close()
    else:
        plt.show() 

def load_mask(mask_path: Union[str, Path]) -> np.ndarray:
    """
    加载掩码文件，支持PNG和NPY格式
    
    Args:
        mask_path: 掩码文件路径
        
    Returns:
        掩码数组
    """
    mask_path = Path(mask_path)
    
    if mask_path.suffix in ['.png', '.jpg', '.jpeg']:
        # 加载图像格式
        # 先尝试加载为彩色图像，检查是否是多标签PNG
        img = cv2.imread(str(mask_path), cv2.IMREAD_COLOR)
        if img is not None and img.shape[2] == 3:
            # 检查是否是多标签PNG格式（只有255和0值）
            unique_values = np.unique(img)
            if len(unique_values) <= 2 and set(unique_values).issubset({0, 255}):
                # 是多标签PNG格式，转换为多通道掩码
                mask = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.float32)
                mask[:, :, 0] = (img[:, :, 0] > 127).astype(np.float32)  # B: 背景
                mask[:, :, 1] = (img[:, :, 1] > 127).astype(np.float32)  # G: 主轨道
                mask[:, :, 2] = (img[:, :, 2] > 127).astype(np.float32)  # R: 分叉轨道
                return mask
        
        # 普通灰度掩码
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if mask is None:
            raise ValueError(f"无法加载掩码文件: {mask_path}")
    else:
        raise ValueError(f"不支持的掩码格式: {mask_path.suffix}, 仅支持PNG格式")
    
    return mask


def save_mask_visualization(mask: np.ndarray, save_path: Union[str, Path], 
                           format: str = 'png', colormap: dict = None):
    """
    保存掩码的可视化结果
    
    Args:
        mask: 掩码数组 (H, W) 或 (H, W, C)
        save_path: 保存路径
        format: 保存格式 ('png' 或 'npy')
        colormap: 类别颜色映射
    """
    save_path = Path(save_path)
    
    if colormap is None:
        colormap = {
            0: [0, 0, 0],       # 背景 - 黑色
            1: [255, 0, 0],     # 主轨道 - 红色
            2: [0, 255, 0]      # 分叉轨道 - 绿色
        }
    
    # 统一使用PNG格式
    if len(mask.shape) == 3 and mask.shape[2] == 3:
        # 多标签格式 - 保存为BGR PNG
        bgr_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        bgr_mask[:, :, 0] = (mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
        bgr_mask[:, :, 1] = (mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
        bgr_mask[:, :, 2] = (mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
        cv2.imwrite(str(save_path.with_suffix('.png')), bgr_mask)
    else:
        # 转换为彩色可视化并保存
        if len(mask.shape) == 3:
            # 多通道掩码，转换为单通道
            single_channel = np.zeros(mask.shape[:2], dtype=np.uint8)
            single_channel[mask[:, :, 1] > 0.5] = 1   # 主轨道
            single_channel[mask[:, :, 2] > 0.5] = 2   # 分叉轨道
            mask = single_channel
        
        # 创建彩色图像
        colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        for class_id, color in colormap.items():
            colored_mask[mask == class_id] = color
        
        cv2.imwrite(str(save_path.with_suffix('.png')), 
                    cv2.cvtColor(colored_mask, cv2.COLOR_RGB2BGR))


def create_multilabel_visualization_for_tensorboard(
    images: torch.Tensor,
    masks: torch.Tensor,
    predictions: Optional[torch.Tensor] = None,
    num_samples: int = 4,
    threshold: float = 0.5
) -> torch.Tensor:
    """
    为TensorBoard创建美观的多标签可视化
    
    Args:
        images: 原始图像 [B, C, H, W] 或 [B, H, W, C]
        masks: 真实掩码 [B, C, H, W] 或 [B, H, W, C]
        predictions: 预测掩码 [B, C, H, W] 或 [B, H, W, C]
        num_samples: 可视化样本数
        threshold: 二值化阈值
        
    Returns:
        可视化图像 [N, C, H, W] 适合TensorBoard
    """
    # 确保输入是CHW格式
    if images.shape[-1] == 3:  # HWC格式
        images = images.permute(0, 3, 1, 2)
    if masks.shape[-1] == 3:  # HWC格式
        masks = masks.permute(0, 3, 1, 2)
    if predictions is not None and predictions.shape[-1] == 3:
        predictions = predictions.permute(0, 3, 1, 2)
    
    batch_size = min(images.size(0), num_samples)
    
    # 美观的颜色方案
    colors = {
        'background': torch.tensor([30, 30, 30], dtype=torch.float32) / 255,
        'main_track': torch.tensor([255, 140, 0], dtype=torch.float32) / 255,
        'fork_track': torch.tensor([0, 191, 255], dtype=torch.float32) / 255,
        'overlap': torch.tensor([255, 20, 147], dtype=torch.float32) / 255
    }
    
    visualizations = []
    
    for i in range(batch_size):
        # 获取单个样本
        image = images[i]
        mask = masks[i]
        
        # 归一化图像到[0,1]
        if image.max() > 1:
            image = image / 255.0
        
        # 创建彩色掩码可视化
        colored_mask = create_colored_mask_tensor(mask, colors, threshold)
        
        # 创建叠加图像
        overlay = image * 0.6 + colored_mask * 0.4
        
        if predictions is not None:
            pred = predictions[i]
            colored_pred = create_colored_mask_tensor(pred, colors, threshold)
            pred_overlay = image * 0.6 + colored_pred * 0.4
            
            # 组合：原图 | 真实掩码 | 预测掩码 | 叠加
            row1 = torch.cat([image, colored_mask], dim=2)
            row2 = torch.cat([overlay, pred_overlay], dim=2)
            combined = torch.cat([row1, row2], dim=1)
        else:
            # 组合：原图 | 掩码 | 叠加
            combined = torch.cat([image, colored_mask, overlay], dim=2)
        
        visualizations.append(combined)
    
    return torch.stack(visualizations)


def create_colored_mask_tensor(
    mask: torch.Tensor,
    colors: Dict[str, torch.Tensor],
    threshold: float = 0.5
) -> torch.Tensor:
    """
    创建彩色掩码张量
    
    Args:
        mask: 掩码 [C, H, W]，C=3 (背景、主轨道、分叉轨道)
        colors: 颜色映射
        threshold: 二值化阈值
        
    Returns:
        彩色掩码 [3, H, W] RGB格式
    """
    h, w = mask.shape[1:]
    colored = torch.zeros(3, h, w, device=mask.device)
    
    # 提取各通道
    if mask.shape[0] >= 3:
        background = mask[0] > threshold
        main_track = mask[1] > threshold
        fork_track = mask[2] > threshold
    else:
        # 单通道掩码
        background = mask[0] == 0
        main_track = mask[0] == 1
        fork_track = mask[0] == 2
    
    # 计算重叠区域
    overlap = main_track & fork_track
    
    # 应用颜色
    for c in range(3):
        colored[c][background] = colors['background'][c]
        colored[c][main_track] = colors['main_track'][c]
        colored[c][fork_track] = colors['fork_track'][c]
        colored[c][overlap] = colors['overlap'][c]
    
    return colored


def log_multilabel_predictions_to_tensorboard(
    writer,
    images: torch.Tensor,
    masks: torch.Tensor,
    predictions: torch.Tensor,
    global_step: int,
    tag_prefix: str = 'Predictions',
    num_samples: int = 4
):
    """
    将多标签预测记录到TensorBoard
    
    Args:
        writer: TensorBoard SummaryWriter
        images: 原始图像
        masks: 真实掩码
        predictions: 预测掩码
        global_step: 全局步数
        tag_prefix: 标签前缀
        num_samples: 样本数量
    """
    # 创建可视化
    viz = create_multilabel_visualization_for_tensorboard(
        images, masks, predictions, num_samples
    )
    
    # 记录到TensorBoard
    writer.add_images(f'{tag_prefix}/MultiLabel', viz, global_step)
    
    # 记录各类别的IoU
    with torch.no_grad():
        # 计算各类别IoU
        for class_idx, class_name in enumerate(['background', 'main_track', 'fork_track']):
            if class_idx < masks.shape[1]:
                mask_class = masks[:, class_idx] > 0.5
                pred_class = predictions[:, class_idx] > 0.5
                
                intersection = (mask_class & pred_class).float().sum(dim=(1, 2))
                union = (mask_class | pred_class).float().sum(dim=(1, 2))
                
                iou = (intersection / (union + 1e-6)).mean()
                writer.add_scalar(f'{tag_prefix}/IoU/{class_name}', iou, global_step)
    
    # 记录重叠区域统计
    if masks.shape[1] >= 3:
        overlap_true = (masks[:, 1] > 0.5) & (masks[:, 2] > 0.5)
        overlap_pred = (predictions[:, 1] > 0.5) & (predictions[:, 2] > 0.5)
        
        overlap_ratio_true = overlap_true.float().mean()
        overlap_ratio_pred = overlap_pred.float().mean()
        
        writer.add_scalar(f'{tag_prefix}/Overlap/true_ratio', overlap_ratio_true, global_step)
        writer.add_scalar(f'{tag_prefix}/Overlap/pred_ratio', overlap_ratio_pred, global_step)
