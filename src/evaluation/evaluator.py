"""
评估器模块
提供模型评估功能
"""

import time
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import cv2
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt

# 可选依赖
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    print("警告: seaborn未安装，部分可视化功能将不可用")

from ..utils.metrics import compute_metrics, iou_coef, dice_coef, precision_recall_f1
from ..utils.visualization import visualize_predictions


class Evaluator:
    """
    模型评估器
    """
    
    def __init__(self, 
                 model: torch.nn.Module,
                 test_loader: DataLoader,
                 device: torch.device,
                 logger: Any):
        """
        初始化评估器
        
        Args:
            model: 要评估的模型
            test_loader: 测试数据加载器
            device: 设备
            logger: 日志记录器
        """
        self.model = model
        self.test_loader = test_loader
        self.device = device
        self.logger = logger
    
    def evaluate(self, 
                 save_predictions: bool = False,
                 save_dir: Optional[Path] = None,
                 threshold: float = 0.5) -> Dict[str, Any]:
        """
        评估模型
        
        Args:
            save_predictions: 是否保存预测结果
            save_dir: 保存目录
            threshold: 二值化阈值
            
        Returns:
            评估指标字典
        """
        self.model.eval()
        
        all_metrics = []
        total_inference_time = 0
        num_samples = 0
        
        # 用于可视化的数据收集
        visualization_data = []
        max_vis_samples = 10  # 最多收集10个样本用于可视化
        
        self.logger.info("开始模型评估...")
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(self.test_loader):
                # 移动数据到设备
                images = batch['image'].to(self.device)
                if 'mask' in batch:
                    masks = batch['mask'].to(self.device)
                else:
                    # 如果没有mask，创建虚拟mask
                    masks = torch.zeros_like(images)
                    self.logger.warning(f"批次 {batch_idx} 没有找到mask，使用虚拟mask")
                
                # 推理
                start_time = time.time()
                outputs = self.model(images)
                inference_time = time.time() - start_time
                
                total_inference_time += inference_time
                num_samples += images.size(0)
                
                # 调试信息：检查模型输出
                if batch_idx == 0:
                    self.logger.info(f"模型输出形状: {outputs.shape}")
                    self.logger.info(f"模型输出范围: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
                    self.logger.info(f"掩码形状: {masks.shape}")
                    self.logger.info(f"掩码范围: [{masks.min().item():.4f}, {masks.max().item():.4f}]")
                    self.logger.info(f"掩码唯一值: {torch.unique(masks).cpu().numpy()}")
                
                # 应用激活函数
                if hasattr(self.model, 'activation'):
                    if self.model.activation == 'sigmoid':
                        predictions = torch.sigmoid(outputs)
                    elif self.model.activation == 'softmax':
                        predictions = torch.softmax(outputs, dim=1)
                    else:
                        predictions = outputs
                else:
                    # 根据输出范围判断是否需要激活
                    if outputs.min() < 0 or outputs.max() > 1:
                        predictions = torch.sigmoid(outputs)
                    else:
                        predictions = outputs
                
                if batch_idx == 0:
                    self.logger.info(f"激活后预测范围: [{predictions.min().item():.4f}, {predictions.max().item():.4f}]")
                
                # 计算指标前的详细检查
                try:
                    # 检查数据统计
                    pred_binary = (predictions > threshold).float()
                    mask_binary = masks.float()
                    
                    if batch_idx == 0:
                        self.logger.info(f"二值化预测的正样本比例: {pred_binary.mean().item():.4f}")
                        self.logger.info(f"真实掩码的正样本比例: {mask_binary.mean().item():.4f}")
                    
                    # 计算指标
                    batch_metrics = compute_metrics(predictions, masks, threshold)
                    all_metrics.append(batch_metrics)
                    
                    # 记录一些批次的详细信息
                    if batch_idx < 5:
                        self.logger.info(f"批次 {batch_idx} 指标: IoU={batch_metrics['val_iou']:.4f}, "
                                       f"Dice={batch_metrics['val_dice']:.4f}, "
                                       f"F1={batch_metrics['val_f1']:.4f}")
                
                except Exception as e:
                    self.logger.warning(f"计算批次 {batch_idx} 指标时出错: {e}")
                    continue
                
                # 收集可视化数据
                if len(visualization_data) < max_vis_samples:
                    visualization_data.append({
                        'image': images.cpu(),
                        'mask': masks.cpu(),
                        'prediction': predictions.cpu(),
                        'filename': batch.get('filename', [f'batch_{batch_idx}_sample_0.png'])[0]
                    })
                
                # 保存预测结果（如果需要）
                if save_predictions and save_dir:
                    self._save_predictions(batch, predictions, save_dir, batch_idx)
                
                # 限制处理的批次数量（用于调试）
                if batch_idx >= 50:  # 只处理前50个批次进行调试
                    self.logger.info(f"调试模式：只处理前 {batch_idx + 1} 个批次")
                    break
        
        # 计算平均指标
        if all_metrics:
            avg_metrics = {}
            for key in all_metrics[0].keys():
                values = [m[key] for m in all_metrics]
                avg_metrics[key.replace('val_', '')] = np.mean(values)
                # 添加标准差信息
                avg_metrics[key.replace('val_', '') + '_std'] = np.std(values)
            
            self.logger.info(f"处理了 {len(all_metrics)} 个有效批次")
            self.logger.info(f"指标统计: IoU={avg_metrics['iou']:.4f}±{avg_metrics['iou_std']:.4f}")
        else:
            # 如果没有有效的指标，返回默认值
            avg_metrics = {
                'iou': 0.0,
                'dice': 0.0,
                'pixel_acc': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0
            }
            self.logger.warning("没有计算到有效的指标")
        
        # 计算FPS
        if total_inference_time > 0:
            fps = num_samples / total_inference_time
        else:
            fps = 0.0
        
        avg_metrics['fps'] = fps
        
        # 保存可视化结果
        if save_dir and visualization_data:
            self._save_visualizations(visualization_data, save_dir, threshold)
        
        return avg_metrics
    
    def _save_predictions(self, 
                         batch: Dict[str, Any], 
                         outputs: torch.Tensor,
                         save_dir: Path,
                         batch_idx: int):
        """
        保存预测结果
        
        Args:
            batch: 批次数据
            outputs: 模型输出
            save_dir: 保存目录
            batch_idx: 批次索引
        """
        pred_dir = save_dir / 'predictions'
        pred_dir.mkdir(parents=True, exist_ok=True)
        
        # 将输出转换为numpy
        predictions = outputs.cpu().numpy()
        
        # 获取掩码格式配置
        mask_format = self.config.get('data', {}).get('mask_format', 'png')
        use_multilabel = self.config.get('data', {}).get('use_multilabel', False)
        
        for i in range(predictions.shape[0]):
            filename = batch.get('filename', [f'batch_{batch_idx}_sample_{i}'])[i]
            filename_without_ext = Path(filename).stem
            
            if mask_format == 'npy':
                # 保存为.npy格式
                save_path = pred_dir / f'{filename_without_ext}.npy'
                if use_multilabel and predictions.shape[1] > 1:
                    # 多标签格式 (H, W, C)
                    pred_mask = predictions[i].transpose(1, 2, 0)  # CHW -> HWC
                    pred_mask = (pred_mask > 0.5).astype(np.float32)
                else:
                    pred_mask = predictions[i]
                np.save(str(save_path), pred_mask)
            else:
                # 保存为.png格式
                save_path = pred_dir / f'{filename_without_ext}.png'
                pred_mask = (predictions[i] > 0.5).astype(np.uint8)
                
                if pred_mask.shape[0] > 1:
                    # 多通道，转换为单通道可视化
                    single_channel = np.zeros(pred_mask.shape[1:], dtype=np.uint8)
                    single_channel[pred_mask[1] > 0] = 85   # 主轨道
                    single_channel[pred_mask[2] > 0] = 170  # 分叉轨道
                    pred_mask = single_channel
                else:
                    pred_mask = pred_mask[0] * 255
                
                cv2.imwrite(str(save_path), pred_mask)
    
    def _save_visualizations(self, 
                           visualization_data: List[Dict[str, Any]], 
                           save_dir: Path, 
                           threshold: float):
        """
        保存可视化结果
        
        Args:
            visualization_data: 可视化数据列表
            save_dir: 保存目录
            threshold: 二值化阈值
        """
        vis_dir = save_dir / 'visualizations'
        vis_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"保存 {len(visualization_data)} 个可视化样本到 {vis_dir}")
        
        for idx, data in enumerate(visualization_data):
            # 创建对比图
            self._create_comparison_plot(
                data['image'][0],  # 取第一个样本
                data['mask'][0],
                data['prediction'][0],
                data['filename'],
                vis_dir / f'comparison_{idx:03d}_{data["filename"]}',
                threshold
            )
    
    def _create_comparison_plot(self, 
                              image: torch.Tensor, 
                              mask: torch.Tensor, 
                              prediction: torch.Tensor,
                              filename: str,
                              save_path: Path,
                              threshold: float):
        """
        创建单个样本的对比图
        
        Args:
            image: 原始图像 [C, H, W]
            mask: 真实掩码 [C, H, W]
            prediction: 预测结果 [C, H, W]
            filename: 文件名
            save_path: 保存路径
            threshold: 二值化阈值
        """
        # 转换为numpy
        if image.dim() == 3 and image.shape[0] == 3:
            img_np = image.permute(1, 2, 0).numpy()
        else:
            img_np = image[0].numpy() if image.dim() == 3 else image.numpy()
        
        # 处理掩码和预测
        if mask.dim() == 3 and mask.shape[0] > 1:
            # 多通道掩码，合并为单通道
            mask_np = torch.argmax(mask, dim=0).numpy()
        else:
            mask_np = mask[0].numpy() if mask.dim() == 3 else mask.numpy()
        
        if prediction.dim() == 3 and prediction.shape[0] > 1:
            # 多通道预测，合并为单通道
            pred_np = torch.argmax(prediction, dim=0).numpy()
            pred_prob = torch.max(prediction, dim=0)[0].numpy()
        else:
            pred_prob = prediction[0].numpy() if prediction.dim() == 3 else prediction.numpy()
            pred_np = (pred_prob > threshold).astype(np.uint8)
        
        # 创建图形
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 原始图像
        if len(img_np.shape) == 3:
            axes[0, 0].imshow(img_np)
        else:
            axes[0, 0].imshow(img_np, cmap='gray')
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 真实掩码
        axes[0, 1].imshow(mask_np, cmap='viridis')
        axes[0, 1].set_title(f'真实掩码\n唯一值: {np.unique(mask_np)}')
        axes[0, 1].axis('off')
        
        # 预测概率
        axes[0, 2].imshow(pred_prob, cmap='hot', vmin=0, vmax=1)
        axes[0, 2].set_title(f'预测概率\n范围: [{pred_prob.min():.3f}, {pred_prob.max():.3f}]')
        axes[0, 2].axis('off')
        
        # 二值化预测
        axes[1, 0].imshow(pred_np, cmap='gray')
        axes[1, 0].set_title(f'二值化预测 (阈值={threshold})\n正样本比例: {pred_np.mean():.3f}')
        axes[1, 0].axis('off')
        
        # 重叠显示
        if len(img_np.shape) == 3:
            overlay = img_np.copy()
        else:
            overlay = np.stack([img_np, img_np, img_np], axis=-1)
        
        # 添加掩码覆盖 (绿色为真实，红色为预测)
        mask_colored = np.zeros_like(overlay)
        pred_colored = np.zeros_like(overlay)
        
        mask_colored[mask_np > 0] = [0, 1, 0]  # 绿色
        pred_colored[pred_np > 0] = [1, 0, 0]  # 红色
        
        axes[1, 1].imshow(overlay)
        axes[1, 1].imshow(mask_colored, alpha=0.3)
        axes[1, 1].set_title('真实掩码覆盖 (绿色)')
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(overlay)
        axes[1, 2].imshow(pred_colored, alpha=0.3)
        axes[1, 2].set_title('预测掩码覆盖 (红色)')
        axes[1, 2].axis('off')
        
        plt.suptitle(f'样本对比: {filename}', fontsize=16)
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def benchmark_inference_speed(self) -> Dict[str, Dict[str, float]]:
        """
        基准测试推理速度
        
        Returns:
            基准测试结果
        """
        self.model.eval()
        
        results = {}
        batch_sizes = [1, 2, 4, 8]
        
        for batch_size in batch_sizes:
            if len(self.test_loader.dataset) == 0:
                continue
                
            # 创建虚拟输入
            sample = self.test_loader.dataset[0]
            if isinstance(sample['image'], torch.Tensor):
                height, width = sample['image'].shape[-2:]
                channels = sample['image'].shape[0]
            else:
                height, width, channels = sample['image'].shape
                
            dummy_input = torch.randn(batch_size, channels, height, width).to(self.device)
            
            # 预热
            with torch.no_grad():
                for _ in range(5):
                    _ = self.model(dummy_input)
            
            # 基准测试
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            start_time = time.time()
            
            with torch.no_grad():
                for _ in range(20):
                    _ = self.model(dummy_input)
            
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            end_time = time.time()
            
            total_time = end_time - start_time
            time_per_batch = total_time / 20
            time_per_image = time_per_batch / batch_size
            fps = 1.0 / time_per_image
            
            results[f'batch_size_{batch_size}'] = {
                'fps': fps,
                'time_per_image': time_per_image,
                'time_per_batch': time_per_batch
            }
        
        return results 