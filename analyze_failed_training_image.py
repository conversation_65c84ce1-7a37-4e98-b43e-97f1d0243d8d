#!/usr/bin/env python3
"""
分析预测失败的训练集图像
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_failed_image():
    """分析预测失败的训练集图像"""
    
    image_path = "/home/<USER>/data/railway_track_dataset/images/20250119133344135.near.avi_frame_236.png"
    mask_path = "/home/<USER>/data/railway_track_dataset/masks/20250119133344135.near.avi_frame_236.png"
    
    print("="*80)
    print("🔍 分析预测失败的训练集图像")
    print("="*80)
    
    # 读取图像
    image = cv2.imread(image_path)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 读取标注
    mask = cv2.imread(mask_path, cv2.IMREAD_UNCHANGED)
    
    print(f"📊 图像信息:")
    print(f"   图像尺寸: {image_rgb.shape}")
    print(f"   标注尺寸: {mask.shape}")
    print(f"   图像数据类型: {image_rgb.dtype}")
    print(f"   标注数据类型: {mask.dtype}")
    
    # 分析图像统计
    gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
    print(f"\n🖼️  图像统计:")
    print(f"   灰度均值: {gray.mean():.2f}")
    print(f"   灰度标准差: {gray.std():.2f}")
    print(f"   最小值: {gray.min()}, 最大值: {gray.max()}")
    
    # 分析标注
    if len(mask.shape) == 3:
        print(f"\n🎯 多通道标注分析:")
        for i in range(mask.shape[2]):
            channel = mask[:, :, i]
            unique_values = np.unique(channel)
            non_zero_pixels = (channel > 0).sum()
            total_pixels = channel.size
            print(f"   通道 {i}: 唯一值={unique_values}, 非零像素={non_zero_pixels} ({non_zero_pixels/total_pixels*100:.2f}%)")
    else:
        print(f"\n🎯 单通道标注分析:")
        unique_values = np.unique(mask)
        print(f"   唯一值: {unique_values}")
        for val in unique_values:
            count = (mask == val).sum()
            percent = count / mask.size * 100
            print(f"   值 {val}: {count} 像素 ({percent:.2f}%)")
    
    # 创建可视化
    if len(mask.shape) == 3:
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    else:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    fig.suptitle(f'预测失败的训练图像分析: {Path(image_path).name}', fontsize=14)
    
    # 原始图像
    axes[0, 0].imshow(image_rgb)
    axes[0, 0].set_title('原始图像')
    axes[0, 0].axis('off')
    
    # 灰度图像
    axes[0, 1].imshow(gray, cmap='gray')
    axes[0, 1].set_title('灰度图像')
    axes[0, 1].axis('off')
    
    # 亮度直方图
    axes[1, 0].hist(gray.flatten(), bins=50, alpha=0.7, color='blue', edgecolor='black')
    axes[1, 0].set_title('亮度直方图')
    axes[1, 0].set_xlabel('亮度值')
    axes[1, 0].set_ylabel('像素数量')
    
    if len(mask.shape) == 3:
        # 多通道标注
        for i in range(min(3, mask.shape[2])):
            if i < 2:
                axes[0, 2].imshow(mask[:, :, i], cmap='hot' if i == 0 else 'viridis')
                axes[0, 2].set_title(f'标注通道 {i}')
                axes[0, 2].axis('off')
        
        # 合成标注
        if mask.shape[2] >= 3:
            composite_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
            composite_mask[:, :, 0] = mask[:, :, 1] * 255  # 主轨道 - 红色
            composite_mask[:, :, 1] = mask[:, :, 2] * 255  # 分叉轨道 - 绿色
            axes[1, 0].imshow(composite_mask)
            axes[1, 0].set_title('合成标注 (红=主轨道, 绿=分叉)')
            axes[1, 0].axis('off')
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        axes[1, 2].imshow(edges, cmap='gray')
        axes[1, 2].set_title('边缘检测')
        axes[1, 2].axis('off')
    else:
        # 单通道标注 - 创建标注覆盖
        overlay = image_rgb.copy()
        track_mask = mask > 0
        overlay[track_mask] = overlay[track_mask] * 0.5 + np.array([255, 0, 0]) * 0.5  # 红色高亮
        axes[1, 1].imshow(overlay)
        axes[1, 1].set_title('标注覆盖')
        axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('failed_training_image_analysis.png', dpi=150, bbox_inches='tight')
    print(f"\n✅ 分析结果已保存: failed_training_image_analysis.png")
    
    # 给出诊断
    print(f"\n💡 诊断分析:")
    
    if gray.mean() < 50:
        print("   ⚠️  图像极暗，可能是夜间或隧道场景")
    elif gray.mean() > 200:
        print("   ⚠️  图像过亮，可能过曝")
    else:
        print(f"   ✅ 图像亮度正常 (均值: {gray.mean():.1f})")
    
    if len(mask.shape) == 3 and mask.shape[2] >= 3:
        main_track_pixels = (mask[:, :, 1] > 0).sum()
        fork_track_pixels = (mask[:, :, 2] > 0).sum()
        if main_track_pixels == 0 and fork_track_pixels == 0:
            print("   🚨 关键发现：标注中没有任何轨道标记！")
            print("   这可能是一个'负样本'图像（纯背景）")
        else:
            print(f"   ✅ 标注包含轨道: 主轨道{main_track_pixels}像素, 分叉轨道{fork_track_pixels}像素")
    else:
        # 单通道标注分析
        track_pixels = (mask > 0).sum()
        if track_pixels == 0:
            print("   🚨 关键发现：标注中没有任何轨道标记！")
            print("   这可能是一个'负样本'图像（纯背景）")
        else:
            print(f"   ⚠️  重要发现：标注包含 {track_pixels} 个轨道像素 ({track_pixels/mask.size*100:.2f}%)")
            print("   但模型预测几乎为0，这说明存在严重的训练问题！")
            print("   可能原因：")
            print("     - 单通道标注格式与模型期望的多通道格式不匹配")
            print("     - 标注值(85)与模型训练时使用的值不匹配")
            print("     - 数据加载器处理标注时有问题")
    
    print(f"\n🔍 可能的问题:")
    print("   1. 标注格式不匹配（单通道 vs 多通道）")
    print("   2. 标注值编码问题（值85可能不对应正确的类别）")
    print("   3. 数据加载器在训练时处理此图像有问题")
    print("   4. 模型对这种场景泛化能力差")
    print("   5. 训练数据预处理pipeline有bug")

if __name__ == "__main__":
    analyze_failed_image() 