# 铁路轨道分割训练指南

本指南帮助您快速开始训练铁路轨道分割模型。

## 🚀 快速开始（推荐）

### 方法1：基础训练（简单稳定）
使用独立训练脚本，无需复杂配置：

```bash
# 1. 基础训练（10个epoch测试）
python train_standalone.py --epochs 10 --batch-size 4

# 2. 自定义参数训练
python train_standalone.py \
    --data-dir /home/<USER>/data/railway_track_dataset \
    --epochs 50 \
    --batch-size 8 \
    --lr 0.001 \
    --device cuda
```

### 方法2：现代技术训练（推荐生产使用）
使用包含现代训练技术的简化脚本：

```bash
# 1. 使用默认的现代训练配置
python train_modern_simple.py --epochs 100 --batch-size 8

# 2. 自定义现代训练参数
python train_modern_simple.py \
    --epochs 100 \
    --batch-size 8 \
    --lr 0.001 \
    --warmup-epochs 5 \
    --label-smoothing 0.1 \
    --ema-decay 0.999 \
    --mixed-precision
```

现代训练脚本自动包含：
- ✓ 余弦学习率调度 + 预热
- ✓ 指数移动平均 (EMA)
- ✓ 标签平滑
- ✓ 混合精度训练
- ✓ 梯度裁剪
- ✓ AdamW优化器

## 📊 测试数据加载

在训练前，先测试数据是否正确加载：

```bash
python test_data_loading.py
```

这将：
- 检查数据集中的图像和掩码
- 显示掩码统计信息
- 生成可视化图像 `data_loading_test.png`

## 🎯 训练选项

### 1. 独立训练脚本（推荐新手）

`train_standalone.py` - 最简单的训练方式

**特点：**
- 最少的依赖
- 自包含的代码
- 易于调试和修改
- 包含余弦学习率调度

**参数：**
- `--data-dir`: 数据目录路径（默认：/home/<USER>/data/railway_track_dataset）
- `--epochs`: 训练轮数（默认：10）
- `--batch-size`: 批次大小（默认：4）
- `--lr`: 学习率（默认：0.001）
- `--device`: 设备（默认：cuda）

### 2. 现代训练技术（高级用户）

使用包含最新训练技巧的版本：

```bash
# 使用现代训练配置
python src/train_v2.py --config configs/railway_track_config_modern.yaml

# 或使用便捷脚本
python scripts/train_with_modern_techniques.py --mode train
```

**包含的技术：**
- 余弦学习率调度 + 预热
- 指数移动平均 (EMA)
- 随机权重平均 (SWA)
- 标签平滑
- 混合精度训练
- 高级数据增强

## 🔧 常见问题解决

### 1. 导入错误

如果遇到导入错误，使用独立训练脚本：
```bash
python train_standalone.py
```

### 2. GPU内存不足

减小批次大小：
```bash
python train_standalone.py --batch-size 2
```

### 3. 数据路径问题

确保数据结构正确：
```
/home/<USER>/data/railway_track_dataset/
├── train/
│   ├── images/  # JPG或PNG图像
│   └── masks/   # PNG掩码（BGR编码）
├── val/
│   ├── images/
│   └── masks/
└── test/
    ├── images/
    └── masks/
```

### 4. 训练速度慢

使用更多的数据加载线程（在train_standalone.py中修改num_workers）：
```python
train_loader = DataLoader(
    train_dataset,
    batch_size=args.batch_size,
    shuffle=True,
    num_workers=8,  # 增加此值
    pin_memory=True
)
```

## 📈 监控训练进度

训练过程中会显示：
- 每个批次的损失和IoU
- 每个epoch的训练和验证指标
- 最佳模型的保存信息

输出示例：
```
Epoch 1/10 (LR: 0.001000)
Train - Loss: 0.5432, IoU: 0.7821
Val   - Loss: 0.4321, IoU: 0.8234, Dice: 0.8567
Saved best model with IoU: 0.8234
```

## 💾 模型保存

模型自动保存在 `outputs/checkpoints/` 目录：
- `best_model.pth`: 验证IoU最高的模型
- `final_model.pth`: 训练结束时的模型

## 🚀 下一步

1. **调整超参数**：尝试不同的学习率、批次大小
2. **增加训练轮数**：对于生产模型，建议训练50-100个epoch
3. **使用数据增强**：修改训练脚本添加数据增强
4. **尝试现代技术**：使用train_v2.py获得更好的性能

## 📊 性能基准

在默认设置下的预期性能：
- 10 epochs: ~0.75 IoU
- 50 epochs: ~0.85 IoU
- 100 epochs: ~0.90 IoU

使用现代训练技术可以提升5-10%的性能。

---

有问题？请查看主文档 `docs/README.md` 或提交Issue。