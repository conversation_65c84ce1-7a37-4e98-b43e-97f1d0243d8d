# NPY格式支持更新总结

本文档总结了为支持.npy格式的多标签掩码所做的所有修改。

## 1. 修改的文件列表

### 1.1 配置文件
- **configs/railway_track_config.yaml**
  - 新增 `mask_format: 'npy'` 配置项
  - 保留 `use_multilabel: true` 配置

### 1.2 数据处理模块
- **src/data/railway_dataset.py**
  - 修改路径结构：从 `/images/train` 改为 `/train/images`
  - 更新 `_load_mask()` 方法支持加载.npy文件
  - 更新 `_load_from_processed()` 方法根据配置查找.npy或.png文件

- **src/data/preprocessing.py**
  - 新增 `save_mask()` 方法支持保存.npy和.png格式
  - 新增 `polygon_to_mask()` 方法
  - 更新 `preprocess_json_annotations()` 函数支持mask_format参数

### 1.3 评估和推理模块
- **src/evaluation/evaluator.py**
  - 更新 `_save_predictions()` 方法支持保存.npy格式的预测结果

- **scripts/predict_inference.py**
  - 更新 `save_prediction()` 方法支持保存.npy格式

### 1.4 可视化模块
- **src/utils/visualization.py**
  - 新增 `load_mask()` 函数支持加载.npy和.png格式
  - 新增 `save_mask_visualization()` 函数支持保存不同格式的可视化结果

### 1.5 新增脚本
- **scripts/generate_multilabel_from_json.py**
  - 从JSON标注生成多标签.npy掩码

- **scripts/convert_png_to_multilabel.py**
  - 将现有PNG掩码转换为多标签.npy格式

- **scripts/test_npy_support.py**
  - 测试整个代码库对.npy格式的支持

## 2. 主要功能改进

### 2.1 数据格式
- 支持3通道多标签掩码格式 (H, W, 3)
  - 通道0: 背景
  - 通道1: 主轨道
  - 通道2: 分叉轨道
- 支持轨道重叠区域的正确表示

### 2.2 向后兼容
- 保持对.png格式的支持
- 可通过配置切换掩码格式
- 自动检测文件格式并加载

### 2.3 性能优化
- .npy格式加载速度更快
- 直接支持浮点数精度，无需转换

## 3. 使用指南

### 3.1 生成多标签掩码
```bash
# 从JSON标注生成
python scripts/generate_multilabel_from_json.py

# 从现有PNG转换
python scripts/convert_png_to_multilabel.py
```

### 3.2 配置切换
在 `configs/railway_track_config.yaml` 中设置：
```yaml
data:
  mask_format: 'npy'  # 或 'png'
  use_multilabel: true
```

### 3.3 验证支持
```bash
python scripts/test_npy_support.py
```

## 4. 注意事项

1. **数据集结构**：确保数据集目录结构为 `/split/images/` 和 `/split/masks/`
2. **内存使用**：.npy文件比PNG文件占用更多磁盘空间（未压缩）
3. **可视化**：彩色可视化始终保存为PNG格式，仅原始掩码使用.npy

## 5. 测试结果

所有模块测试通过：
- ✓ 配置文件支持
- ✓ 预处理模块支持
- ✓ 可视化模块支持
- ✓ 数据加载支持

整个代码库已完全支持.npy格式的多标签掩码。