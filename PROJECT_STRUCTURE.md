# 项目结构说明

## 概述

本项目采用了模块化、可扩展的架构设计。新的结构遵循了深度学习项目的最佳实践，提高了代码的可维护性、可读性和可扩展性。

## 目录结构详解

### 1. 配置管理 (`configs/`)

```
configs/
├── base_config.yaml          # 基础配置文件，包含所有默认参数
├── model/                    # 模型特定配置
│   ├── pan_efficientnet.yaml
│   ├── pan_nfnet.yaml
│   └── pan_seresnet.yaml
└── experiment/               # 实验配置
    ├── train_single.yaml
    └── train_ensemble.yaml
```

**特点**：
- 使用YAML格式，易于阅读和修改
- 支持配置继承，减少重复
- 参数集中管理，便于实验追踪

### 2. 核心模块 (`src/core/`)

```
src/core/
├── __init__.py
├── config.py         # 配置管理类
├── registry.py       # 组件注册器
└── base.py          # 基础类定义
```

**功能**：
- `config.py`: 提供配置加载、合并、访问的统一接口
- `registry.py`: 实现组件的动态注册和创建
- `base.py`: 定义模型、训练器、数据集的抽象基类

### 3. 数据处理模块 (`src/data/`)

```
src/data/
├── __init__.py
├── dataset.py        # 数据集类
├── dataloader.py     # 数据加载器
├── transforms.py     # 数据增强
└── preprocessing.py  # 数据预处理
```

**功能**：
- 模块化的数据处理流程
- 灵活的数据增强配置
- 支持K折交叉验证
- 高效的数据加载（支持多进程和GPU预取）

### 4. 模型模块 (`src/models/`)

```
src/models/
├── __init__.py
├── base_model.py     # 模型基类
├── segmentation.py   # 分割模型实现
├── losses.py         # 损失函数
└── ensemble.py       # 集成模型
```

**特点**：
- 支持多种分割架构（UNet、PAN、FPN等）
- 丰富的损失函数（Dice、BCE、Focal等）
- 模型集成支持

### 5. 训练模块 (`src/training/`)

```
src/training/
├── __init__.py
├── trainer.py        # 训练器类
├── callbacks.py      # 训练回调
└── optimizers.py     # 优化器配置
```

**功能**：
- 封装的训练流程
- 支持断点续训
- 自动保存最佳模型
- 训练进度可视化

### 6. 执行脚本 (`scripts/`)

```
scripts/
├── train.py              # 训练脚本
├── evaluate.py           # 评估脚本
├── predict.py            # 预测脚本
├── prepare_data.py       # 数据准备脚本
└── migrate_old_code.py   # 代码迁移脚本
```

**特点**：
- 命令行接口，支持参数覆盖
- 统一的执行入口
- 完整的错误处理

## 主要改进

### 1. 模块化设计

- **分离关注点**：每个模块负责单一功能
- **低耦合**：模块间通过明确的接口通信
- **高内聚**：相关功能组织在一起

### 2. 配置驱动

- **参数外部化**：所有参数通过配置文件管理
- **实验管理**：易于创建和管理不同的实验配置
- **版本控制友好**：配置文件可纳入版本控制

### 3. 注册器模式

```python
# 注册模型
@register_model('segmentation_model')
class SegmentationModel(BaseModel):
    pass

# 动态创建
model = MODEL_REGISTRY.build({'type': 'segmentation_model', ...})
```

### 4. 统一的基类

- 所有组件继承自相应的基类
- 保证接口一致性
- 便于扩展新功能

### 5. 完善的日志系统

- 结构化日志记录
- 训练过程可追踪
- 错误调试更容易

## 扩展指南

### 添加新模型

1. 在 `src/models/` 创建新文件
2. 继承 `BaseModel` 类
3. 使用 `@register_model` 装饰器注册
4. 在配置文件中指定新模型

### 添加新的数据增强

1. 在 `src/data/transforms.py` 添加新函数
2. 使用 `@register_transform` 装饰器
3. 在配置文件中使用

### 添加新的损失函数

1. 在 `src/models/losses.py` 添加新类
2. 继承 `nn.Module`
3. 使用 `@register_loss` 装饰器
4. 在配置中指定使用

## 最佳实践

1. **遵循命名约定**：使用清晰、一致的命名
2. **编写文档字符串**：为所有公共API编写文档
3. **类型注解**：使用类型提示提高代码可读性
4. **单元测试**：为关键功能编写测试
5. **代码复审**：重要更改需要代码审查

## 依赖管理

- 使用 `requirements.txt` 管理Python依赖
- 指定版本号确保环境一致性
- 定期更新依赖，修复安全漏洞

## 未来改进方向

1. **添加API服务**：提供REST API接口
2. **Docker支持**：容器化部署
3. **分布式训练**：支持多GPU和多节点训练
4. **模型压缩**：支持量化和剪枝
5. **自动超参数调优**：集成Optuna等工具

## 总结

项目结构提供了：
- ✅ 更好的代码组织
- ✅ 更容易的维护和扩展
- ✅ 更清晰的职责分离
- ✅ 更灵活的配置管理
- ✅ 更完善的开发体验

