#!/usr/bin/env python3
"""
检查PyTorch版本兼容性并设置合适的优化参数
"""

import torch
import os
import sys

def check_pytorch_version():
    """检查PyTorch版本"""
    print("🔍 PyTorch版本检查")
    print("=" * 50)
    
    pytorch_version = torch.__version__
    print(f"PyTorch版本: {pytorch_version}")
    
    # 解析版本号
    version_parts = pytorch_version.split('.')
    major = int(version_parts[0])
    minor = int(version_parts[1])
    
    print(f"主版本: {major}.{minor}")
    
    return major, minor

def setup_compatible_environment():
    """设置兼容的环境变量"""
    print("\n🔧 设置兼容的环境变量")
    print("=" * 50)
    
    major, minor = check_pytorch_version()
    
    # 基础CUDA内存配置（所有版本兼容）
    base_config = 'max_split_size_mb:128'
    
    # 根据版本添加额外配置
    if major >= 2 or (major == 1 and minor >= 13):
        # PyTorch 1.13+ 支持更多选项
        cuda_config = f"{base_config},roundup_power2_divisions:16"
        print("✅ 使用PyTorch 1.13+优化配置")
    else:
        # 旧版本使用基础配置
        cuda_config = base_config
        print("⚠️  使用兼容旧版本的基础配置")
    
    # 设置环境变量
    env_vars = {
        'PYTORCH_CUDA_ALLOC_CONF': cuda_config,
        'CUDA_LAUNCH_BLOCKING': '0',
        'TORCH_CUDNN_V8_API_ENABLED': '1',
        'OMP_NUM_THREADS': '6',
        'MKL_NUM_THREADS': '6',
        'TOKENIZERS_PARALLELISM': 'false'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key}={value}")
    
    return cuda_config

def check_cuda_availability():
    """检查CUDA可用性"""
    print("\n🔍 CUDA可用性检查")
    print("=" * 50)
    
    if torch.cuda.is_available():
        print("✅ CUDA可用")
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        return True
    else:
        print("❌ CUDA不可用")
        return False

def setup_cuda_optimizations():
    """设置CUDA优化"""
    print("\n⚡ CUDA优化设置")
    print("=" * 50)
    
    try:
        # 基础优化（所有版本兼容）
        torch.backends.cudnn.benchmark = True
        print("✅ 启用cuDNN benchmark")
        
        torch.backends.cudnn.deterministic = False
        print("✅ 禁用确定性以提高性能")
        
        # 检查TF32支持
        if hasattr(torch.backends.cudnn, 'allow_tf32'):
            torch.backends.cudnn.allow_tf32 = True
            print("✅ 启用cuDNN TF32")
        else:
            print("⚠️  cuDNN TF32不支持")
        
        if hasattr(torch.backends.cuda.matmul, 'allow_tf32'):
            torch.backends.cuda.matmul.allow_tf32 = True
            print("✅ 启用matmul TF32")
        else:
            print("⚠️  matmul TF32不支持")
        
        # 设置线程数
        torch.set_num_threads(6)
        print("✅ 设置PyTorch线程数为6")
        
        return True
        
    except Exception as e:
        print(f"❌ CUDA优化设置失败: {e}")
        return False

def test_memory_allocation():
    """测试内存分配"""
    print("\n🧪 内存分配测试")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("⚠️  跳过内存测试（CUDA不可用）")
        return False
    
    try:
        # 清理内存
        torch.cuda.empty_cache()
        
        # 测试小内存分配
        test_tensor = torch.randn(100, 100, device='cuda')
        memory_used = torch.cuda.memory_allocated() / 1024**2  # MB
        
        print(f"✅ 内存分配测试成功")
        print(f"测试张量: {test_tensor.shape}")
        print(f"内存使用: {memory_used:.1f}MB")
        
        # 清理测试张量
        del test_tensor
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ 内存分配测试失败: {e}")
        return False

def generate_compatible_config():
    """生成兼容的配置文件"""
    print("\n📝 生成兼容配置")
    print("=" * 50)
    
    major, minor = check_pytorch_version()
    
    # 根据PyTorch版本调整配置
    if major >= 2 or (major == 1 and minor >= 12):
        # 新版本配置
        config_updates = {
            'project': {
                'num_workers': 6,
                'gpu_optimization': {
                    'pin_memory': True,
                    'persistent_workers': True,
                    'prefetch_factor': 3,
                    'non_blocking': True,
                    'drop_last': True,
                    'channels_last': True,
                    'stable_training': True
                }
            }
        }
        print("✅ 生成新版本PyTorch配置")
    else:
        # 旧版本配置（更保守）
        config_updates = {
            'project': {
                'num_workers': 4,  # 减少工作进程
                'gpu_optimization': {
                    'pin_memory': True,
                    'persistent_workers': False,  # 旧版本可能不稳定
                    'prefetch_factor': 2,
                    'non_blocking': True,
                    'drop_last': True,
                    'stable_training': True
                }
            }
        }
        print("⚠️  生成兼容旧版本的保守配置")
    
    return config_updates

def main():
    """主函数"""
    print("🔧 PyTorch兼容性检查和优化")
    print("=" * 80)
    
    # 1. 检查PyTorch版本
    major, minor = check_pytorch_version()
    
    # 2. 设置兼容环境
    cuda_config = setup_compatible_environment()
    
    # 3. 检查CUDA
    cuda_available = check_cuda_availability()
    
    # 4. 设置CUDA优化
    if cuda_available:
        cuda_optimized = setup_cuda_optimizations()
        
        # 5. 测试内存分配
        memory_test = test_memory_allocation()
    else:
        cuda_optimized = False
        memory_test = False
    
    # 6. 生成兼容配置
    config_updates = generate_compatible_config()
    
    print(f"\n📊 兼容性检查结果:")
    print("=" * 80)
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {'✅' if cuda_available else '❌'}")
    print(f"CUDA优化: {'✅' if cuda_optimized else '❌'}")
    print(f"内存测试: {'✅' if memory_test else '❌'}")
    print(f"环境配置: {cuda_config}")
    
    print(f"\n🚀 建议:")
    if major >= 2 or (major == 1 and minor >= 12):
        print("✅ 您的PyTorch版本较新，可以使用所有优化功能")
    else:
        print("⚠️  您的PyTorch版本较旧，建议升级到1.12+以获得更好性能")
        print("   当前使用兼容配置，性能可能略有限制")
    
    print(f"\n💡 现在可以安全运行训练脚本了！")

if __name__ == '__main__':
    main()
