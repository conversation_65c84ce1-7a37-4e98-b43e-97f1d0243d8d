# 铁路轨道语义分割

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.11%2B-red.svg)](https://pytorch.org/)

## 项目简介

本项目是一个用于铁路轨道语义分割的深度学习系统。该系统采用了多种先进的分割模型，包括PAN（Pyramid Attention Network）架构配合不同的编码器（EfficientNet-B4、NFNet、SE-ResNet），并通过集成学习提高预测精度。

![Railway Infrastructure](images/infrastructure.png)

## 主要特性

- 🏗️ **模块化架构**：清晰的项目结构，易于扩展和维护
- 🔧 **灵活的配置系统**：基于YAML的配置管理，支持实验管理
- 🚀 **多种模型支持**：支持UNet、PAN、FPN、DeepLabV3+等多种分割架构
- 📊 **完整的训练流程**：包含数据增强、模型训练、验证和测试
- 🎯 **集成学习**：支持多模型集成，提高预测准确性
- 📈 **实验跟踪**：自动记录训练日志和指标
- 🔍 **可视化工具**：提供预测结果和训练过程的可视化

## 项目结构

```
railway-infrastructure-segmentation/
├── configs/                       # 配置文件
│   ├── base_config.yaml          # 基础配置
│   ├── model/                    # 模型配置
│   └── experiment/               # 实验配置
├── data/                         # 数据目录
│   ├── raw/                      # 原始数据
│   ├── processed/                # 处理后的数据
│   └── splits/                   # 数据集划分
├── models/                       # 模型权重
│   ├── checkpoints/              # 训练检查点
│   └── final/                    # 最终模型
├── src/                          # 源代码
│   ├── core/                     # 核心功能模块
│   ├── data/                     # 数据处理模块
│   ├── models/                   # 模型定义
│   ├── training/                 # 训练相关
│   ├── evaluation/               # 评估模块
│   ├── inference/                # 推理模块
│   └── utils/                    # 工具函数
├── scripts/                      # 执行脚本
│   ├── train.py                  # 训练脚本
│   ├── evaluate.py               # 评估脚本
│   └── predict.py                # 预测脚本
├── notebooks/                    # Jupyter notebooks
├── tests/                        # 单元测试
└── outputs/                      # 输出结果
    ├── logs/                     # 日志文件
    ├── predictions/              # 预测结果
    └── visualizations/           # 可视化结果
```

## 快速开始

### 环境要求

- Python >= 3.8
- PyTorch >= 1.11.0
- CUDA >= 10.2 (推荐使用GPU)

### 安装

1. 克隆仓库
```bash
git clone 
cd railway-segmentation
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

### 数据准备

1. 将原始数据放置在 `data/raw` 目录下
2. 运行数据预处理脚本：
```bash
python scripts/prepare_data.py --input data/raw --output data/processed
```

### 训练模型

使用默认配置训练：
```bash
python scripts/train.py --config configs/base_config.yaml
```

使用自定义参数：
```bash
python scripts/train.py \
    --config configs/base_config.yaml \
    --epochs 100 \
    --batch-size 16 \
    --lr 0.001 \
    --gpu 0
```

### 评估模型

```bash
python scripts/evaluate.py \
    --config configs/base_config.yaml \
    --checkpoint models/checkpoints/best_model.pth \
    --gpu 0
```

### 预测

对新图像进行预测：

#### 🚀 快速开始
我们提供了完整的独立预测脚本，支持命令行和Python API两种使用方式。

```bash
# 安装预测依赖
pip install -r requirements_inference.txt

# 预测单张图片
python scripts/predict_inference.py \
    --input path/to/image.jpg \
    --output outputs/predictions \
    --visualize --stats --confidence \
    --threshold 0.5

# 批量预测目录中的所有图片
python scripts/predict_inference.py \
    --input data/test_images/ \
    --output outputs/predictions \
    --batch-size 8 \
    --visualize --stats --confidence \
    --threshold 0.5
```

#### 📝 Python API 使用示例
```python
from scripts.predict_inference import RailwaySegmentationPredictor

# 创建预测器
predictor = RailwaySegmentationPredictor(
    config_path='configs/base_config.yaml',
    checkpoint_path='models/final/model.pth'
)

# 预测并可视化
mask, probs = predictor.predict_single('path/to/image.jpg')
overlay, mask = predictor.visualize_prediction(
    'path/to/image.jpg', 
    save_path='outputs/result.png'
)

# 获取预测统计
stats = predictor.get_prediction_stats(mask)
print("类别统计:", stats)
```

#### 📚 详细文档
- 完整预测脚本：[`scripts/predict_inference.py`](scripts/predict_inference.py)
- 详细使用说明：[`docs/inference_usage.md`](docs/inference_usage.md)
- 依赖包列表：[`requirements_inference.txt`](requirements_inference.txt)

#### ✨ 主要功能
- 🎯 **三类分割**：精准识别背景、主轨道、分叉轨道
- 🎨 **可视化结果**：彩色分割图和叠加效果
- 📊 **统计信息**：各类别像素占比统计
- 📈 **置信度分析**：预测置信度统计信息
- ⚡ **GPU加速**：自动检测和使用CUDA
- 🔧 **灵活配置**：支持多种模型架构和阈值调节

## 配置说明

配置文件采用YAML格式，主要包含以下部分：

- **project**: 项目基础设置（名称、随机种子等）
- **data**: 数据相关配置（路径、批次大小、图像尺寸等）
- **model**: 模型配置（架构、编码器、类别数等）
- **training**: 训练配置（优化器、学习率、轮数等）
- **loss**: 损失函数配置
- **augmentation**: 数据增强配置
- **metrics**: 评估指标配置

详细配置说明请参考 `configs/base_config.yaml`

## 模型架构

项目支持以下分割架构：
- UNet / UNet++
- PAN (Pyramid Attention Network)
- FPN (Feature Pyramid Network)
- PSPNet
- DeepLabV3 / DeepLabV3+
- LinkNet
- MANet

支持的编码器：
- EfficientNet系列 (B0-B7)
- ResNet系列 (18/34/50/101/152)
- SE-ResNet系列
- ResNeXt系列
- NFNet系列
- MobileNet系列

## 训练技巧

1. **数据增强**：使用水平翻转、透视变换、亮度对比度调整等增强方法
2. **学习率调度**：使用余弦退火或阶梯下降策略
3. **梯度累积**：在显存有限时使用梯度累积增大有效批次大小
4. **混合精度训练**：使用FP16加速训练并减少显存占用
5. **早停策略**：监控验证集指标，避免过拟合

## 集成学习

本项目实现了基于notebook的集成学习方法，使用多个不同编码器的PAN模型进行融合预测，可显著提升分叉轨道检测性能。

### 集成模型配置

集成学习使用以下三个模型：

1. **PAN + EfficientNet B4** (noisy-student预训练)
2. **PAN + ECA NFNet L2** (ImageNet预训练)  
3. **PAN + SE-ResNet 152d** (ImageNet预训练)

### 训练集成模型

```bash
# 训练所有子模型并优化融合权重
python scripts/ensemble_training.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/railway_track_dataset

# 仅优化权重（如果已有训练好的模型）
python scripts/ensemble_training.py \
    --config configs/railway_track_config.yaml \
    --data-dir /path/to/railway_track_dataset \
    --skip-training
```

训练产物：
- `checkpoints/ensemble_weights/efficientnet_b4.pth` - EfficientNet B4模型权重
- `checkpoints/ensemble_weights/eca_nfnet_l2.pth` - ECA NFNet L2模型权重  
- `checkpoints/ensemble_weights/seresnet152d.pth` - SE-ResNet 152d模型权重
- `checkpoints/ensemble_weights/ensemble_weights.yaml` - 集成融合权重

### 集成预测

```bash
# 单张图像预测
python scripts/ensemble_prediction.py \
    --weights-dir checkpoints/ensemble_weights \
    --config configs/railway_track_config.yaml \
    --input test_image.jpg \
    --output outputs/ \
    --threshold 0.3

# 批量预测
python scripts/ensemble_prediction.py \
    --weights-dir checkpoints/ensemble_weights \
    --config configs/railway_track_config.yaml \
    --input test_images/ \
    --output outputs/ \
    --threshold 0.3
```

### 集成学习优势

相比单个模型，集成学习具有以下优势：

1. **更高的准确性**: 多模型融合减少单个模型的偏差
2. **更好的泛化性**: 不同编码器捕获不同特征
3. **更强的鲁棒性**: 减少模型的过拟合风险
4. **分叉轨道检测**: 特别针对分叉轨道检测进行了权重优化

### 权重优化原理

集成权重通过以下方法优化：

1. **类别特定权重**: 每个类别(背景、主轨道、分叉轨道)独立优化权重
2. **网格搜索**: 在验证集上搜索最优权重组合
3. **IoU最大化**: 以IoU作为优化目标
4. **约束条件**: 权重和为1，保证概率一致性

