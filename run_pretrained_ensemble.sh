#!/bin/bash
# 使用预训练权重的集成训练脚本

echo "=========================================="
echo "预训练权重集成训练"
echo "=========================================="

# 设置默认参数
DATA_DIR="${1:-/home/<USER>/data/railway_track_dataset/}"
OUTPUT_DIR="${2:-pretrained_ensemble_25mm}"

# 检查预训练权重文件
EFFICIENTNET_WEIGHTS="efficientnetb4.pth_converted.pth"
NFNET_WEIGHTS="eca_nfnet_l2.pth_converted.pth"
RESNET_WEIGHTS="seresnet152d.pth_converted.pth"

echo "检查预训练权重文件..."
for weight_file in "$EFFICIENTNET_WEIGHTS" "$NFNET_WEIGHTS" "$RESNET_WEIGHTS"; do
    if [ -f "$weight_file" ]; then
        echo "✅ 找到: $weight_file"
    else
        echo "❌ 缺失: $weight_file"
    fi
done

echo ""
echo "运行参数:"
echo "  数据目录: $DATA_DIR"
echo "  输出目录: $OUTPUT_DIR"
echo "  配置文件: configs/railway_track_config.yaml"
echo ""

# 检查数据目录
if [ ! -d "$DATA_DIR" ]; then
    echo "❌ 错误: 数据目录不存在: $DATA_DIR"
    echo "请提供正确的数据目录路径"
    echo "用法: $0 <数据目录> [输出目录]"
    exit 1
fi

# 设置警告抑制
export PYTHONWARNINGS='ignore::SyntaxWarning,ignore::UserWarning,ignore::FutureWarning'

# 运行训练（单行命令，避免换行问题）
echo "🚀 开始预训练权重集成训练..."
python scripts/ensemble_training_notebook_exact.py --config configs/railway_track_config.yaml --data-dir "$DATA_DIR" --pretrained-efficientnet "$EFFICIENTNET_WEIGHTS" --pretrained-nfnet "$NFNET_WEIGHTS" --pretrained-resnet "$RESNET_WEIGHTS" --output-dir "$OUTPUT_DIR"

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "🎉 训练完成!"
    echo "=========================================="
    echo "输出目录: $OUTPUT_DIR"
    echo "查看结果:"
    echo "  - 模型权重: $OUTPUT_DIR/*.pth.tar"
    echo "  - 训练日志: $OUTPUT_DIR/training.log"
else
    echo ""
    echo "=========================================="
    echo "❌ 训练失败"
    echo "=========================================="
    echo "请检查:"
    echo "1. 预训练权重文件是否存在"
    echo "2. 数据目录路径是否正确"
    echo "3. 配置文件是否存在"
    exit 1
fi
