#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Environment Verification Script for Railway Infrastructure Segmentation
验证railway-seg环境是否正确设置
"""

import sys
import importlib
from typing import Dict, List, <PERSON><PERSON>

def check_python_version() -> Tu<PERSON>[bool, str]:
    """检查Python版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        return True, f"✅ Python {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"❌ Python {version.major}.{version.minor}.{version.micro} (需要 >= 3.10)"

def check_package(package_name: str, min_version: str = None) -> Tuple[bool, str]:
    """检查包是否安装及版本"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'unknown')
        
        if min_version and version != 'unknown':
            # 简单版本比较
            try:
                from packaging import version as pkg_version
                if pkg_version.parse(version) >= pkg_version.parse(min_version):
                    return True, f"✅ {package_name} {version}"
                else:
                    return False, f"⚠️  {package_name} {version} (需要 >= {min_version})"
            except ImportError:
                # 如果没有packaging包，只显示版本
                return True, f"✅ {package_name} {version}"
        else:
            return True, f"✅ {package_name} {version}"
            
    except ImportError:
        return False, f"❌ {package_name} 未安装"

def check_cuda() -> Tuple[bool, str]:
    """检查CUDA可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            cuda_version = torch.version.cuda
            return True, f"✅ CUDA {cuda_version} 可用 ({device_count} GPU: {device_name})"
        else:
            return False, "⚠️  CUDA 不可用 (将使用CPU)"
    except ImportError:
        return False, "❌ PyTorch 未安装，无法检查CUDA"

def main():
    """主验证函数"""
    print("🔍 Railway Infrastructure Segmentation - Environment Verification")
    print("=" * 80)
    
    # 核心包列表
    core_packages = [
        ("torch", "2.0.0"),
        ("torchvision", "0.15.0"),
        ("segmentation_models_pytorch", "0.3.0"),
        ("timm", "0.9.0"),
        ("albumentations", "1.3.0"),
        ("cv2", None),  # opencv-python
        ("numpy", "1.24.0"),
        ("scipy", "1.10.0"),
        ("sklearn", "1.3.0"),  # scikit-learn
        ("matplotlib", "3.7.0"),
        ("pandas", "2.0.0"),
        ("PIL", "9.0.0"),  # Pillow
        ("yaml", "6.0.0"),  # pyyaml
        ("tqdm", "4.66.0"),
    ]
    
    # 可选包列表
    optional_packages = [
        ("tensorboard", "2.13.0"),
        ("wandb", "0.15.0"),
        ("seaborn", "0.12.0"),
        ("plotly", "5.15.0"),
        ("omegaconf", "2.3.0"),
        ("safetensors", "0.5.0"),
        ("psutil", "7.0.0"),
        ("sympy", "1.14.0"),
        ("networkx", "3.4.0"),
    ]
    
    # 检查Python版本
    python_ok, python_msg = check_python_version()
    print(f"🐍 {python_msg}")
    
    # 检查CUDA
    cuda_ok, cuda_msg = check_cuda()
    print(f"🚀 {cuda_msg}")
    
    print(f"\n📦 核心包检查:")
    print("-" * 40)
    
    core_results = []
    for package, min_version in core_packages:
        ok, msg = check_package(package, min_version)
        core_results.append(ok)
        print(f"   {msg}")
    
    print(f"\n📦 可选包检查:")
    print("-" * 40)
    
    optional_results = []
    for package, min_version in optional_packages:
        ok, msg = check_package(package, min_version)
        optional_results.append(ok)
        print(f"   {msg}")
    
    # 总结
    print(f"\n📊 检查总结:")
    print("=" * 40)
    
    core_passed = sum(core_results)
    core_total = len(core_results)
    optional_passed = sum(optional_results)
    optional_total = len(optional_results)
    
    print(f"Python版本: {'✅' if python_ok else '❌'}")
    print(f"CUDA支持: {'✅' if cuda_ok else '⚠️'}")
    print(f"核心包: {core_passed}/{core_total} ({'✅' if core_passed == core_total else '⚠️'})")
    print(f"可选包: {optional_passed}/{optional_total}")
    
    # 整体状态
    if python_ok and core_passed == core_total:
        print(f"\n🎉 环境检查通过！可以开始使用railway-seg项目。")
        if not cuda_ok:
            print(f"💡 提示: CUDA不可用，训练将使用CPU（速度较慢）")
        return True
    else:
        print(f"\n⚠️  环境检查未完全通过，请安装缺失的包。")
        print(f"\n🔧 安装建议:")
        if not python_ok:
            print(f"   - 升级Python到3.10或更高版本")
        if core_passed < core_total:
            print(f"   - 安装缺失的核心包: pip install -r requirements.txt")
        if not cuda_ok:
            print(f"   - 安装CUDA版本的PyTorch: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
