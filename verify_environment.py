#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Railway-seg Environment Verification Script
验证railway-seg环境是否正确设置
"""

import sys
import importlib

def check_package(package_name, expected_version=None):
    """检查包是否安装及版本"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'unknown')
        
        if expected_version and version != 'unknown':
            if version == expected_version:
                return True, f"✅ {package_name} {version}"
            else:
                return True, f"⚠️  {package_name} {version} (期望: {expected_version})"
        else:
            return True, f"✅ {package_name} {version}"
            
    except ImportError:
        return False, f"❌ {package_name} 未安装"

def main():
    """主验证函数"""
    print("🔍 Railway-seg Environment Verification")
    print("=" * 60)
    
    # 基于railway-seg环境的实际包版本
    packages = [
        ("torch", "2.0.0+cu118"),
        ("torchvision", "0.15.0+cu118"),
        ("segmentation_models_pytorch", "0.3.4"),
        ("timm", "0.9.7"),
        ("pretrainedmodels", "0.7.4"),
        ("efficientnet_pytorch", "0.7.1"),
        ("albumentations", "1.3.1"),
        ("cv2", None),
        ("numpy", "1.24.4"),
        ("scipy", "1.10.1"),
        ("sklearn", "1.3.2"),
        ("pandas", "2.0.3"),
        ("matplotlib", "3.7.5"),
        ("seaborn", "0.12.2"),
        ("PIL", "9.5.0"),
        ("yaml", "6.0.2"),
        ("dill", "0.4.0"),
        ("omegaconf", "2.3.0"),
        ("tqdm", "4.66.6"),
        ("tensorboard", "2.13.0"),
        ("wandb", "0.15.12"),
    ]
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"🚀 ✅ CUDA {torch.version.cuda} 可用")
        else:
            print(f"🚀 ⚠️  CUDA 不可用")
    except:
        print(f"🚀 ❌ PyTorch 未安装")
    
    print(f"\n📦 包版本检查:")
    print("-" * 40)
    
    results = []
    for package, expected_version in packages:
        ok, msg = check_package(package, expected_version)
        results.append(ok)
        print(f"   {msg}")
    
    passed = sum(results)
    total = len(results)
    print(f"\n📊 检查结果: {passed}/{total} 包正确")
    
    if passed >= total * 0.9:
        print(f"🎉 环境检查通过！")
        return True
    else:
        print(f"⚠️  请检查缺失的包")
        return False

if __name__ == "__main__":
    main()
