#!/usr/bin/env python3
"""
修复坐标超出边界的问题
"""

import sys
import json
import numpy as np
from pathlib import Path
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser

def fix_coordinate_bounds(points, image_shape):
    """
    修复超出图像边界的坐标
    
    Args:
        points: 点坐标列表 [(x1, y1), (x2, y2), ...]
        image_shape: 图像尺寸 (height, width)
        
    Returns:
        修复后的点坐标列表
    """
    height, width = image_shape
    fixed_points = []
    
    for x, y in points:
        # 限制坐标在图像范围内
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        fixed_points.append((x, y))
    
    return fixed_points

def test_fixed_coordinates():
    """测试修复后的坐标"""
    
    # 目标JSON文件路径
    json_path = Path("/home/<USER>/Downloads/轨道线标注导出/railway_annotation_25mm/20250119132444251.far.avi_frame_830.json")
    
    if not json_path.exists():
        print(f"错误：JSON文件不存在: {json_path}")
        return
    
    print(f"测试修复坐标: {json_path}")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    
    try:
        # 解析JSON文件
        annotation_data = parser.parse_json_file(json_path)
        
        # 测试掩码生成
        image_shape = (1080, 1920)
        print(f"图像尺寸: {image_shape}")
        
        # 修复坐标
        fixed_tracks = []
        for track in annotation_data['tracks']:
            fixed_points = fix_coordinate_bounds(track['points'], image_shape)
            fixed_tracks.append({
                'label': track['label'],
                'class_id': track['class_id'],
                'points': fixed_points
            })
        
        # 使用修复后的数据
        fixed_annotation_data = {
            'filename': annotation_data['filename'],
            'camera_type': annotation_data['camera_type'],
            'tracks': fixed_tracks,
            'image_url': annotation_data['image_url']
        }
        
        # 生成修复后的掩码
        multilabel_mask = parser.create_multilabel_segmentation_mask(fixed_annotation_data, image_shape)
        
        # 统计各通道的像素数
        background_pixels = np.sum(multilabel_mask[:, :, 0] > 0)
        main_track_pixels = np.sum(multilabel_mask[:, :, 1] > 0)
        fork_track_pixels = np.sum(multilabel_mask[:, :, 2] > 0)
        
        print(f"\n=== 修复后的像素统计 ===")
        print(f"背景像素: {background_pixels}")
        print(f"主轨道像素: {main_track_pixels}")
        print(f"分叉轨道像素: {fork_track_pixels}")
        
        # 保存修复后的掩码
        test_mask_path = "fixed_mask_20250119132444251.far.avi_frame_830.png"
        bgr_mask = np.zeros((image_shape[0], image_shape[1], 3), dtype=np.uint8)
        bgr_mask[:, :, 0] = (multilabel_mask[:, :, 0] > 0.5).astype(np.uint8) * 255  # B: 背景
        bgr_mask[:, :, 1] = (multilabel_mask[:, :, 1] > 0.5).astype(np.uint8) * 255  # G: 主轨道
        bgr_mask[:, :, 2] = (multilabel_mask[:, :, 2] > 0.5).astype(np.uint8) * 255  # R: 分叉轨道
        
        cv2.imwrite(test_mask_path, bgr_mask)
        print(f"修复后的掩码已保存到: {test_mask_path}")
        
        # 比较修复前后的差异
        print("\n=== 坐标修复对比 ===")
        for i, (original_track, fixed_track) in enumerate(zip(annotation_data['tracks'], fixed_tracks)):
            print(f"轨道 {i+1}: {original_track['label']}")
            
            # 检查是否有坐标被修复
            fixed_count = 0
            for j, ((orig_x, orig_y), (fixed_x, fixed_y)) in enumerate(zip(original_track['points'], fixed_track['points'])):
                if abs(orig_x - fixed_x) > 0.1 or abs(orig_y - fixed_y) > 0.1:
                    fixed_count += 1
                    if fixed_count <= 3:  # 只显示前3个修复的坐标
                        print(f"  点{j}: ({orig_x:.1f}, {orig_y:.1f}) -> ({fixed_x:.1f}, {fixed_y:.1f})")
            
            if fixed_count > 0:
                print(f"  总共修复了 {fixed_count} 个坐标")
            else:
                print("  无需修复")
        
    except Exception as e:
        print(f"❌ 错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_coordinates() 