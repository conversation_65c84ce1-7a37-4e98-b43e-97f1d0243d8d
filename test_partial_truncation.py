#!/usr/bin/env python3
"""
测试部分截断的轨道处理功能
左边或者右边被截断一部分，没有全部截断的情况
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.preprocessing import RailwayAnnotationParser


def test_partial_truncation():
    """测试部分截断的轨道处理"""
    
    print("🔍 测试部分截断的轨道处理")
    print("左边或者右边被截断一部分，没有全部截断的情况")
    
    # 创建解析器
    parser = RailwayAnnotationParser()
    image_shape = (1080, 1920)
    
    # 创建输出目录
    output_path = Path("outputs/partial_truncation_test")
    output_path.mkdir(exist_ok=True)
    
    # 测试场景1: 左边被截断，右边完整
    print(f"\n📊 场景1: 左边被截断，右边完整")
    left_truncated = [
        (0, 600),    # 从边界开始（被截断）
        (10, 700), 
        (20, 800), 
        (30, 900), 
        (40, 1000),  # 截断结束
    ]
    right_complete = [
        (200, 550),  # 完整的右边轨道
        (220, 650), 
        (240, 750), 
        (260, 850), 
        (280, 950),
        (300, 1050),
    ]
    
    print(f"   左边轨道（截断）: {len(left_truncated)} 个点，X范围: 0-40")
    print(f"   右边轨道（完整）: {len(right_complete)} 个点，X范围: 200-300")
    
    try:
        polygon = parser.create_track_polygon_from_parallel_lines(
            left_truncated, right_complete, image_shape[1], image_shape[0])
        
        if len(polygon) >= 3:
            mask = parser.points_to_mask(polygon, image_shape, 1)
            pixels = np.sum(mask > 0)
            print(f"   ✅ 成功生成多边形: {len(polygon)} 个顶点, {pixels:,} 个像素")
            print(f"   多边形顶点: {polygon}")
            
            # 可视化
            visualize_test_case(left_truncated, right_complete, polygon, mask, 
                              "Left Truncated, Right Complete", output_path / "left_truncated_right_complete.png")
        else:
            print(f"   ❌ 多边形生成失败")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试场景2: 左边完整，右边被截断
    print(f"\n📊 场景2: 左边完整，右边被截断")
    left_complete = [
        (100, 550),  # 完整的左边轨道
        (120, 650), 
        (140, 750), 
        (160, 850), 
        (180, 950),
        (200, 1050),
    ]
    right_truncated = [
        (1880, 600),  # 右边轨道（被截断）
        (1890, 700), 
        (1900, 800), 
        (1910, 900), 
        (1919, 1000), # 到达边界（被截断）
    ]
    
    print(f"   左边轨道（完整）: {len(left_complete)} 个点，X范围: 100-200")
    print(f"   右边轨道（截断）: {len(right_truncated)} 个点，X范围: 1880-1919")
    
    try:
        polygon = parser.create_track_polygon_from_parallel_lines(
            left_complete, right_truncated, image_shape[1], image_shape[0])
        
        if len(polygon) >= 3:
            mask = parser.points_to_mask(polygon, image_shape, 1)
            pixels = np.sum(mask > 0)
            print(f"   ✅ 成功生成多边形: {len(polygon)} 个顶点, {pixels:,} 个像素")
            print(f"   多边形顶点: {polygon}")
            
            # 可视化
            visualize_test_case(left_complete, right_truncated, polygon, mask, 
                              "Left Complete, Right Truncated", output_path / "left_complete_right_truncated.png")
        else:
            print(f"   ❌ 多边形生成失败")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试场景3: 两边都被截断
    print(f"\n📊 场景3: 两边都被截断")
    left_truncated_both = [
        (0, 650),    # 左边从边界开始
        (5, 750), 
        (10, 850), 
        (15, 950),
    ]
    right_truncated_both = [
        (1905, 600), # 右边到边界结束
        (1910, 700), 
        (1915, 800), 
        (1919, 900),
    ]
    
    print(f"   左边轨道（截断）: {len(left_truncated_both)} 个点，X范围: 0-15")
    print(f"   右边轨道（截断）: {len(right_truncated_both)} 个点，X范围: 1905-1919")
    
    try:
        polygon = parser.create_track_polygon_from_parallel_lines(
            left_truncated_both, right_truncated_both, image_shape[1], image_shape[0])
        
        if len(polygon) >= 3:
            mask = parser.points_to_mask(polygon, image_shape, 1)
            pixels = np.sum(mask > 0)
            print(f"   ✅ 成功生成多边形: {len(polygon)} 个顶点, {pixels:,} 个像素")
            print(f"   多边形顶点: {polygon}")
            
            # 可视化
            visualize_test_case(left_truncated_both, right_truncated_both, polygon, mask, 
                              "Both Sides Truncated", output_path / "both_sides_truncated.png")
        else:
            print(f"   ❌ 多边形生成失败")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试场景4: 两边都完整（对比）
    print(f"\n📊 场景4: 两边都完整（对比）")
    left_complete_both = [
        (150, 600), 
        (170, 700), 
        (190, 800), 
        (210, 900),
        (230, 1000),
    ]
    right_complete_both = [
        (350, 600), 
        (370, 700), 
        (390, 800), 
        (410, 900),
        (430, 1000),
    ]
    
    print(f"   左边轨道（完整）: {len(left_complete_both)} 个点，X范围: 150-230")
    print(f"   右边轨道（完整）: {len(right_complete_both)} 个点，X范围: 350-430")
    
    try:
        polygon = parser.create_track_polygon_from_parallel_lines(
            left_complete_both, right_complete_both, image_shape[1], image_shape[0])
        
        if len(polygon) >= 3:
            mask = parser.points_to_mask(polygon, image_shape, 1)
            pixels = np.sum(mask > 0)
            print(f"   ✅ 成功生成多边形: {len(polygon)} 个顶点, {pixels:,} 个像素")
            print(f"   多边形顶点: {polygon}")
            
            # 可视化
            visualize_test_case(left_complete_both, right_complete_both, polygon, mask, 
                              "Both Sides Complete", output_path / "both_sides_complete.png")
        else:
            print(f"   ❌ 多边形生成失败")
    except Exception as e:
        print(f"   ❌ 异常: {e}")


def visualize_test_case(left_points, right_points, polygon_points, mask, title, save_path):
    """可视化测试案例"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'{title} - Partial Truncation Handling', fontsize=16)
    
    # 1. 输入轨道线
    axes[0].set_title('Input Track Lines')
    axes[0].set_xlim(0, 1920)
    axes[0].set_ylim(1080, 0)
    
    if left_points:
        left_x, left_y = zip(*left_points)
        axes[0].plot(left_x, left_y, 'g-o', label=f'Left ({len(left_points)} pts)', markersize=6, linewidth=3)
        # 检查是否被截断
        if min(left_x) <= 5:
            axes[0].text(50, 100, 'Left Truncated', fontsize=12, color='red', 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow"))
    
    if right_points:
        right_x, right_y = zip(*right_points)
        axes[0].plot(right_x, right_y, 'b-o', label=f'Right ({len(right_points)} pts)', markersize=6, linewidth=3)
        # 检查是否被截断
        if max(right_x) >= 1915:
            axes[0].text(1700, 100, 'Right Truncated', fontsize=12, color='red', 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow"))
    
    # 添加图像边界
    axes[0].axvline(x=0, color='black', linestyle='-', alpha=0.7, linewidth=3, label='Image Boundary')
    axes[0].axvline(x=1919, color='black', linestyle='-', alpha=0.7, linewidth=3)
    axes[0].axhline(y=0, color='black', linestyle='-', alpha=0.7, linewidth=2)
    axes[0].axhline(y=1079, color='black', linestyle='-', alpha=0.7, linewidth=2)
    
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. 生成的多边形
    axes[1].set_title('Generated Polygon')
    axes[1].set_xlim(0, 1920)
    axes[1].set_ylim(1080, 0)
    
    if polygon_points:
        polygon_x, polygon_y = zip(*polygon_points)
        axes[1].plot(polygon_x, polygon_y, 'r-', linewidth=3, label=f'Polygon ({len(polygon_points)} vertices)')
        axes[1].scatter(polygon_x, polygon_y, c='red', s=50, zorder=5)
        
        # 标注顶点顺序
        for i, (x, y) in enumerate(polygon_points):
            axes[1].annotate(str(i), (x+30, y+30), fontsize=10, color='darkred', weight='bold')
    
    # 添加图像边界
    axes[1].axvline(x=0, color='black', linestyle='-', alpha=0.7, linewidth=3)
    axes[1].axvline(x=1919, color='black', linestyle='-', alpha=0.7, linewidth=3)
    axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.7, linewidth=2)
    axes[1].axhline(y=1079, color='black', linestyle='-', alpha=0.7, linewidth=2)
    
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 3. 生成的掩码
    axes[2].set_title('Generated Mask')
    axes[2].imshow(mask, cmap='viridis', aspect='auto')
    axes[2].set_xlabel(f'Pixels: {np.sum(mask > 0):,}')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"   可视化已保存: {save_path}")


if __name__ == "__main__":
    test_partial_truncation()
