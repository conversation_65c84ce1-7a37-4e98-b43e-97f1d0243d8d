#!/usr/bin/env python3
"""
集成预测诊断脚本
用于调试为什么集成预测结果全是背景
"""

import sys
import torch
import cv2
import numpy as np
from pathlib import Path
import yaml
import albumentations as A
from albumentations.pytorch import ToTensorV2
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))
from src.models.ensemble import EnsemblePredictor

def load_ensemble_models(weights_dir, config_path):
    """加载集成模型进行诊断"""
    import segmentation_models_pytorch as smp
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    weights_dir = Path(weights_dir)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 模型配置
    models_config = [
        {
            'name': 'efficientnet_b4',
            'architecture': 'pan',
            'encoder': 'tu-tf_efficientnet_b4_ns',
            'encoder_weights': 'noisy-student'
        },
        {
            'name': 'eca_nfnet_l2',
            'architecture': 'pan', 
            'encoder': 'tu-eca_nfnet_l2',
            'encoder_weights': 'imagenet'
        },
        {
            'name': 'seresnet152d',
            'architecture': 'pan',
            'encoder': 'tu-seresnet152d', 
            'encoder_weights': 'imagenet'
        }
    ]
    
    # 加载模型
    models = []
    model_names = []
    
    for model_config in models_config:
        model_name = model_config['name']
        
        # 文件名映射
        name_mapping = {
            'efficientnet_b4': 'efficientnetb4',
            'eca_nfnet_l2': 'eca_nfnet_l2',
            'seresnet152d': 'seresnet152d'
        }
        
        actual_model_name = name_mapping.get(model_name, model_name)
        weights_path = weights_dir / f"{actual_model_name}.pth.tar"
        
        if not weights_path.exists():
            print(f"❌ 找不到模型文件: {weights_path}")
            continue
            
        print(f"🔍 加载模型: {weights_path}")
        
        # 加载模型
        model = torch.load(weights_path, map_location=device, weights_only=False)
        if hasattr(model, 'eval'):
            model.eval()
        
        models.append(model)
        model_names.append(model_name)
        print(f"✅ 模型加载成功: {model_name}")
    
    # 创建均等权重
    num_classes = 3
    fusion_weights = torch.ones(num_classes, len(models)) / len(models)
    
    # 创建集成预测器
    ensemble_predictor = EnsemblePredictor(models, fusion_weights, device)
    
    return ensemble_predictor, model_names, device

def preprocess_image(image_path):
    """预处理图像"""
    # 读取图像
    image = cv2.imread(image_path)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 预处理变换
    transforms = A.Compose([
        A.Resize(height=544, width=960),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])
    
    # 应用变换
    transformed = transforms(image=image_rgb)
    input_tensor = transformed['image'].unsqueeze(0)  # 添加batch维度
    
    return input_tensor, image_rgb.shape[:2]

def diagnose_ensemble_prediction(image_path, weights_dir='finetune', config_path='configs/railway_track_config.yaml'):
    """诊断集成预测问题"""
    print("=" * 80)
    print("🔍 集成预测诊断")
    print("=" * 80)
    
    # 加载模型
    ensemble_predictor, model_names, device = load_ensemble_models(weights_dir, config_path)
    
    # 预处理图像
    input_tensor, original_size = preprocess_image(image_path)
    input_tensor = input_tensor.to(device)
    
    print(f"\n📊 输入信息:")
    print(f"   - 图像路径: {image_path}")
    print(f"   - 原始尺寸: {original_size}")
    print(f"   - 输入张量尺寸: {input_tensor.shape}")
    print(f"   - 设备: {device}")
    
    # 单独测试每个模型
    print(f"\n🔍 单模型诊断:")
    print("-" * 50)
    
    individual_outputs = []
    
    with torch.no_grad():
        for i, (model, name) in enumerate(zip(ensemble_predictor.models, model_names)):
            print(f"\n模型 {i+1}: {name}")
            
            # 模型预测
            raw_output = model(input_tensor)
            sigmoid_output = torch.sigmoid(raw_output)
            
            print(f"   - 原始输出范围: [{raw_output.min().item():.4f}, {raw_output.max().item():.4f}]")
            print(f"   - Sigmoid输出范围: [{sigmoid_output.min().item():.4f}, {sigmoid_output.max().item():.4f}]")
            
            # 每个类别的统计
            for class_idx in range(3):
                class_output = sigmoid_output[0, class_idx]
                print(f"   - 类别 {class_idx}: 均值={class_output.mean().item():.4f}, "
                      f"最大值={class_output.max().item():.4f}, "
                      f">0.5的像素比例={((class_output > 0.5).sum().item() / class_output.numel() * 100):.1f}%")
            
            individual_outputs.append(sigmoid_output.cpu())
    
    # 集成预测
    print(f"\n🔍 集成预测诊断:")
    print("-" * 50)
    
    ensemble_output = ensemble_predictor.predict(input_tensor)
    
    print(f"   - 集成输出范围: [{ensemble_output.min().item():.4f}, {ensemble_output.max().item():.4f}]")
    
    # 每个类别的集成统计
    for class_idx in range(3):
        class_output = ensemble_output[0, class_idx]
        class_names = ['背景', '主轨道', '分叉轨道']
        
        print(f"   - {class_names[class_idx]} (类别 {class_idx}):")
        print(f"     均值={class_output.mean().item():.4f}, "
              f"最大值={class_output.max().item():.4f}")
        
        # 不同阈值下的像素比例
        for threshold in [0.1, 0.3, 0.5, 0.7]:
            ratio = ((class_output > threshold).sum().item() / class_output.numel() * 100)
            print(f"     阈值 {threshold}: {ratio:.1f}% 像素")
    
    # 权重诊断
    print(f"\n🔍 融合权重诊断:")
    print("-" * 50)
    fusion_weights = ensemble_predictor.fusion_weights
    print(f"   - 权重张量形状: {fusion_weights.shape}")
    
    for class_idx in range(3):
        class_names = ['背景', '主轨道', '分叉轨道']
        weights = fusion_weights[class_idx].cpu().numpy()
        print(f"   - {class_names[class_idx]}: {weights}")
    
    # 建议的阈值
    print(f"\n💡 建议:")
    print("-" * 50)
    
    # 分析最佳阈值
    ensemble_np = ensemble_output[0].cpu().numpy()  # (C, H, W)
    
    for class_idx in range(1, 3):  # 跳过背景类别
        class_names = ['背景', '主轨道', '分叉轨道']
        class_data = ensemble_np[class_idx]
        
        # 找到有意义的阈值范围
        non_zero_mean = class_data[class_data > 0.01].mean() if (class_data > 0.01).any() else 0
        percentile_95 = np.percentile(class_data, 95)
        percentile_90 = np.percentile(class_data, 90)
        
        print(f"   - {class_names[class_idx]}:")
        print(f"     推荐阈值: {max(0.1, min(0.4, non_zero_mean)):.2f}")
        print(f"     95%分位数: {percentile_95:.4f}")
        print(f"     90%分位数: {percentile_90:.4f}")
    
    return ensemble_output.cpu().numpy()

if __name__ == "__main__":
    # 测试图像 - 预测失败的训练集图像
    test_image = "/home/<USER>/data/railway_track_dataset/images/20250119133344135.near.avi_frame_236.png"
    
    if Path(test_image).exists():
        result = diagnose_ensemble_prediction(test_image)
        print(f"\n✅ 诊断完成！")
    else:
        print(f"❌ 测试图像不存在: {test_image}") 