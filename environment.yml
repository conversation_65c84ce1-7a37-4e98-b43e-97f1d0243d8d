name: railway-seg
channels:
  - conda-forge
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - bzip2=1.0.8=h4bc722e_7
  - ca-certificates=2025.4.26=hbd8a1cb_0
  - ld_impl_linux-64=2.43=h712a8e2_4
  - libexpat=2.7.0=h5888daf_0
  - libffi=3.4.6=h2dba641_1
  - libgcc=15.1.0=h767d61c_2
  - libgcc-ng=15.1.0=h69a702a_2
  - libgomp=15.1.0=h767d61c_2
  - liblzma=5.8.1=hb9d3cd8_2
  - libnsl=2.0.1=hd590300_0
  - libsqlite=3.50.1=hee588c1_0
  - libuuid=2.38.1=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libzlib=1.3.1=hb9d3cd8_2
  - ncurses=6.5=h2d0b736_3
  - openssl=3.5.0=h7b32b05_1
  - pip=25.1.1=pyh8b19718_0
  - python=3.10.18=hd6af730_0_cpython
  - readline=8.2=h8c095d6_2
  - tk=8.6.13=noxft_hd72426e_102
  - wheel=0.45.1=pyhd8ed1ab_1
  - pip:
      - absl-py==2.3.0
      - albumentations==1.3.1
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - appdirs==1.4.4
      - argon2-cffi==25.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asttokens==3.0.0
      - async-lru==2.0.5
      - attrs==25.3.0
      - babel==2.17.0
      - beautifulsoup4==4.13.4
      - bleach==6.2.0
      - cachetools==5.5.2
      - certifi==2025.4.26
      - cffi==1.17.1
      - charset-normalizer==3.4.2
      - click==8.2.1
      - cmake==4.0.2
      - comm==0.2.2
      - contourpy==1.3.2
      - cycler==0.12.1
      - debugpy==1.8.14
      - decorator==5.2.1
      - defusedxml==0.7.1
      - dill==0.4.0
      - docker-pycreds==0.4.0
      - efficientnet-pytorch==0.7.1
      - exceptiongroup==1.3.0
      - executing==2.2.0
      - fastjsonschema==2.21.1
      - filelock==3.18.0
      - fonttools==4.58.2
      - fqdn==1.5.1
      - fsspec==2025.5.1
      - gitdb==4.0.12
      - gitpython==3.1.44
      - google-auth==2.40.3
      - google-auth-oauthlib==1.0.0
      - gputil==1.4.0
      - grpcio==1.73.0
      - h11==0.16.0
      - h5py==3.9.0
      - hf-xet==1.1.3
      - httpcore==1.0.9
      - httpx==0.28.1
      - huggingface-hub==0.32.4
      - hydra-core==1.3.2
      - idna==3.10
      - imageio==2.37.0
      - ipykernel==6.29.5
      - ipython==8.37.0
      - ipywidgets==8.1.7
      - isoduration==20.11.0
      - jedi==0.19.2
      - jinja2==3.1.6
      - joblib==1.5.1
      - json5==0.12.0
      - jsonpointer==3.0.0
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - jupyter==1.1.1
      - jupyter-client==8.6.3
      - jupyter-console==6.6.3
      - jupyter-core==5.8.1
      - jupyter-events==0.12.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.16.0
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.4.3
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - jupyterlab-widgets==3.0.15
      - kiwisolver==1.4.8
      - lazy-loader==0.4
      - lit==18.1.8
      - loguru==0.7.3
      - markdown==3.8
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - matplotlib==3.7.5
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - memory-profiler==0.61.0
      - mistune==3.1.3
      - mpmath==1.3.0
      - munch==4.0.0
      - nbclient==0.10.2
      - nbconvert==7.16.6
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - notebook==7.4.3
      - notebook-shim==0.2.4
      - numpy==1.24.4
      - oauthlib==3.2.2
      - omegaconf==2.3.0
      - opencv-contrib-python==4.8.1.78
      - opencv-python==4.11.0.86
      - opencv-python-headless==4.11.0.86
      - overrides==7.7.0
      - packaging==25.0
      - pandas==2.0.3
      - pandocfilters==1.5.1
      - parso==0.8.4
      - pathtools==0.1.2
      - pexpect==4.9.0
      - pillow==9.5.0
      - platformdirs==4.3.8
      - plotly==5.15.0
      - pretrainedmodels==0.7.4
      - prometheus-client==0.22.1
      - prompt-toolkit==3.0.51
      - protobuf==4.25.8
      - psutil==7.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pycparser==2.22
      - pygments==2.19.1
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - python-json-logger==3.3.0
      - pytz==2025.2
      - pywavelets==1.8.0
      - pyyaml==6.0.2
      - pyzmq==26.4.0
      - qudida==0.0.4
      - referencing==0.36.2
      - requests==2.32.4
      - requests-oauthlib==2.0.0
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.4.2
      - rpds-py==0.25.1
      - rsa==4.9.1
      - safetensors==0.5.3
      - scikit-image==0.21.0
      - scikit-learn==1.3.2
      - scipy==1.10.1
      - seaborn==0.12.2
      - segmentation-models-pytorch==0.3.4
      - send2trash==1.8.3
      - sentry-sdk==2.29.1
      - setproctitle==1.3.6
      - setuptools==80.9.0
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - soupsieve==2.7
      - stack-data==0.6.3
      - sympy==1.14.0
      - tenacity==9.1.2
      - tensorboard==2.13.0
      - tensorboard-data-server==0.7.2
      - terminado==0.18.1
      - threadpoolctl==3.6.0
      - tifffile==2025.5.10
      - timm==0.9.7
      - tinycss2==1.4.0
      - tomli==2.2.1
      - torch==2.0.0+cu118
      - torchvision==0.15.0+cu118
      - tornado==6.5.1
      - tqdm==4.66.6
      - traitlets==5.14.3
      - triton==2.0.0
      - types-python-dateutil==2.9.0.20250516
      - typing-extensions==4.14.0
      - tzdata==2025.2
      - uri-template==1.3.0
      - urllib3==2.4.0
      - wandb==0.15.12
      - wcwidth==0.2.13
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - werkzeug==3.1.3
      - widgetsnbextension==4.0.14
prefix: /home/<USER>/miniforge3/envs/railway-seg
