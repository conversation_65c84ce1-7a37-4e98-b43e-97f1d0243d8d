{"timestamp": "2025-07-31T11:38:28.157247", "model_paths": ["finetune/efficientnetb4.pth.tar", "finetune/eca_nfnet_l2.pth.tar", "finetune/seresnet152d.pth.tar"], "models_config": [{"name": "efficientnetb4", "architecture": "PAN", "encoder": "tu-tf_efficientnet_b4_ns", "encoder_weights": "noisy-student", "epochs": 25}, {"name": "eca_nfnet_l2", "architecture": "PAN", "encoder": "tu-eca_nfnet_l2", "encoder_weights": "imagenet", "epochs": 25}, {"name": "seresnet152d", "architecture": "PAN", "encoder": "tu-seresnet152d", "encoder_weights": "imagenet", "epochs": 30}], "fusion_weights": {"0": {"weights": [0.006996858451334099, 0.8962478609535963, 0.09675528059506956], "iou": 0.9992056488990784, "class_name": "背景"}, "1": {"weights": [0.005207190302422028, 0.9737245792961857, 0.021068230401392326], "iou": 0.9878259301185608, "class_name": "主轨道"}, "2": {"weights": [0.006580074432457097, 0.9889879606079702, 0.0044319649595726315], "iou": 0.11492282152175903, "class_name": "分叉轨道"}}, "device": "cuda"}