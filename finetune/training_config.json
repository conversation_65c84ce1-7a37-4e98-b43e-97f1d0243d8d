{"data_dir": "/home/<USER>/data/railway_track_dataset/", "config_file": "configs/railway_track_config.yaml", "seed": 0, "requested_models": ["efficientnetb4", "eca_nfnet_l2", "seresnet152d"], "trained_models": [], "skipped_models": ["efficientnetb4", "eca_nfnet_l2", "seresnet152d"], "models_config": [{"name": "efficientnetb4", "architecture": "PAN", "encoder": "tu-tf_efficientnet_b4_ns", "encoder_weights": "noisy-student", "epochs": 25, "pretrained_path": null, "description": "EfficientNet-B4 with Noisy Student weights"}, {"name": "eca_nfnet_l2", "architecture": "PAN", "encoder": "tu-eca_nfnet_l2", "encoder_weights": "imagenet", "epochs": 25, "pretrained_path": null, "description": "ECA-NFNet-L2 with ImageNet weights"}, {"name": "seresnet152d", "architecture": "PAN", "encoder": "tu-seresnet152d", "encoder_weights": "imagenet", "epochs": 30, "pretrained_path": null, "description": "SE-ResNet-152D with ImageNet weights"}], "timestamp": "2025-07-31T10:38:43.651527"}