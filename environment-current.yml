name: base
channels:
  - conda-forge
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - archspec=0.2.5=pyhd8ed1ab_0
  - boltons=24.0.0=pyhd8ed1ab_1
  - brotli-python=1.1.0=py312h2ec8cdc_2
  - bzip2=1.0.8=h4bc722e_7
  - c-ares=1.34.5=hb9d3cd8_0
  - ca-certificates=2025.4.26=hbd8a1cb_0
  - certifi=2025.4.26=pyhd8ed1ab_0
  - cffi=1.17.1=py312h06ac9bb_0
  - charset-normalizer=3.4.2=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - conda=25.3.0=py312h7900ff3_0
  - conda-libmamba-solver=25.3.0=pyhd8ed1ab_0
  - conda-package-handling=2.4.0=pyh7900ff3_2
  - conda-package-streaming=0.11.0=pyhd8ed1ab_1
  - cpp-expected=1.1.0=hff21bea_1
  - distro=1.9.0=pyhd8ed1ab_1
  - fmt=11.1.4=h07f6e7f_1
  - frozendict=2.4.6=py312h66e93f0_0
  - h2=4.2.0=pyhd8ed1ab_0
  - hpack=4.1.0=pyhd8ed1ab_0
  - hyperframe=6.1.0=pyhd8ed1ab_0
  - icu=75.1=he02047a_0
  - idna=3.10=pyhd8ed1ab_1
  - jsonpatch=1.33=pyhd8ed1ab_1
  - jsonpointer=3.0.0=py312h7900ff3_1
  - keyutils=1.6.1=h166bdaf_0
  - krb5=1.21.3=h659f571_0
  - ld_impl_linux-64=2.43=h712a8e2_4
  - libarchive=3.7.7=h75ea233_4
  - libcurl=8.13.0=h332b0f4_0
  - libedit=3.1.20250104=pl5321h7949ede_0
  - libev=4.33=hd590300_2
  - libexpat=2.7.0=h5888daf_0
  - libffi=3.4.6=h2dba641_1
  - libgcc=15.1.0=h767d61c_2
  - libgcc-ng=15.1.0=h69a702a_2
  - libgomp=15.1.0=h767d61c_2
  - libiconv=1.18=h4ce23a2_1
  - liblzma=5.8.1=hb9d3cd8_1
  - libmamba=2.1.1=h430c389_0
  - libmambapy=2.1.1=py312h07448e0_0
  - libnghttp2=1.64.0=h161d5f1_0
  - libnsl=2.0.1=hd590300_0
  - libsolv=0.7.32=h7955e40_2
  - libsqlite=3.49.2=hee588c1_0
  - libssh2=1.11.1=hcf80075_0
  - libstdcxx=15.1.0=h8f9b012_2
  - libstdcxx-ng=15.1.0=h4852527_2
  - libuuid=2.38.1=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libxml2=2.13.8=h4bc477f_0
  - libzlib=1.3.1=hb9d3cd8_2
  - lz4-c=1.10.0=h5888daf_1
  - lzo=2.10=hd590300_1001
  - mamba=2.1.1=had4a41a_0
  - menuinst=2.2.0=py312h7900ff3_0
  - ncurses=6.5=h2d0b736_3
  - nlohmann_json=3.11.3=he02047a_1
  - openssl=3.5.0=h7b32b05_1
  - packaging=25.0=pyh29332c3_1
  - pip=25.1.1=pyh8b19718_0
  - platformdirs=4.3.8=pyhe01879c_0
  - pluggy=1.5.0=pyhd8ed1ab_1
  - pybind11-abi=4=hd8ed1ab_3
  - pycosat=0.6.6=py312h66e93f0_2
  - pycparser=2.22=pyh29332c3_1
  - pysocks=1.7.1=pyha55dd90_7
  - python=3.12.10=h9e4cc4f_0_cpython
  - python_abi=3.12=7_cp312
  - readline=8.2=h8c095d6_2
  - reproc=14.2.5.post0=hb9d3cd8_0
  - reproc-cpp=14.2.5.post0=h5888daf_0
  - requests=2.32.3=pyhd8ed1ab_1
  - ruamel.yaml=0.18.10=py312h66e93f0_0
  - ruamel.yaml.clib=0.2.8=py312h66e93f0_1
  - setuptools=80.1.0=pyhff2d567_0
  - simdjson=3.12.3=h84d6215_0
  - tk=8.6.13=noxft_h4845f30_101
  - tqdm=4.67.1=pyhd8ed1ab_1
  - truststore=0.10.1=pyh29332c3_0
  - urllib3=2.4.0=pyhd8ed1ab_0
  - wheel=0.45.1=pyhd8ed1ab_1
  - yaml-cpp=0.8.0=h3f2d84a_0
  - zstandard=0.23.0=py312h66e93f0_2
  - zstd=1.5.7=hb8e6e7a_2
  - pip:
      - albucore==0.0.24
      - albumentations==2.0.8
      - annotated-types==0.7.0
      - cloudpickle==3.1.1
      - contourpy==1.3.2
      - cycler==0.12.1
      - dill==0.4.0
      - easydict==1.13
      - filelock==3.18.0
      - fonttools==4.58.4
      - fsspec==2025.5.1
      - fvcore==0.1.5.post20221221
      - hf-xet==1.1.4
      - huggingface-hub==0.33.0
      - iopath==0.1.10
      - jinja2==3.1.6
      - joblib==1.5.1
      - kiwisolver==1.4.8
      - markupsafe==3.0.2
      - matplotlib==3.10.3
      - mpmath==1.3.0
      - networkx==3.5
      - numpy==2.3.0
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.6.80
      - nvidia-cuda-nvrtc-cu12==12.6.77
      - nvidia-cuda-runtime-cu12==12.6.77
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-cufile-cu12==********
      - nvidia-curand-cu12==*********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==********
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-ml-py==12.575.51
      - nvidia-nccl-cu12==2.26.2
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.6.77
      - nvitop==1.5.1
      - opencv-contrib-python==*********
      - opencv-python-headless==*********
      - pandas==2.3.1
      - pillow==11.2.1
      - portalocker==3.2.0
      - psutil==7.0.0
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - pytz==2025.2
      - pyyaml==6.0.2
      - safetensors==0.5.3
      - scikit-learn==1.7.0
      - scipy==1.15.3
      - seaborn==0.13.2
      - segmentation-models-pytorch==0.5.0
      - simsimd==6.4.9
      - six==1.17.0
      - stringzilla==3.12.5
      - sympy==1.14.0
      - tabulate==0.9.0
      - termcolor==3.1.0
      - threadpoolctl==3.6.0
      - timm==1.0.15
      - torch==2.7.1
      - torchvision==0.22.1
      - triton==3.3.1
      - typing-extensions==4.14.0
      - typing-inspection==0.4.1
      - tzdata==2025.2
      - yacs==0.1.8
prefix: /home/<USER>/miniforge3
