augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []
camera_configs:
  # 相机特定配置 - 每种相机的训练和推理参数
  25mm:
    batch_size:
      train: 4
      val: 6
    image_size:  # 该相机类型的推荐输入尺寸（基于相机特性优化）
      height: 544
      width: 960
  6mm:
    batch_size:
      train: 4
      val: 6
    image_size:
      height: 544
      width: 960
checkpointing:
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 5
  auto_resume: true
  cleanup_strategy: best_k
data:
  batch_size:
    test: 4
    train: 4
    val: 8
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:  # 训练时图像的目标尺寸 - 原始图像会被缩放到此尺寸进行训练
    height: 544  # 目标高度（像素）
    width: 960   # 目标宽度（像素）
  json_dir: data/railway_annotation_6mm
  k_folds: null
  num_classes: 3
  processed_data_path: /home/<USER>/data/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15
inference:
  output_dir: outputs/predictions
  save_overlay: true
  test_time_augmentation: true
  threshold: 0.5
  tta_transforms:
  - horizontal_flip
  - vertical_flip
logging:
  log_dir: outputs/logs
  log_every_n_steps: 10
  log_images: true
  max_images_to_log: 8
  tensorboard: true
loss:
  losses:
  - smooth: 1.0
    type: dice_loss
  - alpha:
    - 0.2
    - 0.4
    - 0.4
    gamma: 2.0
    type: focal_loss
  type: combined_loss
  weights:
  - 0.5
  - 0.5
metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true
model:
  type: "segmentation_model"
  activation: sigmoid
  architecture: pan
  classes: 3
  encoder: efficientnet-b4
  encoder_weights: imagenet
  ensemble:
    enabled: false
    models:
    - type: "segmentation_model"
      encoder: efficientnet-b4
    - type: "segmentation_model"
      encoder: se_resnext50_32x4d
project:
  device: cuda
  name: railway-track-segmentation
  num_workers: 8
  seed: 42
  gpu_optimization:
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 2
    use_cuda_graph: true
training:
  epochs: 40
  early_stopping_patience: 20
  gradient_accumulation_steps: 2
  clip_gradient: true
  mixed_precision: true
  eval_interval: 1
  save_interval: 5
  
  # 基于步数的保存配置
  enable_step_saving: true
  save_every_n_steps: 500
  keep_step_checkpoints: 3
  
  # 内存管理配置
  memory_cleanup_interval: 20  # 减少频繁清理
  enable_memory_monitoring: true
  memory_leak_threshold: 500.0  # 提高阈值
  
  # 断点重启配置
  resume_training: true  # 支持恢复训练
  save_optimizer_state: true  # 保存优化器状态
  save_scheduler_state: true  # 保存调度器状态
  save_random_state: true  # 保存随机数种子状态
  
  # 验证配置
  validation:
    max_batches: 500  # 限制验证批次数量以控制内存
    sample_for_visualization: 2  # 用于可视化的样本数量
  
  # 性能优化配置
  performance_optimization:
    cudnn_benchmark: true  # 启用cudnn基准测试以优化卷积操作
    use_channels_last: true  # 使用通道最后内存格式，优化GPU性能
    use_data_prefetcher: true  # 使用自定义数据预取器
    gradient_checkpointing: false  # 大模型时考虑启用
    max_grad_norm: 1.0  # 梯度裁剪的最大范数
  
  optimizer:
    betas:
    - 0.9
    - 0.999
    lr: 0.0005
    type: adamw
    weight_decay: 0.0001
  scheduler:
    min_lr: 0.000001
    type: cosine
    warmup_epochs: 5
visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5
  save_dir: outputs/visualizations
  save_predictions: true
