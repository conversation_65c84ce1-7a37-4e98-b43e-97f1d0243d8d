augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []
camera_configs:
  25mm:
    batch_size:
      train: 4
      val: 6
    image_size:
      height: 544
      width: 960
  6mm:
    batch_size:
      train: 4
      val: 6
    image_size:
      height: 544
      width: 960
checkpointing:
  auto_resume: true
  cleanup_strategy: best_k
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 5
data:
  batch_size:
    test: 4
    train: 8
    val: 8
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:
    height: 544
    width: 960
  json_dir: data/railway_annotation_6mm
  k_folds: null
  mask_format: png
  num_classes: 3
  processed_data_path: /home/<USER>/data/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15
  use_multilabel: true
inference:
  output_dir: outputs/predictions
  save_overlay: true
  test_time_augmentation: true
  threshold: 0.3
  tta_transforms:
  - horizontal_flip
  - vertical_flip
logging:
  log_dir: outputs/logs
  log_every_n_steps: 10
  log_images: true
  max_images_to_log: 8
  tensorboard: true
loss:
  alpha:
  - 0.1
  - 0.3
  - 0.6
  focal_weight: 0.5
  gamma: 2.0
  jaccard_weight: 0.5
  type: multilabel_combined_loss
metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true
model:
  activation: sigmoid
  architecture: pan
  classes: 3
  encoder: efficientnet-b4
  encoder_weights: imagenet
  ensemble:
    enabled: false
    models:
    - encoder: efficientnet-b4
      type: segmentation_model
    - encoder: se_resnext50_32x4d
      type: segmentation_model
  type: segmentation_model
project:
  device: cuda
  gpu_optimization:
    non_blocking: true
    persistent_workers: true
    pin_memory: true
    prefetch_factor: 3
  name: railway-track-segmentation
  num_workers: 6
  seed: 42
training:
  clip_gradient: true
  early_stopping_patience: 25
  enable_memory_monitoring: true
  enable_step_saving: true
  epochs: 60
  eval_interval: 1
  gradient_accumulation_steps: 2
  keep_step_checkpoints: 3
  memory_cleanup_interval: 20
  memory_leak_threshold: 500.0
  mixed_precision: true
  optimizer:
    betas:
    - 0.9
    - 0.999
    lr: 0.0003
    type: adamw
    weight_decay: 0.0001
  performance_optimization:
    cudnn_benchmark: true
    gradient_checkpointing: false
    max_grad_norm: 1.0
    use_channels_last: true
    use_data_prefetcher: true
  resume_training: true
  save_every_n_steps: 500
  save_interval: 5
  save_optimizer_state: true
  save_random_state: true
  save_scheduler_state: true
  scheduler:
    min_lr: 1.0e-06
    type: cosine
    warmup_epochs: 5
  validation:
    max_batches: 500
    sample_for_visualization: 2
visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5
  save_dir: outputs/visualizations
  save_predictions: true
