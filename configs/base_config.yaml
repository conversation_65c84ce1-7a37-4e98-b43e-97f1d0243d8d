# 基础配置文件
# 铁路分割项目配置

# 项目基础设置
project:
  name: "railway-infrastructure-segmentation"
  seed: 42
  device: "cuda"
  num_workers: 4

# 数据相关配置
data:
  # 数据路径
  raw_data_path: "data/raw"
  processed_data_path: "data/processed"
  splits_path: "data/splits"
  
  # 图像设置
  image_size:
    width: 960
    height: 544
  
  # 数据集划分
  split_ratio:
    train: 0.7
    val: 0.15
    test: 0.15
  
  # K折交叉验证
  k_folds: 4
  
  # 批次大小
  batch_size:
    train: 8
    val: 16
    test: 1

# 训练相关配置
training:
  epochs: 100
  early_stopping_patience: 15
  gradient_accumulation_steps: 4
  clip_gradient: true
  mixed_precision: true
  
  # 优化器
  optimizer:
    type: "adamw"
    lr: 0.001
    weight_decay: 0.0001
    betas: [0.9, 0.999]
  
  # 学习率调度器
  scheduler:
    type: "cosine"
    warmup_epochs: 5
    min_lr: 0.000001

# 模型相关配置
model:
  # 模型类型（用于注册器）
  type: "segmentation_model"
  # 分割模型架构
  architecture: "pan"  # Pyramid Attention Network
  encoder: "efficientnet-b4"
  encoder_weights: "imagenet"
  classes: 3  # 铁路轨道、机车车辆、背景
  activation: "sigmoid"
  
  # 模型集成
  ensemble:
    enabled: true
    models:
      - type: "segmentation_model"
        encoder: "efficientnet-b4"
      - type: "segmentation_model"
        encoder: "se_resnext50_32x4d"
      - type: "segmentation_model"
        encoder: "timm-nfnet-l0"

# 损失函数配置
loss:
  type: "dice_bce"  # Dice + BCE 组合损失
  dice_weight: 0.5
  bce_weight: 0.5

# 数据增强配置
augmentation:
  train:
    - type: "horizontal_flip"
      p: 0.5
    - type: "perspective"
      scale: [0.05, 0.1]
      p: 0.25
    - type: "coarse_dropout"
      max_holes: 12
      max_height: 256
      max_width: 256
      p: 0.5
    - type: "brightness_contrast"
      brightness_limit: 0.1
      contrast_limit: 0.1
      p: 0.5
    - type: "multiplicative_noise"
      multiplier: [0.5, 1.5]
      p: 0.25
  
  val:
    # 验证集只进行必要的预处理

# 评估指标
metrics:
  - "iou"
  - "dice"
  - "precision"
  - "recall"
  - "f1"

# 日志和保存
logging:
  log_dir: "outputs/logs"
  tensorboard: true
  log_every_n_steps: 10

checkpointing:
  save_dir: "models/checkpoints"
  save_top_k: 3
  monitor: "val_iou"
  mode: "max"

# 推理配置
inference:
  test_time_augmentation: true
  tta_transforms: 
    - "horizontal_flip"
    - "vertical_flip"
  threshold: 0.5
  output_dir: "outputs/predictions"

# 可视化配置
visualization:
  save_dir: "outputs/visualizations"
  save_predictions: true
  overlay_alpha: 0.5
  colors:
    railway_track: [255, 0, 0]      # 红色
    rolling_stock: [0, 255, 0]      # 绿色
    background: [0, 0, 0]           # 黑色 