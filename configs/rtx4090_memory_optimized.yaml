checkpoints:
  mode: max
  monitor: val_iou
  save_last: true
  save_top_k: 3
data:
  batch_size:
    test: 12
    train: 8
    val: 12
  image_size:
    height: 416
    width: 736
  mask_format: png
  num_classes: 3
  use_multilabel: true
loss:
  type: multilabel_combined
  weights:
    bce: 0.5
    dice: 0.3
    focal: 0.2
metrics:
- iou
- dice
- precision
- recall
models:
  ensemble:
  - architecture: PAN
    encoder: tu-eca_nfnet_l2
    encoder_weights: imagenet
    memory_efficient: true
    name: eca_nfnet_l2
  - architecture: PAN
    encoder: tu-seresnet152d
    encoder_weights: imagenet
    memory_efficient: true
    name: seresnet152d
project:
  device: cuda
  gpu_optimization:
    channels_last: true
    compile_model: true
    drop_last: true
    memory_efficient: true
    non_blocking: true
    persistent_workers: true
    pin_memory: true
    prefetch_factor: 3
    use_cuda_graph: false
  num_workers: 6
  seed: 42
training:
  epochs: 60
  gradient_accumulation_steps: 2
  gradient_checkpointing: true
  learning_rate: 0.0001
  lr_scheduler:
    T_max: 60
    eta_min: 1.0e-06
    type: cosine_annealing
  max_grad_norm: 1.0
  optimizer: adamw
  use_amp: true
  warmup_epochs: 3
  weight_decay: 0.01
