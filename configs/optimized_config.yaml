# 优化后的配置文件 - 针对GPU利用率优化
# 基于railway_track_config.yaml

augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []

camera_configs:
  25mm:
    batch_size:
      train: 6  # 增加批次大小（原来是4）
      val: 8    # 增加批次大小（原来是6）
    image_size:
      height: 1088
      width: 1920
  6mm:
    batch_size:
      train: 6  # 增加批次大小（原来是4）
      val: 8    # 增加批次大小（原来是6）
    image_size:
      height: 1088
      width: 1920

checkpointing:
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 5
  auto_resume: true
  cleanup_strategy: best_k

data:
  batch_size:
    test: 4
    train: 12    # 增加批次大小（原来是8）
    val: 16      # 增加批次大小（原来是8）
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:
    height: 544
    width: 960
  json_dir: data/railway_annotation_6mm
  k_folds: null
  num_classes: 3
  processed_data_path: /home/<USER>/data/Download/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15

inference:
  output_dir: outputs/predictions
  save_overlay: true
  test_time_augmentation: true
  threshold: 0.5
  tta_transforms:
  - horizontal_flip
  - vertical_flip

logging:
  log_dir: outputs/logs
  log_every_n_steps: 10
  log_images: true
  max_images_to_log: 8
  tensorboard: true

loss:
  losses:
  - smooth: 1.0
    type: dice_loss
  - alpha:
    - 0.2
    - 0.4
    - 0.4
    gamma: 2.0
    type: focal_loss
  type: combined_loss
  weights:
  - 0.5
  - 0.5

metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true

model:
  type: "segmentation_model"
  activation: sigmoid
  architecture: pan
  classes: 3
  encoder: efficientnet-b4
  encoder_weights: imagenet
  ensemble:
    enabled: false
    models:
    - type: "segmentation_model"
      encoder: efficientnet-b4
    - type: "segmentation_model"
      encoder: se_resnext50_32x4d

project:
  device: cuda
  name: railway-track-segmentation
  num_workers: 12  # 增加工作线程数（原来是8）
  seed: 42
  gpu_optimization:
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 4  # 增加预取因子（原来是2）
    use_cuda_graph: true
    non_blocking: true  # 添加非阻塞数据传输
    benchmark_cudnn: true  # 添加cuDNN基准测试

training:
  epochs: 150
  early_stopping_patience: 20
  gradient_accumulation_steps: 1  # 减少梯度累积步数（原来是2），以增加GPU利用率
  clip_gradient: true
  mixed_precision: true
  eval_interval: 1
  save_interval: 5
  
  # 基于步数的保存配置
  enable_step_saving: true
  save_every_n_steps: 1000  # 增加保存间隔（原来是500）
  keep_step_checkpoints: 3
  
  # 内存管理配置
  memory_cleanup_interval: 20  # 减少频繁清理
  enable_memory_monitoring: true
  memory_leak_threshold: 500.0  # 提高阈值
  
  # 断点重启配置
  resume_training: true
  save_optimizer_state: true
  save_scheduler_state: true
  save_random_state: true
  
  # 验证配置
  validation:
    max_batches: 500
    sample_for_visualization: 2
  
  # 性能优化配置
  performance_optimization:
    cudnn_benchmark: true  # 启用cudnn基准测试
    use_channels_last: true  # 使用通道最后内存格式
    use_data_prefetcher: true  # 使用自定义数据预取器
    gradient_checkpointing: false  # 大模型时考虑启用
    max_grad_norm: 1.0  # 梯度裁剪的最大范数
    jit_compile: true  # 添加JIT编译支持
    compile_mode: "reduce-overhead"  # 编译模式
    find_unused_parameters: false  # 禁用查找未使用参数以提高性能
    deterministic: false  # 禁用确定性计算以提高性能
    optimize_memory_usage: true  # 优化内存使用
    use_tf32: true  # 对A100等新GPU启用TF32
    use_flash_attention: false  # 可选的闪存注意力机制
  
  optimizer:
    betas:
    - 0.9
    - 0.999
    lr: 0.0005
    type: adamw
    weight_decay: 0.0001
  scheduler:
    min_lr: 0.000001
    type: cosine
    warmup_epochs: 5

visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5
  save_dir: outputs/visualizations
  save_predictions: true 