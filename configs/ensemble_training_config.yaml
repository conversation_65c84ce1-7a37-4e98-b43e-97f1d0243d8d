# 集成学习专用配置文件
# 基于 railway_track_config.yaml，添加集成模型配置

# 数据配置
data:
  batch_size:
    test: 4
    train: 4
    val: 4
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:
    height: 544
    width: 960
  json_dir: data/railway_annotation_6mm
  k_folds: null
  num_classes: 3
  processed_data_path: /home/<USER>/data/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15
  use_multilabel: true
  mask_format: 'png'

# 集成模型配置（使用支持的编码器）
ensemble:
  models:
    - name: 'eca_nfnet_l2'
      architecture: 'pan'
      encoder: 'tu-eca_nfnet_l2'
      encoder_weights: 'imagenet'
      epochs: 50
      learning_rate: 0.0002  # 降低学习率
      
    - name: 'efficientnet_b4'
      architecture: 'pan'
      encoder: 'tu-tf_efficientnet_b4_ns'
      encoder_weights: 'noisy-student'
      epochs: 50
      learning_rate: 0.0004
      
    - name: 'seresnet152d'
      architecture: 'pan'
      encoder: 'tu-seresnet152d'
      encoder_weights: 'imagenet'
      epochs: 50
      learning_rate: 0.0003

# 数据增强
augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []

# 损失函数配置
loss:
  type: multilabel_combined_loss
  jaccard_weight: 0.5
  focal_weight: 0.5
  alpha:
  - 0.1    # 背景权重
  - 0.3    # 主轨道权重
  - 0.6    # 分叉轨道权重
  gamma: 2.0

# 模型配置（为兼容性保留）
model:
  type: segmentation_model
  activation: sigmoid
  architecture: pan
  classes: 3
  encoder: efficientnet-b4
  encoder_weights: imagenet

# 项目配置
project:
  device: cuda
  name: railway-track-segmentation
  num_workers: 4  # 降低以避免内存问题
  seed: 42
  gpu_optimization:
    pin_memory: true
    persistent_workers: false  # 集成训练时禁用
    prefetch_factor: 2
    use_cuda_graph: false  # 集成训练时禁用

# 训练配置
training:
  epochs: 60
  early_stopping_patience: 15
  gradient_accumulation_steps: 1  # 集成训练时减少
  clip_gradient: true
  mixed_precision: false  # 集成训练时禁用以提高稳定性
  eval_interval: 1
  save_interval: 5
  optimizer:
    type: adamw
    lr: 0.0003
    weight_decay: 0.0001
    betas:
    - 0.9
    - 0.999
  scheduler:
    type: cosine
    min_lr: 1.0e-06
    warmup_epochs: 3

# 度量配置
metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true

# 日志配置
logging:
  log_dir: outputs/logs
  log_every_n_steps: 10
  log_images: true
  max_images_to_log: 8
  tensorboard: true

# 可视化配置
visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5
  save_dir: outputs/visualizations
  save_predictions: true

# 检查点配置
checkpointing:
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 5
  auto_resume: true
  cleanup_strategy: best_k