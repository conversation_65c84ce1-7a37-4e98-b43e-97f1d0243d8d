# Modern training configuration for railway track segmentation
# 基于原始railway_track_config.yaml的设置

# Data configuration
data:
  # Paths
  train_path: "/home/<USER>/data/railway_track_dataset"
  val_path: "/home/<USER>/data/railway_track_dataset"
  test_path: "/home/<USER>/data/railway_track_dataset"
  
  # Image parameters - 使用原始配置的尺寸
  image_size: 
    height: 544
    width: 960
  num_classes: 3
  use_multilabel: true
  mask_format: 'png'
  
  # DataLoader parameters
  num_workers: 8
  pin_memory: true
  persistent_workers: true
  prefetch_factor: 2
  
  # Class information
  class_names:
    - "background"
    - "main_track"
    - "fork_track"

# Model configuration - 使用原始配置的模型
model:
  architecture: "pan"              # 原始配置使用pan
  backbone: "efficientnet-b4"     # 原始配置使用efficientnet-b4
  pretrained: true
  output_channels: 3
  
  # Architecture specific
  decoder_channels: 256
  encoder_depth: 5
  activation: "sigmoid"

# Training configuration - 使用原始配置的参数
training:
  # Basic parameters
  num_epochs: 60                   # 原始配置使用60
  batch_size: 4                    # 原始配置使用4
  val_batch_size: 8                # 原始配置使用8
  output_dir: "outputs"
  
  # Learning rate and optimizer - 使用原始配置的学习率
  learning_rate: 0.0003            # 原始配置使用0.0003
  min_lr: 0.000001
  weight_decay: 0.0001             # 原始配置使用0.0001
  
  # Gradient settings
  gradient_accumulation_steps: 2   # 原始配置使用2
  gradient_clip_val: 1.0
  mixed_precision: true
  
  # Modern training techniques
  use_ema: true
  ema_decay: 0.999
  use_swa: false                   # 关闭SWA避免复杂性
  swa_start_epoch: 45
  swa_lr: 0.0001
  
  # Validation and checkpointing
  val_frequency: 1
  save_frequency: 5
  save_best_only: false
  early_stopping: true
  early_stopping_patience: 25      # 原始配置使用25
  early_stopping_min_delta: 0.001
  
  # Resume training
  resume_from_checkpoint: null

# Optimizer configuration
optimizer:
  type: "adamw"
  betas: [0.9, 0.999]
  eps: 1e-8
  
  # SGD specific (if using SGD)
  momentum: 0.9
  nesterov: true

# Learning rate scheduler configuration
scheduler:
  type: "cosine"
  
  # Warmup settings
  warmup: true
  warmup_epochs: 5
  
  # Cosine annealing specific
  T_max: 60                        # 匹配num_epochs
  eta_min: 0.000001
  
  # OneCycle specific
  pct_start: 0.3
  anneal_strategy: "cos"
  
  # Step scheduler specific
  step_size: 30
  gamma: 0.1

# Loss function configuration - 使用原始配置的权重
loss:
  type: "multilabel_combined"
  
  # Multi-label weights - 使用原始配置
  main_track_weight: 0.3           # 原始配置值
  fork_track_weight: 0.6           # 原始配置值
  overlap_weight: 3.0
  
  # Label smoothing
  label_smoothing: 0.0             # 关闭标签平滑
  
  # Focal loss parameters
  focal_alpha: 0.25
  focal_gamma: 2.0
  
  # Loss combination weights - 使用原始配置
  ce_weight: 0.5
  dice_weight: 0.5

# Data augmentation configuration - 简化数据增强
augmentation:
  train:
    # 基础几何变换
    - type: "horizontal_flip"
      p: 0.5
    
    - type: "vertical_flip"
      p: 0.3
    
    - type: "rotate"
      limit: 15
      p: 0.5
    
    - type: "perspective"
      scale: [0.05, 0.1]
      p: 0.3
    
    # 颜色增强
    - type: "brightness_contrast"
      brightness_limit: 0.2
      contrast_limit: 0.2
      p: 0.5
    
    # 模糊和噪声
    - type: "motion_blur"
      blur_limit: 7
      p: 0.3
    
    # Dropout
    - type: "coarse_dropout"
      max_holes: 8
      max_height: 128
      max_width: 128
      p: 0.5
  
  val: []

# Logging configuration
logging:
  log_dir: "outputs/logs"
  tensorboard: true
  tensorboard_dir: "outputs/runs"
  
  # Logging frequency
  log_prediction_interval: 5
  num_images_to_log: 4
  log_every_n_steps: 10
  log_images: true
  log_gradients: false
  log_graph: true
  
  # Visualization settings
  log_prediction_interval: 5
  
  # Experiment tracking
  experiment_name: "railway_modern"
  tags: ["cosine_lr", "ema"]

# Inference configuration
inference:
  batch_size: 8
  use_ema_model: true
  use_swa_model: false
  confidence_threshold: 0.3        # 原始配置使用0.3
  
  # Post-processing
  use_crf: false
  use_tta: true
  
  # Output settings
  save_probability_maps: false
  save_visualization: true
  visualization_alpha: 0.5

# Performance optimization
performance:
  cudnn_benchmark: true
  cudnn_deterministic: false
  use_channels_last: true
  gradient_checkpointing: false
  compile_model: false

# Distributed training (optional)
distributed:
  enable: false
  backend: "nccl"
  world_size: 1
  find_unused_parameters: false

# Experiment management
experiment:
  seed: 42
  deterministic: false
  name: "railway_modern_training"
  description: "Railway track segmentation based on original config"