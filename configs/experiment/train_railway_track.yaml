# 铁路轨道分割训练配置
# 继承基础配置，只设置特定的参数

# 数据相关配置
data:
  # 数据路径
  processed_data_path: "data/processed"
  json_dir: "data"  # JSON标注文件目录
  
  # 数据集类型
  dataset_type: "railway_track_dataset"
  
  # 图像设置
  image_size:
    width: 1280  # 调整尺寸提高训练速度
    height: 720
  
  # 批次大小
  batch_size:
    train: 4
    val: 8
    test: 1

# 训练相关配置
training:
  epochs: 100
  early_stopping_patience: 15
  gradient_accumulation_steps: 2
  
  # 优化器
  optimizer:
    type: "adamw"
    lr: 5e-4
    weight_decay: 1e-4

# 模型相关配置
model:
  # 分割模型架构
  architecture: "pan"  # Pyramid Attention Network
  encoder: "efficientnet-b4"
  encoder_weights: "imagenet"
  classes: 3  # 背景、主轨道、分叉轨道
  activation: "sigmoid"

# 损失函数配置
loss:
  type: "combined_loss"
  losses:
    - type: "dice_loss"
      smooth: 1.0
    - type: "focal_loss"
      alpha: [0.2, 0.4, 0.4]  # 背景、主轨道、分叉轨道的权重
      gamma: 2.0
  weights: [0.5, 0.5]  # Dice和Focal损失的权重

# 实验输出设置
checkpointing:
  save_dir: "models/checkpoints/railway_track"

visualization:
  save_dir: "outputs/visualizations/railway_track"

inference:
  output_dir: "outputs/predictions/railway_track"

# 特定相机配置
camera_configs:
  6mm:
    batch_size:
      train: 4
      val: 8
    image_size:
      width: 1280
      height: 720
  25mm:
    batch_size:
      train: 6
      val: 10
    image_size:
      width: 1280
      height: 720 