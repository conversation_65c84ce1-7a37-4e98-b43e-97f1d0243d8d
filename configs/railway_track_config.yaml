augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []
camera_configs:
  25mm:
    batch_size:
      train: 4
      val: 6
    image_size:
      height: 544
      width: 960
  6mm:
    batch_size:
      train: 4
      val: 6
    image_size:
      height: 544
      width: 960
checkpointing:
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 5
  auto_resume: true
  cleanup_strategy: best_k
data:
  batch_size:
    test: 4
    train: 4
    val: 4
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:
    height: 544
    width: 960
  json_dir: data/railway_annotation_6mm
  k_folds: null
  num_classes: 3
  processed_data_path: /home/<USER>/data/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15
  use_multilabel: true  # 启用多标签掩码生成
  mask_format: 'png'  # 掩码格式: 'png' 或 'npy' - PNG格式使用RGB通道存储多标签
inference:
  output_dir: outputs/predictions
  save_overlay: true
  test_time_augmentation: true
  threshold: 0.3
  tta_transforms:
  - horizontal_flip
  - vertical_flip
logging:
  log_dir: outputs/logs
  log_every_n_steps: 10
  log_images: true
  max_images_to_log: 8
  tensorboard: true
loss:
  # 使用多标签损失函数，与notebook保持一致
  type: multilabel_combined_loss
  jaccard_weight: 0.5
  focal_weight: 0.5
  alpha:
  - 0.1    # 背景权重
  - 0.3    # 主轨道权重
  - 0.6    # 分叉轨道权重
  gamma: 2.0
metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true
model:
  type: segmentation_model
  activation: sigmoid
  architecture: pan
  classes: 3
  encoder: efficientnet-b4
  encoder_weights: imagenet
  ensemble:
    enabled: false
    models:
    - type: segmentation_model
      encoder: efficientnet-b4
    - type: segmentation_model
      encoder: se_resnext50_32x4d
project:
  device: cuda
  name: railway-track-segmentation
  num_workers: 6  # 优化工作进程数以提高GPU利用率稳定性
  seed: 42
  gpu_optimization:
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 3  # 增加预取因子
    use_cuda_graph: false  # 禁用CUDA图以避免冲突
    channels_last: true  # 启用channels_last内存格式
    non_blocking: true  # 启用异步数据传输
    drop_last: true  # 确保批次大小一致
    stable_training: true  # 启用稳定训练模式
training:
  epochs: 60
  early_stopping_patience: 25
  gradient_accumulation_steps: 2
  clip_gradient: true
  mixed_precision: true
  eval_interval: 1
  save_interval: 5
  enable_step_saving: true
  save_every_n_steps: 500
  keep_step_checkpoints: 3
  memory_cleanup_interval: 20
  enable_memory_monitoring: true
  memory_leak_threshold: 500.0
  resume_training: true
  save_optimizer_state: true
  save_scheduler_state: true
  save_random_state: true
  validation:
    max_batches: 500
    sample_for_visualization: 2
  performance_optimization:
    cudnn_benchmark: true
    use_channels_last: true
    use_data_prefetcher: true
    gradient_checkpointing: false
    max_grad_norm: 1.0
  optimizer:
    betas:
    - 0.9
    - 0.999
    lr: 0.0003
    type: adamw
    weight_decay: 0.0001
  scheduler:
    min_lr: 1.0e-06
    type: cosine
    warmup_epochs: 5
visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5
  save_dir: outputs/visualizations
  save_predictions: true
