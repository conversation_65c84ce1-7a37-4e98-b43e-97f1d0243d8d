augmentation:
  train:
  - p: 0.5
    type: horizontal_flip
  - p: 0.3
    type: vertical_flip
  - limit: 15
    p: 0.5
    type: rotate
  - p: 0.3
    scale:
    - 0.05
    - 0.1
    type: perspective
  - brightness_limit: 0.2
    contrast_limit: 0.2
    p: 0.5
    type: brightness_contrast
  - blur_limit: 7
    p: 0.3
    type: motion_blur
  - max_height: 128
    max_holes: 8
    max_width: 128
    p: 0.5
    type: coarse_dropout
  val: []

camera_configs:
  25mm:
    batch_size:
      train: 1  # 大幅减少批次大小
      val: 2
    image_size:
      height: 1088
      width: 1920
  6mm:
    batch_size:
      train: 1  # 大幅减少批次大小
      val: 2
    image_size:
      height: 1088
      width: 1920

checkpointing:
  mode: max
  monitor: val_iou
  save_dir: models/checkpoints
  save_last: true
  save_top_k: 3  # 减少保存的检查点数量
  auto_resume: true
  cleanup_strategy: best_k

data:
  batch_size:
    test: 1    # 减少测试批次大小
    train: 2   # 大幅减少训练批次大小
    val: 2     # 减少验证批次大小
  camera_specific_training: false
  camera_types:
  - 6mm
  - 25mm
  classes:
  - background
  - main_track
  - fork_track
  dataset_type: railway_track_dataset
  image_size:
    height: 416   # 减小图像尺寸以节省内存
    width: 736    # 减小图像尺寸以节省内存
  json_dir: data/railway_annotation_6mm
  k_folds: null
  num_classes: 3
  processed_data_path: /home/<USER>/data/Download/railway_track_dataset
  raw_data_path: data/raw
  split_ratio:
    test: 0.15
    train: 0.7
    val: 0.15

inference:
  output_dir: outputs/predictions
  save_overlay: true
  test_time_augmentation: true
  threshold: 0.5
  tta_transforms:
  - horizontal_flip
  - vertical_flip

logging:
  log_dir: outputs/logs
  log_every_n_steps: 20  # 减少日志频率
  log_images: false      # 禁用图像日志以节省内存
  max_images_to_log: 2   # 减少图像数量
  tensorboard: true

loss:
  losses:
  - smooth: 1.0
    type: dice_loss
  - alpha:
    - 0.2
    - 0.4
    - 0.4
    gamma: 2.0
    type: focal_loss
  type: combined_loss
  weights:
  - 0.5
  - 0.5

metrics:
  enabled:
  - iou
  - dice
  - precision
  - recall
  - f1
  per_class_metrics: true

model:
  type: "segmentation_model"
  activation: sigmoid
  architecture: unet    # 使用更轻量的UNet而不是PAN
  classes: 3
  encoder: efficientnet-b0  # 使用更小的编码器
  encoder_weights: imagenet
  ensemble:
    enabled: false  # 禁用模型集成以节省内存

project:
  device: cuda
  name: railway-track-segmentation-memory-optimized
  num_workers: 2    # 减少工作进程数量
  seed: 42
  gpu_optimization:
    pin_memory: false      # 禁用pin_memory以节省内存
    persistent_workers: false  # 禁用持久工作进程
    prefetch_factor: 1     # 减少预取因子
    use_cuda_graph: false  # 禁用CUDA图优化

training:
  epochs: 150
  early_stopping_patience: 20
  gradient_accumulation_steps: 8  # 增加梯度累积步数来补偿小批次
  clip_gradient: true
  mixed_precision: true  # 保持混合精度训练以节省内存
  eval_interval: 2       # 减少验证频率
  save_interval: 10      # 减少保存频率
  
  # 基于步数的保存配置
  enable_step_saving: false  # 禁用步数保存以节省磁盘空间
  save_every_n_steps: 1000
  keep_step_checkpoints: 1
  
  # 内存管理配置
  memory_cleanup_interval: 5   # 增加内存清理频率
  enable_memory_monitoring: true
  memory_leak_threshold: 200.0  # 降低内存泄漏阈值
  
  # 断点重启配置
  resume_training: true
  save_optimizer_state: true
  save_scheduler_state: true
  save_random_state: true
  
  # 验证配置
  validation:
    max_batches: 100  # 大幅限制验证批次数量
    sample_for_visualization: 1  # 减少可视化样本数量
  
  # 性能优化配置
  performance_optimization:
    cudnn_benchmark: false    # 禁用cudnn基准测试
    use_channels_last: false  # 禁用通道最后格式
    use_data_prefetcher: false  # 禁用数据预取器
    gradient_checkpointing: true  # 启用梯度检查点以节省内存
    max_grad_norm: 1.0
  
  optimizer:
    betas:
    - 0.9
    - 0.999
    lr: 0.0005
    type: adamw
    weight_decay: 0.0001
  scheduler:
    min_lr: 0.000001
    type: cosine
    warmup_epochs: 5

visualization:
  colors:
    background:
    - 0
    - 0
    - 0
    fork_track:
    - 0
    - 255
    - 0
    main_track:
    - 255
    - 0
    - 0
  overlay_alpha: 0.5 