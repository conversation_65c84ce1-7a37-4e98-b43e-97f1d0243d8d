{"data_dir": "/home/<USER>/data/railway_track_dataset/", "config_file": "configs/railway_track_config.yaml", "seed": 0, "requested_models": ["eca_nfnet_l2", "seresnet152d"], "trained_models": ["eca_nfnet_l2", "seresnet152d"], "skipped_models": [], "models_config": [{"name": "eca_nfnet_l2", "architecture": "PAN", "encoder": "tu-eca_nfnet_l2", "encoder_weights": "imagenet", "epochs": 25, "pretrained_path": null, "description": "ECA-NFNet-L2 with ImageNet weights"}, {"name": "seresnet152d", "architecture": "PAN", "encoder": "tu-seresnet152d", "encoder_weights": "imagenet", "epochs": 30, "pretrained_path": null, "description": "SE-ResNet-152D with ImageNet weights"}], "timestamp": "2025-07-21T13:54:55.954766"}